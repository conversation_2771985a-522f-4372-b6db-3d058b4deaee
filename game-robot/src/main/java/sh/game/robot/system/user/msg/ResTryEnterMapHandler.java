package sh.game.robot.system.user.msg;

import com.sh.game.common.communication.msg.map.ResTryEnterMapMessage;
import com.sh.server.AbstractHandler;
import sh.game.robot.system.RobotSessionUtil;
import sh.game.robot.system.user.RobotUserManager;

/**
 * <p>玩家尝试进入地图</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-05-07 10:36:47
 */
public class ResTryEnterMapHandler extends AbstractHandler<ResTryEnterMapMessage> {

    @Override
    public void doAction(ResTryEnterMapMessage msg) {
        RobotUserManager.getInstance().resTryEnterMap(RobotSessionUtil.getSession(msg), msg);
    }

}
