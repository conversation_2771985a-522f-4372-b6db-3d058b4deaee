package sh.game.robot.system.user.msg;

import com.sh.game.common.communication.msg.system.tip.ResErrorMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>错误</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-05-07 10:36:45
 */
public class ResErrorHandler extends AbstractHandler<ResErrorMessage> {

    @Override
    public void doAction(ResErrorMessage msg) {
        System.out.println(msg.getError());
    }

}
