package sh.game.robot.system.scene.msg;

import com.sh.game.common.communication.msg.map.ResPlayerEnterMapMessage;
import com.sh.server.AbstractHandler;
import sh.game.robot.system.RobotSessionUtil;
import sh.game.robot.system.user.RobotUserManager;

/**
 * <p>玩家进入地图</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-05-07 10:36:47
 */
public class ResPlayerEnterMapHandler extends AbstractHandler<ResPlayerEnterMapMessage> {

    @Override
    public void doAction(ResPlayerEnterMapMessage msg) {
        RobotUserManager.getInstance().resPlayerEnterMapMessage(RobotSessionUtil.getRobot(msg), msg);
    }

}
