package sh.game.robot.system.user.msg;

import com.sh.game.common.communication.msg.system.activity.ResHappySevenDayActivityDataChangeMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>返回七日狂欢活动数据变动信息</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-05-07 10:36:46
 */
public class ResHappySevenDayActivityDataChangeHandler extends AbstractHandler<ResHappySevenDayActivityDataChangeMessage> {

    @Override
    public void doAction(ResHappySevenDayActivityDataChangeMessage msg) {

    }

}
