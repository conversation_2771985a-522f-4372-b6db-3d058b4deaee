package sh.game.robot.system.user.msg;

import com.sh.game.common.communication.msg.system.treasuremap.ResTreasureMapCountMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>藏宝图剩余使用次数</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-05-07 10:36:47
 */
public class ResTreasureMapCountHandler extends AbstractHandler<ResTreasureMapCountMessage> {

    @Override
    public void doAction(ResTreasureMapCountMessage msg) {

    }

}
