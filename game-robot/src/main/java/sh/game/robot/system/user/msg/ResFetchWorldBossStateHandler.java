package sh.game.robot.system.user.msg;

import com.sh.game.common.communication.msg.map.play.ResFetchWorldBossStateMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>返回对应类型boss的状态</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-05-07 10:36:46
 */
public class ResFetchWorldBossStateHandler extends AbstractHandler<ResFetchWorldBossStateMessage> {

    @Override
    public void doAction(ResFetchWorldBossStateMessage msg) {

    }

}
