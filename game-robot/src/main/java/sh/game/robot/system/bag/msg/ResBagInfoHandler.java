package sh.game.robot.system.bag.msg;

import com.sh.game.common.communication.msg.system.bag.ResBagInfoMessage;
import com.sh.server.AbstractHandler;
import sh.game.robot.system.RobotSessionUtil;
import sh.game.robot.system.bag.RobotBagManager;

/**
 * <p>返回请求背包</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-05-07 10:36:47
 */
public class ResBagInfoHandler extends AbstractHandler<ResBagInfoMessage> {

    @Override
    public void doAction(ResBagInfoMessage msg) {
        RobotBagManager.getInstance().resBagInfoMessage(RobotSessionUtil.getRobot(msg), msg);
    }

}
