package sh.game.robot.system.scene.msg;

import com.sh.game.common.communication.msg.map.ResPlayerChangeMapMessage;
import com.sh.server.AbstractHandler;
import sh.game.robot.system.RobotSessionUtil;
import sh.game.robot.system.user.RobotUserManager;

/**
 * <p>玩家切换地图</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-05-07 10:36:47
 */
public class ResPlayerChangeMapHandler extends AbstractHandler<ResPlayerChangeMapMessage> {

    @Override
    public void doAction(ResPlayerChangeMapMessage msg) {
        RobotUserManager.getInstance().resPlayerChangeMapMessage(RobotSessionUtil.getRobot(msg), msg);
    }

}
