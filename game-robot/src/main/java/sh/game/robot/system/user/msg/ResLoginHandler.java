package sh.game.robot.system.user.msg;

import com.sh.game.common.communication.msg.system.user.ResLoginMessage;
import com.sh.server.AbstractHandler;
import sh.game.robot.system.RobotSessionUtil;
import sh.game.robot.system.user.RobotUserManager;

/**
 * <p>通知登录成功</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-05-07 10:36:47
 */
public class ResLoginHandler extends AbstractHandler<ResLoginMessage> {

    @Override
    public void doAction(ResLoginMessage msg) {
        RobotUserManager.getInstance().resLogin(RobotSessionUtil.getSession(msg), msg.getUid());
    }

}
