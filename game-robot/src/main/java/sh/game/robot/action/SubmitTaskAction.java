package sh.game.robot.action;

import com.sh.game.common.communication.msg.system.task.ReqSubmitTaskMessage;
import sh.game.robot.Robot;
import sh.game.robot.action.constants.ActionEnum;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/5/8
 * @Desc : to do anything
 */
public class SubmitTaskAction implements IAction {
    @Override
    public ActionEnum doAction(Robot robot) {
        ReqSubmitTaskMessage msg = new ReqSubmitTaskMessage();
        msg.setTaskId(robot.getMainTaskData().getTaskId());
        msg.setValue(1);
        robot.sendMessage(msg);
        return ActionEnum.IDLE;
    }
}
