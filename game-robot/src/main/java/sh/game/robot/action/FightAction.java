package sh.game.robot.action;

import com.sh.game.common.communication.msg.map.bean.RoundMonster;
import com.sh.game.common.communication.msg.map.fight.ReqFightMessage;
import com.sh.game.map.util.GeomUtil;
import com.sh.game.common.util.TimeUtil;
import lombok.Getter;
import lombok.Setter;
import sh.game.robot.Robot;
import sh.game.robot.action.constants.ActionEnum;
import sh.game.robot.action.constants.RobotGameConst;
import sh.game.robot.system.scene.entity.ViewData;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/5/8
 * @Desc : to do anything
 */
@Getter
@Setter
public class FightAction implements IAction {

    private int mid;

    private long lid;

    private long lastDoTime = TimeUtil.getNowOfMills();

    private int delta = 400;

    @Override
    public ActionEnum doAction(Robot robot) {
        long now = TimeUtil.getNowOfMills();
        if(now - lastDoTime < delta) {
            return ActionEnum.FIGHT;
        }

        ViewData viewData = robot.getViewData();
        if(lid <= 0) {
            int nearDis = Integer.MAX_VALUE;
            for (RoundMonster roundMonster : viewData.getRoundMonsterMap().values()) {
                if(roundMonster.getMid() == mid) {
                    int dis = GeomUtil.distance(robot.getPoint().x, robot.getPoint().y, roundMonster.getX(), roundMonster.getY());
                    if(dis <= RobotGameConst.FIGHT_DIS) {
                        this.lid = roundMonster.getLid();
                        nearDis = dis;
                        break;
                    } else {
                        if(dis < nearDis) {
                            nearDis = dis;
                            this.lid = roundMonster.getLid();
                        }
                    }
                }
            }
            if(nearDis > RobotGameConst.FIGHT_DIS) {
                if(this.lid > 0) {
                    WalkAction walkAction = robot.getAction(ActionEnum.WALK);
                    walkAction.setMapId(robot.getMapId());
                    RoundMonster roundMonster = viewData.getRoundMonsterMap().get(this.lid);
                    walkAction.setX(roundMonster.getX());
                    walkAction.setY(roundMonster.getY());
                    return ActionEnum.WALK;
                } else {
                    return ActionEnum.FIGHT;
                }
            }
        } else {
            RoundMonster roundMonster = viewData.getRoundMonsterMap().get(lid);
            if(roundMonster == null) {
                this.lid = 0;
                this.mid = 0;
                return ActionEnum.WALK;
            }
            int dis = GeomUtil.distance(robot.getPoint().x, robot.getPoint().y, roundMonster.getX(), roundMonster.getY());
            if(dis > RobotGameConst.FIGHT_DIS) {
                WalkAction walkAction = robot.getAction(ActionEnum.WALK);
                walkAction.setMapId(robot.getMapId());
                walkAction.setX(roundMonster.getX());
                walkAction.setY(roundMonster.getY());
                return ActionEnum.WALK;
            }
        }
        RoundMonster target = viewData.getRoundMonsterMap().get(lid);
        ReqFightMessage msg = new ReqFightMessage();
        msg.setSkillId(10101);
        msg.setX(target.getX());
        msg.setY(target.getY());
        robot.sendMessage(msg);
        return ActionEnum.FIGHT;
    }
}