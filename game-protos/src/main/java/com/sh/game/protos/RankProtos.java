// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: rank.proto

package com.sh.game.protos;

public final class RankProtos {
  private RankProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RankDataBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:rank.RankDataBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *排名
     * </pre>
     *
     * <code>int32 rank = 1;</code>
     * @return The rank.
     */
    int getRank();

    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <pre>
     *职业
     * </pre>
     *
     * <code>int32 parameter = 3;</code>
     * @return The parameter.
     */
    int getParameter();

    /**
     * <pre>
     *排名数据
     * </pre>
     *
     * <code>int32 dataValue = 4;</code>
     * @return The dataValue.
     */
    int getDataValue();

    /**
     * <pre>
     *排名数据2
     * </pre>
     *
     * <code>int32 dataValue2 = 5;</code>
     * @return The dataValue2.
     */
    int getDataValue2();

    /**
     * <pre>
     *行会名称
     * </pre>
     *
     * <code>string unionName = 6;</code>
     * @return The unionName.
     */
    java.lang.String getUnionName();
    /**
     * <pre>
     *行会名称
     * </pre>
     *
     * <code>string unionName = 6;</code>
     * @return The bytes for unionName.
     */
    com.google.protobuf.ByteString
        getUnionNameBytes();

    /**
     * <pre>
     *玩家编号
     * </pre>
     *
     * <code>int64 roleId = 7;</code>
     * @return The roleId.
     */
    long getRoleId();

    /**
     * <pre>
     *参数列表(参数含义自行约定)
     * </pre>
     *
     * <code>repeated int32 parameterList = 8;</code>
     * @return A list containing the parameterList.
     */
    java.util.List<java.lang.Integer> getParameterListList();
    /**
     * <pre>
     *参数列表(参数含义自行约定)
     * </pre>
     *
     * <code>repeated int32 parameterList = 8;</code>
     * @return The count of parameterList.
     */
    int getParameterListCount();
    /**
     * <pre>
     *参数列表(参数含义自行约定)
     * </pre>
     *
     * <code>repeated int32 parameterList = 8;</code>
     * @param index The index of the element to return.
     * @return The parameterList at the given index.
     */
    int getParameterList(int index);
  }
  /**
   * Protobuf type {@code rank.RankDataBean}
   */
  public static final class RankDataBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:rank.RankDataBean)
      RankDataBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RankDataBean.newBuilder() to construct.
    private RankDataBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RankDataBean() {
      name_ = "";
      unionName_ = "";
      parameterList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RankDataBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RankDataBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              rank_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 24: {

              parameter_ = input.readInt32();
              break;
            }
            case 32: {

              dataValue_ = input.readInt32();
              break;
            }
            case 40: {

              dataValue2_ = input.readInt32();
              break;
            }
            case 50: {
              java.lang.String s = input.readStringRequireUtf8();

              unionName_ = s;
              break;
            }
            case 56: {

              roleId_ = input.readInt64();
              break;
            }
            case 64: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                parameterList_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              parameterList_.addInt(input.readInt32());
              break;
            }
            case 66: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                parameterList_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                parameterList_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          parameterList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.RankProtos.internal_static_rank_RankDataBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.RankProtos.internal_static_rank_RankDataBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.RankProtos.RankDataBean.class, com.sh.game.protos.RankProtos.RankDataBean.Builder.class);
    }

    public static final int RANK_FIELD_NUMBER = 1;
    private int rank_;
    /**
     * <pre>
     *排名
     * </pre>
     *
     * <code>int32 rank = 1;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PARAMETER_FIELD_NUMBER = 3;
    private int parameter_;
    /**
     * <pre>
     *职业
     * </pre>
     *
     * <code>int32 parameter = 3;</code>
     * @return The parameter.
     */
    @java.lang.Override
    public int getParameter() {
      return parameter_;
    }

    public static final int DATAVALUE_FIELD_NUMBER = 4;
    private int dataValue_;
    /**
     * <pre>
     *排名数据
     * </pre>
     *
     * <code>int32 dataValue = 4;</code>
     * @return The dataValue.
     */
    @java.lang.Override
    public int getDataValue() {
      return dataValue_;
    }

    public static final int DATAVALUE2_FIELD_NUMBER = 5;
    private int dataValue2_;
    /**
     * <pre>
     *排名数据2
     * </pre>
     *
     * <code>int32 dataValue2 = 5;</code>
     * @return The dataValue2.
     */
    @java.lang.Override
    public int getDataValue2() {
      return dataValue2_;
    }

    public static final int UNIONNAME_FIELD_NUMBER = 6;
    private volatile java.lang.Object unionName_;
    /**
     * <pre>
     *行会名称
     * </pre>
     *
     * <code>string unionName = 6;</code>
     * @return The unionName.
     */
    @java.lang.Override
    public java.lang.String getUnionName() {
      java.lang.Object ref = unionName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        unionName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *行会名称
     * </pre>
     *
     * <code>string unionName = 6;</code>
     * @return The bytes for unionName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUnionNameBytes() {
      java.lang.Object ref = unionName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        unionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ROLEID_FIELD_NUMBER = 7;
    private long roleId_;
    /**
     * <pre>
     *玩家编号
     * </pre>
     *
     * <code>int64 roleId = 7;</code>
     * @return The roleId.
     */
    @java.lang.Override
    public long getRoleId() {
      return roleId_;
    }

    public static final int PARAMETERLIST_FIELD_NUMBER = 8;
    private com.google.protobuf.Internal.IntList parameterList_;
    /**
     * <pre>
     *参数列表(参数含义自行约定)
     * </pre>
     *
     * <code>repeated int32 parameterList = 8;</code>
     * @return A list containing the parameterList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getParameterListList() {
      return parameterList_;
    }
    /**
     * <pre>
     *参数列表(参数含义自行约定)
     * </pre>
     *
     * <code>repeated int32 parameterList = 8;</code>
     * @return The count of parameterList.
     */
    public int getParameterListCount() {
      return parameterList_.size();
    }
    /**
     * <pre>
     *参数列表(参数含义自行约定)
     * </pre>
     *
     * <code>repeated int32 parameterList = 8;</code>
     * @param index The index of the element to return.
     * @return The parameterList at the given index.
     */
    public int getParameterList(int index) {
      return parameterList_.getInt(index);
    }
    private int parameterListMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (rank_ != 0) {
        output.writeInt32(1, rank_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (parameter_ != 0) {
        output.writeInt32(3, parameter_);
      }
      if (dataValue_ != 0) {
        output.writeInt32(4, dataValue_);
      }
      if (dataValue2_ != 0) {
        output.writeInt32(5, dataValue2_);
      }
      if (!getUnionNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, unionName_);
      }
      if (roleId_ != 0L) {
        output.writeInt64(7, roleId_);
      }
      if (getParameterListList().size() > 0) {
        output.writeUInt32NoTag(66);
        output.writeUInt32NoTag(parameterListMemoizedSerializedSize);
      }
      for (int i = 0; i < parameterList_.size(); i++) {
        output.writeInt32NoTag(parameterList_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rank_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rank_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (parameter_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, parameter_);
      }
      if (dataValue_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, dataValue_);
      }
      if (dataValue2_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, dataValue2_);
      }
      if (!getUnionNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, unionName_);
      }
      if (roleId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, roleId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < parameterList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(parameterList_.getInt(i));
        }
        size += dataSize;
        if (!getParameterListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        parameterListMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.RankProtos.RankDataBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.RankProtos.RankDataBean other = (com.sh.game.protos.RankProtos.RankDataBean) obj;

      if (getRank()
          != other.getRank()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (getParameter()
          != other.getParameter()) return false;
      if (getDataValue()
          != other.getDataValue()) return false;
      if (getDataValue2()
          != other.getDataValue2()) return false;
      if (!getUnionName()
          .equals(other.getUnionName())) return false;
      if (getRoleId()
          != other.getRoleId()) return false;
      if (!getParameterListList()
          .equals(other.getParameterListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + PARAMETER_FIELD_NUMBER;
      hash = (53 * hash) + getParameter();
      hash = (37 * hash) + DATAVALUE_FIELD_NUMBER;
      hash = (53 * hash) + getDataValue();
      hash = (37 * hash) + DATAVALUE2_FIELD_NUMBER;
      hash = (53 * hash) + getDataValue2();
      hash = (37 * hash) + UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUnionName().hashCode();
      hash = (37 * hash) + ROLEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoleId());
      if (getParameterListCount() > 0) {
        hash = (37 * hash) + PARAMETERLIST_FIELD_NUMBER;
        hash = (53 * hash) + getParameterListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.RankDataBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.RankProtos.RankDataBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code rank.RankDataBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:rank.RankDataBean)
        com.sh.game.protos.RankProtos.RankDataBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.RankProtos.internal_static_rank_RankDataBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.RankProtos.internal_static_rank_RankDataBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.RankProtos.RankDataBean.class, com.sh.game.protos.RankProtos.RankDataBean.Builder.class);
      }

      // Construct using com.sh.game.protos.RankProtos.RankDataBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        rank_ = 0;

        name_ = "";

        parameter_ = 0;

        dataValue_ = 0;

        dataValue2_ = 0;

        unionName_ = "";

        roleId_ = 0L;

        parameterList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.RankProtos.internal_static_rank_RankDataBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.RankDataBean getDefaultInstanceForType() {
        return com.sh.game.protos.RankProtos.RankDataBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.RankDataBean build() {
        com.sh.game.protos.RankProtos.RankDataBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.RankDataBean buildPartial() {
        com.sh.game.protos.RankProtos.RankDataBean result = new com.sh.game.protos.RankProtos.RankDataBean(this);
        int from_bitField0_ = bitField0_;
        result.rank_ = rank_;
        result.name_ = name_;
        result.parameter_ = parameter_;
        result.dataValue_ = dataValue_;
        result.dataValue2_ = dataValue2_;
        result.unionName_ = unionName_;
        result.roleId_ = roleId_;
        if (((bitField0_ & 0x00000001) != 0)) {
          parameterList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.parameterList_ = parameterList_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.RankProtos.RankDataBean) {
          return mergeFrom((com.sh.game.protos.RankProtos.RankDataBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.RankProtos.RankDataBean other) {
        if (other == com.sh.game.protos.RankProtos.RankDataBean.getDefaultInstance()) return this;
        if (other.getRank() != 0) {
          setRank(other.getRank());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.getParameter() != 0) {
          setParameter(other.getParameter());
        }
        if (other.getDataValue() != 0) {
          setDataValue(other.getDataValue());
        }
        if (other.getDataValue2() != 0) {
          setDataValue2(other.getDataValue2());
        }
        if (!other.getUnionName().isEmpty()) {
          unionName_ = other.unionName_;
          onChanged();
        }
        if (other.getRoleId() != 0L) {
          setRoleId(other.getRoleId());
        }
        if (!other.parameterList_.isEmpty()) {
          if (parameterList_.isEmpty()) {
            parameterList_ = other.parameterList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureParameterListIsMutable();
            parameterList_.addAll(other.parameterList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.RankProtos.RankDataBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.RankProtos.RankDataBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int rank_ ;
      /**
       * <pre>
       *排名
       * </pre>
       *
       * <code>int32 rank = 1;</code>
       * @return The rank.
       */
      @java.lang.Override
      public int getRank() {
        return rank_;
      }
      /**
       * <pre>
       *排名
       * </pre>
       *
       * <code>int32 rank = 1;</code>
       * @param value The rank to set.
       * @return This builder for chaining.
       */
      public Builder setRank(int value) {
        
        rank_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *排名
       * </pre>
       *
       * <code>int32 rank = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRank() {
        
        rank_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private int parameter_ ;
      /**
       * <pre>
       *职业
       * </pre>
       *
       * <code>int32 parameter = 3;</code>
       * @return The parameter.
       */
      @java.lang.Override
      public int getParameter() {
        return parameter_;
      }
      /**
       * <pre>
       *职业
       * </pre>
       *
       * <code>int32 parameter = 3;</code>
       * @param value The parameter to set.
       * @return This builder for chaining.
       */
      public Builder setParameter(int value) {
        
        parameter_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *职业
       * </pre>
       *
       * <code>int32 parameter = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearParameter() {
        
        parameter_ = 0;
        onChanged();
        return this;
      }

      private int dataValue_ ;
      /**
       * <pre>
       *排名数据
       * </pre>
       *
       * <code>int32 dataValue = 4;</code>
       * @return The dataValue.
       */
      @java.lang.Override
      public int getDataValue() {
        return dataValue_;
      }
      /**
       * <pre>
       *排名数据
       * </pre>
       *
       * <code>int32 dataValue = 4;</code>
       * @param value The dataValue to set.
       * @return This builder for chaining.
       */
      public Builder setDataValue(int value) {
        
        dataValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *排名数据
       * </pre>
       *
       * <code>int32 dataValue = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataValue() {
        
        dataValue_ = 0;
        onChanged();
        return this;
      }

      private int dataValue2_ ;
      /**
       * <pre>
       *排名数据2
       * </pre>
       *
       * <code>int32 dataValue2 = 5;</code>
       * @return The dataValue2.
       */
      @java.lang.Override
      public int getDataValue2() {
        return dataValue2_;
      }
      /**
       * <pre>
       *排名数据2
       * </pre>
       *
       * <code>int32 dataValue2 = 5;</code>
       * @param value The dataValue2 to set.
       * @return This builder for chaining.
       */
      public Builder setDataValue2(int value) {
        
        dataValue2_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *排名数据2
       * </pre>
       *
       * <code>int32 dataValue2 = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDataValue2() {
        
        dataValue2_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object unionName_ = "";
      /**
       * <pre>
       *行会名称
       * </pre>
       *
       * <code>string unionName = 6;</code>
       * @return The unionName.
       */
      public java.lang.String getUnionName() {
        java.lang.Object ref = unionName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          unionName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *行会名称
       * </pre>
       *
       * <code>string unionName = 6;</code>
       * @return The bytes for unionName.
       */
      public com.google.protobuf.ByteString
          getUnionNameBytes() {
        java.lang.Object ref = unionName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          unionName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *行会名称
       * </pre>
       *
       * <code>string unionName = 6;</code>
       * @param value The unionName to set.
       * @return This builder for chaining.
       */
      public Builder setUnionName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        unionName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *行会名称
       * </pre>
       *
       * <code>string unionName = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnionName() {
        
        unionName_ = getDefaultInstance().getUnionName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *行会名称
       * </pre>
       *
       * <code>string unionName = 6;</code>
       * @param value The bytes for unionName to set.
       * @return This builder for chaining.
       */
      public Builder setUnionNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        unionName_ = value;
        onChanged();
        return this;
      }

      private long roleId_ ;
      /**
       * <pre>
       *玩家编号
       * </pre>
       *
       * <code>int64 roleId = 7;</code>
       * @return The roleId.
       */
      @java.lang.Override
      public long getRoleId() {
        return roleId_;
      }
      /**
       * <pre>
       *玩家编号
       * </pre>
       *
       * <code>int64 roleId = 7;</code>
       * @param value The roleId to set.
       * @return This builder for chaining.
       */
      public Builder setRoleId(long value) {
        
        roleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家编号
       * </pre>
       *
       * <code>int64 roleId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearRoleId() {
        
        roleId_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList parameterList_ = emptyIntList();
      private void ensureParameterListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          parameterList_ = mutableCopy(parameterList_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       *参数列表(参数含义自行约定)
       * </pre>
       *
       * <code>repeated int32 parameterList = 8;</code>
       * @return A list containing the parameterList.
       */
      public java.util.List<java.lang.Integer>
          getParameterListList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(parameterList_) : parameterList_;
      }
      /**
       * <pre>
       *参数列表(参数含义自行约定)
       * </pre>
       *
       * <code>repeated int32 parameterList = 8;</code>
       * @return The count of parameterList.
       */
      public int getParameterListCount() {
        return parameterList_.size();
      }
      /**
       * <pre>
       *参数列表(参数含义自行约定)
       * </pre>
       *
       * <code>repeated int32 parameterList = 8;</code>
       * @param index The index of the element to return.
       * @return The parameterList at the given index.
       */
      public int getParameterList(int index) {
        return parameterList_.getInt(index);
      }
      /**
       * <pre>
       *参数列表(参数含义自行约定)
       * </pre>
       *
       * <code>repeated int32 parameterList = 8;</code>
       * @param index The index to set the value at.
       * @param value The parameterList to set.
       * @return This builder for chaining.
       */
      public Builder setParameterList(
          int index, int value) {
        ensureParameterListIsMutable();
        parameterList_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *参数列表(参数含义自行约定)
       * </pre>
       *
       * <code>repeated int32 parameterList = 8;</code>
       * @param value The parameterList to add.
       * @return This builder for chaining.
       */
      public Builder addParameterList(int value) {
        ensureParameterListIsMutable();
        parameterList_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *参数列表(参数含义自行约定)
       * </pre>
       *
       * <code>repeated int32 parameterList = 8;</code>
       * @param values The parameterList to add.
       * @return This builder for chaining.
       */
      public Builder addAllParameterList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureParameterListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, parameterList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *参数列表(参数含义自行约定)
       * </pre>
       *
       * <code>repeated int32 parameterList = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearParameterList() {
        parameterList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:rank.RankDataBean)
    }

    // @@protoc_insertion_point(class_scope:rank.RankDataBean)
    private static final com.sh.game.protos.RankProtos.RankDataBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.RankProtos.RankDataBean();
    }

    public static com.sh.game.protos.RankProtos.RankDataBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RankDataBean>
        PARSER = new com.google.protobuf.AbstractParser<RankDataBean>() {
      @java.lang.Override
      public RankDataBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RankDataBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RankDataBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RankDataBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.RankProtos.RankDataBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqLookRankOrBuilder extends
      // @@protoc_insertion_point(interface_extends:rank.ReqLookRank)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ReqLookRank' id='1' desc='请求查看排行榜' 
   * </pre>
   *
   * Protobuf type {@code rank.ReqLookRank}
   */
  public static final class ReqLookRank extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:rank.ReqLookRank)
      ReqLookRankOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqLookRank.newBuilder() to construct.
    private ReqLookRank(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqLookRank() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqLookRank();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqLookRank(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.RankProtos.internal_static_rank_ReqLookRank_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.RankProtos.internal_static_rank_ReqLookRank_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.RankProtos.ReqLookRank.class, com.sh.game.protos.RankProtos.ReqLookRank.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.RankProtos.ReqLookRank)) {
        return super.equals(obj);
      }
      com.sh.game.protos.RankProtos.ReqLookRank other = (com.sh.game.protos.RankProtos.ReqLookRank) obj;

      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ReqLookRank parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.RankProtos.ReqLookRank prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqLookRank' id='1' desc='请求查看排行榜' 
     * </pre>
     *
     * Protobuf type {@code rank.ReqLookRank}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:rank.ReqLookRank)
        com.sh.game.protos.RankProtos.ReqLookRankOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ReqLookRank_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ReqLookRank_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.RankProtos.ReqLookRank.class, com.sh.game.protos.RankProtos.ReqLookRank.Builder.class);
      }

      // Construct using com.sh.game.protos.RankProtos.ReqLookRank.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ReqLookRank_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ReqLookRank getDefaultInstanceForType() {
        return com.sh.game.protos.RankProtos.ReqLookRank.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ReqLookRank build() {
        com.sh.game.protos.RankProtos.ReqLookRank result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ReqLookRank buildPartial() {
        com.sh.game.protos.RankProtos.ReqLookRank result = new com.sh.game.protos.RankProtos.ReqLookRank(this);
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.RankProtos.ReqLookRank) {
          return mergeFrom((com.sh.game.protos.RankProtos.ReqLookRank)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.RankProtos.ReqLookRank other) {
        if (other == com.sh.game.protos.RankProtos.ReqLookRank.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.RankProtos.ReqLookRank parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.RankProtos.ReqLookRank) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:rank.ReqLookRank)
    }

    // @@protoc_insertion_point(class_scope:rank.ReqLookRank)
    private static final com.sh.game.protos.RankProtos.ReqLookRank DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.RankProtos.ReqLookRank();
    }

    public static com.sh.game.protos.RankProtos.ReqLookRank getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqLookRank>
        PARSER = new com.google.protobuf.AbstractParser<ReqLookRank>() {
      @java.lang.Override
      public ReqLookRank parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqLookRank(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqLookRank> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqLookRank> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.RankProtos.ReqLookRank getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResLookRankOrBuilder extends
      // @@protoc_insertion_point(interface_extends:rank.ResLookRank)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *第一名简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
     * @return Whether the firstAvatar field is set.
     */
    boolean hasFirstAvatar();
    /**
     * <pre>
     *第一名简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
     * @return The firstAvatar.
     */
    com.sh.game.protos.AbcProtos.RoleSimpleBean getFirstAvatar();
    /**
     * <pre>
     *第一名简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
     */
    com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder getFirstAvatarOrBuilder();

    /**
     * <pre>
     *我的排名
     * </pre>
     *
     * <code>.rank.RankDataBean myRank = 3;</code>
     * @return Whether the myRank field is set.
     */
    boolean hasMyRank();
    /**
     * <pre>
     *我的排名
     * </pre>
     *
     * <code>.rank.RankDataBean myRank = 3;</code>
     * @return The myRank.
     */
    com.sh.game.protos.RankProtos.RankDataBean getMyRank();
    /**
     * <pre>
     *我的排名
     * </pre>
     *
     * <code>.rank.RankDataBean myRank = 3;</code>
     */
    com.sh.game.protos.RankProtos.RankDataBeanOrBuilder getMyRankOrBuilder();

    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    java.util.List<com.sh.game.protos.RankProtos.RankDataBean> 
        getRankListList();
    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    com.sh.game.protos.RankProtos.RankDataBean getRankList(int index);
    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    int getRankListCount();
    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    java.util.List<? extends com.sh.game.protos.RankProtos.RankDataBeanOrBuilder> 
        getRankListOrBuilderList();
    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    com.sh.game.protos.RankProtos.RankDataBeanOrBuilder getRankListOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResLookRank' id='2' desc='排行榜数据' 
   * </pre>
   *
   * Protobuf type {@code rank.ResLookRank}
   */
  public static final class ResLookRank extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:rank.ResLookRank)
      ResLookRankOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResLookRank.newBuilder() to construct.
    private ResLookRank(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResLookRank() {
      rankList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResLookRank();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResLookRank(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 18: {
              com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder subBuilder = null;
              if (firstAvatar_ != null) {
                subBuilder = firstAvatar_.toBuilder();
              }
              firstAvatar_ = input.readMessage(com.sh.game.protos.AbcProtos.RoleSimpleBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(firstAvatar_);
                firstAvatar_ = subBuilder.buildPartial();
              }

              break;
            }
            case 26: {
              com.sh.game.protos.RankProtos.RankDataBean.Builder subBuilder = null;
              if (myRank_ != null) {
                subBuilder = myRank_.toBuilder();
              }
              myRank_ = input.readMessage(com.sh.game.protos.RankProtos.RankDataBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(myRank_);
                myRank_ = subBuilder.buildPartial();
              }

              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rankList_ = new java.util.ArrayList<com.sh.game.protos.RankProtos.RankDataBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              rankList_.add(
                  input.readMessage(com.sh.game.protos.RankProtos.RankDataBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          rankList_ = java.util.Collections.unmodifiableList(rankList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.RankProtos.internal_static_rank_ResLookRank_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.RankProtos.internal_static_rank_ResLookRank_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.RankProtos.ResLookRank.class, com.sh.game.protos.RankProtos.ResLookRank.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int FIRSTAVATAR_FIELD_NUMBER = 2;
    private com.sh.game.protos.AbcProtos.RoleSimpleBean firstAvatar_;
    /**
     * <pre>
     *第一名简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
     * @return Whether the firstAvatar field is set.
     */
    @java.lang.Override
    public boolean hasFirstAvatar() {
      return firstAvatar_ != null;
    }
    /**
     * <pre>
     *第一名简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
     * @return The firstAvatar.
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.RoleSimpleBean getFirstAvatar() {
      return firstAvatar_ == null ? com.sh.game.protos.AbcProtos.RoleSimpleBean.getDefaultInstance() : firstAvatar_;
    }
    /**
     * <pre>
     *第一名简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder getFirstAvatarOrBuilder() {
      return getFirstAvatar();
    }

    public static final int MYRANK_FIELD_NUMBER = 3;
    private com.sh.game.protos.RankProtos.RankDataBean myRank_;
    /**
     * <pre>
     *我的排名
     * </pre>
     *
     * <code>.rank.RankDataBean myRank = 3;</code>
     * @return Whether the myRank field is set.
     */
    @java.lang.Override
    public boolean hasMyRank() {
      return myRank_ != null;
    }
    /**
     * <pre>
     *我的排名
     * </pre>
     *
     * <code>.rank.RankDataBean myRank = 3;</code>
     * @return The myRank.
     */
    @java.lang.Override
    public com.sh.game.protos.RankProtos.RankDataBean getMyRank() {
      return myRank_ == null ? com.sh.game.protos.RankProtos.RankDataBean.getDefaultInstance() : myRank_;
    }
    /**
     * <pre>
     *我的排名
     * </pre>
     *
     * <code>.rank.RankDataBean myRank = 3;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.RankProtos.RankDataBeanOrBuilder getMyRankOrBuilder() {
      return getMyRank();
    }

    public static final int RANKLIST_FIELD_NUMBER = 4;
    private java.util.List<com.sh.game.protos.RankProtos.RankDataBean> rankList_;
    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.RankProtos.RankDataBean> getRankListList() {
      return rankList_;
    }
    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.RankProtos.RankDataBeanOrBuilder> 
        getRankListOrBuilderList() {
      return rankList_;
    }
    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    @java.lang.Override
    public int getRankListCount() {
      return rankList_.size();
    }
    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.RankProtos.RankDataBean getRankList(int index) {
      return rankList_.get(index);
    }
    /**
     * <pre>
     *排行榜数据
     * </pre>
     *
     * <code>repeated .rank.RankDataBean rankList = 4;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.RankProtos.RankDataBeanOrBuilder getRankListOrBuilder(
        int index) {
      return rankList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (firstAvatar_ != null) {
        output.writeMessage(2, getFirstAvatar());
      }
      if (myRank_ != null) {
        output.writeMessage(3, getMyRank());
      }
      for (int i = 0; i < rankList_.size(); i++) {
        output.writeMessage(4, rankList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (firstAvatar_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getFirstAvatar());
      }
      if (myRank_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getMyRank());
      }
      for (int i = 0; i < rankList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, rankList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.RankProtos.ResLookRank)) {
        return super.equals(obj);
      }
      com.sh.game.protos.RankProtos.ResLookRank other = (com.sh.game.protos.RankProtos.ResLookRank) obj;

      if (getType()
          != other.getType()) return false;
      if (hasFirstAvatar() != other.hasFirstAvatar()) return false;
      if (hasFirstAvatar()) {
        if (!getFirstAvatar()
            .equals(other.getFirstAvatar())) return false;
      }
      if (hasMyRank() != other.hasMyRank()) return false;
      if (hasMyRank()) {
        if (!getMyRank()
            .equals(other.getMyRank())) return false;
      }
      if (!getRankListList()
          .equals(other.getRankListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      if (hasFirstAvatar()) {
        hash = (37 * hash) + FIRSTAVATAR_FIELD_NUMBER;
        hash = (53 * hash) + getFirstAvatar().hashCode();
      }
      if (hasMyRank()) {
        hash = (37 * hash) + MYRANK_FIELD_NUMBER;
        hash = (53 * hash) + getMyRank().hashCode();
      }
      if (getRankListCount() > 0) {
        hash = (37 * hash) + RANKLIST_FIELD_NUMBER;
        hash = (53 * hash) + getRankListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ResLookRank parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.RankProtos.ResLookRank prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResLookRank' id='2' desc='排行榜数据' 
     * </pre>
     *
     * Protobuf type {@code rank.ResLookRank}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:rank.ResLookRank)
        com.sh.game.protos.RankProtos.ResLookRankOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ResLookRank_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ResLookRank_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.RankProtos.ResLookRank.class, com.sh.game.protos.RankProtos.ResLookRank.Builder.class);
      }

      // Construct using com.sh.game.protos.RankProtos.ResLookRank.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRankListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        if (firstAvatarBuilder_ == null) {
          firstAvatar_ = null;
        } else {
          firstAvatar_ = null;
          firstAvatarBuilder_ = null;
        }
        if (myRankBuilder_ == null) {
          myRank_ = null;
        } else {
          myRank_ = null;
          myRankBuilder_ = null;
        }
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rankListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ResLookRank_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ResLookRank getDefaultInstanceForType() {
        return com.sh.game.protos.RankProtos.ResLookRank.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ResLookRank build() {
        com.sh.game.protos.RankProtos.ResLookRank result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ResLookRank buildPartial() {
        com.sh.game.protos.RankProtos.ResLookRank result = new com.sh.game.protos.RankProtos.ResLookRank(this);
        int from_bitField0_ = bitField0_;
        result.type_ = type_;
        if (firstAvatarBuilder_ == null) {
          result.firstAvatar_ = firstAvatar_;
        } else {
          result.firstAvatar_ = firstAvatarBuilder_.build();
        }
        if (myRankBuilder_ == null) {
          result.myRank_ = myRank_;
        } else {
          result.myRank_ = myRankBuilder_.build();
        }
        if (rankListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rankList_ = java.util.Collections.unmodifiableList(rankList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rankList_ = rankList_;
        } else {
          result.rankList_ = rankListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.RankProtos.ResLookRank) {
          return mergeFrom((com.sh.game.protos.RankProtos.ResLookRank)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.RankProtos.ResLookRank other) {
        if (other == com.sh.game.protos.RankProtos.ResLookRank.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.hasFirstAvatar()) {
          mergeFirstAvatar(other.getFirstAvatar());
        }
        if (other.hasMyRank()) {
          mergeMyRank(other.getMyRank());
        }
        if (rankListBuilder_ == null) {
          if (!other.rankList_.isEmpty()) {
            if (rankList_.isEmpty()) {
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRankListIsMutable();
              rankList_.addAll(other.rankList_);
            }
            onChanged();
          }
        } else {
          if (!other.rankList_.isEmpty()) {
            if (rankListBuilder_.isEmpty()) {
              rankListBuilder_.dispose();
              rankListBuilder_ = null;
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rankListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRankListFieldBuilder() : null;
            } else {
              rankListBuilder_.addAllMessages(other.rankList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.RankProtos.ResLookRank parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.RankProtos.ResLookRank) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private com.sh.game.protos.AbcProtos.RoleSimpleBean firstAvatar_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.AbcProtos.RoleSimpleBean, com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder, com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder> firstAvatarBuilder_;
      /**
       * <pre>
       *第一名简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
       * @return Whether the firstAvatar field is set.
       */
      public boolean hasFirstAvatar() {
        return firstAvatarBuilder_ != null || firstAvatar_ != null;
      }
      /**
       * <pre>
       *第一名简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
       * @return The firstAvatar.
       */
      public com.sh.game.protos.AbcProtos.RoleSimpleBean getFirstAvatar() {
        if (firstAvatarBuilder_ == null) {
          return firstAvatar_ == null ? com.sh.game.protos.AbcProtos.RoleSimpleBean.getDefaultInstance() : firstAvatar_;
        } else {
          return firstAvatarBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *第一名简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
       */
      public Builder setFirstAvatar(com.sh.game.protos.AbcProtos.RoleSimpleBean value) {
        if (firstAvatarBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          firstAvatar_ = value;
          onChanged();
        } else {
          firstAvatarBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *第一名简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
       */
      public Builder setFirstAvatar(
          com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder builderForValue) {
        if (firstAvatarBuilder_ == null) {
          firstAvatar_ = builderForValue.build();
          onChanged();
        } else {
          firstAvatarBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *第一名简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
       */
      public Builder mergeFirstAvatar(com.sh.game.protos.AbcProtos.RoleSimpleBean value) {
        if (firstAvatarBuilder_ == null) {
          if (firstAvatar_ != null) {
            firstAvatar_ =
              com.sh.game.protos.AbcProtos.RoleSimpleBean.newBuilder(firstAvatar_).mergeFrom(value).buildPartial();
          } else {
            firstAvatar_ = value;
          }
          onChanged();
        } else {
          firstAvatarBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *第一名简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
       */
      public Builder clearFirstAvatar() {
        if (firstAvatarBuilder_ == null) {
          firstAvatar_ = null;
          onChanged();
        } else {
          firstAvatar_ = null;
          firstAvatarBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *第一名简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder getFirstAvatarBuilder() {
        
        onChanged();
        return getFirstAvatarFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *第一名简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder getFirstAvatarOrBuilder() {
        if (firstAvatarBuilder_ != null) {
          return firstAvatarBuilder_.getMessageOrBuilder();
        } else {
          return firstAvatar_ == null ?
              com.sh.game.protos.AbcProtos.RoleSimpleBean.getDefaultInstance() : firstAvatar_;
        }
      }
      /**
       * <pre>
       *第一名简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean firstAvatar = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.AbcProtos.RoleSimpleBean, com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder, com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder> 
          getFirstAvatarFieldBuilder() {
        if (firstAvatarBuilder_ == null) {
          firstAvatarBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.AbcProtos.RoleSimpleBean, com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder, com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder>(
                  getFirstAvatar(),
                  getParentForChildren(),
                  isClean());
          firstAvatar_ = null;
        }
        return firstAvatarBuilder_;
      }

      private com.sh.game.protos.RankProtos.RankDataBean myRank_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.RankProtos.RankDataBean, com.sh.game.protos.RankProtos.RankDataBean.Builder, com.sh.game.protos.RankProtos.RankDataBeanOrBuilder> myRankBuilder_;
      /**
       * <pre>
       *我的排名
       * </pre>
       *
       * <code>.rank.RankDataBean myRank = 3;</code>
       * @return Whether the myRank field is set.
       */
      public boolean hasMyRank() {
        return myRankBuilder_ != null || myRank_ != null;
      }
      /**
       * <pre>
       *我的排名
       * </pre>
       *
       * <code>.rank.RankDataBean myRank = 3;</code>
       * @return The myRank.
       */
      public com.sh.game.protos.RankProtos.RankDataBean getMyRank() {
        if (myRankBuilder_ == null) {
          return myRank_ == null ? com.sh.game.protos.RankProtos.RankDataBean.getDefaultInstance() : myRank_;
        } else {
          return myRankBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *我的排名
       * </pre>
       *
       * <code>.rank.RankDataBean myRank = 3;</code>
       */
      public Builder setMyRank(com.sh.game.protos.RankProtos.RankDataBean value) {
        if (myRankBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          myRank_ = value;
          onChanged();
        } else {
          myRankBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *我的排名
       * </pre>
       *
       * <code>.rank.RankDataBean myRank = 3;</code>
       */
      public Builder setMyRank(
          com.sh.game.protos.RankProtos.RankDataBean.Builder builderForValue) {
        if (myRankBuilder_ == null) {
          myRank_ = builderForValue.build();
          onChanged();
        } else {
          myRankBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *我的排名
       * </pre>
       *
       * <code>.rank.RankDataBean myRank = 3;</code>
       */
      public Builder mergeMyRank(com.sh.game.protos.RankProtos.RankDataBean value) {
        if (myRankBuilder_ == null) {
          if (myRank_ != null) {
            myRank_ =
              com.sh.game.protos.RankProtos.RankDataBean.newBuilder(myRank_).mergeFrom(value).buildPartial();
          } else {
            myRank_ = value;
          }
          onChanged();
        } else {
          myRankBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *我的排名
       * </pre>
       *
       * <code>.rank.RankDataBean myRank = 3;</code>
       */
      public Builder clearMyRank() {
        if (myRankBuilder_ == null) {
          myRank_ = null;
          onChanged();
        } else {
          myRank_ = null;
          myRankBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *我的排名
       * </pre>
       *
       * <code>.rank.RankDataBean myRank = 3;</code>
       */
      public com.sh.game.protos.RankProtos.RankDataBean.Builder getMyRankBuilder() {
        
        onChanged();
        return getMyRankFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *我的排名
       * </pre>
       *
       * <code>.rank.RankDataBean myRank = 3;</code>
       */
      public com.sh.game.protos.RankProtos.RankDataBeanOrBuilder getMyRankOrBuilder() {
        if (myRankBuilder_ != null) {
          return myRankBuilder_.getMessageOrBuilder();
        } else {
          return myRank_ == null ?
              com.sh.game.protos.RankProtos.RankDataBean.getDefaultInstance() : myRank_;
        }
      }
      /**
       * <pre>
       *我的排名
       * </pre>
       *
       * <code>.rank.RankDataBean myRank = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.RankProtos.RankDataBean, com.sh.game.protos.RankProtos.RankDataBean.Builder, com.sh.game.protos.RankProtos.RankDataBeanOrBuilder> 
          getMyRankFieldBuilder() {
        if (myRankBuilder_ == null) {
          myRankBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.RankProtos.RankDataBean, com.sh.game.protos.RankProtos.RankDataBean.Builder, com.sh.game.protos.RankProtos.RankDataBeanOrBuilder>(
                  getMyRank(),
                  getParentForChildren(),
                  isClean());
          myRank_ = null;
        }
        return myRankBuilder_;
      }

      private java.util.List<com.sh.game.protos.RankProtos.RankDataBean> rankList_ =
        java.util.Collections.emptyList();
      private void ensureRankListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rankList_ = new java.util.ArrayList<com.sh.game.protos.RankProtos.RankDataBean>(rankList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.RankProtos.RankDataBean, com.sh.game.protos.RankProtos.RankDataBean.Builder, com.sh.game.protos.RankProtos.RankDataBeanOrBuilder> rankListBuilder_;

      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public java.util.List<com.sh.game.protos.RankProtos.RankDataBean> getRankListList() {
        if (rankListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rankList_);
        } else {
          return rankListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public int getRankListCount() {
        if (rankListBuilder_ == null) {
          return rankList_.size();
        } else {
          return rankListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public com.sh.game.protos.RankProtos.RankDataBean getRankList(int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);
        } else {
          return rankListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public Builder setRankList(
          int index, com.sh.game.protos.RankProtos.RankDataBean value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.set(index, value);
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public Builder setRankList(
          int index, com.sh.game.protos.RankProtos.RankDataBean.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public Builder addRankList(com.sh.game.protos.RankProtos.RankDataBean value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public Builder addRankList(
          int index, com.sh.game.protos.RankProtos.RankDataBean value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(index, value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public Builder addRankList(
          com.sh.game.protos.RankProtos.RankDataBean.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public Builder addRankList(
          int index, com.sh.game.protos.RankProtos.RankDataBean.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public Builder addAllRankList(
          java.lang.Iterable<? extends com.sh.game.protos.RankProtos.RankDataBean> values) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rankList_);
          onChanged();
        } else {
          rankListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public Builder clearRankList() {
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rankListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public Builder removeRankList(int index) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.remove(index);
          onChanged();
        } else {
          rankListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public com.sh.game.protos.RankProtos.RankDataBean.Builder getRankListBuilder(
          int index) {
        return getRankListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public com.sh.game.protos.RankProtos.RankDataBeanOrBuilder getRankListOrBuilder(
          int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);  } else {
          return rankListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public java.util.List<? extends com.sh.game.protos.RankProtos.RankDataBeanOrBuilder> 
           getRankListOrBuilderList() {
        if (rankListBuilder_ != null) {
          return rankListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rankList_);
        }
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public com.sh.game.protos.RankProtos.RankDataBean.Builder addRankListBuilder() {
        return getRankListFieldBuilder().addBuilder(
            com.sh.game.protos.RankProtos.RankDataBean.getDefaultInstance());
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public com.sh.game.protos.RankProtos.RankDataBean.Builder addRankListBuilder(
          int index) {
        return getRankListFieldBuilder().addBuilder(
            index, com.sh.game.protos.RankProtos.RankDataBean.getDefaultInstance());
      }
      /**
       * <pre>
       *排行榜数据
       * </pre>
       *
       * <code>repeated .rank.RankDataBean rankList = 4;</code>
       */
      public java.util.List<com.sh.game.protos.RankProtos.RankDataBean.Builder> 
           getRankListBuilderList() {
        return getRankListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.RankProtos.RankDataBean, com.sh.game.protos.RankProtos.RankDataBean.Builder, com.sh.game.protos.RankProtos.RankDataBeanOrBuilder> 
          getRankListFieldBuilder() {
        if (rankListBuilder_ == null) {
          rankListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.RankProtos.RankDataBean, com.sh.game.protos.RankProtos.RankDataBean.Builder, com.sh.game.protos.RankProtos.RankDataBeanOrBuilder>(
                  rankList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rankList_ = null;
        }
        return rankListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:rank.ResLookRank)
    }

    // @@protoc_insertion_point(class_scope:rank.ResLookRank)
    private static final com.sh.game.protos.RankProtos.ResLookRank DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.RankProtos.ResLookRank();
    }

    public static com.sh.game.protos.RankProtos.ResLookRank getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResLookRank>
        PARSER = new com.google.protobuf.AbstractParser<ResLookRank>() {
      @java.lang.Override
      public ResLookRank parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResLookRank(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResLookRank> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResLookRank> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.RankProtos.ResLookRank getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqRankSimpleOrBuilder extends
      // @@protoc_insertion_point(interface_extends:rank.ReqRankSimple)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *玩家编号
     * </pre>
     *
     * <code>int64 roleId = 1;</code>
     * @return The roleId.
     */
    long getRoleId();
  }
  /**
   * <pre>
   ** class='ReqRankSimple' id='3' desc='请求查看排行榜对象外观' 
   * </pre>
   *
   * Protobuf type {@code rank.ReqRankSimple}
   */
  public static final class ReqRankSimple extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:rank.ReqRankSimple)
      ReqRankSimpleOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqRankSimple.newBuilder() to construct.
    private ReqRankSimple(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqRankSimple() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqRankSimple();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqRankSimple(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              roleId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.RankProtos.internal_static_rank_ReqRankSimple_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.RankProtos.internal_static_rank_ReqRankSimple_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.RankProtos.ReqRankSimple.class, com.sh.game.protos.RankProtos.ReqRankSimple.Builder.class);
    }

    public static final int ROLEID_FIELD_NUMBER = 1;
    private long roleId_;
    /**
     * <pre>
     *玩家编号
     * </pre>
     *
     * <code>int64 roleId = 1;</code>
     * @return The roleId.
     */
    @java.lang.Override
    public long getRoleId() {
      return roleId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (roleId_ != 0L) {
        output.writeInt64(1, roleId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (roleId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, roleId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.RankProtos.ReqRankSimple)) {
        return super.equals(obj);
      }
      com.sh.game.protos.RankProtos.ReqRankSimple other = (com.sh.game.protos.RankProtos.ReqRankSimple) obj;

      if (getRoleId()
          != other.getRoleId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ROLEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoleId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ReqRankSimple parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.RankProtos.ReqRankSimple prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqRankSimple' id='3' desc='请求查看排行榜对象外观' 
     * </pre>
     *
     * Protobuf type {@code rank.ReqRankSimple}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:rank.ReqRankSimple)
        com.sh.game.protos.RankProtos.ReqRankSimpleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ReqRankSimple_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ReqRankSimple_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.RankProtos.ReqRankSimple.class, com.sh.game.protos.RankProtos.ReqRankSimple.Builder.class);
      }

      // Construct using com.sh.game.protos.RankProtos.ReqRankSimple.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        roleId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ReqRankSimple_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ReqRankSimple getDefaultInstanceForType() {
        return com.sh.game.protos.RankProtos.ReqRankSimple.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ReqRankSimple build() {
        com.sh.game.protos.RankProtos.ReqRankSimple result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ReqRankSimple buildPartial() {
        com.sh.game.protos.RankProtos.ReqRankSimple result = new com.sh.game.protos.RankProtos.ReqRankSimple(this);
        result.roleId_ = roleId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.RankProtos.ReqRankSimple) {
          return mergeFrom((com.sh.game.protos.RankProtos.ReqRankSimple)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.RankProtos.ReqRankSimple other) {
        if (other == com.sh.game.protos.RankProtos.ReqRankSimple.getDefaultInstance()) return this;
        if (other.getRoleId() != 0L) {
          setRoleId(other.getRoleId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.RankProtos.ReqRankSimple parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.RankProtos.ReqRankSimple) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long roleId_ ;
      /**
       * <pre>
       *玩家编号
       * </pre>
       *
       * <code>int64 roleId = 1;</code>
       * @return The roleId.
       */
      @java.lang.Override
      public long getRoleId() {
        return roleId_;
      }
      /**
       * <pre>
       *玩家编号
       * </pre>
       *
       * <code>int64 roleId = 1;</code>
       * @param value The roleId to set.
       * @return This builder for chaining.
       */
      public Builder setRoleId(long value) {
        
        roleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家编号
       * </pre>
       *
       * <code>int64 roleId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRoleId() {
        
        roleId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:rank.ReqRankSimple)
    }

    // @@protoc_insertion_point(class_scope:rank.ReqRankSimple)
    private static final com.sh.game.protos.RankProtos.ReqRankSimple DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.RankProtos.ReqRankSimple();
    }

    public static com.sh.game.protos.RankProtos.ReqRankSimple getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqRankSimple>
        PARSER = new com.google.protobuf.AbstractParser<ReqRankSimple>() {
      @java.lang.Override
      public ReqRankSimple parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqRankSimple(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqRankSimple> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqRankSimple> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.RankProtos.ReqRankSimple getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResRankSimpleOrBuilder extends
      // @@protoc_insertion_point(interface_extends:rank.ResRankSimple)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *玩家简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
     * @return Whether the roleInfo field is set.
     */
    boolean hasRoleInfo();
    /**
     * <pre>
     *玩家简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
     * @return The roleInfo.
     */
    com.sh.game.protos.AbcProtos.RoleSimpleBean getRoleInfo();
    /**
     * <pre>
     *玩家简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
     */
    com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder getRoleInfoOrBuilder();
  }
  /**
   * <pre>
   ** class='ResRankSimple' id='4' desc='查看排行榜对象外观响应' 
   * </pre>
   *
   * Protobuf type {@code rank.ResRankSimple}
   */
  public static final class ResRankSimple extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:rank.ResRankSimple)
      ResRankSimpleOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResRankSimple.newBuilder() to construct.
    private ResRankSimple(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResRankSimple() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResRankSimple();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResRankSimple(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder subBuilder = null;
              if (roleInfo_ != null) {
                subBuilder = roleInfo_.toBuilder();
              }
              roleInfo_ = input.readMessage(com.sh.game.protos.AbcProtos.RoleSimpleBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(roleInfo_);
                roleInfo_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.RankProtos.internal_static_rank_ResRankSimple_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.RankProtos.internal_static_rank_ResRankSimple_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.RankProtos.ResRankSimple.class, com.sh.game.protos.RankProtos.ResRankSimple.Builder.class);
    }

    public static final int ROLEINFO_FIELD_NUMBER = 1;
    private com.sh.game.protos.AbcProtos.RoleSimpleBean roleInfo_;
    /**
     * <pre>
     *玩家简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
     * @return Whether the roleInfo field is set.
     */
    @java.lang.Override
    public boolean hasRoleInfo() {
      return roleInfo_ != null;
    }
    /**
     * <pre>
     *玩家简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
     * @return The roleInfo.
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.RoleSimpleBean getRoleInfo() {
      return roleInfo_ == null ? com.sh.game.protos.AbcProtos.RoleSimpleBean.getDefaultInstance() : roleInfo_;
    }
    /**
     * <pre>
     *玩家简略信息
     * </pre>
     *
     * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder getRoleInfoOrBuilder() {
      return getRoleInfo();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (roleInfo_ != null) {
        output.writeMessage(1, getRoleInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (roleInfo_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getRoleInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.RankProtos.ResRankSimple)) {
        return super.equals(obj);
      }
      com.sh.game.protos.RankProtos.ResRankSimple other = (com.sh.game.protos.RankProtos.ResRankSimple) obj;

      if (hasRoleInfo() != other.hasRoleInfo()) return false;
      if (hasRoleInfo()) {
        if (!getRoleInfo()
            .equals(other.getRoleInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasRoleInfo()) {
        hash = (37 * hash) + ROLEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getRoleInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.RankProtos.ResRankSimple parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.RankProtos.ResRankSimple prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResRankSimple' id='4' desc='查看排行榜对象外观响应' 
     * </pre>
     *
     * Protobuf type {@code rank.ResRankSimple}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:rank.ResRankSimple)
        com.sh.game.protos.RankProtos.ResRankSimpleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ResRankSimple_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ResRankSimple_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.RankProtos.ResRankSimple.class, com.sh.game.protos.RankProtos.ResRankSimple.Builder.class);
      }

      // Construct using com.sh.game.protos.RankProtos.ResRankSimple.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (roleInfoBuilder_ == null) {
          roleInfo_ = null;
        } else {
          roleInfo_ = null;
          roleInfoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.RankProtos.internal_static_rank_ResRankSimple_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ResRankSimple getDefaultInstanceForType() {
        return com.sh.game.protos.RankProtos.ResRankSimple.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ResRankSimple build() {
        com.sh.game.protos.RankProtos.ResRankSimple result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.RankProtos.ResRankSimple buildPartial() {
        com.sh.game.protos.RankProtos.ResRankSimple result = new com.sh.game.protos.RankProtos.ResRankSimple(this);
        if (roleInfoBuilder_ == null) {
          result.roleInfo_ = roleInfo_;
        } else {
          result.roleInfo_ = roleInfoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.RankProtos.ResRankSimple) {
          return mergeFrom((com.sh.game.protos.RankProtos.ResRankSimple)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.RankProtos.ResRankSimple other) {
        if (other == com.sh.game.protos.RankProtos.ResRankSimple.getDefaultInstance()) return this;
        if (other.hasRoleInfo()) {
          mergeRoleInfo(other.getRoleInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.RankProtos.ResRankSimple parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.RankProtos.ResRankSimple) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.sh.game.protos.AbcProtos.RoleSimpleBean roleInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.AbcProtos.RoleSimpleBean, com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder, com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder> roleInfoBuilder_;
      /**
       * <pre>
       *玩家简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
       * @return Whether the roleInfo field is set.
       */
      public boolean hasRoleInfo() {
        return roleInfoBuilder_ != null || roleInfo_ != null;
      }
      /**
       * <pre>
       *玩家简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
       * @return The roleInfo.
       */
      public com.sh.game.protos.AbcProtos.RoleSimpleBean getRoleInfo() {
        if (roleInfoBuilder_ == null) {
          return roleInfo_ == null ? com.sh.game.protos.AbcProtos.RoleSimpleBean.getDefaultInstance() : roleInfo_;
        } else {
          return roleInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *玩家简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
       */
      public Builder setRoleInfo(com.sh.game.protos.AbcProtos.RoleSimpleBean value) {
        if (roleInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          roleInfo_ = value;
          onChanged();
        } else {
          roleInfoBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *玩家简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
       */
      public Builder setRoleInfo(
          com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder builderForValue) {
        if (roleInfoBuilder_ == null) {
          roleInfo_ = builderForValue.build();
          onChanged();
        } else {
          roleInfoBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *玩家简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
       */
      public Builder mergeRoleInfo(com.sh.game.protos.AbcProtos.RoleSimpleBean value) {
        if (roleInfoBuilder_ == null) {
          if (roleInfo_ != null) {
            roleInfo_ =
              com.sh.game.protos.AbcProtos.RoleSimpleBean.newBuilder(roleInfo_).mergeFrom(value).buildPartial();
          } else {
            roleInfo_ = value;
          }
          onChanged();
        } else {
          roleInfoBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *玩家简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
       */
      public Builder clearRoleInfo() {
        if (roleInfoBuilder_ == null) {
          roleInfo_ = null;
          onChanged();
        } else {
          roleInfo_ = null;
          roleInfoBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *玩家简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
       */
      public com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder getRoleInfoBuilder() {
        
        onChanged();
        return getRoleInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *玩家简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
       */
      public com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder getRoleInfoOrBuilder() {
        if (roleInfoBuilder_ != null) {
          return roleInfoBuilder_.getMessageOrBuilder();
        } else {
          return roleInfo_ == null ?
              com.sh.game.protos.AbcProtos.RoleSimpleBean.getDefaultInstance() : roleInfo_;
        }
      }
      /**
       * <pre>
       *玩家简略信息
       * </pre>
       *
       * <code>.abc.RoleSimpleBean roleInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.AbcProtos.RoleSimpleBean, com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder, com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder> 
          getRoleInfoFieldBuilder() {
        if (roleInfoBuilder_ == null) {
          roleInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.AbcProtos.RoleSimpleBean, com.sh.game.protos.AbcProtos.RoleSimpleBean.Builder, com.sh.game.protos.AbcProtos.RoleSimpleBeanOrBuilder>(
                  getRoleInfo(),
                  getParentForChildren(),
                  isClean());
          roleInfo_ = null;
        }
        return roleInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:rank.ResRankSimple)
    }

    // @@protoc_insertion_point(class_scope:rank.ResRankSimple)
    private static final com.sh.game.protos.RankProtos.ResRankSimple DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.RankProtos.ResRankSimple();
    }

    public static com.sh.game.protos.RankProtos.ResRankSimple getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResRankSimple>
        PARSER = new com.google.protobuf.AbstractParser<ResRankSimple>() {
      @java.lang.Override
      public ResRankSimple parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResRankSimple(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResRankSimple> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResRankSimple> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.RankProtos.ResRankSimple getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_rank_RankDataBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_rank_RankDataBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_rank_ReqLookRank_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_rank_ReqLookRank_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_rank_ResLookRank_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_rank_ResLookRank_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_rank_ReqRankSimple_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_rank_ReqRankSimple_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_rank_ResRankSimple_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_rank_ResRankSimple_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nrank.proto\022\004rank\032\tabc.proto\"\236\001\n\014RankDa" +
      "taBean\022\014\n\004rank\030\001 \001(\005\022\014\n\004name\030\002 \001(\t\022\021\n\tpa" +
      "rameter\030\003 \001(\005\022\021\n\tdataValue\030\004 \001(\005\022\022\n\ndata" +
      "Value2\030\005 \001(\005\022\021\n\tunionName\030\006 \001(\t\022\016\n\006roleI" +
      "d\030\007 \001(\003\022\025\n\rparameterList\030\010 \003(\005\"\033\n\013ReqLoo" +
      "kRank\022\014\n\004type\030\001 \001(\005\"\217\001\n\013ResLookRank\022\014\n\004t" +
      "ype\030\001 \001(\005\022(\n\013firstAvatar\030\002 \001(\0132\023.abc.Rol" +
      "eSimpleBean\022\"\n\006myRank\030\003 \001(\0132\022.rank.RankD" +
      "ataBean\022$\n\010rankList\030\004 \003(\0132\022.rank.RankDat" +
      "aBean\"\037\n\rReqRankSimple\022\016\n\006roleId\030\001 \001(\003\"6" +
      "\n\rResRankSimple\022%\n\010roleInfo\030\001 \001(\0132\023.abc." +
      "RoleSimpleBeanB \n\022com.sh.game.protosB\nRa" +
      "nkProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.sh.game.protos.AbcProtos.getDescriptor(),
        });
    internal_static_rank_RankDataBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_rank_RankDataBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_rank_RankDataBean_descriptor,
        new java.lang.String[] { "Rank", "Name", "Parameter", "DataValue", "DataValue2", "UnionName", "RoleId", "ParameterList", });
    internal_static_rank_ReqLookRank_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_rank_ReqLookRank_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_rank_ReqLookRank_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_rank_ResLookRank_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_rank_ResLookRank_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_rank_ResLookRank_descriptor,
        new java.lang.String[] { "Type", "FirstAvatar", "MyRank", "RankList", });
    internal_static_rank_ReqRankSimple_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_rank_ReqRankSimple_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_rank_ReqRankSimple_descriptor,
        new java.lang.String[] { "RoleId", });
    internal_static_rank_ResRankSimple_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_rank_ResRankSimple_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_rank_ResRankSimple_descriptor,
        new java.lang.String[] { "RoleInfo", });
    com.sh.game.protos.AbcProtos.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
