// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lunhuita.proto

package com.sh.game.protos;

public final class LunhuitaProtos {
  private LunhuitaProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqQueryLunHuiTaOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lunhuita.ReqQueryLunHuiTa)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqQueryLunHuiTa' id='1' desc='请求当前轮回塔状态信息' 
   * </pre>
   *
   * Protobuf type {@code lunhuita.ReqQueryLunHuiTa}
   */
  public static final class ReqQueryLunHuiTa extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lunhuita.ReqQueryLunHuiTa)
      ReqQueryLunHuiTaOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqQueryLunHuiTa.newBuilder() to construct.
    private ReqQueryLunHuiTa(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqQueryLunHuiTa() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqQueryLunHuiTa();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqQueryLunHuiTa(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqQueryLunHuiTa_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqQueryLunHuiTa_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa.class, com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa other = (com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqQueryLunHuiTa' id='1' desc='请求当前轮回塔状态信息' 
     * </pre>
     *
     * Protobuf type {@code lunhuita.ReqQueryLunHuiTa}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lunhuita.ReqQueryLunHuiTa)
        com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTaOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqQueryLunHuiTa_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqQueryLunHuiTa_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa.class, com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa.Builder.class);
      }

      // Construct using com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqQueryLunHuiTa_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa getDefaultInstanceForType() {
        return com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa build() {
        com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa buildPartial() {
        com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa result = new com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa) {
          return mergeFrom((com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa other) {
        if (other == com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lunhuita.ReqQueryLunHuiTa)
    }

    // @@protoc_insertion_point(class_scope:lunhuita.ReqQueryLunHuiTa)
    private static final com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa();
    }

    public static com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqQueryLunHuiTa>
        PARSER = new com.google.protobuf.AbstractParser<ReqQueryLunHuiTa>() {
      @java.lang.Override
      public ReqQueryLunHuiTa parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqQueryLunHuiTa(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqQueryLunHuiTa> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqQueryLunHuiTa> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LunhuitaProtos.ReqQueryLunHuiTa getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResQueryLunHuiTaOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lunhuita.ResQueryLunHuiTa)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *已通关层数
     * </pre>
     *
     * <code>int32 layer = 1;</code>
     * @return The layer.
     */
    int getLayer();

    /**
     * <pre>
     *剩余挑战次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    int getCount();

    /**
     * <pre>
     *最后一层奖历的领取状态(只有通关后才可以)
     * </pre>
     *
     * <code>int32 recState = 3;</code>
     * @return The recState.
     */
    int getRecState();

    /**
     * <pre>
     *当天可挑战总次数
     * </pre>
     *
     * <code>int32 totalCount = 4;</code>
     * @return The totalCount.
     */
    int getTotalCount();

    /**
     * <pre>
     *当天挑战所在大陆
     * </pre>
     *
     * <code>int32 landID = 5;</code>
     * @return The landID.
     */
    int getLandID();
  }
  /**
   * <pre>
   ** class='ResQueryLunHuiTa' id='2' desc='返回当前轮回塔状态信息, 当有新充值次数或每日重置则自动推送一次' 
   * </pre>
   *
   * Protobuf type {@code lunhuita.ResQueryLunHuiTa}
   */
  public static final class ResQueryLunHuiTa extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lunhuita.ResQueryLunHuiTa)
      ResQueryLunHuiTaOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResQueryLunHuiTa.newBuilder() to construct.
    private ResQueryLunHuiTa(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResQueryLunHuiTa() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResQueryLunHuiTa();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResQueryLunHuiTa(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              layer_ = input.readInt32();
              break;
            }
            case 16: {

              count_ = input.readInt32();
              break;
            }
            case 24: {

              recState_ = input.readInt32();
              break;
            }
            case 32: {

              totalCount_ = input.readInt32();
              break;
            }
            case 40: {

              landID_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResQueryLunHuiTa_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResQueryLunHuiTa_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa.class, com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa.Builder.class);
    }

    public static final int LAYER_FIELD_NUMBER = 1;
    private int layer_;
    /**
     * <pre>
     *已通关层数
     * </pre>
     *
     * <code>int32 layer = 1;</code>
     * @return The layer.
     */
    @java.lang.Override
    public int getLayer() {
      return layer_;
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private int count_;
    /**
     * <pre>
     *剩余挑战次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    public static final int RECSTATE_FIELD_NUMBER = 3;
    private int recState_;
    /**
     * <pre>
     *最后一层奖历的领取状态(只有通关后才可以)
     * </pre>
     *
     * <code>int32 recState = 3;</code>
     * @return The recState.
     */
    @java.lang.Override
    public int getRecState() {
      return recState_;
    }

    public static final int TOTALCOUNT_FIELD_NUMBER = 4;
    private int totalCount_;
    /**
     * <pre>
     *当天可挑战总次数
     * </pre>
     *
     * <code>int32 totalCount = 4;</code>
     * @return The totalCount.
     */
    @java.lang.Override
    public int getTotalCount() {
      return totalCount_;
    }

    public static final int LANDID_FIELD_NUMBER = 5;
    private int landID_;
    /**
     * <pre>
     *当天挑战所在大陆
     * </pre>
     *
     * <code>int32 landID = 5;</code>
     * @return The landID.
     */
    @java.lang.Override
    public int getLandID() {
      return landID_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (layer_ != 0) {
        output.writeInt32(1, layer_);
      }
      if (count_ != 0) {
        output.writeInt32(2, count_);
      }
      if (recState_ != 0) {
        output.writeInt32(3, recState_);
      }
      if (totalCount_ != 0) {
        output.writeInt32(4, totalCount_);
      }
      if (landID_ != 0) {
        output.writeInt32(5, landID_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (layer_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, layer_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, count_);
      }
      if (recState_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, recState_);
      }
      if (totalCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, totalCount_);
      }
      if (landID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, landID_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa other = (com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa) obj;

      if (getLayer()
          != other.getLayer()) return false;
      if (getCount()
          != other.getCount()) return false;
      if (getRecState()
          != other.getRecState()) return false;
      if (getTotalCount()
          != other.getTotalCount()) return false;
      if (getLandID()
          != other.getLandID()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + LAYER_FIELD_NUMBER;
      hash = (53 * hash) + getLayer();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (37 * hash) + RECSTATE_FIELD_NUMBER;
      hash = (53 * hash) + getRecState();
      hash = (37 * hash) + TOTALCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getTotalCount();
      hash = (37 * hash) + LANDID_FIELD_NUMBER;
      hash = (53 * hash) + getLandID();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResQueryLunHuiTa' id='2' desc='返回当前轮回塔状态信息, 当有新充值次数或每日重置则自动推送一次' 
     * </pre>
     *
     * Protobuf type {@code lunhuita.ResQueryLunHuiTa}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lunhuita.ResQueryLunHuiTa)
        com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTaOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResQueryLunHuiTa_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResQueryLunHuiTa_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa.class, com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa.Builder.class);
      }

      // Construct using com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        layer_ = 0;

        count_ = 0;

        recState_ = 0;

        totalCount_ = 0;

        landID_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResQueryLunHuiTa_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa getDefaultInstanceForType() {
        return com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa build() {
        com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa buildPartial() {
        com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa result = new com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa(this);
        result.layer_ = layer_;
        result.count_ = count_;
        result.recState_ = recState_;
        result.totalCount_ = totalCount_;
        result.landID_ = landID_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa) {
          return mergeFrom((com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa other) {
        if (other == com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa.getDefaultInstance()) return this;
        if (other.getLayer() != 0) {
          setLayer(other.getLayer());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        if (other.getRecState() != 0) {
          setRecState(other.getRecState());
        }
        if (other.getTotalCount() != 0) {
          setTotalCount(other.getTotalCount());
        }
        if (other.getLandID() != 0) {
          setLandID(other.getLandID());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int layer_ ;
      /**
       * <pre>
       *已通关层数
       * </pre>
       *
       * <code>int32 layer = 1;</code>
       * @return The layer.
       */
      @java.lang.Override
      public int getLayer() {
        return layer_;
      }
      /**
       * <pre>
       *已通关层数
       * </pre>
       *
       * <code>int32 layer = 1;</code>
       * @param value The layer to set.
       * @return This builder for chaining.
       */
      public Builder setLayer(int value) {
        
        layer_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *已通关层数
       * </pre>
       *
       * <code>int32 layer = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLayer() {
        
        layer_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       *剩余挑战次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       *剩余挑战次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *剩余挑战次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }

      private int recState_ ;
      /**
       * <pre>
       *最后一层奖历的领取状态(只有通关后才可以)
       * </pre>
       *
       * <code>int32 recState = 3;</code>
       * @return The recState.
       */
      @java.lang.Override
      public int getRecState() {
        return recState_;
      }
      /**
       * <pre>
       *最后一层奖历的领取状态(只有通关后才可以)
       * </pre>
       *
       * <code>int32 recState = 3;</code>
       * @param value The recState to set.
       * @return This builder for chaining.
       */
      public Builder setRecState(int value) {
        
        recState_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *最后一层奖历的领取状态(只有通关后才可以)
       * </pre>
       *
       * <code>int32 recState = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRecState() {
        
        recState_ = 0;
        onChanged();
        return this;
      }

      private int totalCount_ ;
      /**
       * <pre>
       *当天可挑战总次数
       * </pre>
       *
       * <code>int32 totalCount = 4;</code>
       * @return The totalCount.
       */
      @java.lang.Override
      public int getTotalCount() {
        return totalCount_;
      }
      /**
       * <pre>
       *当天可挑战总次数
       * </pre>
       *
       * <code>int32 totalCount = 4;</code>
       * @param value The totalCount to set.
       * @return This builder for chaining.
       */
      public Builder setTotalCount(int value) {
        
        totalCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当天可挑战总次数
       * </pre>
       *
       * <code>int32 totalCount = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalCount() {
        
        totalCount_ = 0;
        onChanged();
        return this;
      }

      private int landID_ ;
      /**
       * <pre>
       *当天挑战所在大陆
       * </pre>
       *
       * <code>int32 landID = 5;</code>
       * @return The landID.
       */
      @java.lang.Override
      public int getLandID() {
        return landID_;
      }
      /**
       * <pre>
       *当天挑战所在大陆
       * </pre>
       *
       * <code>int32 landID = 5;</code>
       * @param value The landID to set.
       * @return This builder for chaining.
       */
      public Builder setLandID(int value) {
        
        landID_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当天挑战所在大陆
       * </pre>
       *
       * <code>int32 landID = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearLandID() {
        
        landID_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lunhuita.ResQueryLunHuiTa)
    }

    // @@protoc_insertion_point(class_scope:lunhuita.ResQueryLunHuiTa)
    private static final com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa();
    }

    public static com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResQueryLunHuiTa>
        PARSER = new com.google.protobuf.AbstractParser<ResQueryLunHuiTa>() {
      @java.lang.Override
      public ResQueryLunHuiTa parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResQueryLunHuiTa(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResQueryLunHuiTa> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResQueryLunHuiTa> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LunhuitaProtos.ResQueryLunHuiTa getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqEnterLunHuiTaOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lunhuita.ReqEnterLunHuiTa)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqEnterLunHuiTa' id='3' desc='请求进入轮回塔(直接进入需要挑战的那一层, 挑战成功的不再重复进入' 
   * </pre>
   *
   * Protobuf type {@code lunhuita.ReqEnterLunHuiTa}
   */
  public static final class ReqEnterLunHuiTa extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lunhuita.ReqEnterLunHuiTa)
      ReqEnterLunHuiTaOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqEnterLunHuiTa.newBuilder() to construct.
    private ReqEnterLunHuiTa(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqEnterLunHuiTa() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqEnterLunHuiTa();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqEnterLunHuiTa(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqEnterLunHuiTa_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqEnterLunHuiTa_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa.class, com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa other = (com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqEnterLunHuiTa' id='3' desc='请求进入轮回塔(直接进入需要挑战的那一层, 挑战成功的不再重复进入' 
     * </pre>
     *
     * Protobuf type {@code lunhuita.ReqEnterLunHuiTa}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lunhuita.ReqEnterLunHuiTa)
        com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTaOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqEnterLunHuiTa_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqEnterLunHuiTa_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa.class, com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa.Builder.class);
      }

      // Construct using com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqEnterLunHuiTa_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa getDefaultInstanceForType() {
        return com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa build() {
        com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa buildPartial() {
        com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa result = new com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa) {
          return mergeFrom((com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa other) {
        if (other == com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lunhuita.ReqEnterLunHuiTa)
    }

    // @@protoc_insertion_point(class_scope:lunhuita.ReqEnterLunHuiTa)
    private static final com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa();
    }

    public static com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqEnterLunHuiTa>
        PARSER = new com.google.protobuf.AbstractParser<ReqEnterLunHuiTa>() {
      @java.lang.Override
      public ReqEnterLunHuiTa parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqEnterLunHuiTa(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqEnterLunHuiTa> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqEnterLunHuiTa> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LunhuitaProtos.ReqEnterLunHuiTa getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResEnterLunHuiTaOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lunhuita.ResEnterLunHuiTa)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *0: 进入成功  1: 进入失败,没有可挑战任务   2: 最后一层奖历未领取
     * </pre>
     *
     * <code>int32 state = 1;</code>
     * @return The state.
     */
    int getState();
  }
  /**
   * <pre>
   ** class='ResEnterLunHuiTa' id='4' desc='进入是否成功应答' 
   * </pre>
   *
   * Protobuf type {@code lunhuita.ResEnterLunHuiTa}
   */
  public static final class ResEnterLunHuiTa extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lunhuita.ResEnterLunHuiTa)
      ResEnterLunHuiTaOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResEnterLunHuiTa.newBuilder() to construct.
    private ResEnterLunHuiTa(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResEnterLunHuiTa() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResEnterLunHuiTa();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResEnterLunHuiTa(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              state_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResEnterLunHuiTa_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResEnterLunHuiTa_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa.class, com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa.Builder.class);
    }

    public static final int STATE_FIELD_NUMBER = 1;
    private int state_;
    /**
     * <pre>
     *0: 进入成功  1: 进入失败,没有可挑战任务   2: 最后一层奖历未领取
     * </pre>
     *
     * <code>int32 state = 1;</code>
     * @return The state.
     */
    @java.lang.Override
    public int getState() {
      return state_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (state_ != 0) {
        output.writeInt32(1, state_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (state_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, state_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa other = (com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa) obj;

      if (getState()
          != other.getState()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + STATE_FIELD_NUMBER;
      hash = (53 * hash) + getState();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResEnterLunHuiTa' id='4' desc='进入是否成功应答' 
     * </pre>
     *
     * Protobuf type {@code lunhuita.ResEnterLunHuiTa}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lunhuita.ResEnterLunHuiTa)
        com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTaOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResEnterLunHuiTa_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResEnterLunHuiTa_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa.class, com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa.Builder.class);
      }

      // Construct using com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        state_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResEnterLunHuiTa_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa getDefaultInstanceForType() {
        return com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa build() {
        com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa buildPartial() {
        com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa result = new com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa(this);
        result.state_ = state_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa) {
          return mergeFrom((com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa other) {
        if (other == com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa.getDefaultInstance()) return this;
        if (other.getState() != 0) {
          setState(other.getState());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int state_ ;
      /**
       * <pre>
       *0: 进入成功  1: 进入失败,没有可挑战任务   2: 最后一层奖历未领取
       * </pre>
       *
       * <code>int32 state = 1;</code>
       * @return The state.
       */
      @java.lang.Override
      public int getState() {
        return state_;
      }
      /**
       * <pre>
       *0: 进入成功  1: 进入失败,没有可挑战任务   2: 最后一层奖历未领取
       * </pre>
       *
       * <code>int32 state = 1;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(int value) {
        
        state_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0: 进入成功  1: 进入失败,没有可挑战任务   2: 最后一层奖历未领取
       * </pre>
       *
       * <code>int32 state = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        
        state_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lunhuita.ResEnterLunHuiTa)
    }

    // @@protoc_insertion_point(class_scope:lunhuita.ResEnterLunHuiTa)
    private static final com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa();
    }

    public static com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResEnterLunHuiTa>
        PARSER = new com.google.protobuf.AbstractParser<ResEnterLunHuiTa>() {
      @java.lang.Override
      public ResEnterLunHuiTa parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResEnterLunHuiTa(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResEnterLunHuiTa> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResEnterLunHuiTa> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LunhuitaProtos.ResEnterLunHuiTa getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqReceiveLunHuiTaRewardOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lunhuita.ReqReceiveLunHuiTaReward)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqReceiveLunHuiTaReward' id='5' desc='请求领取挑战奖励' 
   * </pre>
   *
   * Protobuf type {@code lunhuita.ReqReceiveLunHuiTaReward}
   */
  public static final class ReqReceiveLunHuiTaReward extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lunhuita.ReqReceiveLunHuiTaReward)
      ReqReceiveLunHuiTaRewardOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqReceiveLunHuiTaReward.newBuilder() to construct.
    private ReqReceiveLunHuiTaReward(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqReceiveLunHuiTaReward() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqReceiveLunHuiTaReward();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqReceiveLunHuiTaReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqReceiveLunHuiTaReward_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqReceiveLunHuiTaReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward.class, com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward other = (com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqReceiveLunHuiTaReward' id='5' desc='请求领取挑战奖励' 
     * </pre>
     *
     * Protobuf type {@code lunhuita.ReqReceiveLunHuiTaReward}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lunhuita.ReqReceiveLunHuiTaReward)
        com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqReceiveLunHuiTaReward_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqReceiveLunHuiTaReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward.class, com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward.Builder.class);
      }

      // Construct using com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ReqReceiveLunHuiTaReward_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward getDefaultInstanceForType() {
        return com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward build() {
        com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward buildPartial() {
        com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward result = new com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward) {
          return mergeFrom((com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward other) {
        if (other == com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lunhuita.ReqReceiveLunHuiTaReward)
    }

    // @@protoc_insertion_point(class_scope:lunhuita.ReqReceiveLunHuiTaReward)
    private static final com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward();
    }

    public static com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqReceiveLunHuiTaReward>
        PARSER = new com.google.protobuf.AbstractParser<ReqReceiveLunHuiTaReward>() {
      @java.lang.Override
      public ReqReceiveLunHuiTaReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqReceiveLunHuiTaReward(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqReceiveLunHuiTaReward> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqReceiveLunHuiTaReward> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LunhuitaProtos.ReqReceiveLunHuiTaReward getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResReceiveLunHuiTaRewardOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lunhuita.ResReceiveLunHuiTaReward)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *已通关层数
     * </pre>
     *
     * <code>int32 layer = 1;</code>
     * @return The layer.
     */
    int getLayer();

    /**
     * <pre>
     *剩余挑战次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    int getCount();

    /**
     * <pre>
     *最后一层奖历的领取状态(只有通关后才可以)
     * </pre>
     *
     * <code>int32 recState = 3;</code>
     * @return The recState.
     */
    int getRecState();

    /**
     * <pre>
     *当天可挑战总次数
     * </pre>
     *
     * <code>int32 totalCount = 4;</code>
     * @return The totalCount.
     */
    int getTotalCount();

    /**
     * <pre>
     *当天挑战所在大陆
     * </pre>
     *
     * <code>int32 landID = 5;</code>
     * @return The landID.
     */
    int getLandID();
  }
  /**
   * <pre>
   ** class='ResReceiveLunHuiTaReward' id='6' desc='领取成功后应答,如果还有挑战次数则自动重置挑战信息' 
   * </pre>
   *
   * Protobuf type {@code lunhuita.ResReceiveLunHuiTaReward}
   */
  public static final class ResReceiveLunHuiTaReward extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lunhuita.ResReceiveLunHuiTaReward)
      ResReceiveLunHuiTaRewardOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResReceiveLunHuiTaReward.newBuilder() to construct.
    private ResReceiveLunHuiTaReward(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResReceiveLunHuiTaReward() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResReceiveLunHuiTaReward();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResReceiveLunHuiTaReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              layer_ = input.readInt32();
              break;
            }
            case 16: {

              count_ = input.readInt32();
              break;
            }
            case 24: {

              recState_ = input.readInt32();
              break;
            }
            case 32: {

              totalCount_ = input.readInt32();
              break;
            }
            case 40: {

              landID_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResReceiveLunHuiTaReward_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResReceiveLunHuiTaReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward.class, com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward.Builder.class);
    }

    public static final int LAYER_FIELD_NUMBER = 1;
    private int layer_;
    /**
     * <pre>
     *已通关层数
     * </pre>
     *
     * <code>int32 layer = 1;</code>
     * @return The layer.
     */
    @java.lang.Override
    public int getLayer() {
      return layer_;
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private int count_;
    /**
     * <pre>
     *剩余挑战次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    public static final int RECSTATE_FIELD_NUMBER = 3;
    private int recState_;
    /**
     * <pre>
     *最后一层奖历的领取状态(只有通关后才可以)
     * </pre>
     *
     * <code>int32 recState = 3;</code>
     * @return The recState.
     */
    @java.lang.Override
    public int getRecState() {
      return recState_;
    }

    public static final int TOTALCOUNT_FIELD_NUMBER = 4;
    private int totalCount_;
    /**
     * <pre>
     *当天可挑战总次数
     * </pre>
     *
     * <code>int32 totalCount = 4;</code>
     * @return The totalCount.
     */
    @java.lang.Override
    public int getTotalCount() {
      return totalCount_;
    }

    public static final int LANDID_FIELD_NUMBER = 5;
    private int landID_;
    /**
     * <pre>
     *当天挑战所在大陆
     * </pre>
     *
     * <code>int32 landID = 5;</code>
     * @return The landID.
     */
    @java.lang.Override
    public int getLandID() {
      return landID_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (layer_ != 0) {
        output.writeInt32(1, layer_);
      }
      if (count_ != 0) {
        output.writeInt32(2, count_);
      }
      if (recState_ != 0) {
        output.writeInt32(3, recState_);
      }
      if (totalCount_ != 0) {
        output.writeInt32(4, totalCount_);
      }
      if (landID_ != 0) {
        output.writeInt32(5, landID_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (layer_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, layer_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, count_);
      }
      if (recState_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, recState_);
      }
      if (totalCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, totalCount_);
      }
      if (landID_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, landID_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward other = (com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward) obj;

      if (getLayer()
          != other.getLayer()) return false;
      if (getCount()
          != other.getCount()) return false;
      if (getRecState()
          != other.getRecState()) return false;
      if (getTotalCount()
          != other.getTotalCount()) return false;
      if (getLandID()
          != other.getLandID()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + LAYER_FIELD_NUMBER;
      hash = (53 * hash) + getLayer();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (37 * hash) + RECSTATE_FIELD_NUMBER;
      hash = (53 * hash) + getRecState();
      hash = (37 * hash) + TOTALCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getTotalCount();
      hash = (37 * hash) + LANDID_FIELD_NUMBER;
      hash = (53 * hash) + getLandID();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResReceiveLunHuiTaReward' id='6' desc='领取成功后应答,如果还有挑战次数则自动重置挑战信息' 
     * </pre>
     *
     * Protobuf type {@code lunhuita.ResReceiveLunHuiTaReward}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lunhuita.ResReceiveLunHuiTaReward)
        com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResReceiveLunHuiTaReward_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResReceiveLunHuiTaReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward.class, com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward.Builder.class);
      }

      // Construct using com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        layer_ = 0;

        count_ = 0;

        recState_ = 0;

        totalCount_ = 0;

        landID_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LunhuitaProtos.internal_static_lunhuita_ResReceiveLunHuiTaReward_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward getDefaultInstanceForType() {
        return com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward build() {
        com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward buildPartial() {
        com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward result = new com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward(this);
        result.layer_ = layer_;
        result.count_ = count_;
        result.recState_ = recState_;
        result.totalCount_ = totalCount_;
        result.landID_ = landID_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward) {
          return mergeFrom((com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward other) {
        if (other == com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward.getDefaultInstance()) return this;
        if (other.getLayer() != 0) {
          setLayer(other.getLayer());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        if (other.getRecState() != 0) {
          setRecState(other.getRecState());
        }
        if (other.getTotalCount() != 0) {
          setTotalCount(other.getTotalCount());
        }
        if (other.getLandID() != 0) {
          setLandID(other.getLandID());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int layer_ ;
      /**
       * <pre>
       *已通关层数
       * </pre>
       *
       * <code>int32 layer = 1;</code>
       * @return The layer.
       */
      @java.lang.Override
      public int getLayer() {
        return layer_;
      }
      /**
       * <pre>
       *已通关层数
       * </pre>
       *
       * <code>int32 layer = 1;</code>
       * @param value The layer to set.
       * @return This builder for chaining.
       */
      public Builder setLayer(int value) {
        
        layer_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *已通关层数
       * </pre>
       *
       * <code>int32 layer = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLayer() {
        
        layer_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       *剩余挑战次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       *剩余挑战次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *剩余挑战次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }

      private int recState_ ;
      /**
       * <pre>
       *最后一层奖历的领取状态(只有通关后才可以)
       * </pre>
       *
       * <code>int32 recState = 3;</code>
       * @return The recState.
       */
      @java.lang.Override
      public int getRecState() {
        return recState_;
      }
      /**
       * <pre>
       *最后一层奖历的领取状态(只有通关后才可以)
       * </pre>
       *
       * <code>int32 recState = 3;</code>
       * @param value The recState to set.
       * @return This builder for chaining.
       */
      public Builder setRecState(int value) {
        
        recState_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *最后一层奖历的领取状态(只有通关后才可以)
       * </pre>
       *
       * <code>int32 recState = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRecState() {
        
        recState_ = 0;
        onChanged();
        return this;
      }

      private int totalCount_ ;
      /**
       * <pre>
       *当天可挑战总次数
       * </pre>
       *
       * <code>int32 totalCount = 4;</code>
       * @return The totalCount.
       */
      @java.lang.Override
      public int getTotalCount() {
        return totalCount_;
      }
      /**
       * <pre>
       *当天可挑战总次数
       * </pre>
       *
       * <code>int32 totalCount = 4;</code>
       * @param value The totalCount to set.
       * @return This builder for chaining.
       */
      public Builder setTotalCount(int value) {
        
        totalCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当天可挑战总次数
       * </pre>
       *
       * <code>int32 totalCount = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalCount() {
        
        totalCount_ = 0;
        onChanged();
        return this;
      }

      private int landID_ ;
      /**
       * <pre>
       *当天挑战所在大陆
       * </pre>
       *
       * <code>int32 landID = 5;</code>
       * @return The landID.
       */
      @java.lang.Override
      public int getLandID() {
        return landID_;
      }
      /**
       * <pre>
       *当天挑战所在大陆
       * </pre>
       *
       * <code>int32 landID = 5;</code>
       * @param value The landID to set.
       * @return This builder for chaining.
       */
      public Builder setLandID(int value) {
        
        landID_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当天挑战所在大陆
       * </pre>
       *
       * <code>int32 landID = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearLandID() {
        
        landID_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lunhuita.ResReceiveLunHuiTaReward)
    }

    // @@protoc_insertion_point(class_scope:lunhuita.ResReceiveLunHuiTaReward)
    private static final com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward();
    }

    public static com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResReceiveLunHuiTaReward>
        PARSER = new com.google.protobuf.AbstractParser<ResReceiveLunHuiTaReward>() {
      @java.lang.Override
      public ResReceiveLunHuiTaReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResReceiveLunHuiTaReward(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResReceiveLunHuiTaReward> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResReceiveLunHuiTaReward> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LunhuitaProtos.ResReceiveLunHuiTaReward getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lunhuita_ReqQueryLunHuiTa_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lunhuita_ReqQueryLunHuiTa_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lunhuita_ResQueryLunHuiTa_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lunhuita_ResQueryLunHuiTa_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lunhuita_ReqEnterLunHuiTa_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lunhuita_ReqEnterLunHuiTa_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lunhuita_ResEnterLunHuiTa_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lunhuita_ResEnterLunHuiTa_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lunhuita_ReqReceiveLunHuiTaReward_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lunhuita_ReqReceiveLunHuiTaReward_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lunhuita_ResReceiveLunHuiTaReward_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lunhuita_ResReceiveLunHuiTaReward_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016lunhuita.proto\022\010lunhuita\"\022\n\020ReqQueryLu" +
      "nHuiTa\"f\n\020ResQueryLunHuiTa\022\r\n\005layer\030\001 \001(" +
      "\005\022\r\n\005count\030\002 \001(\005\022\020\n\010recState\030\003 \001(\005\022\022\n\nto" +
      "talCount\030\004 \001(\005\022\016\n\006landID\030\005 \001(\005\"\022\n\020ReqEnt" +
      "erLunHuiTa\"!\n\020ResEnterLunHuiTa\022\r\n\005state\030" +
      "\001 \001(\005\"\032\n\030ReqReceiveLunHuiTaReward\"n\n\030Res" +
      "ReceiveLunHuiTaReward\022\r\n\005layer\030\001 \001(\005\022\r\n\005" +
      "count\030\002 \001(\005\022\020\n\010recState\030\003 \001(\005\022\022\n\ntotalCo" +
      "unt\030\004 \001(\005\022\016\n\006landID\030\005 \001(\005B$\n\022com.sh.game" +
      ".protosB\016LunhuitaProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_lunhuita_ReqQueryLunHuiTa_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_lunhuita_ReqQueryLunHuiTa_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lunhuita_ReqQueryLunHuiTa_descriptor,
        new java.lang.String[] { });
    internal_static_lunhuita_ResQueryLunHuiTa_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_lunhuita_ResQueryLunHuiTa_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lunhuita_ResQueryLunHuiTa_descriptor,
        new java.lang.String[] { "Layer", "Count", "RecState", "TotalCount", "LandID", });
    internal_static_lunhuita_ReqEnterLunHuiTa_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_lunhuita_ReqEnterLunHuiTa_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lunhuita_ReqEnterLunHuiTa_descriptor,
        new java.lang.String[] { });
    internal_static_lunhuita_ResEnterLunHuiTa_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_lunhuita_ResEnterLunHuiTa_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lunhuita_ResEnterLunHuiTa_descriptor,
        new java.lang.String[] { "State", });
    internal_static_lunhuita_ReqReceiveLunHuiTaReward_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_lunhuita_ReqReceiveLunHuiTaReward_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lunhuita_ReqReceiveLunHuiTaReward_descriptor,
        new java.lang.String[] { });
    internal_static_lunhuita_ResReceiveLunHuiTaReward_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_lunhuita_ResReceiveLunHuiTaReward_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lunhuita_ResReceiveLunHuiTaReward_descriptor,
        new java.lang.String[] { "Layer", "Count", "RecState", "TotalCount", "LandID", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
