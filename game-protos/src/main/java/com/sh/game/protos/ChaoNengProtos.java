// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: chaoneng.proto

package com.sh.game.protos;

public final class ChaoNengProtos {
  private ChaoNengProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqLevelUpOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqLevelUp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *唯一id
     * </pre>
     *
     * <code>int64 chaoNengId = 1;</code>
     * @return The chaoNengId.
     */
    long getChaoNengId();

    /**
     * <pre>
     *要升的等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 index = 3;</code>
     * @return The index.
     */
    int getIndex();
  }
  /**
   * <pre>
   ** class='ReqLevelUp' id='1' desc='请求升级超能' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqLevelUp}
   */
  public static final class ReqLevelUp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqLevelUp)
      ReqLevelUpOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqLevelUp.newBuilder() to construct.
    private ReqLevelUp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqLevelUp() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqLevelUp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqLevelUp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              chaoNengId_ = input.readInt64();
              break;
            }
            case 16: {

              level_ = input.readInt32();
              break;
            }
            case 24: {

              index_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqLevelUp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqLevelUp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqLevelUp.class, com.sh.game.protos.ChaoNengProtos.ReqLevelUp.Builder.class);
    }

    public static final int CHAONENGID_FIELD_NUMBER = 1;
    private long chaoNengId_;
    /**
     * <pre>
     *唯一id
     * </pre>
     *
     * <code>int64 chaoNengId = 1;</code>
     * @return The chaoNengId.
     */
    @java.lang.Override
    public long getChaoNengId() {
      return chaoNengId_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     *要升的等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int INDEX_FIELD_NUMBER = 3;
    private int index_;
    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 index = 3;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chaoNengId_ != 0L) {
        output.writeInt64(1, chaoNengId_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      if (index_ != 0) {
        output.writeInt32(3, index_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chaoNengId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, chaoNengId_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, index_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqLevelUp)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqLevelUp other = (com.sh.game.protos.ChaoNengProtos.ReqLevelUp) obj;

      if (getChaoNengId()
          != other.getChaoNengId()) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (getIndex()
          != other.getIndex()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAONENGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getChaoNengId());
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqLevelUp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqLevelUp' id='1' desc='请求升级超能' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqLevelUp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqLevelUp)
        com.sh.game.protos.ChaoNengProtos.ReqLevelUpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqLevelUp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqLevelUp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqLevelUp.class, com.sh.game.protos.ChaoNengProtos.ReqLevelUp.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqLevelUp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chaoNengId_ = 0L;

        level_ = 0;

        index_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqLevelUp_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqLevelUp getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqLevelUp.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqLevelUp build() {
        com.sh.game.protos.ChaoNengProtos.ReqLevelUp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqLevelUp buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqLevelUp result = new com.sh.game.protos.ChaoNengProtos.ReqLevelUp(this);
        result.chaoNengId_ = chaoNengId_;
        result.level_ = level_;
        result.index_ = index_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqLevelUp) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqLevelUp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqLevelUp other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqLevelUp.getDefaultInstance()) return this;
        if (other.getChaoNengId() != 0L) {
          setChaoNengId(other.getChaoNengId());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqLevelUp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqLevelUp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long chaoNengId_ ;
      /**
       * <pre>
       *唯一id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @return The chaoNengId.
       */
      @java.lang.Override
      public long getChaoNengId() {
        return chaoNengId_;
      }
      /**
       * <pre>
       *唯一id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @param value The chaoNengId to set.
       * @return This builder for chaining.
       */
      public Builder setChaoNengId(long value) {
        
        chaoNengId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *唯一id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChaoNengId() {
        
        chaoNengId_ = 0L;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       *要升的等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       *要升的等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *要升的等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 index = 3;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 index = 3;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 index = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqLevelUp)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqLevelUp)
    private static final com.sh.game.protos.ChaoNengProtos.ReqLevelUp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqLevelUp();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqLevelUp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqLevelUp>
        PARSER = new com.google.protobuf.AbstractParser<ReqLevelUp>() {
      @java.lang.Override
      public ReqLevelUp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqLevelUp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqLevelUp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqLevelUp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqLevelUp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqResetLevelOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqResetLevel)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *唯一id
     * </pre>
     *
     * <code>int64 chaoNengId = 1;</code>
     * @return The chaoNengId.
     */
    long getChaoNengId();

    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    int getIndex();
  }
  /**
   * <pre>
   ** class='ReqResetLevel' id='2' desc='请求重置等级' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqResetLevel}
   */
  public static final class ReqResetLevel extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqResetLevel)
      ReqResetLevelOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqResetLevel.newBuilder() to construct.
    private ReqResetLevel(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqResetLevel() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqResetLevel();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqResetLevel(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              chaoNengId_ = input.readInt64();
              break;
            }
            case 16: {

              index_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResetLevel_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResetLevel_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqResetLevel.class, com.sh.game.protos.ChaoNengProtos.ReqResetLevel.Builder.class);
    }

    public static final int CHAONENGID_FIELD_NUMBER = 1;
    private long chaoNengId_;
    /**
     * <pre>
     *唯一id
     * </pre>
     *
     * <code>int64 chaoNengId = 1;</code>
     * @return The chaoNengId.
     */
    @java.lang.Override
    public long getChaoNengId() {
      return chaoNengId_;
    }

    public static final int INDEX_FIELD_NUMBER = 2;
    private int index_;
    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chaoNengId_ != 0L) {
        output.writeInt64(1, chaoNengId_);
      }
      if (index_ != 0) {
        output.writeInt32(2, index_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chaoNengId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, chaoNengId_);
      }
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, index_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqResetLevel)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqResetLevel other = (com.sh.game.protos.ChaoNengProtos.ReqResetLevel) obj;

      if (getChaoNengId()
          != other.getChaoNengId()) return false;
      if (getIndex()
          != other.getIndex()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAONENGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getChaoNengId());
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqResetLevel prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqResetLevel' id='2' desc='请求重置等级' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqResetLevel}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqResetLevel)
        com.sh.game.protos.ChaoNengProtos.ReqResetLevelOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResetLevel_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResetLevel_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqResetLevel.class, com.sh.game.protos.ChaoNengProtos.ReqResetLevel.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqResetLevel.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chaoNengId_ = 0L;

        index_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResetLevel_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqResetLevel getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqResetLevel.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqResetLevel build() {
        com.sh.game.protos.ChaoNengProtos.ReqResetLevel result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqResetLevel buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqResetLevel result = new com.sh.game.protos.ChaoNengProtos.ReqResetLevel(this);
        result.chaoNengId_ = chaoNengId_;
        result.index_ = index_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqResetLevel) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqResetLevel)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqResetLevel other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqResetLevel.getDefaultInstance()) return this;
        if (other.getChaoNengId() != 0L) {
          setChaoNengId(other.getChaoNengId());
        }
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqResetLevel parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqResetLevel) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long chaoNengId_ ;
      /**
       * <pre>
       *唯一id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @return The chaoNengId.
       */
      @java.lang.Override
      public long getChaoNengId() {
        return chaoNengId_;
      }
      /**
       * <pre>
       *唯一id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @param value The chaoNengId to set.
       * @return This builder for chaining.
       */
      public Builder setChaoNengId(long value) {
        
        chaoNengId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *唯一id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChaoNengId() {
        
        chaoNengId_ = 0L;
        onChanged();
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqResetLevel)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqResetLevel)
    private static final com.sh.game.protos.ChaoNengProtos.ReqResetLevel DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqResetLevel();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqResetLevel getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqResetLevel>
        PARSER = new com.google.protobuf.AbstractParser<ReqResetLevel>() {
      @java.lang.Override
      public ReqResetLevel parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqResetLevel(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqResetLevel> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqResetLevel> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqResetLevel getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqPutOnChaoNengOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqPutOnChaoNeng)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *装配方案索引
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    int getIndex();

    /**
     * <pre>
     *装配位置
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *唯一id
     * </pre>
     *
     * <code>int64 chaoNengId = 3;</code>
     * @return The chaoNengId.
     */
    long getChaoNengId();
  }
  /**
   * <pre>
   ** class='ReqPutOnChaoNeng' id='3' desc='请求上阵超能' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqPutOnChaoNeng}
   */
  public static final class ReqPutOnChaoNeng extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqPutOnChaoNeng)
      ReqPutOnChaoNengOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqPutOnChaoNeng.newBuilder() to construct.
    private ReqPutOnChaoNeng(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqPutOnChaoNeng() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqPutOnChaoNeng();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqPutOnChaoNeng(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              index_ = input.readInt32();
              break;
            }
            case 16: {

              type_ = input.readInt32();
              break;
            }
            case 24: {

              chaoNengId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNeng_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNeng_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng.class, com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng.Builder.class);
    }

    public static final int INDEX_FIELD_NUMBER = 1;
    private int index_;
    /**
     * <pre>
     *装配方案索引
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     *装配位置
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int CHAONENGID_FIELD_NUMBER = 3;
    private long chaoNengId_;
    /**
     * <pre>
     *唯一id
     * </pre>
     *
     * <code>int64 chaoNengId = 3;</code>
     * @return The chaoNengId.
     */
    @java.lang.Override
    public long getChaoNengId() {
      return chaoNengId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (index_ != 0) {
        output.writeInt32(1, index_);
      }
      if (type_ != 0) {
        output.writeInt32(2, type_);
      }
      if (chaoNengId_ != 0L) {
        output.writeInt64(3, chaoNengId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, index_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      if (chaoNengId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, chaoNengId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng other = (com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng) obj;

      if (getIndex()
          != other.getIndex()) return false;
      if (getType()
          != other.getType()) return false;
      if (getChaoNengId()
          != other.getChaoNengId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + CHAONENGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getChaoNengId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqPutOnChaoNeng' id='3' desc='请求上阵超能' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqPutOnChaoNeng}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqPutOnChaoNeng)
        com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNeng_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNeng_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng.class, com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        index_ = 0;

        type_ = 0;

        chaoNengId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNeng_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng build() {
        com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng result = new com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng(this);
        result.index_ = index_;
        result.type_ = type_;
        result.chaoNengId_ = chaoNengId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng.getDefaultInstance()) return this;
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getChaoNengId() != 0L) {
          setChaoNengId(other.getChaoNengId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *装配方案索引
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *装配方案索引
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *装配方案索引
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *装配位置
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *装配位置
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *装配位置
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private long chaoNengId_ ;
      /**
       * <pre>
       *唯一id
       * </pre>
       *
       * <code>int64 chaoNengId = 3;</code>
       * @return The chaoNengId.
       */
      @java.lang.Override
      public long getChaoNengId() {
        return chaoNengId_;
      }
      /**
       * <pre>
       *唯一id
       * </pre>
       *
       * <code>int64 chaoNengId = 3;</code>
       * @param value The chaoNengId to set.
       * @return This builder for chaining.
       */
      public Builder setChaoNengId(long value) {
        
        chaoNengId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *唯一id
       * </pre>
       *
       * <code>int64 chaoNengId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearChaoNengId() {
        
        chaoNengId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqPutOnChaoNeng)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqPutOnChaoNeng)
    private static final com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqPutOnChaoNeng>
        PARSER = new com.google.protobuf.AbstractParser<ReqPutOnChaoNeng>() {
      @java.lang.Override
      public ReqPutOnChaoNeng parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqPutOnChaoNeng(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqPutOnChaoNeng> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqPutOnChaoNeng> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNeng getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqTakeOffChaoNengOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqTakeOffChaoNeng)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *装配方案索引
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    int getIndex();

    /**
     * <pre>
     *装配位置
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ReqTakeOffChaoNeng' id='4' desc='卸下超能' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqTakeOffChaoNeng}
   */
  public static final class ReqTakeOffChaoNeng extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqTakeOffChaoNeng)
      ReqTakeOffChaoNengOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqTakeOffChaoNeng.newBuilder() to construct.
    private ReqTakeOffChaoNeng(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqTakeOffChaoNeng() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqTakeOffChaoNeng();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqTakeOffChaoNeng(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              index_ = input.readInt32();
              break;
            }
            case 16: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNeng_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNeng_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng.class, com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng.Builder.class);
    }

    public static final int INDEX_FIELD_NUMBER = 1;
    private int index_;
    /**
     * <pre>
     *装配方案索引
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     *装配位置
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (index_ != 0) {
        output.writeInt32(1, index_);
      }
      if (type_ != 0) {
        output.writeInt32(2, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, index_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng other = (com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng) obj;

      if (getIndex()
          != other.getIndex()) return false;
      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqTakeOffChaoNeng' id='4' desc='卸下超能' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqTakeOffChaoNeng}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqTakeOffChaoNeng)
        com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNeng_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNeng_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng.class, com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        index_ = 0;

        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNeng_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng build() {
        com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng result = new com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng(this);
        result.index_ = index_;
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng.getDefaultInstance()) return this;
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *装配方案索引
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *装配方案索引
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *装配方案索引
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *装配位置
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *装配位置
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *装配位置
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqTakeOffChaoNeng)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqTakeOffChaoNeng)
    private static final com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqTakeOffChaoNeng>
        PARSER = new com.google.protobuf.AbstractParser<ReqTakeOffChaoNeng>() {
      @java.lang.Override
      public ReqTakeOffChaoNeng parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqTakeOffChaoNeng(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqTakeOffChaoNeng> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqTakeOffChaoNeng> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNeng getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqUseChaoNengOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqUseChaoNeng)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *装配方案索引
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    int getIndex();
  }
  /**
   * <pre>
   ** class='ReqUseChaoNeng' id='5' desc='使用超能' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqUseChaoNeng}
   */
  public static final class ReqUseChaoNeng extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqUseChaoNeng)
      ReqUseChaoNengOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqUseChaoNeng.newBuilder() to construct.
    private ReqUseChaoNeng(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqUseChaoNeng() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqUseChaoNeng();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqUseChaoNeng(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              index_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqUseChaoNeng_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqUseChaoNeng_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng.class, com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng.Builder.class);
    }

    public static final int INDEX_FIELD_NUMBER = 1;
    private int index_;
    /**
     * <pre>
     *装配方案索引
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (index_ != 0) {
        output.writeInt32(1, index_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, index_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng other = (com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng) obj;

      if (getIndex()
          != other.getIndex()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqUseChaoNeng' id='5' desc='使用超能' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqUseChaoNeng}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqUseChaoNeng)
        com.sh.game.protos.ChaoNengProtos.ReqUseChaoNengOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqUseChaoNeng_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqUseChaoNeng_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng.class, com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        index_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqUseChaoNeng_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng build() {
        com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng result = new com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng(this);
        result.index_ = index_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng.getDefaultInstance()) return this;
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *装配方案索引
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *装配方案索引
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *装配方案索引
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqUseChaoNeng)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqUseChaoNeng)
    private static final com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqUseChaoNeng>
        PARSER = new com.google.protobuf.AbstractParser<ReqUseChaoNeng>() {
      @java.lang.Override
      public ReqUseChaoNeng parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqUseChaoNeng(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqUseChaoNeng> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqUseChaoNeng> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqUseChaoNeng getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqBreakThroughChaoNengOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqBreakThroughChaoNeng)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *超能唯一id
     * </pre>
     *
     * <code>repeated int64 chaoNengId = 1;</code>
     * @return A list containing the chaoNengId.
     */
    java.util.List<java.lang.Long> getChaoNengIdList();
    /**
     * <pre>
     *超能唯一id
     * </pre>
     *
     * <code>repeated int64 chaoNengId = 1;</code>
     * @return The count of chaoNengId.
     */
    int getChaoNengIdCount();
    /**
     * <pre>
     *超能唯一id
     * </pre>
     *
     * <code>repeated int64 chaoNengId = 1;</code>
     * @param index The index of the element to return.
     * @return The chaoNengId at the given index.
     */
    long getChaoNengId(int index);

    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    int getIndex();

    /**
     * <pre>
     *是否一键突破
     * </pre>
     *
     * <code>bool oneKey = 3;</code>
     * @return The oneKey.
     */
    boolean getOneKey();
  }
  /**
   * <pre>
   ** class='ReqBreakThroughChaoNeng' id='6' desc='请求突破超能' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqBreakThroughChaoNeng}
   */
  public static final class ReqBreakThroughChaoNeng extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqBreakThroughChaoNeng)
      ReqBreakThroughChaoNengOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqBreakThroughChaoNeng.newBuilder() to construct.
    private ReqBreakThroughChaoNeng(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqBreakThroughChaoNeng() {
      chaoNengId_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqBreakThroughChaoNeng();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqBreakThroughChaoNeng(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                chaoNengId_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              chaoNengId_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                chaoNengId_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                chaoNengId_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 16: {

              index_ = input.readInt32();
              break;
            }
            case 24: {

              oneKey_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          chaoNengId_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqBreakThroughChaoNeng_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqBreakThroughChaoNeng_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng.class, com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng.Builder.class);
    }

    public static final int CHAONENGID_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList chaoNengId_;
    /**
     * <pre>
     *超能唯一id
     * </pre>
     *
     * <code>repeated int64 chaoNengId = 1;</code>
     * @return A list containing the chaoNengId.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getChaoNengIdList() {
      return chaoNengId_;
    }
    /**
     * <pre>
     *超能唯一id
     * </pre>
     *
     * <code>repeated int64 chaoNengId = 1;</code>
     * @return The count of chaoNengId.
     */
    public int getChaoNengIdCount() {
      return chaoNengId_.size();
    }
    /**
     * <pre>
     *超能唯一id
     * </pre>
     *
     * <code>repeated int64 chaoNengId = 1;</code>
     * @param index The index of the element to return.
     * @return The chaoNengId at the given index.
     */
    public long getChaoNengId(int index) {
      return chaoNengId_.getLong(index);
    }
    private int chaoNengIdMemoizedSerializedSize = -1;

    public static final int INDEX_FIELD_NUMBER = 2;
    private int index_;
    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    public static final int ONEKEY_FIELD_NUMBER = 3;
    private boolean oneKey_;
    /**
     * <pre>
     *是否一键突破
     * </pre>
     *
     * <code>bool oneKey = 3;</code>
     * @return The oneKey.
     */
    @java.lang.Override
    public boolean getOneKey() {
      return oneKey_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getChaoNengIdList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(chaoNengIdMemoizedSerializedSize);
      }
      for (int i = 0; i < chaoNengId_.size(); i++) {
        output.writeInt64NoTag(chaoNengId_.getLong(i));
      }
      if (index_ != 0) {
        output.writeInt32(2, index_);
      }
      if (oneKey_ != false) {
        output.writeBool(3, oneKey_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < chaoNengId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(chaoNengId_.getLong(i));
        }
        size += dataSize;
        if (!getChaoNengIdList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        chaoNengIdMemoizedSerializedSize = dataSize;
      }
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, index_);
      }
      if (oneKey_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, oneKey_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng other = (com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng) obj;

      if (!getChaoNengIdList()
          .equals(other.getChaoNengIdList())) return false;
      if (getIndex()
          != other.getIndex()) return false;
      if (getOneKey()
          != other.getOneKey()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChaoNengIdCount() > 0) {
        hash = (37 * hash) + CHAONENGID_FIELD_NUMBER;
        hash = (53 * hash) + getChaoNengIdList().hashCode();
      }
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + ONEKEY_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getOneKey());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqBreakThroughChaoNeng' id='6' desc='请求突破超能' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqBreakThroughChaoNeng}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqBreakThroughChaoNeng)
        com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNengOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqBreakThroughChaoNeng_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqBreakThroughChaoNeng_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng.class, com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chaoNengId_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        index_ = 0;

        oneKey_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqBreakThroughChaoNeng_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng build() {
        com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng result = new com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          chaoNengId_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.chaoNengId_ = chaoNengId_;
        result.index_ = index_;
        result.oneKey_ = oneKey_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng.getDefaultInstance()) return this;
        if (!other.chaoNengId_.isEmpty()) {
          if (chaoNengId_.isEmpty()) {
            chaoNengId_ = other.chaoNengId_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureChaoNengIdIsMutable();
            chaoNengId_.addAll(other.chaoNengId_);
          }
          onChanged();
        }
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getOneKey() != false) {
          setOneKey(other.getOneKey());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList chaoNengId_ = emptyLongList();
      private void ensureChaoNengIdIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          chaoNengId_ = mutableCopy(chaoNengId_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       *超能唯一id
       * </pre>
       *
       * <code>repeated int64 chaoNengId = 1;</code>
       * @return A list containing the chaoNengId.
       */
      public java.util.List<java.lang.Long>
          getChaoNengIdList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(chaoNengId_) : chaoNengId_;
      }
      /**
       * <pre>
       *超能唯一id
       * </pre>
       *
       * <code>repeated int64 chaoNengId = 1;</code>
       * @return The count of chaoNengId.
       */
      public int getChaoNengIdCount() {
        return chaoNengId_.size();
      }
      /**
       * <pre>
       *超能唯一id
       * </pre>
       *
       * <code>repeated int64 chaoNengId = 1;</code>
       * @param index The index of the element to return.
       * @return The chaoNengId at the given index.
       */
      public long getChaoNengId(int index) {
        return chaoNengId_.getLong(index);
      }
      /**
       * <pre>
       *超能唯一id
       * </pre>
       *
       * <code>repeated int64 chaoNengId = 1;</code>
       * @param index The index to set the value at.
       * @param value The chaoNengId to set.
       * @return This builder for chaining.
       */
      public Builder setChaoNengId(
          int index, long value) {
        ensureChaoNengIdIsMutable();
        chaoNengId_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *超能唯一id
       * </pre>
       *
       * <code>repeated int64 chaoNengId = 1;</code>
       * @param value The chaoNengId to add.
       * @return This builder for chaining.
       */
      public Builder addChaoNengId(long value) {
        ensureChaoNengIdIsMutable();
        chaoNengId_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *超能唯一id
       * </pre>
       *
       * <code>repeated int64 chaoNengId = 1;</code>
       * @param values The chaoNengId to add.
       * @return This builder for chaining.
       */
      public Builder addAllChaoNengId(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureChaoNengIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, chaoNengId_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *超能唯一id
       * </pre>
       *
       * <code>repeated int64 chaoNengId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChaoNengId() {
        chaoNengId_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private boolean oneKey_ ;
      /**
       * <pre>
       *是否一键突破
       * </pre>
       *
       * <code>bool oneKey = 3;</code>
       * @return The oneKey.
       */
      @java.lang.Override
      public boolean getOneKey() {
        return oneKey_;
      }
      /**
       * <pre>
       *是否一键突破
       * </pre>
       *
       * <code>bool oneKey = 3;</code>
       * @param value The oneKey to set.
       * @return This builder for chaining.
       */
      public Builder setOneKey(boolean value) {
        
        oneKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否一键突破
       * </pre>
       *
       * <code>bool oneKey = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearOneKey() {
        
        oneKey_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqBreakThroughChaoNeng)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqBreakThroughChaoNeng)
    private static final com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqBreakThroughChaoNeng>
        PARSER = new com.google.protobuf.AbstractParser<ReqBreakThroughChaoNeng>() {
      @java.lang.Override
      public ReqBreakThroughChaoNeng parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqBreakThroughChaoNeng(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqBreakThroughChaoNeng> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqBreakThroughChaoNeng> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqBreakThroughChaoNeng getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqPutOnChaoNengMingWenOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqPutOnChaoNengMingWen)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *超能id
     * </pre>
     *
     * <code>int64 chaoNengId = 1;</code>
     * @return The chaoNengId.
     */
    long getChaoNengId();

    /**
     * <pre>
     *铭文id
     * </pre>
     *
     * <code>int64 mingWenId = 2;</code>
     * @return The mingWenId.
     */
    long getMingWenId();

    /**
     * <pre>
     *铭文索引
     * </pre>
     *
     * <code>int32 index = 3;</code>
     * @return The index.
     */
    int getIndex();

    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 lineUpindex = 4;</code>
     * @return The lineUpindex.
     */
    int getLineUpindex();
  }
  /**
   * <pre>
   ** class='ReqPutOnChaoNengMingWen' id='7' desc='请求上阵超能铭文' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqPutOnChaoNengMingWen}
   */
  public static final class ReqPutOnChaoNengMingWen extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqPutOnChaoNengMingWen)
      ReqPutOnChaoNengMingWenOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqPutOnChaoNengMingWen.newBuilder() to construct.
    private ReqPutOnChaoNengMingWen(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqPutOnChaoNengMingWen() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqPutOnChaoNengMingWen();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqPutOnChaoNengMingWen(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              chaoNengId_ = input.readInt64();
              break;
            }
            case 16: {

              mingWenId_ = input.readInt64();
              break;
            }
            case 24: {

              index_ = input.readInt32();
              break;
            }
            case 32: {

              lineUpindex_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNengMingWen_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNengMingWen_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen.class, com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen.Builder.class);
    }

    public static final int CHAONENGID_FIELD_NUMBER = 1;
    private long chaoNengId_;
    /**
     * <pre>
     *超能id
     * </pre>
     *
     * <code>int64 chaoNengId = 1;</code>
     * @return The chaoNengId.
     */
    @java.lang.Override
    public long getChaoNengId() {
      return chaoNengId_;
    }

    public static final int MINGWENID_FIELD_NUMBER = 2;
    private long mingWenId_;
    /**
     * <pre>
     *铭文id
     * </pre>
     *
     * <code>int64 mingWenId = 2;</code>
     * @return The mingWenId.
     */
    @java.lang.Override
    public long getMingWenId() {
      return mingWenId_;
    }

    public static final int INDEX_FIELD_NUMBER = 3;
    private int index_;
    /**
     * <pre>
     *铭文索引
     * </pre>
     *
     * <code>int32 index = 3;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    public static final int LINEUPINDEX_FIELD_NUMBER = 4;
    private int lineUpindex_;
    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 lineUpindex = 4;</code>
     * @return The lineUpindex.
     */
    @java.lang.Override
    public int getLineUpindex() {
      return lineUpindex_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chaoNengId_ != 0L) {
        output.writeInt64(1, chaoNengId_);
      }
      if (mingWenId_ != 0L) {
        output.writeInt64(2, mingWenId_);
      }
      if (index_ != 0) {
        output.writeInt32(3, index_);
      }
      if (lineUpindex_ != 0) {
        output.writeInt32(4, lineUpindex_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chaoNengId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, chaoNengId_);
      }
      if (mingWenId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, mingWenId_);
      }
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, index_);
      }
      if (lineUpindex_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, lineUpindex_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen other = (com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen) obj;

      if (getChaoNengId()
          != other.getChaoNengId()) return false;
      if (getMingWenId()
          != other.getMingWenId()) return false;
      if (getIndex()
          != other.getIndex()) return false;
      if (getLineUpindex()
          != other.getLineUpindex()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAONENGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getChaoNengId());
      hash = (37 * hash) + MINGWENID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMingWenId());
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + LINEUPINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getLineUpindex();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqPutOnChaoNengMingWen' id='7' desc='请求上阵超能铭文' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqPutOnChaoNengMingWen}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqPutOnChaoNengMingWen)
        com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNengMingWen_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNengMingWen_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen.class, com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chaoNengId_ = 0L;

        mingWenId_ = 0L;

        index_ = 0;

        lineUpindex_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqPutOnChaoNengMingWen_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen build() {
        com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen result = new com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen(this);
        result.chaoNengId_ = chaoNengId_;
        result.mingWenId_ = mingWenId_;
        result.index_ = index_;
        result.lineUpindex_ = lineUpindex_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen.getDefaultInstance()) return this;
        if (other.getChaoNengId() != 0L) {
          setChaoNengId(other.getChaoNengId());
        }
        if (other.getMingWenId() != 0L) {
          setMingWenId(other.getMingWenId());
        }
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getLineUpindex() != 0) {
          setLineUpindex(other.getLineUpindex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long chaoNengId_ ;
      /**
       * <pre>
       *超能id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @return The chaoNengId.
       */
      @java.lang.Override
      public long getChaoNengId() {
        return chaoNengId_;
      }
      /**
       * <pre>
       *超能id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @param value The chaoNengId to set.
       * @return This builder for chaining.
       */
      public Builder setChaoNengId(long value) {
        
        chaoNengId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *超能id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChaoNengId() {
        
        chaoNengId_ = 0L;
        onChanged();
        return this;
      }

      private long mingWenId_ ;
      /**
       * <pre>
       *铭文id
       * </pre>
       *
       * <code>int64 mingWenId = 2;</code>
       * @return The mingWenId.
       */
      @java.lang.Override
      public long getMingWenId() {
        return mingWenId_;
      }
      /**
       * <pre>
       *铭文id
       * </pre>
       *
       * <code>int64 mingWenId = 2;</code>
       * @param value The mingWenId to set.
       * @return This builder for chaining.
       */
      public Builder setMingWenId(long value) {
        
        mingWenId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *铭文id
       * </pre>
       *
       * <code>int64 mingWenId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMingWenId() {
        
        mingWenId_ = 0L;
        onChanged();
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *铭文索引
       * </pre>
       *
       * <code>int32 index = 3;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *铭文索引
       * </pre>
       *
       * <code>int32 index = 3;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *铭文索引
       * </pre>
       *
       * <code>int32 index = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private int lineUpindex_ ;
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 lineUpindex = 4;</code>
       * @return The lineUpindex.
       */
      @java.lang.Override
      public int getLineUpindex() {
        return lineUpindex_;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 lineUpindex = 4;</code>
       * @param value The lineUpindex to set.
       * @return This builder for chaining.
       */
      public Builder setLineUpindex(int value) {
        
        lineUpindex_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 lineUpindex = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLineUpindex() {
        
        lineUpindex_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqPutOnChaoNengMingWen)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqPutOnChaoNengMingWen)
    private static final com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqPutOnChaoNengMingWen>
        PARSER = new com.google.protobuf.AbstractParser<ReqPutOnChaoNengMingWen>() {
      @java.lang.Override
      public ReqPutOnChaoNengMingWen parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqPutOnChaoNengMingWen(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqPutOnChaoNengMingWen> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqPutOnChaoNengMingWen> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqPutOnChaoNengMingWen getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqTakeOffChaoNengMingWenOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqTakeOffChaoNengMingWen)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *超能id
     * </pre>
     *
     * <code>int64 chaoNengId = 1;</code>
     * @return The chaoNengId.
     */
    long getChaoNengId();

    /**
     * <pre>
     *铭文索引
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    int getIndex();

    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 lineUpindex = 3;</code>
     * @return The lineUpindex.
     */
    int getLineUpindex();
  }
  /**
   * <pre>
   ** class='ReqTakeOffChaoNengMingWen' id='8' desc='卸下超能铭文' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqTakeOffChaoNengMingWen}
   */
  public static final class ReqTakeOffChaoNengMingWen extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqTakeOffChaoNengMingWen)
      ReqTakeOffChaoNengMingWenOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqTakeOffChaoNengMingWen.newBuilder() to construct.
    private ReqTakeOffChaoNengMingWen(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqTakeOffChaoNengMingWen() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqTakeOffChaoNengMingWen();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqTakeOffChaoNengMingWen(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              chaoNengId_ = input.readInt64();
              break;
            }
            case 16: {

              index_ = input.readInt32();
              break;
            }
            case 24: {

              lineUpindex_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNengMingWen_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNengMingWen_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen.class, com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen.Builder.class);
    }

    public static final int CHAONENGID_FIELD_NUMBER = 1;
    private long chaoNengId_;
    /**
     * <pre>
     *超能id
     * </pre>
     *
     * <code>int64 chaoNengId = 1;</code>
     * @return The chaoNengId.
     */
    @java.lang.Override
    public long getChaoNengId() {
      return chaoNengId_;
    }

    public static final int INDEX_FIELD_NUMBER = 2;
    private int index_;
    /**
     * <pre>
     *铭文索引
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    public static final int LINEUPINDEX_FIELD_NUMBER = 3;
    private int lineUpindex_;
    /**
     * <pre>
     *投影方案索引，超能在背包时，此值需要设置为-1
     * </pre>
     *
     * <code>int32 lineUpindex = 3;</code>
     * @return The lineUpindex.
     */
    @java.lang.Override
    public int getLineUpindex() {
      return lineUpindex_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chaoNengId_ != 0L) {
        output.writeInt64(1, chaoNengId_);
      }
      if (index_ != 0) {
        output.writeInt32(2, index_);
      }
      if (lineUpindex_ != 0) {
        output.writeInt32(3, lineUpindex_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chaoNengId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, chaoNengId_);
      }
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, index_);
      }
      if (lineUpindex_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, lineUpindex_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen other = (com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen) obj;

      if (getChaoNengId()
          != other.getChaoNengId()) return false;
      if (getIndex()
          != other.getIndex()) return false;
      if (getLineUpindex()
          != other.getLineUpindex()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAONENGID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getChaoNengId());
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + LINEUPINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getLineUpindex();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqTakeOffChaoNengMingWen' id='8' desc='卸下超能铭文' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqTakeOffChaoNengMingWen}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqTakeOffChaoNengMingWen)
        com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWenOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNengMingWen_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNengMingWen_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen.class, com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chaoNengId_ = 0L;

        index_ = 0;

        lineUpindex_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqTakeOffChaoNengMingWen_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen build() {
        com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen result = new com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen(this);
        result.chaoNengId_ = chaoNengId_;
        result.index_ = index_;
        result.lineUpindex_ = lineUpindex_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen.getDefaultInstance()) return this;
        if (other.getChaoNengId() != 0L) {
          setChaoNengId(other.getChaoNengId());
        }
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getLineUpindex() != 0) {
          setLineUpindex(other.getLineUpindex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long chaoNengId_ ;
      /**
       * <pre>
       *超能id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @return The chaoNengId.
       */
      @java.lang.Override
      public long getChaoNengId() {
        return chaoNengId_;
      }
      /**
       * <pre>
       *超能id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @param value The chaoNengId to set.
       * @return This builder for chaining.
       */
      public Builder setChaoNengId(long value) {
        
        chaoNengId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *超能id
       * </pre>
       *
       * <code>int64 chaoNengId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChaoNengId() {
        
        chaoNengId_ = 0L;
        onChanged();
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *铭文索引
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *铭文索引
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *铭文索引
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private int lineUpindex_ ;
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 lineUpindex = 3;</code>
       * @return The lineUpindex.
       */
      @java.lang.Override
      public int getLineUpindex() {
        return lineUpindex_;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 lineUpindex = 3;</code>
       * @param value The lineUpindex to set.
       * @return This builder for chaining.
       */
      public Builder setLineUpindex(int value) {
        
        lineUpindex_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *投影方案索引，超能在背包时，此值需要设置为-1
       * </pre>
       *
       * <code>int32 lineUpindex = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLineUpindex() {
        
        lineUpindex_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqTakeOffChaoNengMingWen)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqTakeOffChaoNengMingWen)
    private static final com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqTakeOffChaoNengMingWen>
        PARSER = new com.google.protobuf.AbstractParser<ReqTakeOffChaoNengMingWen>() {
      @java.lang.Override
      public ReqTakeOffChaoNengMingWen parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqTakeOffChaoNengMingWen(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqTakeOffChaoNengMingWen> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqTakeOffChaoNengMingWen> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqTakeOffChaoNengMingWen getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChaoNengListBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ChaoNengListBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *超能装配位置
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *超能信息
     * </pre>
     *
     * <code>.abc.CommonItemBean chaoNeng = 2;</code>
     * @return Whether the chaoNeng field is set.
     */
    boolean hasChaoNeng();
    /**
     * <pre>
     *超能信息
     * </pre>
     *
     * <code>.abc.CommonItemBean chaoNeng = 2;</code>
     * @return The chaoNeng.
     */
    com.sh.game.protos.AbcProtos.CommonItemBean getChaoNeng();
    /**
     * <pre>
     *超能信息
     * </pre>
     *
     * <code>.abc.CommonItemBean chaoNeng = 2;</code>
     */
    com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder getChaoNengOrBuilder();
  }
  /**
   * Protobuf type {@code chaoneng.ChaoNengListBean}
   */
  public static final class ChaoNengListBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ChaoNengListBean)
      ChaoNengListBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ChaoNengListBean.newBuilder() to construct.
    private ChaoNengListBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ChaoNengListBean() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ChaoNengListBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ChaoNengListBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 18: {
              com.sh.game.protos.AbcProtos.CommonItemBean.Builder subBuilder = null;
              if (chaoNeng_ != null) {
                subBuilder = chaoNeng_.toBuilder();
              }
              chaoNeng_ = input.readMessage(com.sh.game.protos.AbcProtos.CommonItemBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(chaoNeng_);
                chaoNeng_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ChaoNengListBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ChaoNengListBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ChaoNengListBean.class, com.sh.game.protos.ChaoNengProtos.ChaoNengListBean.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *超能装配位置
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int CHAONENG_FIELD_NUMBER = 2;
    private com.sh.game.protos.AbcProtos.CommonItemBean chaoNeng_;
    /**
     * <pre>
     *超能信息
     * </pre>
     *
     * <code>.abc.CommonItemBean chaoNeng = 2;</code>
     * @return Whether the chaoNeng field is set.
     */
    @java.lang.Override
    public boolean hasChaoNeng() {
      return chaoNeng_ != null;
    }
    /**
     * <pre>
     *超能信息
     * </pre>
     *
     * <code>.abc.CommonItemBean chaoNeng = 2;</code>
     * @return The chaoNeng.
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonItemBean getChaoNeng() {
      return chaoNeng_ == null ? com.sh.game.protos.AbcProtos.CommonItemBean.getDefaultInstance() : chaoNeng_;
    }
    /**
     * <pre>
     *超能信息
     * </pre>
     *
     * <code>.abc.CommonItemBean chaoNeng = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder getChaoNengOrBuilder() {
      return getChaoNeng();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (chaoNeng_ != null) {
        output.writeMessage(2, getChaoNeng());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (chaoNeng_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getChaoNeng());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ChaoNengListBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ChaoNengListBean other = (com.sh.game.protos.ChaoNengProtos.ChaoNengListBean) obj;

      if (getType()
          != other.getType()) return false;
      if (hasChaoNeng() != other.hasChaoNeng()) return false;
      if (hasChaoNeng()) {
        if (!getChaoNeng()
            .equals(other.getChaoNeng())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      if (hasChaoNeng()) {
        hash = (37 * hash) + CHAONENG_FIELD_NUMBER;
        hash = (53 * hash) + getChaoNeng().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ChaoNengListBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code chaoneng.ChaoNengListBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ChaoNengListBean)
        com.sh.game.protos.ChaoNengProtos.ChaoNengListBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ChaoNengListBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ChaoNengListBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ChaoNengListBean.class, com.sh.game.protos.ChaoNengProtos.ChaoNengListBean.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ChaoNengListBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        if (chaoNengBuilder_ == null) {
          chaoNeng_ = null;
        } else {
          chaoNeng_ = null;
          chaoNengBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ChaoNengListBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ChaoNengListBean getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ChaoNengListBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ChaoNengListBean build() {
        com.sh.game.protos.ChaoNengProtos.ChaoNengListBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ChaoNengListBean buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ChaoNengListBean result = new com.sh.game.protos.ChaoNengProtos.ChaoNengListBean(this);
        result.type_ = type_;
        if (chaoNengBuilder_ == null) {
          result.chaoNeng_ = chaoNeng_;
        } else {
          result.chaoNeng_ = chaoNengBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ChaoNengListBean) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ChaoNengListBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ChaoNengListBean other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ChaoNengListBean.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.hasChaoNeng()) {
          mergeChaoNeng(other.getChaoNeng());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ChaoNengListBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ChaoNengListBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *超能装配位置
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *超能装配位置
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *超能装配位置
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private com.sh.game.protos.AbcProtos.CommonItemBean chaoNeng_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonItemBean, com.sh.game.protos.AbcProtos.CommonItemBean.Builder, com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder> chaoNengBuilder_;
      /**
       * <pre>
       *超能信息
       * </pre>
       *
       * <code>.abc.CommonItemBean chaoNeng = 2;</code>
       * @return Whether the chaoNeng field is set.
       */
      public boolean hasChaoNeng() {
        return chaoNengBuilder_ != null || chaoNeng_ != null;
      }
      /**
       * <pre>
       *超能信息
       * </pre>
       *
       * <code>.abc.CommonItemBean chaoNeng = 2;</code>
       * @return The chaoNeng.
       */
      public com.sh.game.protos.AbcProtos.CommonItemBean getChaoNeng() {
        if (chaoNengBuilder_ == null) {
          return chaoNeng_ == null ? com.sh.game.protos.AbcProtos.CommonItemBean.getDefaultInstance() : chaoNeng_;
        } else {
          return chaoNengBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *超能信息
       * </pre>
       *
       * <code>.abc.CommonItemBean chaoNeng = 2;</code>
       */
      public Builder setChaoNeng(com.sh.game.protos.AbcProtos.CommonItemBean value) {
        if (chaoNengBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chaoNeng_ = value;
          onChanged();
        } else {
          chaoNengBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *超能信息
       * </pre>
       *
       * <code>.abc.CommonItemBean chaoNeng = 2;</code>
       */
      public Builder setChaoNeng(
          com.sh.game.protos.AbcProtos.CommonItemBean.Builder builderForValue) {
        if (chaoNengBuilder_ == null) {
          chaoNeng_ = builderForValue.build();
          onChanged();
        } else {
          chaoNengBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *超能信息
       * </pre>
       *
       * <code>.abc.CommonItemBean chaoNeng = 2;</code>
       */
      public Builder mergeChaoNeng(com.sh.game.protos.AbcProtos.CommonItemBean value) {
        if (chaoNengBuilder_ == null) {
          if (chaoNeng_ != null) {
            chaoNeng_ =
              com.sh.game.protos.AbcProtos.CommonItemBean.newBuilder(chaoNeng_).mergeFrom(value).buildPartial();
          } else {
            chaoNeng_ = value;
          }
          onChanged();
        } else {
          chaoNengBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *超能信息
       * </pre>
       *
       * <code>.abc.CommonItemBean chaoNeng = 2;</code>
       */
      public Builder clearChaoNeng() {
        if (chaoNengBuilder_ == null) {
          chaoNeng_ = null;
          onChanged();
        } else {
          chaoNeng_ = null;
          chaoNengBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *超能信息
       * </pre>
       *
       * <code>.abc.CommonItemBean chaoNeng = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonItemBean.Builder getChaoNengBuilder() {
        
        onChanged();
        return getChaoNengFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *超能信息
       * </pre>
       *
       * <code>.abc.CommonItemBean chaoNeng = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder getChaoNengOrBuilder() {
        if (chaoNengBuilder_ != null) {
          return chaoNengBuilder_.getMessageOrBuilder();
        } else {
          return chaoNeng_ == null ?
              com.sh.game.protos.AbcProtos.CommonItemBean.getDefaultInstance() : chaoNeng_;
        }
      }
      /**
       * <pre>
       *超能信息
       * </pre>
       *
       * <code>.abc.CommonItemBean chaoNeng = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonItemBean, com.sh.game.protos.AbcProtos.CommonItemBean.Builder, com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder> 
          getChaoNengFieldBuilder() {
        if (chaoNengBuilder_ == null) {
          chaoNengBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonItemBean, com.sh.game.protos.AbcProtos.CommonItemBean.Builder, com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder>(
                  getChaoNeng(),
                  getParentForChildren(),
                  isClean());
          chaoNeng_ = null;
        }
        return chaoNengBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ChaoNengListBean)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ChaoNengListBean)
    private static final com.sh.game.protos.ChaoNengProtos.ChaoNengListBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ChaoNengListBean();
    }

    public static com.sh.game.protos.ChaoNengProtos.ChaoNengListBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChaoNengListBean>
        PARSER = new com.google.protobuf.AbstractParser<ChaoNengListBean>() {
      @java.lang.Override
      public ChaoNengListBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ChaoNengListBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ChaoNengListBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChaoNengListBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ChaoNengListBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMingWenLevelUpOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqMingWenLevelUp)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqMingWenLevelUp' id='11' desc='一键升级所有超能铭文' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqMingWenLevelUp}
   */
  public static final class ReqMingWenLevelUp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqMingWenLevelUp)
      ReqMingWenLevelUpOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMingWenLevelUp.newBuilder() to construct.
    private ReqMingWenLevelUp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMingWenLevelUp() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMingWenLevelUp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMingWenLevelUp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqMingWenLevelUp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqMingWenLevelUp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp.class, com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp other = (com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMingWenLevelUp' id='11' desc='一键升级所有超能铭文' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqMingWenLevelUp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqMingWenLevelUp)
        com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqMingWenLevelUp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqMingWenLevelUp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp.class, com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqMingWenLevelUp_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp build() {
        com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp result = new com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqMingWenLevelUp)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqMingWenLevelUp)
    private static final com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMingWenLevelUp>
        PARSER = new com.google.protobuf.AbstractParser<ReqMingWenLevelUp>() {
      @java.lang.Override
      public ReqMingWenLevelUp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMingWenLevelUp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMingWenLevelUp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqMingWenLevelUp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqMingWenLevelUp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqResonanceLevelUpOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqResonanceLevelUp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *配置id
     * </pre>
     *
     * <code>int32 cid = 1;</code>
     * @return The cid.
     */
    int getCid();
  }
  /**
   * <pre>
   ** class='ReqResonanceLevelUp' id='12' desc='请求超能图鉴升级' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqResonanceLevelUp}
   */
  public static final class ReqResonanceLevelUp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqResonanceLevelUp)
      ReqResonanceLevelUpOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqResonanceLevelUp.newBuilder() to construct.
    private ReqResonanceLevelUp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqResonanceLevelUp() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqResonanceLevelUp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqResonanceLevelUp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              cid_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceLevelUp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceLevelUp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp.class, com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp.Builder.class);
    }

    public static final int CID_FIELD_NUMBER = 1;
    private int cid_;
    /**
     * <pre>
     *配置id
     * </pre>
     *
     * <code>int32 cid = 1;</code>
     * @return The cid.
     */
    @java.lang.Override
    public int getCid() {
      return cid_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (cid_ != 0) {
        output.writeInt32(1, cid_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (cid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, cid_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp other = (com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp) obj;

      if (getCid()
          != other.getCid()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CID_FIELD_NUMBER;
      hash = (53 * hash) + getCid();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqResonanceLevelUp' id='12' desc='请求超能图鉴升级' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqResonanceLevelUp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqResonanceLevelUp)
        com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceLevelUp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceLevelUp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp.class, com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        cid_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceLevelUp_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp build() {
        com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp result = new com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp(this);
        result.cid_ = cid_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp.getDefaultInstance()) return this;
        if (other.getCid() != 0) {
          setCid(other.getCid());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int cid_ ;
      /**
       * <pre>
       *配置id
       * </pre>
       *
       * <code>int32 cid = 1;</code>
       * @return The cid.
       */
      @java.lang.Override
      public int getCid() {
        return cid_;
      }
      /**
       * <pre>
       *配置id
       * </pre>
       *
       * <code>int32 cid = 1;</code>
       * @param value The cid to set.
       * @return This builder for chaining.
       */
      public Builder setCid(int value) {
        
        cid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *配置id
       * </pre>
       *
       * <code>int32 cid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCid() {
        
        cid_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqResonanceLevelUp)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqResonanceLevelUp)
    private static final com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqResonanceLevelUp>
        PARSER = new com.google.protobuf.AbstractParser<ReqResonanceLevelUp>() {
      @java.lang.Override
      public ReqResonanceLevelUp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqResonanceLevelUp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqResonanceLevelUp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqResonanceLevelUp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqResonanceLevelUp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqResonanceInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ReqResonanceInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqResonanceInfo' id='13' desc='请求超能图鉴信息' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ReqResonanceInfo}
   */
  public static final class ReqResonanceInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ReqResonanceInfo)
      ReqResonanceInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqResonanceInfo.newBuilder() to construct.
    private ReqResonanceInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqResonanceInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqResonanceInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqResonanceInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo.class, com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo other = (com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqResonanceInfo' id='13' desc='请求超能图鉴信息' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ReqResonanceInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ReqResonanceInfo)
        com.sh.game.protos.ChaoNengProtos.ReqResonanceInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo.class, com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ReqResonanceInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo build() {
        com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo result = new com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ReqResonanceInfo)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ReqResonanceInfo)
    private static final com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo();
    }

    public static com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqResonanceInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqResonanceInfo>() {
      @java.lang.Override
      public ReqResonanceInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqResonanceInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqResonanceInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqResonanceInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ReqResonanceInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResResonanceInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chaoneng.ResResonanceInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> 
        getResonanceList();
    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBean getResonance(int index);
    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    int getResonanceCount();
    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getResonanceOrBuilderList();
    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getResonanceOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResResonanceInfo' id='14' desc='返回超能图鉴升级' 
   * </pre>
   *
   * Protobuf type {@code chaoneng.ResResonanceInfo}
   */
  public static final class ResResonanceInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chaoneng.ResResonanceInfo)
      ResResonanceInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResResonanceInfo.newBuilder() to construct.
    private ResResonanceInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResResonanceInfo() {
      resonance_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResResonanceInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResResonanceInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                resonance_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              resonance_.add(
                  input.readMessage(com.sh.game.protos.AbcProtos.CommonKeyValueBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          resonance_ = java.util.Collections.unmodifiableList(resonance_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ResResonanceInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ResResonanceInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.class, com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.Builder.class);
    }

    public static final int RESONANCE_FIELD_NUMBER = 1;
    private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> resonance_;
    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getResonanceList() {
      return resonance_;
    }
    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getResonanceOrBuilderList() {
      return resonance_;
    }
    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    @java.lang.Override
    public int getResonanceCount() {
      return resonance_.size();
    }
    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBean getResonance(int index) {
      return resonance_.get(index);
    }
    /**
     * <pre>
     *图鉴信息
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getResonanceOrBuilder(
        int index) {
      return resonance_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < resonance_.size(); i++) {
        output.writeMessage(1, resonance_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < resonance_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, resonance_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChaoNengProtos.ResResonanceInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChaoNengProtos.ResResonanceInfo other = (com.sh.game.protos.ChaoNengProtos.ResResonanceInfo) obj;

      if (!getResonanceList()
          .equals(other.getResonanceList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getResonanceCount() > 0) {
        hash = (37 * hash) + RESONANCE_FIELD_NUMBER;
        hash = (53 * hash) + getResonanceList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChaoNengProtos.ResResonanceInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResResonanceInfo' id='14' desc='返回超能图鉴升级' 
     * </pre>
     *
     * Protobuf type {@code chaoneng.ResResonanceInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chaoneng.ResResonanceInfo)
        com.sh.game.protos.ChaoNengProtos.ResResonanceInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ResResonanceInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ResResonanceInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.class, com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getResonanceFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (resonanceBuilder_ == null) {
          resonance_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          resonanceBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChaoNengProtos.internal_static_chaoneng_ResResonanceInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ResResonanceInfo getDefaultInstanceForType() {
        return com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ResResonanceInfo build() {
        com.sh.game.protos.ChaoNengProtos.ResResonanceInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChaoNengProtos.ResResonanceInfo buildPartial() {
        com.sh.game.protos.ChaoNengProtos.ResResonanceInfo result = new com.sh.game.protos.ChaoNengProtos.ResResonanceInfo(this);
        int from_bitField0_ = bitField0_;
        if (resonanceBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            resonance_ = java.util.Collections.unmodifiableList(resonance_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.resonance_ = resonance_;
        } else {
          result.resonance_ = resonanceBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChaoNengProtos.ResResonanceInfo) {
          return mergeFrom((com.sh.game.protos.ChaoNengProtos.ResResonanceInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChaoNengProtos.ResResonanceInfo other) {
        if (other == com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.getDefaultInstance()) return this;
        if (resonanceBuilder_ == null) {
          if (!other.resonance_.isEmpty()) {
            if (resonance_.isEmpty()) {
              resonance_ = other.resonance_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureResonanceIsMutable();
              resonance_.addAll(other.resonance_);
            }
            onChanged();
          }
        } else {
          if (!other.resonance_.isEmpty()) {
            if (resonanceBuilder_.isEmpty()) {
              resonanceBuilder_.dispose();
              resonanceBuilder_ = null;
              resonance_ = other.resonance_;
              bitField0_ = (bitField0_ & ~0x00000001);
              resonanceBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getResonanceFieldBuilder() : null;
            } else {
              resonanceBuilder_.addAllMessages(other.resonance_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChaoNengProtos.ResResonanceInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChaoNengProtos.ResResonanceInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> resonance_ =
        java.util.Collections.emptyList();
      private void ensureResonanceIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          resonance_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>(resonance_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> resonanceBuilder_;

      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getResonanceList() {
        if (resonanceBuilder_ == null) {
          return java.util.Collections.unmodifiableList(resonance_);
        } else {
          return resonanceBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public int getResonanceCount() {
        if (resonanceBuilder_ == null) {
          return resonance_.size();
        } else {
          return resonanceBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean getResonance(int index) {
        if (resonanceBuilder_ == null) {
          return resonance_.get(index);
        } else {
          return resonanceBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public Builder setResonance(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (resonanceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResonanceIsMutable();
          resonance_.set(index, value);
          onChanged();
        } else {
          resonanceBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public Builder setResonance(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (resonanceBuilder_ == null) {
          ensureResonanceIsMutable();
          resonance_.set(index, builderForValue.build());
          onChanged();
        } else {
          resonanceBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public Builder addResonance(com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (resonanceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResonanceIsMutable();
          resonance_.add(value);
          onChanged();
        } else {
          resonanceBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public Builder addResonance(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (resonanceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResonanceIsMutable();
          resonance_.add(index, value);
          onChanged();
        } else {
          resonanceBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public Builder addResonance(
          com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (resonanceBuilder_ == null) {
          ensureResonanceIsMutable();
          resonance_.add(builderForValue.build());
          onChanged();
        } else {
          resonanceBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public Builder addResonance(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (resonanceBuilder_ == null) {
          ensureResonanceIsMutable();
          resonance_.add(index, builderForValue.build());
          onChanged();
        } else {
          resonanceBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public Builder addAllResonance(
          java.lang.Iterable<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBean> values) {
        if (resonanceBuilder_ == null) {
          ensureResonanceIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, resonance_);
          onChanged();
        } else {
          resonanceBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public Builder clearResonance() {
        if (resonanceBuilder_ == null) {
          resonance_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          resonanceBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public Builder removeResonance(int index) {
        if (resonanceBuilder_ == null) {
          ensureResonanceIsMutable();
          resonance_.remove(index);
          onChanged();
        } else {
          resonanceBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder getResonanceBuilder(
          int index) {
        return getResonanceFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getResonanceOrBuilder(
          int index) {
        if (resonanceBuilder_ == null) {
          return resonance_.get(index);  } else {
          return resonanceBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
           getResonanceOrBuilderList() {
        if (resonanceBuilder_ != null) {
          return resonanceBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(resonance_);
        }
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addResonanceBuilder() {
        return getResonanceFieldBuilder().addBuilder(
            com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addResonanceBuilder(
          int index) {
        return getResonanceFieldBuilder().addBuilder(
            index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       *图鉴信息
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean resonance = 1;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder> 
           getResonanceBuilderList() {
        return getResonanceFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
          getResonanceFieldBuilder() {
        if (resonanceBuilder_ == null) {
          resonanceBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder>(
                  resonance_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          resonance_ = null;
        }
        return resonanceBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chaoneng.ResResonanceInfo)
    }

    // @@protoc_insertion_point(class_scope:chaoneng.ResResonanceInfo)
    private static final com.sh.game.protos.ChaoNengProtos.ResResonanceInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChaoNengProtos.ResResonanceInfo();
    }

    public static com.sh.game.protos.ChaoNengProtos.ResResonanceInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResResonanceInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResResonanceInfo>() {
      @java.lang.Override
      public ResResonanceInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResResonanceInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResResonanceInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResResonanceInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChaoNengProtos.ResResonanceInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqLevelUp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqLevelUp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqResetLevel_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqResetLevel_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqPutOnChaoNeng_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqPutOnChaoNeng_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqTakeOffChaoNeng_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqTakeOffChaoNeng_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqUseChaoNeng_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqUseChaoNeng_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqBreakThroughChaoNeng_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqBreakThroughChaoNeng_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqPutOnChaoNengMingWen_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqPutOnChaoNengMingWen_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqTakeOffChaoNengMingWen_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqTakeOffChaoNengMingWen_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ChaoNengListBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ChaoNengListBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqMingWenLevelUp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqMingWenLevelUp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqResonanceLevelUp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqResonanceLevelUp_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ReqResonanceInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ReqResonanceInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chaoneng_ResResonanceInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chaoneng_ResResonanceInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016chaoneng.proto\022\010chaoneng\032\tabc.proto\">\n" +
      "\nReqLevelUp\022\022\n\nchaoNengId\030\001 \001(\003\022\r\n\005level" +
      "\030\002 \001(\005\022\r\n\005index\030\003 \001(\005\"2\n\rReqResetLevel\022\022" +
      "\n\nchaoNengId\030\001 \001(\003\022\r\n\005index\030\002 \001(\005\"C\n\020Req" +
      "PutOnChaoNeng\022\r\n\005index\030\001 \001(\005\022\014\n\004type\030\002 \001" +
      "(\005\022\022\n\nchaoNengId\030\003 \001(\003\"1\n\022ReqTakeOffChao" +
      "Neng\022\r\n\005index\030\001 \001(\005\022\014\n\004type\030\002 \001(\005\"\037\n\016Req" +
      "UseChaoNeng\022\r\n\005index\030\001 \001(\005\"L\n\027ReqBreakTh" +
      "roughChaoNeng\022\022\n\nchaoNengId\030\001 \003(\003\022\r\n\005ind" +
      "ex\030\002 \001(\005\022\016\n\006oneKey\030\003 \001(\010\"d\n\027ReqPutOnChao" +
      "NengMingWen\022\022\n\nchaoNengId\030\001 \001(\003\022\021\n\tmingW" +
      "enId\030\002 \001(\003\022\r\n\005index\030\003 \001(\005\022\023\n\013lineUpindex" +
      "\030\004 \001(\005\"S\n\031ReqTakeOffChaoNengMingWen\022\022\n\nc" +
      "haoNengId\030\001 \001(\003\022\r\n\005index\030\002 \001(\005\022\023\n\013lineUp" +
      "index\030\003 \001(\005\"G\n\020ChaoNengListBean\022\014\n\004type\030" +
      "\001 \001(\005\022%\n\010chaoNeng\030\002 \001(\0132\023.abc.CommonItem" +
      "Bean\"\023\n\021ReqMingWenLevelUp\"\"\n\023ReqResonanc" +
      "eLevelUp\022\013\n\003cid\030\001 \001(\005\"\022\n\020ReqResonanceInf" +
      "o\">\n\020ResResonanceInfo\022*\n\tresonance\030\001 \003(\013" +
      "2\027.abc.CommonKeyValueBeanB$\n\022com.sh.game" +
      ".protosB\016ChaoNengProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.sh.game.protos.AbcProtos.getDescriptor(),
        });
    internal_static_chaoneng_ReqLevelUp_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_chaoneng_ReqLevelUp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqLevelUp_descriptor,
        new java.lang.String[] { "ChaoNengId", "Level", "Index", });
    internal_static_chaoneng_ReqResetLevel_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_chaoneng_ReqResetLevel_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqResetLevel_descriptor,
        new java.lang.String[] { "ChaoNengId", "Index", });
    internal_static_chaoneng_ReqPutOnChaoNeng_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_chaoneng_ReqPutOnChaoNeng_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqPutOnChaoNeng_descriptor,
        new java.lang.String[] { "Index", "Type", "ChaoNengId", });
    internal_static_chaoneng_ReqTakeOffChaoNeng_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_chaoneng_ReqTakeOffChaoNeng_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqTakeOffChaoNeng_descriptor,
        new java.lang.String[] { "Index", "Type", });
    internal_static_chaoneng_ReqUseChaoNeng_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_chaoneng_ReqUseChaoNeng_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqUseChaoNeng_descriptor,
        new java.lang.String[] { "Index", });
    internal_static_chaoneng_ReqBreakThroughChaoNeng_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_chaoneng_ReqBreakThroughChaoNeng_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqBreakThroughChaoNeng_descriptor,
        new java.lang.String[] { "ChaoNengId", "Index", "OneKey", });
    internal_static_chaoneng_ReqPutOnChaoNengMingWen_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_chaoneng_ReqPutOnChaoNengMingWen_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqPutOnChaoNengMingWen_descriptor,
        new java.lang.String[] { "ChaoNengId", "MingWenId", "Index", "LineUpindex", });
    internal_static_chaoneng_ReqTakeOffChaoNengMingWen_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_chaoneng_ReqTakeOffChaoNengMingWen_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqTakeOffChaoNengMingWen_descriptor,
        new java.lang.String[] { "ChaoNengId", "Index", "LineUpindex", });
    internal_static_chaoneng_ChaoNengListBean_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_chaoneng_ChaoNengListBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ChaoNengListBean_descriptor,
        new java.lang.String[] { "Type", "ChaoNeng", });
    internal_static_chaoneng_ReqMingWenLevelUp_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_chaoneng_ReqMingWenLevelUp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqMingWenLevelUp_descriptor,
        new java.lang.String[] { });
    internal_static_chaoneng_ReqResonanceLevelUp_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_chaoneng_ReqResonanceLevelUp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqResonanceLevelUp_descriptor,
        new java.lang.String[] { "Cid", });
    internal_static_chaoneng_ReqResonanceInfo_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_chaoneng_ReqResonanceInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ReqResonanceInfo_descriptor,
        new java.lang.String[] { });
    internal_static_chaoneng_ResResonanceInfo_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_chaoneng_ResResonanceInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chaoneng_ResResonanceInfo_descriptor,
        new java.lang.String[] { "Resonance", });
    com.sh.game.protos.AbcProtos.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
