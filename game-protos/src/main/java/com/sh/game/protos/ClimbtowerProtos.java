// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: climbtower.proto

package com.sh.game.protos;

public final class ClimbtowerProtos {
  private ClimbtowerProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ClimbTowerBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ClimbTowerBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *今日剩余免费次数
     * </pre>
     *
     * <code>int32 freeTimes = 1;</code>
     * @return The freeTimes.
     */
    int getFreeTimes();

    /**
     * <pre>
     *今日已购买普通骰子次数
     * </pre>
     *
     * <code>int32 times1 = 2;</code>
     * @return The times1.
     */
    int getTimes1();

    /**
     * <pre>
     *今日已购买金骰子次数
     * </pre>
     *
     * <code>int32 times2 = 3;</code>
     * @return The times2.
     */
    int getTimes2();

    /**
     * <pre>
     *当前层数
     * </pre>
     *
     * <code>int32 currId = 4;</code>
     * @return The currId.
     */
    int getCurrId();
  }
  /**
   * Protobuf type {@code climbtower.ClimbTowerBean}
   */
  public static final class ClimbTowerBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ClimbTowerBean)
      ClimbTowerBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClimbTowerBean.newBuilder() to construct.
    private ClimbTowerBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClimbTowerBean() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClimbTowerBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClimbTowerBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              freeTimes_ = input.readInt32();
              break;
            }
            case 16: {

              times1_ = input.readInt32();
              break;
            }
            case 24: {

              times2_ = input.readInt32();
              break;
            }
            case 32: {

              currId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.class, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder.class);
    }

    public static final int FREETIMES_FIELD_NUMBER = 1;
    private int freeTimes_;
    /**
     * <pre>
     *今日剩余免费次数
     * </pre>
     *
     * <code>int32 freeTimes = 1;</code>
     * @return The freeTimes.
     */
    @java.lang.Override
    public int getFreeTimes() {
      return freeTimes_;
    }

    public static final int TIMES1_FIELD_NUMBER = 2;
    private int times1_;
    /**
     * <pre>
     *今日已购买普通骰子次数
     * </pre>
     *
     * <code>int32 times1 = 2;</code>
     * @return The times1.
     */
    @java.lang.Override
    public int getTimes1() {
      return times1_;
    }

    public static final int TIMES2_FIELD_NUMBER = 3;
    private int times2_;
    /**
     * <pre>
     *今日已购买金骰子次数
     * </pre>
     *
     * <code>int32 times2 = 3;</code>
     * @return The times2.
     */
    @java.lang.Override
    public int getTimes2() {
      return times2_;
    }

    public static final int CURRID_FIELD_NUMBER = 4;
    private int currId_;
    /**
     * <pre>
     *当前层数
     * </pre>
     *
     * <code>int32 currId = 4;</code>
     * @return The currId.
     */
    @java.lang.Override
    public int getCurrId() {
      return currId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (freeTimes_ != 0) {
        output.writeInt32(1, freeTimes_);
      }
      if (times1_ != 0) {
        output.writeInt32(2, times1_);
      }
      if (times2_ != 0) {
        output.writeInt32(3, times2_);
      }
      if (currId_ != 0) {
        output.writeInt32(4, currId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (freeTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, freeTimes_);
      }
      if (times1_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, times1_);
      }
      if (times2_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, times2_);
      }
      if (currId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, currId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean other = (com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean) obj;

      if (getFreeTimes()
          != other.getFreeTimes()) return false;
      if (getTimes1()
          != other.getTimes1()) return false;
      if (getTimes2()
          != other.getTimes2()) return false;
      if (getCurrId()
          != other.getCurrId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + FREETIMES_FIELD_NUMBER;
      hash = (53 * hash) + getFreeTimes();
      hash = (37 * hash) + TIMES1_FIELD_NUMBER;
      hash = (53 * hash) + getTimes1();
      hash = (37 * hash) + TIMES2_FIELD_NUMBER;
      hash = (53 * hash) + getTimes2();
      hash = (37 * hash) + CURRID_FIELD_NUMBER;
      hash = (53 * hash) + getCurrId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code climbtower.ClimbTowerBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ClimbTowerBean)
        com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.class, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        freeTimes_ = 0;

        times1_ = 0;

        times2_ = 0;

        currId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean build() {
        com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean result = new com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean(this);
        result.freeTimes_ = freeTimes_;
        result.times1_ = times1_;
        result.times2_ = times2_;
        result.currId_ = currId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.getDefaultInstance()) return this;
        if (other.getFreeTimes() != 0) {
          setFreeTimes(other.getFreeTimes());
        }
        if (other.getTimes1() != 0) {
          setTimes1(other.getTimes1());
        }
        if (other.getTimes2() != 0) {
          setTimes2(other.getTimes2());
        }
        if (other.getCurrId() != 0) {
          setCurrId(other.getCurrId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int freeTimes_ ;
      /**
       * <pre>
       *今日剩余免费次数
       * </pre>
       *
       * <code>int32 freeTimes = 1;</code>
       * @return The freeTimes.
       */
      @java.lang.Override
      public int getFreeTimes() {
        return freeTimes_;
      }
      /**
       * <pre>
       *今日剩余免费次数
       * </pre>
       *
       * <code>int32 freeTimes = 1;</code>
       * @param value The freeTimes to set.
       * @return This builder for chaining.
       */
      public Builder setFreeTimes(int value) {
        
        freeTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *今日剩余免费次数
       * </pre>
       *
       * <code>int32 freeTimes = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFreeTimes() {
        
        freeTimes_ = 0;
        onChanged();
        return this;
      }

      private int times1_ ;
      /**
       * <pre>
       *今日已购买普通骰子次数
       * </pre>
       *
       * <code>int32 times1 = 2;</code>
       * @return The times1.
       */
      @java.lang.Override
      public int getTimes1() {
        return times1_;
      }
      /**
       * <pre>
       *今日已购买普通骰子次数
       * </pre>
       *
       * <code>int32 times1 = 2;</code>
       * @param value The times1 to set.
       * @return This builder for chaining.
       */
      public Builder setTimes1(int value) {
        
        times1_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *今日已购买普通骰子次数
       * </pre>
       *
       * <code>int32 times1 = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimes1() {
        
        times1_ = 0;
        onChanged();
        return this;
      }

      private int times2_ ;
      /**
       * <pre>
       *今日已购买金骰子次数
       * </pre>
       *
       * <code>int32 times2 = 3;</code>
       * @return The times2.
       */
      @java.lang.Override
      public int getTimes2() {
        return times2_;
      }
      /**
       * <pre>
       *今日已购买金骰子次数
       * </pre>
       *
       * <code>int32 times2 = 3;</code>
       * @param value The times2 to set.
       * @return This builder for chaining.
       */
      public Builder setTimes2(int value) {
        
        times2_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *今日已购买金骰子次数
       * </pre>
       *
       * <code>int32 times2 = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimes2() {
        
        times2_ = 0;
        onChanged();
        return this;
      }

      private int currId_ ;
      /**
       * <pre>
       *当前层数
       * </pre>
       *
       * <code>int32 currId = 4;</code>
       * @return The currId.
       */
      @java.lang.Override
      public int getCurrId() {
        return currId_;
      }
      /**
       * <pre>
       *当前层数
       * </pre>
       *
       * <code>int32 currId = 4;</code>
       * @param value The currId to set.
       * @return This builder for chaining.
       */
      public Builder setCurrId(int value) {
        
        currId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前层数
       * </pre>
       *
       * <code>int32 currId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrId() {
        
        currId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ClimbTowerBean)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ClimbTowerBean)
    private static final com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ClimbTowerBean>
        PARSER = new com.google.protobuf.AbstractParser<ClimbTowerBean>() {
      @java.lang.Override
      public ClimbTowerBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClimbTowerBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClimbTowerBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClimbTowerBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClimbTowerItemBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ClimbTowerItemBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *表中id
     * </pre>
     *
     * <code>int32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <pre>
     *格子的序号 0开始
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    int getIndex();

    /**
     * <pre>
     *物品id
     * </pre>
     *
     * <code>int32 itemId = 3;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <pre>
     *物品个数
     * </pre>
     *
     * <code>int32 itemCount = 4;</code>
     * @return The itemCount.
     */
    int getItemCount();

    /**
     * <pre>
     *是否已经被抽取
     * </pre>
     *
     * <code>bool isGet = 5;</code>
     * @return The isGet.
     */
    boolean getIsGet();
  }
  /**
   * Protobuf type {@code climbtower.ClimbTowerItemBean}
   */
  public static final class ClimbTowerItemBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ClimbTowerItemBean)
      ClimbTowerItemBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClimbTowerItemBean.newBuilder() to construct.
    private ClimbTowerItemBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClimbTowerItemBean() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClimbTowerItemBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClimbTowerItemBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              id_ = input.readInt32();
              break;
            }
            case 16: {

              index_ = input.readInt32();
              break;
            }
            case 24: {

              itemId_ = input.readInt32();
              break;
            }
            case 32: {

              itemCount_ = input.readInt32();
              break;
            }
            case 40: {

              isGet_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerItemBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerItemBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.class, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <pre>
     *表中id
     * </pre>
     *
     * <code>int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int INDEX_FIELD_NUMBER = 2;
    private int index_;
    /**
     * <pre>
     *格子的序号 0开始
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    public static final int ITEMID_FIELD_NUMBER = 3;
    private int itemId_;
    /**
     * <pre>
     *物品id
     * </pre>
     *
     * <code>int32 itemId = 3;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int ITEMCOUNT_FIELD_NUMBER = 4;
    private int itemCount_;
    /**
     * <pre>
     *物品个数
     * </pre>
     *
     * <code>int32 itemCount = 4;</code>
     * @return The itemCount.
     */
    @java.lang.Override
    public int getItemCount() {
      return itemCount_;
    }

    public static final int ISGET_FIELD_NUMBER = 5;
    private boolean isGet_;
    /**
     * <pre>
     *是否已经被抽取
     * </pre>
     *
     * <code>bool isGet = 5;</code>
     * @return The isGet.
     */
    @java.lang.Override
    public boolean getIsGet() {
      return isGet_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeInt32(1, id_);
      }
      if (index_ != 0) {
        output.writeInt32(2, index_);
      }
      if (itemId_ != 0) {
        output.writeInt32(3, itemId_);
      }
      if (itemCount_ != 0) {
        output.writeInt32(4, itemCount_);
      }
      if (isGet_ != false) {
        output.writeBool(5, isGet_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, index_);
      }
      if (itemId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, itemId_);
      }
      if (itemCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, itemCount_);
      }
      if (isGet_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, isGet_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean other = (com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean) obj;

      if (getId()
          != other.getId()) return false;
      if (getIndex()
          != other.getIndex()) return false;
      if (getItemId()
          != other.getItemId()) return false;
      if (getItemCount()
          != other.getItemCount()) return false;
      if (getIsGet()
          != other.getIsGet()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + ITEMID_FIELD_NUMBER;
      hash = (53 * hash) + getItemId();
      hash = (37 * hash) + ITEMCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getItemCount();
      hash = (37 * hash) + ISGET_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsGet());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code climbtower.ClimbTowerItemBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ClimbTowerItemBean)
        com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerItemBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerItemBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.class, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = 0;

        index_ = 0;

        itemId_ = 0;

        itemCount_ = 0;

        isGet_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ClimbTowerItemBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean build() {
        com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean result = new com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean(this);
        result.id_ = id_;
        result.index_ = index_;
        result.itemId_ = itemId_;
        result.itemCount_ = itemCount_;
        result.isGet_ = isGet_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getItemId() != 0) {
          setItemId(other.getItemId());
        }
        if (other.getItemCount() != 0) {
          setItemCount(other.getItemCount());
        }
        if (other.getIsGet() != false) {
          setIsGet(other.getIsGet());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int id_ ;
      /**
       * <pre>
       *表中id
       * </pre>
       *
       * <code>int32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <pre>
       *表中id
       * </pre>
       *
       * <code>int32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *表中id
       * </pre>
       *
       * <code>int32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        
        id_ = 0;
        onChanged();
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *格子的序号 0开始
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *格子的序号 0开始
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *格子的序号 0开始
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private int itemId_ ;
      /**
       * <pre>
       *物品id
       * </pre>
       *
       * <code>int32 itemId = 3;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       *物品id
       * </pre>
       *
       * <code>int32 itemId = 3;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *物品id
       * </pre>
       *
       * <code>int32 itemId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        
        itemId_ = 0;
        onChanged();
        return this;
      }

      private int itemCount_ ;
      /**
       * <pre>
       *物品个数
       * </pre>
       *
       * <code>int32 itemCount = 4;</code>
       * @return The itemCount.
       */
      @java.lang.Override
      public int getItemCount() {
        return itemCount_;
      }
      /**
       * <pre>
       *物品个数
       * </pre>
       *
       * <code>int32 itemCount = 4;</code>
       * @param value The itemCount to set.
       * @return This builder for chaining.
       */
      public Builder setItemCount(int value) {
        
        itemCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *物品个数
       * </pre>
       *
       * <code>int32 itemCount = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemCount() {
        
        itemCount_ = 0;
        onChanged();
        return this;
      }

      private boolean isGet_ ;
      /**
       * <pre>
       *是否已经被抽取
       * </pre>
       *
       * <code>bool isGet = 5;</code>
       * @return The isGet.
       */
      @java.lang.Override
      public boolean getIsGet() {
        return isGet_;
      }
      /**
       * <pre>
       *是否已经被抽取
       * </pre>
       *
       * <code>bool isGet = 5;</code>
       * @param value The isGet to set.
       * @return This builder for chaining.
       */
      public Builder setIsGet(boolean value) {
        
        isGet_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否已经被抽取
       * </pre>
       *
       * <code>bool isGet = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsGet() {
        
        isGet_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ClimbTowerItemBean)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ClimbTowerItemBean)
    private static final com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ClimbTowerItemBean>
        PARSER = new com.google.protobuf.AbstractParser<ClimbTowerItemBean>() {
      @java.lang.Override
      public ClimbTowerItemBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClimbTowerItemBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClimbTowerItemBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClimbTowerItemBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqClimbTowerOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ReqClimbTower)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqClimbTower' id='1' desc='请求数据' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ReqClimbTower}
   */
  public static final class ReqClimbTower extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ReqClimbTower)
      ReqClimbTowerOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqClimbTower.newBuilder() to construct.
    private ReqClimbTower(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqClimbTower() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqClimbTower();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqClimbTower(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTower_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTower_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ReqClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ReqClimbTower.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ReqClimbTower)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ReqClimbTower other = (com.sh.game.protos.ClimbtowerProtos.ReqClimbTower) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ReqClimbTower prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqClimbTower' id='1' desc='请求数据' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ReqClimbTower}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ReqClimbTower)
        com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTower_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTower_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ReqClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ReqClimbTower.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ReqClimbTower.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTower_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqClimbTower getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ReqClimbTower.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqClimbTower build() {
        com.sh.game.protos.ClimbtowerProtos.ReqClimbTower result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqClimbTower buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ReqClimbTower result = new com.sh.game.protos.ClimbtowerProtos.ReqClimbTower(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ReqClimbTower) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ReqClimbTower)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ReqClimbTower other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ReqClimbTower.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ReqClimbTower parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ReqClimbTower) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ReqClimbTower)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ReqClimbTower)
    private static final com.sh.game.protos.ClimbtowerProtos.ReqClimbTower DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ReqClimbTower();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTower getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqClimbTower>
        PARSER = new com.google.protobuf.AbstractParser<ReqClimbTower>() {
      @java.lang.Override
      public ReqClimbTower parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqClimbTower(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqClimbTower> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqClimbTower> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ReqClimbTower getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResClimbTowerOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ResClimbTower)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     * @return Whether the base field is set.
     */
    boolean hasBase();
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     * @return The base.
     */
    com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean getBase();
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     */
    com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder getBaseOrBuilder();

    /**
     * <pre>
     *今日到过的层数id
     * </pre>
     *
     * <code>repeated int32 ids = 2;</code>
     * @return A list containing the ids.
     */
    java.util.List<java.lang.Integer> getIdsList();
    /**
     * <pre>
     *今日到过的层数id
     * </pre>
     *
     * <code>repeated int32 ids = 2;</code>
     * @return The count of ids.
     */
    int getIdsCount();
    /**
     * <pre>
     *今日到过的层数id
     * </pre>
     *
     * <code>repeated int32 ids = 2;</code>
     * @param index The index of the element to return.
     * @return The ids at the given index.
     */
    int getIds(int index);
  }
  /**
   * <pre>
   ** class='ResClimbTower' id='2' desc='请求数据返回' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ResClimbTower}
   */
  public static final class ResClimbTower extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ResClimbTower)
      ResClimbTowerOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResClimbTower.newBuilder() to construct.
    private ResClimbTower(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResClimbTower() {
      ids_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResClimbTower();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResClimbTower(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder subBuilder = null;
              if (base_ != null) {
                subBuilder = base_.toBuilder();
              }
              base_ = input.readMessage(com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(base_);
                base_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                ids_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              ids_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                ids_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                ids_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          ids_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTower_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTower_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ResClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ResClimbTower.Builder.class);
    }

    public static final int BASE_FIELD_NUMBER = 1;
    private com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean base_;
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     * @return Whether the base field is set.
     */
    @java.lang.Override
    public boolean hasBase() {
      return base_ != null;
    }
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     * @return The base.
     */
    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean getBase() {
      return base_ == null ? com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.getDefaultInstance() : base_;
    }
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder getBaseOrBuilder() {
      return getBase();
    }

    public static final int IDS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList ids_;
    /**
     * <pre>
     *今日到过的层数id
     * </pre>
     *
     * <code>repeated int32 ids = 2;</code>
     * @return A list containing the ids.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getIdsList() {
      return ids_;
    }
    /**
     * <pre>
     *今日到过的层数id
     * </pre>
     *
     * <code>repeated int32 ids = 2;</code>
     * @return The count of ids.
     */
    public int getIdsCount() {
      return ids_.size();
    }
    /**
     * <pre>
     *今日到过的层数id
     * </pre>
     *
     * <code>repeated int32 ids = 2;</code>
     * @param index The index of the element to return.
     * @return The ids at the given index.
     */
    public int getIds(int index) {
      return ids_.getInt(index);
    }
    private int idsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (base_ != null) {
        output.writeMessage(1, getBase());
      }
      if (getIdsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(idsMemoizedSerializedSize);
      }
      for (int i = 0; i < ids_.size(); i++) {
        output.writeInt32NoTag(ids_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (base_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getBase());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < ids_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(ids_.getInt(i));
        }
        size += dataSize;
        if (!getIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        idsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ResClimbTower)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ResClimbTower other = (com.sh.game.protos.ClimbtowerProtos.ResClimbTower) obj;

      if (hasBase() != other.hasBase()) return false;
      if (hasBase()) {
        if (!getBase()
            .equals(other.getBase())) return false;
      }
      if (!getIdsList()
          .equals(other.getIdsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBase()) {
        hash = (37 * hash) + BASE_FIELD_NUMBER;
        hash = (53 * hash) + getBase().hashCode();
      }
      if (getIdsCount() > 0) {
        hash = (37 * hash) + IDS_FIELD_NUMBER;
        hash = (53 * hash) + getIdsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ResClimbTower prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResClimbTower' id='2' desc='请求数据返回' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ResClimbTower}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ResClimbTower)
        com.sh.game.protos.ClimbtowerProtos.ResClimbTowerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTower_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTower_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ResClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ResClimbTower.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ResClimbTower.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (baseBuilder_ == null) {
          base_ = null;
        } else {
          base_ = null;
          baseBuilder_ = null;
        }
        ids_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTower_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResClimbTower getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ResClimbTower.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResClimbTower build() {
        com.sh.game.protos.ClimbtowerProtos.ResClimbTower result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResClimbTower buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ResClimbTower result = new com.sh.game.protos.ClimbtowerProtos.ResClimbTower(this);
        int from_bitField0_ = bitField0_;
        if (baseBuilder_ == null) {
          result.base_ = base_;
        } else {
          result.base_ = baseBuilder_.build();
        }
        if (((bitField0_ & 0x00000001) != 0)) {
          ids_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.ids_ = ids_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ResClimbTower) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ResClimbTower)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ResClimbTower other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ResClimbTower.getDefaultInstance()) return this;
        if (other.hasBase()) {
          mergeBase(other.getBase());
        }
        if (!other.ids_.isEmpty()) {
          if (ids_.isEmpty()) {
            ids_ = other.ids_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureIdsIsMutable();
            ids_.addAll(other.ids_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ResClimbTower parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ResClimbTower) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean base_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder> baseBuilder_;
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       * @return Whether the base field is set.
       */
      public boolean hasBase() {
        return baseBuilder_ != null || base_ != null;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       * @return The base.
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean getBase() {
        if (baseBuilder_ == null) {
          return base_ == null ? com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.getDefaultInstance() : base_;
        } else {
          return baseBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public Builder setBase(com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean value) {
        if (baseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          base_ = value;
          onChanged();
        } else {
          baseBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public Builder setBase(
          com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder builderForValue) {
        if (baseBuilder_ == null) {
          base_ = builderForValue.build();
          onChanged();
        } else {
          baseBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public Builder mergeBase(com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean value) {
        if (baseBuilder_ == null) {
          if (base_ != null) {
            base_ =
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.newBuilder(base_).mergeFrom(value).buildPartial();
          } else {
            base_ = value;
          }
          onChanged();
        } else {
          baseBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public Builder clearBase() {
        if (baseBuilder_ == null) {
          base_ = null;
          onChanged();
        } else {
          base_ = null;
          baseBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder getBaseBuilder() {
        
        onChanged();
        return getBaseFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder getBaseOrBuilder() {
        if (baseBuilder_ != null) {
          return baseBuilder_.getMessageOrBuilder();
        } else {
          return base_ == null ?
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.getDefaultInstance() : base_;
        }
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder> 
          getBaseFieldBuilder() {
        if (baseBuilder_ == null) {
          baseBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder>(
                  getBase(),
                  getParentForChildren(),
                  isClean());
          base_ = null;
        }
        return baseBuilder_;
      }

      private com.google.protobuf.Internal.IntList ids_ = emptyIntList();
      private void ensureIdsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          ids_ = mutableCopy(ids_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       *今日到过的层数id
       * </pre>
       *
       * <code>repeated int32 ids = 2;</code>
       * @return A list containing the ids.
       */
      public java.util.List<java.lang.Integer>
          getIdsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(ids_) : ids_;
      }
      /**
       * <pre>
       *今日到过的层数id
       * </pre>
       *
       * <code>repeated int32 ids = 2;</code>
       * @return The count of ids.
       */
      public int getIdsCount() {
        return ids_.size();
      }
      /**
       * <pre>
       *今日到过的层数id
       * </pre>
       *
       * <code>repeated int32 ids = 2;</code>
       * @param index The index of the element to return.
       * @return The ids at the given index.
       */
      public int getIds(int index) {
        return ids_.getInt(index);
      }
      /**
       * <pre>
       *今日到过的层数id
       * </pre>
       *
       * <code>repeated int32 ids = 2;</code>
       * @param index The index to set the value at.
       * @param value The ids to set.
       * @return This builder for chaining.
       */
      public Builder setIds(
          int index, int value) {
        ensureIdsIsMutable();
        ids_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *今日到过的层数id
       * </pre>
       *
       * <code>repeated int32 ids = 2;</code>
       * @param value The ids to add.
       * @return This builder for chaining.
       */
      public Builder addIds(int value) {
        ensureIdsIsMutable();
        ids_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *今日到过的层数id
       * </pre>
       *
       * <code>repeated int32 ids = 2;</code>
       * @param values The ids to add.
       * @return This builder for chaining.
       */
      public Builder addAllIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, ids_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *今日到过的层数id
       * </pre>
       *
       * <code>repeated int32 ids = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIds() {
        ids_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ResClimbTower)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ResClimbTower)
    private static final com.sh.game.protos.ClimbtowerProtos.ResClimbTower DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ResClimbTower();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTower getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResClimbTower>
        PARSER = new com.google.protobuf.AbstractParser<ResClimbTower>() {
      @java.lang.Override
      public ResClimbTower parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResClimbTower(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResClimbTower> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResClimbTower> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ResClimbTower getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqStartClimbTowerOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ReqStartClimbTower)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqStartClimbTower' id='3' desc='开始挑战' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ReqStartClimbTower}
   */
  public static final class ReqStartClimbTower extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ReqStartClimbTower)
      ReqStartClimbTowerOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqStartClimbTower.newBuilder() to construct.
    private ReqStartClimbTower(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqStartClimbTower() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqStartClimbTower();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqStartClimbTower(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqStartClimbTower_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqStartClimbTower_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower other = (com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqStartClimbTower' id='3' desc='开始挑战' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ReqStartClimbTower}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ReqStartClimbTower)
        com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTowerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqStartClimbTower_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqStartClimbTower_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqStartClimbTower_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower build() {
        com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower result = new com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ReqStartClimbTower)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ReqStartClimbTower)
    private static final com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqStartClimbTower>
        PARSER = new com.google.protobuf.AbstractParser<ReqStartClimbTower>() {
      @java.lang.Override
      public ReqStartClimbTower parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqStartClimbTower(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqStartClimbTower> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqStartClimbTower> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ReqStartClimbTower getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqRollClimbTowerOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ReqRollClimbTower)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 1：免费或者普通 2：金
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *金骰子时选择的值
     * </pre>
     *
     * <code>int32 val = 2;</code>
     * @return The val.
     */
    int getVal();
  }
  /**
   * <pre>
   ** class='ReqRollClimbTower' id='4' desc='投骰子' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ReqRollClimbTower}
   */
  public static final class ReqRollClimbTower extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ReqRollClimbTower)
      ReqRollClimbTowerOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqRollClimbTower.newBuilder() to construct.
    private ReqRollClimbTower(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqRollClimbTower() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqRollClimbTower();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqRollClimbTower(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 16: {

              val_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTower_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTower_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     * 1：免费或者普通 2：金
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int VAL_FIELD_NUMBER = 2;
    private int val_;
    /**
     * <pre>
     *金骰子时选择的值
     * </pre>
     *
     * <code>int32 val = 2;</code>
     * @return The val.
     */
    @java.lang.Override
    public int getVal() {
      return val_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (val_ != 0) {
        output.writeInt32(2, val_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (val_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, val_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower other = (com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower) obj;

      if (getType()
          != other.getType()) return false;
      if (getVal()
          != other.getVal()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + VAL_FIELD_NUMBER;
      hash = (53 * hash) + getVal();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqRollClimbTower' id='4' desc='投骰子' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ReqRollClimbTower}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ReqRollClimbTower)
        com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTower_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTower_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        val_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTower_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower build() {
        com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower result = new com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower(this);
        result.type_ = type_;
        result.val_ = val_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getVal() != 0) {
          setVal(other.getVal());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       * 1：免费或者普通 2：金
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       * 1：免费或者普通 2：金
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 1：免费或者普通 2：金
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int val_ ;
      /**
       * <pre>
       *金骰子时选择的值
       * </pre>
       *
       * <code>int32 val = 2;</code>
       * @return The val.
       */
      @java.lang.Override
      public int getVal() {
        return val_;
      }
      /**
       * <pre>
       *金骰子时选择的值
       * </pre>
       *
       * <code>int32 val = 2;</code>
       * @param value The val to set.
       * @return This builder for chaining.
       */
      public Builder setVal(int value) {
        
        val_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *金骰子时选择的值
       * </pre>
       *
       * <code>int32 val = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearVal() {
        
        val_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ReqRollClimbTower)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ReqRollClimbTower)
    private static final com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqRollClimbTower>
        PARSER = new com.google.protobuf.AbstractParser<ReqRollClimbTower>() {
      @java.lang.Override
      public ReqRollClimbTower parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqRollClimbTower(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqRollClimbTower> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqRollClimbTower> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTower getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResRollClimbTowerOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ResRollClimbTower)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     * @return Whether the base field is set.
     */
    boolean hasBase();
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     * @return The base.
     */
    com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean getBase();
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     */
    com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder getBaseOrBuilder();

    /**
     * <pre>
     *摇到的值
     * </pre>
     *
     * <code>int32 ret = 2;</code>
     * @return The ret.
     */
    int getRet();
  }
  /**
   * <pre>
   ** class='ResRollClimbTower' id='5' desc='投骰子结果' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ResRollClimbTower}
   */
  public static final class ResRollClimbTower extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ResRollClimbTower)
      ResRollClimbTowerOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResRollClimbTower.newBuilder() to construct.
    private ResRollClimbTower(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResRollClimbTower() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResRollClimbTower();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResRollClimbTower(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder subBuilder = null;
              if (base_ != null) {
                subBuilder = base_.toBuilder();
              }
              base_ = input.readMessage(com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(base_);
                base_ = subBuilder.buildPartial();
              }

              break;
            }
            case 16: {

              ret_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTower_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTower_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower.Builder.class);
    }

    public static final int BASE_FIELD_NUMBER = 1;
    private com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean base_;
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     * @return Whether the base field is set.
     */
    @java.lang.Override
    public boolean hasBase() {
      return base_ != null;
    }
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     * @return The base.
     */
    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean getBase() {
      return base_ == null ? com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.getDefaultInstance() : base_;
    }
    /**
     * <pre>
     *基础信息
     * </pre>
     *
     * <code>.climbtower.ClimbTowerBean base = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder getBaseOrBuilder() {
      return getBase();
    }

    public static final int RET_FIELD_NUMBER = 2;
    private int ret_;
    /**
     * <pre>
     *摇到的值
     * </pre>
     *
     * <code>int32 ret = 2;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (base_ != null) {
        output.writeMessage(1, getBase());
      }
      if (ret_ != 0) {
        output.writeInt32(2, ret_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (base_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getBase());
      }
      if (ret_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, ret_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower other = (com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower) obj;

      if (hasBase() != other.hasBase()) return false;
      if (hasBase()) {
        if (!getBase()
            .equals(other.getBase())) return false;
      }
      if (getRet()
          != other.getRet()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBase()) {
        hash = (37 * hash) + BASE_FIELD_NUMBER;
        hash = (53 * hash) + getBase().hashCode();
      }
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResRollClimbTower' id='5' desc='投骰子结果' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ResRollClimbTower}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ResRollClimbTower)
        com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTower_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTower_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (baseBuilder_ == null) {
          base_ = null;
        } else {
          base_ = null;
          baseBuilder_ = null;
        }
        ret_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTower_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower build() {
        com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower result = new com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower(this);
        if (baseBuilder_ == null) {
          result.base_ = base_;
        } else {
          result.base_ = baseBuilder_.build();
        }
        result.ret_ = ret_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower.getDefaultInstance()) return this;
        if (other.hasBase()) {
          mergeBase(other.getBase());
        }
        if (other.getRet() != 0) {
          setRet(other.getRet());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean base_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder> baseBuilder_;
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       * @return Whether the base field is set.
       */
      public boolean hasBase() {
        return baseBuilder_ != null || base_ != null;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       * @return The base.
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean getBase() {
        if (baseBuilder_ == null) {
          return base_ == null ? com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.getDefaultInstance() : base_;
        } else {
          return baseBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public Builder setBase(com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean value) {
        if (baseBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          base_ = value;
          onChanged();
        } else {
          baseBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public Builder setBase(
          com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder builderForValue) {
        if (baseBuilder_ == null) {
          base_ = builderForValue.build();
          onChanged();
        } else {
          baseBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public Builder mergeBase(com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean value) {
        if (baseBuilder_ == null) {
          if (base_ != null) {
            base_ =
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.newBuilder(base_).mergeFrom(value).buildPartial();
          } else {
            base_ = value;
          }
          onChanged();
        } else {
          baseBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public Builder clearBase() {
        if (baseBuilder_ == null) {
          base_ = null;
          onChanged();
        } else {
          base_ = null;
          baseBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder getBaseBuilder() {
        
        onChanged();
        return getBaseFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder getBaseOrBuilder() {
        if (baseBuilder_ != null) {
          return baseBuilder_.getMessageOrBuilder();
        } else {
          return base_ == null ?
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.getDefaultInstance() : base_;
        }
      }
      /**
       * <pre>
       *基础信息
       * </pre>
       *
       * <code>.climbtower.ClimbTowerBean base = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder> 
          getBaseFieldBuilder() {
        if (baseBuilder_ == null) {
          baseBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBean.Builder, com.sh.game.protos.ClimbtowerProtos.ClimbTowerBeanOrBuilder>(
                  getBase(),
                  getParentForChildren(),
                  isClean());
          base_ = null;
        }
        return baseBuilder_;
      }

      private int ret_ ;
      /**
       * <pre>
       *摇到的值
       * </pre>
       *
       * <code>int32 ret = 2;</code>
       * @return The ret.
       */
      @java.lang.Override
      public int getRet() {
        return ret_;
      }
      /**
       * <pre>
       *摇到的值
       * </pre>
       *
       * <code>int32 ret = 2;</code>
       * @param value The ret to set.
       * @return This builder for chaining.
       */
      public Builder setRet(int value) {
        
        ret_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *摇到的值
       * </pre>
       *
       * <code>int32 ret = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRet() {
        
        ret_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ResRollClimbTower)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ResRollClimbTower)
    private static final com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResRollClimbTower>
        PARSER = new com.google.protobuf.AbstractParser<ResRollClimbTower>() {
      @java.lang.Override
      public ResRollClimbTower parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResRollClimbTower(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResRollClimbTower> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResRollClimbTower> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ResRollClimbTower getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqJumpClimbTowerOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ReqJumpClimbTower)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqJumpClimbTower' id='6' desc='开始跳' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ReqJumpClimbTower}
   */
  public static final class ReqJumpClimbTower extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ReqJumpClimbTower)
      ReqJumpClimbTowerOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqJumpClimbTower.newBuilder() to construct.
    private ReqJumpClimbTower(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqJumpClimbTower() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqJumpClimbTower();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqJumpClimbTower(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqJumpClimbTower_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqJumpClimbTower_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower other = (com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqJumpClimbTower' id='6' desc='开始跳' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ReqJumpClimbTower}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ReqJumpClimbTower)
        com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTowerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqJumpClimbTower_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqJumpClimbTower_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqJumpClimbTower_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower build() {
        com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower result = new com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ReqJumpClimbTower)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ReqJumpClimbTower)
    private static final com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqJumpClimbTower>
        PARSER = new com.google.protobuf.AbstractParser<ReqJumpClimbTower>() {
      @java.lang.Override
      public ReqJumpClimbTower parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqJumpClimbTower(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqJumpClimbTower> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqJumpClimbTower> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ReqJumpClimbTower getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResJumpClimbTowerOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ResJumpClimbTower)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *当前层数
     * </pre>
     *
     * <code>int32 currId = 1;</code>
     * @return The currId.
     */
    int getCurrId();

    /**
     * <pre>
     *当前层权利 表中的类型
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *剩余时间
     * </pre>
     *
     * <code>int32 time = 3;</code>
     * @return The time.
     */
    int getTime();
  }
  /**
   * <pre>
   ** class='ResJumpClimbTower' id='7' desc='开始跳返回' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ResJumpClimbTower}
   */
  public static final class ResJumpClimbTower extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ResJumpClimbTower)
      ResJumpClimbTowerOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResJumpClimbTower.newBuilder() to construct.
    private ResJumpClimbTower(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResJumpClimbTower() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResJumpClimbTower();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResJumpClimbTower(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              currId_ = input.readInt32();
              break;
            }
            case 16: {

              type_ = input.readInt32();
              break;
            }
            case 24: {

              time_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResJumpClimbTower_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResJumpClimbTower_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower.Builder.class);
    }

    public static final int CURRID_FIELD_NUMBER = 1;
    private int currId_;
    /**
     * <pre>
     *当前层数
     * </pre>
     *
     * <code>int32 currId = 1;</code>
     * @return The currId.
     */
    @java.lang.Override
    public int getCurrId() {
      return currId_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <pre>
     *当前层权利 表中的类型
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int TIME_FIELD_NUMBER = 3;
    private int time_;
    /**
     * <pre>
     *剩余时间
     * </pre>
     *
     * <code>int32 time = 3;</code>
     * @return The time.
     */
    @java.lang.Override
    public int getTime() {
      return time_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (currId_ != 0) {
        output.writeInt32(1, currId_);
      }
      if (type_ != 0) {
        output.writeInt32(2, type_);
      }
      if (time_ != 0) {
        output.writeInt32(3, time_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (currId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, currId_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      if (time_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, time_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower other = (com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower) obj;

      if (getCurrId()
          != other.getCurrId()) return false;
      if (getType()
          != other.getType()) return false;
      if (getTime()
          != other.getTime()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CURRID_FIELD_NUMBER;
      hash = (53 * hash) + getCurrId();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + getTime();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResJumpClimbTower' id='7' desc='开始跳返回' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ResJumpClimbTower}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ResJumpClimbTower)
        com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTowerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResJumpClimbTower_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResJumpClimbTower_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower.class, com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        currId_ = 0;

        type_ = 0;

        time_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResJumpClimbTower_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower build() {
        com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower result = new com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower(this);
        result.currId_ = currId_;
        result.type_ = type_;
        result.time_ = time_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower.getDefaultInstance()) return this;
        if (other.getCurrId() != 0) {
          setCurrId(other.getCurrId());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getTime() != 0) {
          setTime(other.getTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int currId_ ;
      /**
       * <pre>
       *当前层数
       * </pre>
       *
       * <code>int32 currId = 1;</code>
       * @return The currId.
       */
      @java.lang.Override
      public int getCurrId() {
        return currId_;
      }
      /**
       * <pre>
       *当前层数
       * </pre>
       *
       * <code>int32 currId = 1;</code>
       * @param value The currId to set.
       * @return This builder for chaining.
       */
      public Builder setCurrId(int value) {
        
        currId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前层数
       * </pre>
       *
       * <code>int32 currId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrId() {
        
        currId_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *当前层权利 表中的类型
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *当前层权利 表中的类型
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前层权利 表中的类型
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int time_ ;
      /**
       * <pre>
       *剩余时间
       * </pre>
       *
       * <code>int32 time = 3;</code>
       * @return The time.
       */
      @java.lang.Override
      public int getTime() {
        return time_;
      }
      /**
       * <pre>
       *剩余时间
       * </pre>
       *
       * <code>int32 time = 3;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(int value) {
        
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *剩余时间
       * </pre>
       *
       * <code>int32 time = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        
        time_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ResJumpClimbTower)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ResJumpClimbTower)
    private static final com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResJumpClimbTower>
        PARSER = new com.google.protobuf.AbstractParser<ResJumpClimbTower>() {
      @java.lang.Override
      public ResJumpClimbTower parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResJumpClimbTower(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResJumpClimbTower> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResJumpClimbTower> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ResJumpClimbTower getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqClimbTowerTurnOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ReqClimbTowerTurn)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ReqClimbTowerTurn' id='8' desc='请求转盘数据' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ReqClimbTowerTurn}
   */
  public static final class ReqClimbTowerTurn extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ReqClimbTowerTurn)
      ReqClimbTowerTurnOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqClimbTowerTurn.newBuilder() to construct.
    private ReqClimbTowerTurn(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqClimbTowerTurn() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqClimbTowerTurn();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqClimbTowerTurn(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTowerTurn_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTowerTurn_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn other = (com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn) obj;

      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqClimbTowerTurn' id='8' desc='请求转盘数据' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ReqClimbTowerTurn}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ReqClimbTowerTurn)
        com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurnOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTowerTurn_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn build() {
        com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn result = new com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn(this);
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ReqClimbTowerTurn)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ReqClimbTowerTurn)
    private static final com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqClimbTowerTurn>
        PARSER = new com.google.protobuf.AbstractParser<ReqClimbTowerTurn>() {
      @java.lang.Override
      public ReqClimbTowerTurn parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqClimbTowerTurn(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqClimbTowerTurn> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqClimbTowerTurn> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResClimbTowerTurnOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ResClimbTowerTurn)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    java.util.List<com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean> 
        getItemList();
    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean getItem(int index);
    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    int getItemCount();
    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    java.util.List<? extends com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder> 
        getItemOrBuilderList();
    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder getItemOrBuilder(
        int index);

    /**
     * <pre>
     *今日已经转的次数
     * </pre>
     *
     * <code>int32 times = 2;</code>
     * @return The times.
     */
    int getTimes();

    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 3;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ResClimbTowerTurn' id='9' desc='返回转盘次数' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ResClimbTowerTurn}
   */
  public static final class ResClimbTowerTurn extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ResClimbTowerTurn)
      ResClimbTowerTurnOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResClimbTowerTurn.newBuilder() to construct.
    private ResClimbTowerTurn(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResClimbTowerTurn() {
      item_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResClimbTowerTurn();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResClimbTowerTurn(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                item_ = new java.util.ArrayList<com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              item_.add(
                  input.readMessage(com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.parser(), extensionRegistry));
              break;
            }
            case 16: {

              times_ = input.readInt32();
              break;
            }
            case 24: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTowerTurn_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTowerTurn_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn.Builder.class);
    }

    public static final int ITEM_FIELD_NUMBER = 1;
    private java.util.List<com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean> item_;
    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean> getItemList() {
      return item_;
    }
    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    @java.lang.Override
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean getItem(int index) {
      return item_.get(index);
    }
    /**
     * <pre>
     *奖池
     * </pre>
     *
     * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    public static final int TIMES_FIELD_NUMBER = 2;
    private int times_;
    /**
     * <pre>
     *今日已经转的次数
     * </pre>
     *
     * <code>int32 times = 2;</code>
     * @return The times.
     */
    @java.lang.Override
    public int getTimes() {
      return times_;
    }

    public static final int TYPE_FIELD_NUMBER = 3;
    private int type_;
    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 3;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(1, item_.get(i));
      }
      if (times_ != 0) {
        output.writeInt32(2, times_);
      }
      if (type_ != 0) {
        output.writeInt32(3, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, item_.get(i));
      }
      if (times_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, times_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn other = (com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn) obj;

      if (!getItemList()
          .equals(other.getItemList())) return false;
      if (getTimes()
          != other.getTimes()) return false;
      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getItemCount() > 0) {
        hash = (37 * hash) + ITEM_FIELD_NUMBER;
        hash = (53 * hash) + getItemList().hashCode();
      }
      hash = (37 * hash) + TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getTimes();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResClimbTowerTurn' id='9' desc='返回转盘次数' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ResClimbTowerTurn}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ResClimbTowerTurn)
        com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurnOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTowerTurn_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getItemFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          itemBuilder_.clear();
        }
        times_ = 0;

        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn build() {
        com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn result = new com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn(this);
        int from_bitField0_ = bitField0_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        result.times_ = times_;
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn.getDefaultInstance()) return this;
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000001);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (other.getTimes() != 0) {
          setTimes(other.getTimes());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          item_ = new java.util.ArrayList<com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean>(item_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder> itemBuilder_;

      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public java.util.List<com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public Builder setItem(
          int index, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public Builder setItem(
          int index, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public Builder addItem(com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public Builder addItem(
          int index, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public Builder addItem(
          com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public Builder addItem(
          int index, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public java.util.List<? extends com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.getDefaultInstance());
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.getDefaultInstance());
      }
      /**
       * <pre>
       *奖池
       * </pre>
       *
       * <code>repeated .climbtower.ClimbTowerItemBean item = 1;</code>
       */
      public java.util.List<com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBean.Builder, com.sh.game.protos.ClimbtowerProtos.ClimbTowerItemBeanOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      private int times_ ;
      /**
       * <pre>
       *今日已经转的次数
       * </pre>
       *
       * <code>int32 times = 2;</code>
       * @return The times.
       */
      @java.lang.Override
      public int getTimes() {
        return times_;
      }
      /**
       * <pre>
       *今日已经转的次数
       * </pre>
       *
       * <code>int32 times = 2;</code>
       * @param value The times to set.
       * @return This builder for chaining.
       */
      public Builder setTimes(int value) {
        
        times_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *今日已经转的次数
       * </pre>
       *
       * <code>int32 times = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimes() {
        
        times_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 3;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 3;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ResClimbTowerTurn)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ResClimbTowerTurn)
    private static final com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResClimbTowerTurn>
        PARSER = new com.google.protobuf.AbstractParser<ResClimbTowerTurn>() {
      @java.lang.Override
      public ResClimbTowerTurn parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResClimbTowerTurn(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResClimbTowerTurn> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResClimbTowerTurn> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ResClimbTowerTurn getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqRollClimbTowerTurnOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ReqRollClimbTowerTurn)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ReqRollClimbTowerTurn' id='10' desc='请求转' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ReqRollClimbTowerTurn}
   */
  public static final class ReqRollClimbTowerTurn extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ReqRollClimbTowerTurn)
      ReqRollClimbTowerTurnOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqRollClimbTowerTurn.newBuilder() to construct.
    private ReqRollClimbTowerTurn(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqRollClimbTowerTurn() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqRollClimbTowerTurn();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqRollClimbTowerTurn(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTowerTurn_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTowerTurn_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn other = (com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn) obj;

      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqRollClimbTowerTurn' id='10' desc='请求转' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ReqRollClimbTowerTurn}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ReqRollClimbTowerTurn)
        com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurnOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTowerTurn_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqRollClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn build() {
        com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn result = new com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn(this);
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ReqRollClimbTowerTurn)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ReqRollClimbTowerTurn)
    private static final com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqRollClimbTowerTurn>
        PARSER = new com.google.protobuf.AbstractParser<ReqRollClimbTowerTurn>() {
      @java.lang.Override
      public ReqRollClimbTowerTurn parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqRollClimbTowerTurn(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqRollClimbTowerTurn> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqRollClimbTowerTurn> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ReqRollClimbTowerTurn getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResRollClimbTowerTurnOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ResRollClimbTowerTurn)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *结果 0-11
     * </pre>
     *
     * <code>int32 ret = 2;</code>
     * @return The ret.
     */
    int getRet();

    /**
     * <pre>
     *今日已经转的次数
     * </pre>
     *
     * <code>int32 times = 3;</code>
     * @return The times.
     */
    int getTimes();
  }
  /**
   * <pre>
   ** class='ResRollClimbTowerTurn' id='11' desc='返回转' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ResRollClimbTowerTurn}
   */
  public static final class ResRollClimbTowerTurn extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ResRollClimbTowerTurn)
      ResRollClimbTowerTurnOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResRollClimbTowerTurn.newBuilder() to construct.
    private ResRollClimbTowerTurn(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResRollClimbTowerTurn() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResRollClimbTowerTurn();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResRollClimbTowerTurn(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 16: {

              ret_ = input.readInt32();
              break;
            }
            case 24: {

              times_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTowerTurn_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTowerTurn_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int RET_FIELD_NUMBER = 2;
    private int ret_;
    /**
     * <pre>
     *结果 0-11
     * </pre>
     *
     * <code>int32 ret = 2;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }

    public static final int TIMES_FIELD_NUMBER = 3;
    private int times_;
    /**
     * <pre>
     *今日已经转的次数
     * </pre>
     *
     * <code>int32 times = 3;</code>
     * @return The times.
     */
    @java.lang.Override
    public int getTimes() {
      return times_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (ret_ != 0) {
        output.writeInt32(2, ret_);
      }
      if (times_ != 0) {
        output.writeInt32(3, times_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (ret_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, ret_);
      }
      if (times_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, times_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn other = (com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn) obj;

      if (getType()
          != other.getType()) return false;
      if (getRet()
          != other.getRet()) return false;
      if (getTimes()
          != other.getTimes()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
      hash = (37 * hash) + TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getTimes();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResRollClimbTowerTurn' id='11' desc='返回转' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ResRollClimbTowerTurn}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ResRollClimbTowerTurn)
        com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurnOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTowerTurn_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        ret_ = 0;

        times_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ResRollClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn build() {
        com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn result = new com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn(this);
        result.type_ = type_;
        result.ret_ = ret_;
        result.times_ = times_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getRet() != 0) {
          setRet(other.getRet());
        }
        if (other.getTimes() != 0) {
          setTimes(other.getTimes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int ret_ ;
      /**
       * <pre>
       *结果 0-11
       * </pre>
       *
       * <code>int32 ret = 2;</code>
       * @return The ret.
       */
      @java.lang.Override
      public int getRet() {
        return ret_;
      }
      /**
       * <pre>
       *结果 0-11
       * </pre>
       *
       * <code>int32 ret = 2;</code>
       * @param value The ret to set.
       * @return This builder for chaining.
       */
      public Builder setRet(int value) {
        
        ret_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *结果 0-11
       * </pre>
       *
       * <code>int32 ret = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRet() {
        
        ret_ = 0;
        onChanged();
        return this;
      }

      private int times_ ;
      /**
       * <pre>
       *今日已经转的次数
       * </pre>
       *
       * <code>int32 times = 3;</code>
       * @return The times.
       */
      @java.lang.Override
      public int getTimes() {
        return times_;
      }
      /**
       * <pre>
       *今日已经转的次数
       * </pre>
       *
       * <code>int32 times = 3;</code>
       * @param value The times to set.
       * @return This builder for chaining.
       */
      public Builder setTimes(int value) {
        
        times_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *今日已经转的次数
       * </pre>
       *
       * <code>int32 times = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimes() {
        
        times_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ResRollClimbTowerTurn)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ResRollClimbTowerTurn)
    private static final com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResRollClimbTowerTurn>
        PARSER = new com.google.protobuf.AbstractParser<ResRollClimbTowerTurn>() {
      @java.lang.Override
      public ResRollClimbTowerTurn parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResRollClimbTowerTurn(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResRollClimbTowerTurn> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResRollClimbTowerTurn> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ResRollClimbTowerTurn getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqAcquireClimbTowerTurnOrBuilder extends
      // @@protoc_insertion_point(interface_extends:climbtower.ReqAcquireClimbTowerTurn)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ReqAcquireClimbTowerTurn' id='12' desc='请求转的领取' 
   * </pre>
   *
   * Protobuf type {@code climbtower.ReqAcquireClimbTowerTurn}
   */
  public static final class ReqAcquireClimbTowerTurn extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:climbtower.ReqAcquireClimbTowerTurn)
      ReqAcquireClimbTowerTurnOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqAcquireClimbTowerTurn.newBuilder() to construct.
    private ReqAcquireClimbTowerTurn(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqAcquireClimbTowerTurn() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqAcquireClimbTowerTurn();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqAcquireClimbTowerTurn(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqAcquireClimbTowerTurn_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqAcquireClimbTowerTurn_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn other = (com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn) obj;

      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqAcquireClimbTowerTurn' id='12' desc='请求转的领取' 
     * </pre>
     *
     * Protobuf type {@code climbtower.ReqAcquireClimbTowerTurn}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:climbtower.ReqAcquireClimbTowerTurn)
        com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurnOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqAcquireClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqAcquireClimbTowerTurn_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn.class, com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn.Builder.class);
      }

      // Construct using com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ClimbtowerProtos.internal_static_climbtower_ReqAcquireClimbTowerTurn_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn getDefaultInstanceForType() {
        return com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn build() {
        com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn buildPartial() {
        com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn result = new com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn(this);
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn) {
          return mergeFrom((com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn other) {
        if (other == com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:climbtower.ReqAcquireClimbTowerTurn)
    }

    // @@protoc_insertion_point(class_scope:climbtower.ReqAcquireClimbTowerTurn)
    private static final com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn();
    }

    public static com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqAcquireClimbTowerTurn>
        PARSER = new com.google.protobuf.AbstractParser<ReqAcquireClimbTowerTurn>() {
      @java.lang.Override
      public ReqAcquireClimbTowerTurn parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqAcquireClimbTowerTurn(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqAcquireClimbTowerTurn> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqAcquireClimbTowerTurn> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ClimbtowerProtos.ReqAcquireClimbTowerTurn getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ClimbTowerBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ClimbTowerBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ClimbTowerItemBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ClimbTowerItemBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ReqClimbTower_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ReqClimbTower_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ResClimbTower_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ResClimbTower_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ReqStartClimbTower_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ReqStartClimbTower_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ReqRollClimbTower_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ReqRollClimbTower_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ResRollClimbTower_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ResRollClimbTower_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ReqJumpClimbTower_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ReqJumpClimbTower_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ResJumpClimbTower_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ResJumpClimbTower_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ReqClimbTowerTurn_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ReqClimbTowerTurn_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ResClimbTowerTurn_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ResClimbTowerTurn_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ReqRollClimbTowerTurn_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ReqRollClimbTowerTurn_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ResRollClimbTowerTurn_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ResRollClimbTowerTurn_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_climbtower_ReqAcquireClimbTowerTurn_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_climbtower_ReqAcquireClimbTowerTurn_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020climbtower.proto\022\nclimbtower\"S\n\016ClimbT" +
      "owerBean\022\021\n\tfreeTimes\030\001 \001(\005\022\016\n\006times1\030\002 " +
      "\001(\005\022\016\n\006times2\030\003 \001(\005\022\016\n\006currId\030\004 \001(\005\"a\n\022C" +
      "limbTowerItemBean\022\n\n\002id\030\001 \001(\005\022\r\n\005index\030\002" +
      " \001(\005\022\016\n\006itemId\030\003 \001(\005\022\021\n\titemCount\030\004 \001(\005\022" +
      "\r\n\005isGet\030\005 \001(\010\"\017\n\rReqClimbTower\"F\n\rResCl" +
      "imbTower\022(\n\004base\030\001 \001(\0132\032.climbtower.Clim" +
      "bTowerBean\022\013\n\003ids\030\002 \003(\005\"\024\n\022ReqStartClimb" +
      "Tower\".\n\021ReqRollClimbTower\022\014\n\004type\030\001 \001(\005" +
      "\022\013\n\003val\030\002 \001(\005\"J\n\021ResRollClimbTower\022(\n\004ba" +
      "se\030\001 \001(\0132\032.climbtower.ClimbTowerBean\022\013\n\003" +
      "ret\030\002 \001(\005\"\023\n\021ReqJumpClimbTower\"?\n\021ResJum" +
      "pClimbTower\022\016\n\006currId\030\001 \001(\005\022\014\n\004type\030\002 \001(" +
      "\005\022\014\n\004time\030\003 \001(\005\"!\n\021ReqClimbTowerTurn\022\014\n\004" +
      "type\030\001 \001(\005\"^\n\021ResClimbTowerTurn\022,\n\004item\030" +
      "\001 \003(\0132\036.climbtower.ClimbTowerItemBean\022\r\n" +
      "\005times\030\002 \001(\005\022\014\n\004type\030\003 \001(\005\"%\n\025ReqRollCli" +
      "mbTowerTurn\022\014\n\004type\030\001 \001(\005\"A\n\025ResRollClim" +
      "bTowerTurn\022\014\n\004type\030\001 \001(\005\022\013\n\003ret\030\002 \001(\005\022\r\n" +
      "\005times\030\003 \001(\005\"(\n\030ReqAcquireClimbTowerTurn" +
      "\022\014\n\004type\030\001 \001(\005B&\n\022com.sh.game.protosB\020Cl" +
      "imbtowerProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_climbtower_ClimbTowerBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_climbtower_ClimbTowerBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ClimbTowerBean_descriptor,
        new java.lang.String[] { "FreeTimes", "Times1", "Times2", "CurrId", });
    internal_static_climbtower_ClimbTowerItemBean_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_climbtower_ClimbTowerItemBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ClimbTowerItemBean_descriptor,
        new java.lang.String[] { "Id", "Index", "ItemId", "ItemCount", "IsGet", });
    internal_static_climbtower_ReqClimbTower_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_climbtower_ReqClimbTower_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ReqClimbTower_descriptor,
        new java.lang.String[] { });
    internal_static_climbtower_ResClimbTower_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_climbtower_ResClimbTower_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ResClimbTower_descriptor,
        new java.lang.String[] { "Base", "Ids", });
    internal_static_climbtower_ReqStartClimbTower_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_climbtower_ReqStartClimbTower_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ReqStartClimbTower_descriptor,
        new java.lang.String[] { });
    internal_static_climbtower_ReqRollClimbTower_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_climbtower_ReqRollClimbTower_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ReqRollClimbTower_descriptor,
        new java.lang.String[] { "Type", "Val", });
    internal_static_climbtower_ResRollClimbTower_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_climbtower_ResRollClimbTower_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ResRollClimbTower_descriptor,
        new java.lang.String[] { "Base", "Ret", });
    internal_static_climbtower_ReqJumpClimbTower_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_climbtower_ReqJumpClimbTower_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ReqJumpClimbTower_descriptor,
        new java.lang.String[] { });
    internal_static_climbtower_ResJumpClimbTower_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_climbtower_ResJumpClimbTower_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ResJumpClimbTower_descriptor,
        new java.lang.String[] { "CurrId", "Type", "Time", });
    internal_static_climbtower_ReqClimbTowerTurn_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_climbtower_ReqClimbTowerTurn_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ReqClimbTowerTurn_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_climbtower_ResClimbTowerTurn_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_climbtower_ResClimbTowerTurn_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ResClimbTowerTurn_descriptor,
        new java.lang.String[] { "Item", "Times", "Type", });
    internal_static_climbtower_ReqRollClimbTowerTurn_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_climbtower_ReqRollClimbTowerTurn_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ReqRollClimbTowerTurn_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_climbtower_ResRollClimbTowerTurn_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_climbtower_ResRollClimbTowerTurn_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ResRollClimbTowerTurn_descriptor,
        new java.lang.String[] { "Type", "Ret", "Times", });
    internal_static_climbtower_ReqAcquireClimbTowerTurn_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_climbtower_ReqAcquireClimbTowerTurn_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_climbtower_ReqAcquireClimbTowerTurn_descriptor,
        new java.lang.String[] { "Type", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
