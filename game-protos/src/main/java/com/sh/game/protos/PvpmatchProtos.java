// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: pvpmatch.proto

package com.sh.game.protos;

public final class PvpmatchProtos {
  private PvpmatchProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MatchPlayerBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.MatchPlayerBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *玩家id
     * </pre>
     *
     * <code>int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     *玩家名
     * </pre>
     *
     * <code>string roleName = 2;</code>
     * @return The roleName.
     */
    java.lang.String getRoleName();
    /**
     * <pre>
     *玩家名
     * </pre>
     *
     * <code>string roleName = 2;</code>
     * @return The bytes for roleName.
     */
    com.google.protobuf.ByteString
        getRoleNameBytes();

    /**
     * <pre>
     *队伍匹配参数
     * </pre>
     *
     * <code>int32 fightPower = 3;</code>
     * @return The fightPower.
     */
    int getFightPower();

    /**
     * <pre>
     *性别
     * </pre>
     *
     * <code>int32 sex = 4;</code>
     * @return The sex.
     */
    int getSex();

    /**
     * <pre>
     *职业
     * </pre>
     *
     * <code>int32 career = 5;</code>
     * @return The career.
     */
    int getCareer();

    /**
     * <pre>
     *玩家所在服务器id
     * </pre>
     *
     * <code>int32 hostId = 6;</code>
     * @return The hostId.
     */
    int getHostId();

    /**
     * <pre>
     *玩家所在服务名称
     * </pre>
     *
     * <code>string serverName = 7;</code>
     * @return The serverName.
     */
    java.lang.String getServerName();
    /**
     * <pre>
     *玩家所在服务名称
     * </pre>
     *
     * <code>string serverName = 7;</code>
     * @return The bytes for serverName.
     */
    com.google.protobuf.ByteString
        getServerNameBytes();

    /**
     * <pre>
     *胜率
     * </pre>
     *
     * <code>int32 successRate = 8;</code>
     * @return The successRate.
     */
    int getSuccessRate();

    /**
     * <pre>
     *玩家段位参数
     * </pre>
     *
     * <code>int32 duanWei = 9;</code>
     * @return The duanWei.
     */
    int getDuanWei();
  }
  /**
   * Protobuf type {@code pvpmatch.MatchPlayerBean}
   */
  public static final class MatchPlayerBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.MatchPlayerBean)
      MatchPlayerBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MatchPlayerBean.newBuilder() to construct.
    private MatchPlayerBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MatchPlayerBean() {
      roleName_ = "";
      serverName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MatchPlayerBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MatchPlayerBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              roleName_ = s;
              break;
            }
            case 24: {

              fightPower_ = input.readInt32();
              break;
            }
            case 32: {

              sex_ = input.readInt32();
              break;
            }
            case 40: {

              career_ = input.readInt32();
              break;
            }
            case 48: {

              hostId_ = input.readInt32();
              break;
            }
            case 58: {
              java.lang.String s = input.readStringRequireUtf8();

              serverName_ = s;
              break;
            }
            case 64: {

              successRate_ = input.readInt32();
              break;
            }
            case 72: {

              duanWei_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchPlayerBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchPlayerBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.class, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder.class);
    }

    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     *玩家id
     * </pre>
     *
     * <code>int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int ROLENAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object roleName_;
    /**
     * <pre>
     *玩家名
     * </pre>
     *
     * <code>string roleName = 2;</code>
     * @return The roleName.
     */
    @java.lang.Override
    public java.lang.String getRoleName() {
      java.lang.Object ref = roleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        roleName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *玩家名
     * </pre>
     *
     * <code>string roleName = 2;</code>
     * @return The bytes for roleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getRoleNameBytes() {
      java.lang.Object ref = roleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        roleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FIGHTPOWER_FIELD_NUMBER = 3;
    private int fightPower_;
    /**
     * <pre>
     *队伍匹配参数
     * </pre>
     *
     * <code>int32 fightPower = 3;</code>
     * @return The fightPower.
     */
    @java.lang.Override
    public int getFightPower() {
      return fightPower_;
    }

    public static final int SEX_FIELD_NUMBER = 4;
    private int sex_;
    /**
     * <pre>
     *性别
     * </pre>
     *
     * <code>int32 sex = 4;</code>
     * @return The sex.
     */
    @java.lang.Override
    public int getSex() {
      return sex_;
    }

    public static final int CAREER_FIELD_NUMBER = 5;
    private int career_;
    /**
     * <pre>
     *职业
     * </pre>
     *
     * <code>int32 career = 5;</code>
     * @return The career.
     */
    @java.lang.Override
    public int getCareer() {
      return career_;
    }

    public static final int HOSTID_FIELD_NUMBER = 6;
    private int hostId_;
    /**
     * <pre>
     *玩家所在服务器id
     * </pre>
     *
     * <code>int32 hostId = 6;</code>
     * @return The hostId.
     */
    @java.lang.Override
    public int getHostId() {
      return hostId_;
    }

    public static final int SERVERNAME_FIELD_NUMBER = 7;
    private volatile java.lang.Object serverName_;
    /**
     * <pre>
     *玩家所在服务名称
     * </pre>
     *
     * <code>string serverName = 7;</code>
     * @return The serverName.
     */
    @java.lang.Override
    public java.lang.String getServerName() {
      java.lang.Object ref = serverName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        serverName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *玩家所在服务名称
     * </pre>
     *
     * <code>string serverName = 7;</code>
     * @return The bytes for serverName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getServerNameBytes() {
      java.lang.Object ref = serverName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        serverName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SUCCESSRATE_FIELD_NUMBER = 8;
    private int successRate_;
    /**
     * <pre>
     *胜率
     * </pre>
     *
     * <code>int32 successRate = 8;</code>
     * @return The successRate.
     */
    @java.lang.Override
    public int getSuccessRate() {
      return successRate_;
    }

    public static final int DUANWEI_FIELD_NUMBER = 9;
    private int duanWei_;
    /**
     * <pre>
     *玩家段位参数
     * </pre>
     *
     * <code>int32 duanWei = 9;</code>
     * @return The duanWei.
     */
    @java.lang.Override
    public int getDuanWei() {
      return duanWei_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (playerId_ != 0L) {
        output.writeInt64(1, playerId_);
      }
      if (!getRoleNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, roleName_);
      }
      if (fightPower_ != 0) {
        output.writeInt32(3, fightPower_);
      }
      if (sex_ != 0) {
        output.writeInt32(4, sex_);
      }
      if (career_ != 0) {
        output.writeInt32(5, career_);
      }
      if (hostId_ != 0) {
        output.writeInt32(6, hostId_);
      }
      if (!getServerNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, serverName_);
      }
      if (successRate_ != 0) {
        output.writeInt32(8, successRate_);
      }
      if (duanWei_ != 0) {
        output.writeInt32(9, duanWei_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (playerId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (!getRoleNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, roleName_);
      }
      if (fightPower_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, fightPower_);
      }
      if (sex_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, sex_);
      }
      if (career_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, career_);
      }
      if (hostId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, hostId_);
      }
      if (!getServerNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, serverName_);
      }
      if (successRate_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, successRate_);
      }
      if (duanWei_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, duanWei_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.MatchPlayerBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.MatchPlayerBean other = (com.sh.game.protos.PvpmatchProtos.MatchPlayerBean) obj;

      if (getPlayerId()
          != other.getPlayerId()) return false;
      if (!getRoleName()
          .equals(other.getRoleName())) return false;
      if (getFightPower()
          != other.getFightPower()) return false;
      if (getSex()
          != other.getSex()) return false;
      if (getCareer()
          != other.getCareer()) return false;
      if (getHostId()
          != other.getHostId()) return false;
      if (!getServerName()
          .equals(other.getServerName())) return false;
      if (getSuccessRate()
          != other.getSuccessRate()) return false;
      if (getDuanWei()
          != other.getDuanWei()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getPlayerId());
      hash = (37 * hash) + ROLENAME_FIELD_NUMBER;
      hash = (53 * hash) + getRoleName().hashCode();
      hash = (37 * hash) + FIGHTPOWER_FIELD_NUMBER;
      hash = (53 * hash) + getFightPower();
      hash = (37 * hash) + SEX_FIELD_NUMBER;
      hash = (53 * hash) + getSex();
      hash = (37 * hash) + CAREER_FIELD_NUMBER;
      hash = (53 * hash) + getCareer();
      hash = (37 * hash) + HOSTID_FIELD_NUMBER;
      hash = (53 * hash) + getHostId();
      hash = (37 * hash) + SERVERNAME_FIELD_NUMBER;
      hash = (53 * hash) + getServerName().hashCode();
      hash = (37 * hash) + SUCCESSRATE_FIELD_NUMBER;
      hash = (53 * hash) + getSuccessRate();
      hash = (37 * hash) + DUANWEI_FIELD_NUMBER;
      hash = (53 * hash) + getDuanWei();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.MatchPlayerBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code pvpmatch.MatchPlayerBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.MatchPlayerBean)
        com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchPlayerBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchPlayerBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.class, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;

        roleName_ = "";

        fightPower_ = 0;

        sex_ = 0;

        career_ = 0;

        hostId_ = 0;

        serverName_ = "";

        successRate_ = 0;

        duanWei_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchPlayerBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean build() {
        com.sh.game.protos.PvpmatchProtos.MatchPlayerBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean buildPartial() {
        com.sh.game.protos.PvpmatchProtos.MatchPlayerBean result = new com.sh.game.protos.PvpmatchProtos.MatchPlayerBean(this);
        result.playerId_ = playerId_;
        result.roleName_ = roleName_;
        result.fightPower_ = fightPower_;
        result.sex_ = sex_;
        result.career_ = career_;
        result.hostId_ = hostId_;
        result.serverName_ = serverName_;
        result.successRate_ = successRate_;
        result.duanWei_ = duanWei_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.MatchPlayerBean) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.MatchPlayerBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.MatchPlayerBean other) {
        if (other == com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.getDefaultInstance()) return this;
        if (other.getPlayerId() != 0L) {
          setPlayerId(other.getPlayerId());
        }
        if (!other.getRoleName().isEmpty()) {
          roleName_ = other.roleName_;
          onChanged();
        }
        if (other.getFightPower() != 0) {
          setFightPower(other.getFightPower());
        }
        if (other.getSex() != 0) {
          setSex(other.getSex());
        }
        if (other.getCareer() != 0) {
          setCareer(other.getCareer());
        }
        if (other.getHostId() != 0) {
          setHostId(other.getHostId());
        }
        if (!other.getServerName().isEmpty()) {
          serverName_ = other.serverName_;
          onChanged();
        }
        if (other.getSuccessRate() != 0) {
          setSuccessRate(other.getSuccessRate());
        }
        if (other.getDuanWei() != 0) {
          setDuanWei(other.getDuanWei());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.MatchPlayerBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.MatchPlayerBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long playerId_ ;
      /**
       * <pre>
       *玩家id
       * </pre>
       *
       * <code>int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       *玩家id
       * </pre>
       *
       * <code>int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家id
       * </pre>
       *
       * <code>int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object roleName_ = "";
      /**
       * <pre>
       *玩家名
       * </pre>
       *
       * <code>string roleName = 2;</code>
       * @return The roleName.
       */
      public java.lang.String getRoleName() {
        java.lang.Object ref = roleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          roleName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *玩家名
       * </pre>
       *
       * <code>string roleName = 2;</code>
       * @return The bytes for roleName.
       */
      public com.google.protobuf.ByteString
          getRoleNameBytes() {
        java.lang.Object ref = roleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          roleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *玩家名
       * </pre>
       *
       * <code>string roleName = 2;</code>
       * @param value The roleName to set.
       * @return This builder for chaining.
       */
      public Builder setRoleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        roleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家名
       * </pre>
       *
       * <code>string roleName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRoleName() {
        
        roleName_ = getDefaultInstance().getRoleName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家名
       * </pre>
       *
       * <code>string roleName = 2;</code>
       * @param value The bytes for roleName to set.
       * @return This builder for chaining.
       */
      public Builder setRoleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        roleName_ = value;
        onChanged();
        return this;
      }

      private int fightPower_ ;
      /**
       * <pre>
       *队伍匹配参数
       * </pre>
       *
       * <code>int32 fightPower = 3;</code>
       * @return The fightPower.
       */
      @java.lang.Override
      public int getFightPower() {
        return fightPower_;
      }
      /**
       * <pre>
       *队伍匹配参数
       * </pre>
       *
       * <code>int32 fightPower = 3;</code>
       * @param value The fightPower to set.
       * @return This builder for chaining.
       */
      public Builder setFightPower(int value) {
        
        fightPower_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *队伍匹配参数
       * </pre>
       *
       * <code>int32 fightPower = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearFightPower() {
        
        fightPower_ = 0;
        onChanged();
        return this;
      }

      private int sex_ ;
      /**
       * <pre>
       *性别
       * </pre>
       *
       * <code>int32 sex = 4;</code>
       * @return The sex.
       */
      @java.lang.Override
      public int getSex() {
        return sex_;
      }
      /**
       * <pre>
       *性别
       * </pre>
       *
       * <code>int32 sex = 4;</code>
       * @param value The sex to set.
       * @return This builder for chaining.
       */
      public Builder setSex(int value) {
        
        sex_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *性别
       * </pre>
       *
       * <code>int32 sex = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSex() {
        
        sex_ = 0;
        onChanged();
        return this;
      }

      private int career_ ;
      /**
       * <pre>
       *职业
       * </pre>
       *
       * <code>int32 career = 5;</code>
       * @return The career.
       */
      @java.lang.Override
      public int getCareer() {
        return career_;
      }
      /**
       * <pre>
       *职业
       * </pre>
       *
       * <code>int32 career = 5;</code>
       * @param value The career to set.
       * @return This builder for chaining.
       */
      public Builder setCareer(int value) {
        
        career_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *职业
       * </pre>
       *
       * <code>int32 career = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCareer() {
        
        career_ = 0;
        onChanged();
        return this;
      }

      private int hostId_ ;
      /**
       * <pre>
       *玩家所在服务器id
       * </pre>
       *
       * <code>int32 hostId = 6;</code>
       * @return The hostId.
       */
      @java.lang.Override
      public int getHostId() {
        return hostId_;
      }
      /**
       * <pre>
       *玩家所在服务器id
       * </pre>
       *
       * <code>int32 hostId = 6;</code>
       * @param value The hostId to set.
       * @return This builder for chaining.
       */
      public Builder setHostId(int value) {
        
        hostId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家所在服务器id
       * </pre>
       *
       * <code>int32 hostId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearHostId() {
        
        hostId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object serverName_ = "";
      /**
       * <pre>
       *玩家所在服务名称
       * </pre>
       *
       * <code>string serverName = 7;</code>
       * @return The serverName.
       */
      public java.lang.String getServerName() {
        java.lang.Object ref = serverName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          serverName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *玩家所在服务名称
       * </pre>
       *
       * <code>string serverName = 7;</code>
       * @return The bytes for serverName.
       */
      public com.google.protobuf.ByteString
          getServerNameBytes() {
        java.lang.Object ref = serverName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          serverName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *玩家所在服务名称
       * </pre>
       *
       * <code>string serverName = 7;</code>
       * @param value The serverName to set.
       * @return This builder for chaining.
       */
      public Builder setServerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        serverName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家所在服务名称
       * </pre>
       *
       * <code>string serverName = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearServerName() {
        
        serverName_ = getDefaultInstance().getServerName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家所在服务名称
       * </pre>
       *
       * <code>string serverName = 7;</code>
       * @param value The bytes for serverName to set.
       * @return This builder for chaining.
       */
      public Builder setServerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        serverName_ = value;
        onChanged();
        return this;
      }

      private int successRate_ ;
      /**
       * <pre>
       *胜率
       * </pre>
       *
       * <code>int32 successRate = 8;</code>
       * @return The successRate.
       */
      @java.lang.Override
      public int getSuccessRate() {
        return successRate_;
      }
      /**
       * <pre>
       *胜率
       * </pre>
       *
       * <code>int32 successRate = 8;</code>
       * @param value The successRate to set.
       * @return This builder for chaining.
       */
      public Builder setSuccessRate(int value) {
        
        successRate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *胜率
       * </pre>
       *
       * <code>int32 successRate = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuccessRate() {
        
        successRate_ = 0;
        onChanged();
        return this;
      }

      private int duanWei_ ;
      /**
       * <pre>
       *玩家段位参数
       * </pre>
       *
       * <code>int32 duanWei = 9;</code>
       * @return The duanWei.
       */
      @java.lang.Override
      public int getDuanWei() {
        return duanWei_;
      }
      /**
       * <pre>
       *玩家段位参数
       * </pre>
       *
       * <code>int32 duanWei = 9;</code>
       * @param value The duanWei to set.
       * @return This builder for chaining.
       */
      public Builder setDuanWei(int value) {
        
        duanWei_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家段位参数
       * </pre>
       *
       * <code>int32 duanWei = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearDuanWei() {
        
        duanWei_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.MatchPlayerBean)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.MatchPlayerBean)
    private static final com.sh.game.protos.PvpmatchProtos.MatchPlayerBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.MatchPlayerBean();
    }

    public static com.sh.game.protos.PvpmatchProtos.MatchPlayerBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MatchPlayerBean>
        PARSER = new com.google.protobuf.AbstractParser<MatchPlayerBean>() {
      @java.lang.Override
      public MatchPlayerBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MatchPlayerBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MatchPlayerBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MatchPlayerBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MatchTeamBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.MatchTeamBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *队伍id
     * </pre>
     *
     * <code>int64 teamId = 1;</code>
     * @return The teamId.
     */
    long getTeamId();

    /**
     * <pre>
     *队伍匹配参数总和
     * </pre>
     *
     * <code>int32 fightPower = 2;</code>
     * @return The fightPower.
     */
    int getFightPower();

    /**
     * <pre>
     *pvp类型
     * </pre>
     *
     * <code>int32 pvpType = 3;</code>
     * @return The pvpType.
     */
    int getPvpType();

    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> 
        getPlayerList();
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    com.sh.game.protos.PvpmatchProtos.MatchPlayerBean getPlayer(int index);
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    int getPlayerCount();
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    java.util.List<? extends com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> 
        getPlayerOrBuilderList();
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder getPlayerOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code pvpmatch.MatchTeamBean}
   */
  public static final class MatchTeamBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.MatchTeamBean)
      MatchTeamBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MatchTeamBean.newBuilder() to construct.
    private MatchTeamBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MatchTeamBean() {
      player_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MatchTeamBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MatchTeamBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              teamId_ = input.readInt64();
              break;
            }
            case 16: {

              fightPower_ = input.readInt32();
              break;
            }
            case 24: {

              pvpType_ = input.readInt32();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                player_ = new java.util.ArrayList<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              player_.add(
                  input.readMessage(com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          player_ = java.util.Collections.unmodifiableList(player_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchTeamBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchTeamBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.MatchTeamBean.class, com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder.class);
    }

    public static final int TEAMID_FIELD_NUMBER = 1;
    private long teamId_;
    /**
     * <pre>
     *队伍id
     * </pre>
     *
     * <code>int64 teamId = 1;</code>
     * @return The teamId.
     */
    @java.lang.Override
    public long getTeamId() {
      return teamId_;
    }

    public static final int FIGHTPOWER_FIELD_NUMBER = 2;
    private int fightPower_;
    /**
     * <pre>
     *队伍匹配参数总和
     * </pre>
     *
     * <code>int32 fightPower = 2;</code>
     * @return The fightPower.
     */
    @java.lang.Override
    public int getFightPower() {
      return fightPower_;
    }

    public static final int PVPTYPE_FIELD_NUMBER = 3;
    private int pvpType_;
    /**
     * <pre>
     *pvp类型
     * </pre>
     *
     * <code>int32 pvpType = 3;</code>
     * @return The pvpType.
     */
    @java.lang.Override
    public int getPvpType() {
      return pvpType_;
    }

    public static final int PLAYER_FIELD_NUMBER = 4;
    private java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> player_;
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> getPlayerList() {
      return player_;
    }
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> 
        getPlayerOrBuilderList() {
      return player_;
    }
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public int getPlayerCount() {
      return player_.size();
    }
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean getPlayer(int index) {
      return player_.get(index);
    }
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder getPlayerOrBuilder(
        int index) {
      return player_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (teamId_ != 0L) {
        output.writeInt64(1, teamId_);
      }
      if (fightPower_ != 0) {
        output.writeInt32(2, fightPower_);
      }
      if (pvpType_ != 0) {
        output.writeInt32(3, pvpType_);
      }
      for (int i = 0; i < player_.size(); i++) {
        output.writeMessage(4, player_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (teamId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, teamId_);
      }
      if (fightPower_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, fightPower_);
      }
      if (pvpType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, pvpType_);
      }
      for (int i = 0; i < player_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, player_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.MatchTeamBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.MatchTeamBean other = (com.sh.game.protos.PvpmatchProtos.MatchTeamBean) obj;

      if (getTeamId()
          != other.getTeamId()) return false;
      if (getFightPower()
          != other.getFightPower()) return false;
      if (getPvpType()
          != other.getPvpType()) return false;
      if (!getPlayerList()
          .equals(other.getPlayerList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TEAMID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTeamId());
      hash = (37 * hash) + FIGHTPOWER_FIELD_NUMBER;
      hash = (53 * hash) + getFightPower();
      hash = (37 * hash) + PVPTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getPvpType();
      if (getPlayerCount() > 0) {
        hash = (37 * hash) + PLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.MatchTeamBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code pvpmatch.MatchTeamBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.MatchTeamBean)
        com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchTeamBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchTeamBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.MatchTeamBean.class, com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.MatchTeamBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPlayerFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        teamId_ = 0L;

        fightPower_ = 0;

        pvpType_ = 0;

        if (playerBuilder_ == null) {
          player_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          playerBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchTeamBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.MatchTeamBean getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.MatchTeamBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.MatchTeamBean build() {
        com.sh.game.protos.PvpmatchProtos.MatchTeamBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.MatchTeamBean buildPartial() {
        com.sh.game.protos.PvpmatchProtos.MatchTeamBean result = new com.sh.game.protos.PvpmatchProtos.MatchTeamBean(this);
        int from_bitField0_ = bitField0_;
        result.teamId_ = teamId_;
        result.fightPower_ = fightPower_;
        result.pvpType_ = pvpType_;
        if (playerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            player_ = java.util.Collections.unmodifiableList(player_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.player_ = player_;
        } else {
          result.player_ = playerBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.MatchTeamBean) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.MatchTeamBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.MatchTeamBean other) {
        if (other == com.sh.game.protos.PvpmatchProtos.MatchTeamBean.getDefaultInstance()) return this;
        if (other.getTeamId() != 0L) {
          setTeamId(other.getTeamId());
        }
        if (other.getFightPower() != 0) {
          setFightPower(other.getFightPower());
        }
        if (other.getPvpType() != 0) {
          setPvpType(other.getPvpType());
        }
        if (playerBuilder_ == null) {
          if (!other.player_.isEmpty()) {
            if (player_.isEmpty()) {
              player_ = other.player_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePlayerIsMutable();
              player_.addAll(other.player_);
            }
            onChanged();
          }
        } else {
          if (!other.player_.isEmpty()) {
            if (playerBuilder_.isEmpty()) {
              playerBuilder_.dispose();
              playerBuilder_ = null;
              player_ = other.player_;
              bitField0_ = (bitField0_ & ~0x00000001);
              playerBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPlayerFieldBuilder() : null;
            } else {
              playerBuilder_.addAllMessages(other.player_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.MatchTeamBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.MatchTeamBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long teamId_ ;
      /**
       * <pre>
       *队伍id
       * </pre>
       *
       * <code>int64 teamId = 1;</code>
       * @return The teamId.
       */
      @java.lang.Override
      public long getTeamId() {
        return teamId_;
      }
      /**
       * <pre>
       *队伍id
       * </pre>
       *
       * <code>int64 teamId = 1;</code>
       * @param value The teamId to set.
       * @return This builder for chaining.
       */
      public Builder setTeamId(long value) {
        
        teamId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *队伍id
       * </pre>
       *
       * <code>int64 teamId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTeamId() {
        
        teamId_ = 0L;
        onChanged();
        return this;
      }

      private int fightPower_ ;
      /**
       * <pre>
       *队伍匹配参数总和
       * </pre>
       *
       * <code>int32 fightPower = 2;</code>
       * @return The fightPower.
       */
      @java.lang.Override
      public int getFightPower() {
        return fightPower_;
      }
      /**
       * <pre>
       *队伍匹配参数总和
       * </pre>
       *
       * <code>int32 fightPower = 2;</code>
       * @param value The fightPower to set.
       * @return This builder for chaining.
       */
      public Builder setFightPower(int value) {
        
        fightPower_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *队伍匹配参数总和
       * </pre>
       *
       * <code>int32 fightPower = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFightPower() {
        
        fightPower_ = 0;
        onChanged();
        return this;
      }

      private int pvpType_ ;
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 3;</code>
       * @return The pvpType.
       */
      @java.lang.Override
      public int getPvpType() {
        return pvpType_;
      }
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 3;</code>
       * @param value The pvpType to set.
       * @return This builder for chaining.
       */
      public Builder setPvpType(int value) {
        
        pvpType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPvpType() {
        
        pvpType_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> player_ =
        java.util.Collections.emptyList();
      private void ensurePlayerIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          player_ = new java.util.ArrayList<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean>(player_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchPlayerBean, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> playerBuilder_;

      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> getPlayerList() {
        if (playerBuilder_ == null) {
          return java.util.Collections.unmodifiableList(player_);
        } else {
          return playerBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public int getPlayerCount() {
        if (playerBuilder_ == null) {
          return player_.size();
        } else {
          return playerBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean getPlayer(int index) {
        if (playerBuilder_ == null) {
          return player_.get(index);
        } else {
          return playerBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder setPlayer(
          int index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerIsMutable();
          player_.set(index, value);
          onChanged();
        } else {
          playerBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder setPlayer(
          int index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder builderForValue) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.set(index, builderForValue.build());
          onChanged();
        } else {
          playerBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addPlayer(com.sh.game.protos.PvpmatchProtos.MatchPlayerBean value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerIsMutable();
          player_.add(value);
          onChanged();
        } else {
          playerBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addPlayer(
          int index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerIsMutable();
          player_.add(index, value);
          onChanged();
        } else {
          playerBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addPlayer(
          com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder builderForValue) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.add(builderForValue.build());
          onChanged();
        } else {
          playerBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addPlayer(
          int index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder builderForValue) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.add(index, builderForValue.build());
          onChanged();
        } else {
          playerBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addAllPlayer(
          java.lang.Iterable<? extends com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> values) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, player_);
          onChanged();
        } else {
          playerBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder clearPlayer() {
        if (playerBuilder_ == null) {
          player_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          playerBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder removePlayer(int index) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.remove(index);
          onChanged();
        } else {
          playerBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder getPlayerBuilder(
          int index) {
        return getPlayerFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder getPlayerOrBuilder(
          int index) {
        if (playerBuilder_ == null) {
          return player_.get(index);  } else {
          return playerBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public java.util.List<? extends com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> 
           getPlayerOrBuilderList() {
        if (playerBuilder_ != null) {
          return playerBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(player_);
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder addPlayerBuilder() {
        return getPlayerFieldBuilder().addBuilder(
            com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.getDefaultInstance());
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder addPlayerBuilder(
          int index) {
        return getPlayerFieldBuilder().addBuilder(
            index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.getDefaultInstance());
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder> 
           getPlayerBuilderList() {
        return getPlayerFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchPlayerBean, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> 
          getPlayerFieldBuilder() {
        if (playerBuilder_ == null) {
          playerBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.PvpmatchProtos.MatchPlayerBean, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder>(
                  player_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          player_ = null;
        }
        return playerBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.MatchTeamBean)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.MatchTeamBean)
    private static final com.sh.game.protos.PvpmatchProtos.MatchTeamBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.MatchTeamBean();
    }

    public static com.sh.game.protos.PvpmatchProtos.MatchTeamBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MatchTeamBean>
        PARSER = new com.google.protobuf.AbstractParser<MatchTeamBean>() {
      @java.lang.Override
      public MatchTeamBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MatchTeamBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MatchTeamBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MatchTeamBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchTeamBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MatchRankBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.MatchRankBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *排名
     * </pre>
     *
     * <code>int32 rank = 1;</code>
     * @return The rank.
     */
    int getRank();

    /**
     * <pre>
     *玩家id
     * </pre>
     *
     * <code>int64 rid = 2;</code>
     * @return The rid.
     */
    long getRid();

    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <pre>
     *玩家归属服务器
     * </pre>
     *
     * <code>int32 hostId = 4;</code>
     * @return The hostId.
     */
    int getHostId();

    /**
     * <pre>
     *修罗段位配置id
     * </pre>
     *
     * <code>int32 duanWeiCfgId = 5;</code>
     * @return The duanWeiCfgId.
     */
    int getDuanWeiCfgId();
  }
  /**
   * Protobuf type {@code pvpmatch.MatchRankBean}
   */
  public static final class MatchRankBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.MatchRankBean)
      MatchRankBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MatchRankBean.newBuilder() to construct.
    private MatchRankBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MatchRankBean() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MatchRankBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MatchRankBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              rank_ = input.readInt32();
              break;
            }
            case 16: {

              rid_ = input.readInt64();
              break;
            }
            case 26: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 32: {

              hostId_ = input.readInt32();
              break;
            }
            case 40: {

              duanWeiCfgId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchRankBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchRankBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.MatchRankBean.class, com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder.class);
    }

    public static final int RANK_FIELD_NUMBER = 1;
    private int rank_;
    /**
     * <pre>
     *排名
     * </pre>
     *
     * <code>int32 rank = 1;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }

    public static final int RID_FIELD_NUMBER = 2;
    private long rid_;
    /**
     * <pre>
     *玩家id
     * </pre>
     *
     * <code>int64 rid = 2;</code>
     * @return The rid.
     */
    @java.lang.Override
    public long getRid() {
      return rid_;
    }

    public static final int NAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *名字
     * </pre>
     *
     * <code>string name = 3;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int HOSTID_FIELD_NUMBER = 4;
    private int hostId_;
    /**
     * <pre>
     *玩家归属服务器
     * </pre>
     *
     * <code>int32 hostId = 4;</code>
     * @return The hostId.
     */
    @java.lang.Override
    public int getHostId() {
      return hostId_;
    }

    public static final int DUANWEICFGID_FIELD_NUMBER = 5;
    private int duanWeiCfgId_;
    /**
     * <pre>
     *修罗段位配置id
     * </pre>
     *
     * <code>int32 duanWeiCfgId = 5;</code>
     * @return The duanWeiCfgId.
     */
    @java.lang.Override
    public int getDuanWeiCfgId() {
      return duanWeiCfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (rank_ != 0) {
        output.writeInt32(1, rank_);
      }
      if (rid_ != 0L) {
        output.writeInt64(2, rid_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
      }
      if (hostId_ != 0) {
        output.writeInt32(4, hostId_);
      }
      if (duanWeiCfgId_ != 0) {
        output.writeInt32(5, duanWeiCfgId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rank_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rank_);
      }
      if (rid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, rid_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
      }
      if (hostId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, hostId_);
      }
      if (duanWeiCfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, duanWeiCfgId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.MatchRankBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.MatchRankBean other = (com.sh.game.protos.PvpmatchProtos.MatchRankBean) obj;

      if (getRank()
          != other.getRank()) return false;
      if (getRid()
          != other.getRid()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (getHostId()
          != other.getHostId()) return false;
      if (getDuanWeiCfgId()
          != other.getDuanWeiCfgId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RANK_FIELD_NUMBER;
      hash = (53 * hash) + getRank();
      hash = (37 * hash) + RID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRid());
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + HOSTID_FIELD_NUMBER;
      hash = (53 * hash) + getHostId();
      hash = (37 * hash) + DUANWEICFGID_FIELD_NUMBER;
      hash = (53 * hash) + getDuanWeiCfgId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.MatchRankBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code pvpmatch.MatchRankBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.MatchRankBean)
        com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchRankBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchRankBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.MatchRankBean.class, com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.MatchRankBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        rank_ = 0;

        rid_ = 0L;

        name_ = "";

        hostId_ = 0;

        duanWeiCfgId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_MatchRankBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.MatchRankBean getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.MatchRankBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.MatchRankBean build() {
        com.sh.game.protos.PvpmatchProtos.MatchRankBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.MatchRankBean buildPartial() {
        com.sh.game.protos.PvpmatchProtos.MatchRankBean result = new com.sh.game.protos.PvpmatchProtos.MatchRankBean(this);
        result.rank_ = rank_;
        result.rid_ = rid_;
        result.name_ = name_;
        result.hostId_ = hostId_;
        result.duanWeiCfgId_ = duanWeiCfgId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.MatchRankBean) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.MatchRankBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.MatchRankBean other) {
        if (other == com.sh.game.protos.PvpmatchProtos.MatchRankBean.getDefaultInstance()) return this;
        if (other.getRank() != 0) {
          setRank(other.getRank());
        }
        if (other.getRid() != 0L) {
          setRid(other.getRid());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.getHostId() != 0) {
          setHostId(other.getHostId());
        }
        if (other.getDuanWeiCfgId() != 0) {
          setDuanWeiCfgId(other.getDuanWeiCfgId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.MatchRankBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.MatchRankBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int rank_ ;
      /**
       * <pre>
       *排名
       * </pre>
       *
       * <code>int32 rank = 1;</code>
       * @return The rank.
       */
      @java.lang.Override
      public int getRank() {
        return rank_;
      }
      /**
       * <pre>
       *排名
       * </pre>
       *
       * <code>int32 rank = 1;</code>
       * @param value The rank to set.
       * @return This builder for chaining.
       */
      public Builder setRank(int value) {
        
        rank_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *排名
       * </pre>
       *
       * <code>int32 rank = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRank() {
        
        rank_ = 0;
        onChanged();
        return this;
      }

      private long rid_ ;
      /**
       * <pre>
       *玩家id
       * </pre>
       *
       * <code>int64 rid = 2;</code>
       * @return The rid.
       */
      @java.lang.Override
      public long getRid() {
        return rid_;
      }
      /**
       * <pre>
       *玩家id
       * </pre>
       *
       * <code>int64 rid = 2;</code>
       * @param value The rid to set.
       * @return This builder for chaining.
       */
      public Builder setRid(long value) {
        
        rid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家id
       * </pre>
       *
       * <code>int64 rid = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRid() {
        
        rid_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 3;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 3;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 3;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *名字
       * </pre>
       *
       * <code>string name = 3;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private int hostId_ ;
      /**
       * <pre>
       *玩家归属服务器
       * </pre>
       *
       * <code>int32 hostId = 4;</code>
       * @return The hostId.
       */
      @java.lang.Override
      public int getHostId() {
        return hostId_;
      }
      /**
       * <pre>
       *玩家归属服务器
       * </pre>
       *
       * <code>int32 hostId = 4;</code>
       * @param value The hostId to set.
       * @return This builder for chaining.
       */
      public Builder setHostId(int value) {
        
        hostId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家归属服务器
       * </pre>
       *
       * <code>int32 hostId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHostId() {
        
        hostId_ = 0;
        onChanged();
        return this;
      }

      private int duanWeiCfgId_ ;
      /**
       * <pre>
       *修罗段位配置id
       * </pre>
       *
       * <code>int32 duanWeiCfgId = 5;</code>
       * @return The duanWeiCfgId.
       */
      @java.lang.Override
      public int getDuanWeiCfgId() {
        return duanWeiCfgId_;
      }
      /**
       * <pre>
       *修罗段位配置id
       * </pre>
       *
       * <code>int32 duanWeiCfgId = 5;</code>
       * @param value The duanWeiCfgId to set.
       * @return This builder for chaining.
       */
      public Builder setDuanWeiCfgId(int value) {
        
        duanWeiCfgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *修罗段位配置id
       * </pre>
       *
       * <code>int32 duanWeiCfgId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDuanWeiCfgId() {
        
        duanWeiCfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.MatchRankBean)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.MatchRankBean)
    private static final com.sh.game.protos.PvpmatchProtos.MatchRankBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.MatchRankBean();
    }

    public static com.sh.game.protos.PvpmatchProtos.MatchRankBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MatchRankBean>
        PARSER = new com.google.protobuf.AbstractParser<MatchRankBean>() {
      @java.lang.Override
      public MatchRankBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MatchRankBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MatchRankBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MatchRankBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchRankBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMatchInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.ResMatchInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *队伍id
     * </pre>
     *
     * <code>int64 teamId = 1;</code>
     * @return The teamId.
     */
    long getTeamId();

    /**
     * <pre>
     *队伍匹配参数总和
     * </pre>
     *
     * <code>int32 fightPower = 2;</code>
     * @return The fightPower.
     */
    int getFightPower();

    /**
     * <pre>
     *pvp类型
     * </pre>
     *
     * <code>int32 pvpType = 3;</code>
     * @return The pvpType.
     */
    int getPvpType();

    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> 
        getPlayerList();
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    com.sh.game.protos.PvpmatchProtos.MatchPlayerBean getPlayer(int index);
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    int getPlayerCount();
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    java.util.List<? extends com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> 
        getPlayerOrBuilderList();
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder getPlayerOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResMatchInfo' id='40' desc='返回匹配队伍信息' 
   * </pre>
   *
   * Protobuf type {@code pvpmatch.ResMatchInfo}
   */
  public static final class ResMatchInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.ResMatchInfo)
      ResMatchInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMatchInfo.newBuilder() to construct.
    private ResMatchInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMatchInfo() {
      player_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMatchInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMatchInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              teamId_ = input.readInt64();
              break;
            }
            case 16: {

              fightPower_ = input.readInt32();
              break;
            }
            case 24: {

              pvpType_ = input.readInt32();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                player_ = new java.util.ArrayList<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              player_.add(
                  input.readMessage(com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          player_ = java.util.Collections.unmodifiableList(player_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.ResMatchInfo.class, com.sh.game.protos.PvpmatchProtos.ResMatchInfo.Builder.class);
    }

    public static final int TEAMID_FIELD_NUMBER = 1;
    private long teamId_;
    /**
     * <pre>
     *队伍id
     * </pre>
     *
     * <code>int64 teamId = 1;</code>
     * @return The teamId.
     */
    @java.lang.Override
    public long getTeamId() {
      return teamId_;
    }

    public static final int FIGHTPOWER_FIELD_NUMBER = 2;
    private int fightPower_;
    /**
     * <pre>
     *队伍匹配参数总和
     * </pre>
     *
     * <code>int32 fightPower = 2;</code>
     * @return The fightPower.
     */
    @java.lang.Override
    public int getFightPower() {
      return fightPower_;
    }

    public static final int PVPTYPE_FIELD_NUMBER = 3;
    private int pvpType_;
    /**
     * <pre>
     *pvp类型
     * </pre>
     *
     * <code>int32 pvpType = 3;</code>
     * @return The pvpType.
     */
    @java.lang.Override
    public int getPvpType() {
      return pvpType_;
    }

    public static final int PLAYER_FIELD_NUMBER = 4;
    private java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> player_;
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> getPlayerList() {
      return player_;
    }
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> 
        getPlayerOrBuilderList() {
      return player_;
    }
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public int getPlayerCount() {
      return player_.size();
    }
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean getPlayer(int index) {
      return player_.get(index);
    }
    /**
     * <pre>
     *队员
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder getPlayerOrBuilder(
        int index) {
      return player_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (teamId_ != 0L) {
        output.writeInt64(1, teamId_);
      }
      if (fightPower_ != 0) {
        output.writeInt32(2, fightPower_);
      }
      if (pvpType_ != 0) {
        output.writeInt32(3, pvpType_);
      }
      for (int i = 0; i < player_.size(); i++) {
        output.writeMessage(4, player_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (teamId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, teamId_);
      }
      if (fightPower_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, fightPower_);
      }
      if (pvpType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, pvpType_);
      }
      for (int i = 0; i < player_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, player_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.ResMatchInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.ResMatchInfo other = (com.sh.game.protos.PvpmatchProtos.ResMatchInfo) obj;

      if (getTeamId()
          != other.getTeamId()) return false;
      if (getFightPower()
          != other.getFightPower()) return false;
      if (getPvpType()
          != other.getPvpType()) return false;
      if (!getPlayerList()
          .equals(other.getPlayerList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TEAMID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTeamId());
      hash = (37 * hash) + FIGHTPOWER_FIELD_NUMBER;
      hash = (53 * hash) + getFightPower();
      hash = (37 * hash) + PVPTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getPvpType();
      if (getPlayerCount() > 0) {
        hash = (37 * hash) + PLAYER_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.ResMatchInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMatchInfo' id='40' desc='返回匹配队伍信息' 
     * </pre>
     *
     * Protobuf type {@code pvpmatch.ResMatchInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.ResMatchInfo)
        com.sh.game.protos.PvpmatchProtos.ResMatchInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.ResMatchInfo.class, com.sh.game.protos.PvpmatchProtos.ResMatchInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.ResMatchInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPlayerFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        teamId_ = 0L;

        fightPower_ = 0;

        pvpType_ = 0;

        if (playerBuilder_ == null) {
          player_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          playerBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchInfo getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.ResMatchInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchInfo build() {
        com.sh.game.protos.PvpmatchProtos.ResMatchInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchInfo buildPartial() {
        com.sh.game.protos.PvpmatchProtos.ResMatchInfo result = new com.sh.game.protos.PvpmatchProtos.ResMatchInfo(this);
        int from_bitField0_ = bitField0_;
        result.teamId_ = teamId_;
        result.fightPower_ = fightPower_;
        result.pvpType_ = pvpType_;
        if (playerBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            player_ = java.util.Collections.unmodifiableList(player_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.player_ = player_;
        } else {
          result.player_ = playerBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.ResMatchInfo) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.ResMatchInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.ResMatchInfo other) {
        if (other == com.sh.game.protos.PvpmatchProtos.ResMatchInfo.getDefaultInstance()) return this;
        if (other.getTeamId() != 0L) {
          setTeamId(other.getTeamId());
        }
        if (other.getFightPower() != 0) {
          setFightPower(other.getFightPower());
        }
        if (other.getPvpType() != 0) {
          setPvpType(other.getPvpType());
        }
        if (playerBuilder_ == null) {
          if (!other.player_.isEmpty()) {
            if (player_.isEmpty()) {
              player_ = other.player_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePlayerIsMutable();
              player_.addAll(other.player_);
            }
            onChanged();
          }
        } else {
          if (!other.player_.isEmpty()) {
            if (playerBuilder_.isEmpty()) {
              playerBuilder_.dispose();
              playerBuilder_ = null;
              player_ = other.player_;
              bitField0_ = (bitField0_ & ~0x00000001);
              playerBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPlayerFieldBuilder() : null;
            } else {
              playerBuilder_.addAllMessages(other.player_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.ResMatchInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.ResMatchInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long teamId_ ;
      /**
       * <pre>
       *队伍id
       * </pre>
       *
       * <code>int64 teamId = 1;</code>
       * @return The teamId.
       */
      @java.lang.Override
      public long getTeamId() {
        return teamId_;
      }
      /**
       * <pre>
       *队伍id
       * </pre>
       *
       * <code>int64 teamId = 1;</code>
       * @param value The teamId to set.
       * @return This builder for chaining.
       */
      public Builder setTeamId(long value) {
        
        teamId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *队伍id
       * </pre>
       *
       * <code>int64 teamId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTeamId() {
        
        teamId_ = 0L;
        onChanged();
        return this;
      }

      private int fightPower_ ;
      /**
       * <pre>
       *队伍匹配参数总和
       * </pre>
       *
       * <code>int32 fightPower = 2;</code>
       * @return The fightPower.
       */
      @java.lang.Override
      public int getFightPower() {
        return fightPower_;
      }
      /**
       * <pre>
       *队伍匹配参数总和
       * </pre>
       *
       * <code>int32 fightPower = 2;</code>
       * @param value The fightPower to set.
       * @return This builder for chaining.
       */
      public Builder setFightPower(int value) {
        
        fightPower_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *队伍匹配参数总和
       * </pre>
       *
       * <code>int32 fightPower = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFightPower() {
        
        fightPower_ = 0;
        onChanged();
        return this;
      }

      private int pvpType_ ;
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 3;</code>
       * @return The pvpType.
       */
      @java.lang.Override
      public int getPvpType() {
        return pvpType_;
      }
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 3;</code>
       * @param value The pvpType to set.
       * @return This builder for chaining.
       */
      public Builder setPvpType(int value) {
        
        pvpType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPvpType() {
        
        pvpType_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> player_ =
        java.util.Collections.emptyList();
      private void ensurePlayerIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          player_ = new java.util.ArrayList<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean>(player_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchPlayerBean, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> playerBuilder_;

      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> getPlayerList() {
        if (playerBuilder_ == null) {
          return java.util.Collections.unmodifiableList(player_);
        } else {
          return playerBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public int getPlayerCount() {
        if (playerBuilder_ == null) {
          return player_.size();
        } else {
          return playerBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean getPlayer(int index) {
        if (playerBuilder_ == null) {
          return player_.get(index);
        } else {
          return playerBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder setPlayer(
          int index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerIsMutable();
          player_.set(index, value);
          onChanged();
        } else {
          playerBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder setPlayer(
          int index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder builderForValue) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.set(index, builderForValue.build());
          onChanged();
        } else {
          playerBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addPlayer(com.sh.game.protos.PvpmatchProtos.MatchPlayerBean value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerIsMutable();
          player_.add(value);
          onChanged();
        } else {
          playerBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addPlayer(
          int index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerIsMutable();
          player_.add(index, value);
          onChanged();
        } else {
          playerBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addPlayer(
          com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder builderForValue) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.add(builderForValue.build());
          onChanged();
        } else {
          playerBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addPlayer(
          int index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder builderForValue) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.add(index, builderForValue.build());
          onChanged();
        } else {
          playerBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder addAllPlayer(
          java.lang.Iterable<? extends com.sh.game.protos.PvpmatchProtos.MatchPlayerBean> values) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, player_);
          onChanged();
        } else {
          playerBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder clearPlayer() {
        if (playerBuilder_ == null) {
          player_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          playerBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public Builder removePlayer(int index) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.remove(index);
          onChanged();
        } else {
          playerBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder getPlayerBuilder(
          int index) {
        return getPlayerFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder getPlayerOrBuilder(
          int index) {
        if (playerBuilder_ == null) {
          return player_.get(index);  } else {
          return playerBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public java.util.List<? extends com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> 
           getPlayerOrBuilderList() {
        if (playerBuilder_ != null) {
          return playerBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(player_);
        }
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder addPlayerBuilder() {
        return getPlayerFieldBuilder().addBuilder(
            com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.getDefaultInstance());
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder addPlayerBuilder(
          int index) {
        return getPlayerFieldBuilder().addBuilder(
            index, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.getDefaultInstance());
      }
      /**
       * <pre>
       *队员
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchPlayerBean player = 4;</code>
       */
      public java.util.List<com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder> 
           getPlayerBuilderList() {
        return getPlayerFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchPlayerBean, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder> 
          getPlayerFieldBuilder() {
        if (playerBuilder_ == null) {
          playerBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.PvpmatchProtos.MatchPlayerBean, com.sh.game.protos.PvpmatchProtos.MatchPlayerBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchPlayerBeanOrBuilder>(
                  player_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          player_ = null;
        }
        return playerBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.ResMatchInfo)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.ResMatchInfo)
    private static final com.sh.game.protos.PvpmatchProtos.ResMatchInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.ResMatchInfo();
    }

    public static com.sh.game.protos.PvpmatchProtos.ResMatchInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMatchInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResMatchInfo>() {
      @java.lang.Override
      public ResMatchInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMatchInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMatchInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResMatchInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.ResMatchInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMatchFailOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.ResMatchFail)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *pvp类型
     * </pre>
     *
     * <code>int32 pvpType = 1;</code>
     * @return The pvpType.
     */
    int getPvpType();

    /**
     * <pre>
     *失败原因
     * </pre>
     *
     * <code>string info = 2;</code>
     * @return The info.
     */
    java.lang.String getInfo();
    /**
     * <pre>
     *失败原因
     * </pre>
     *
     * <code>string info = 2;</code>
     * @return The bytes for info.
     */
    com.google.protobuf.ByteString
        getInfoBytes();
  }
  /**
   * <pre>
   ** class='ResMatchFail' id='41' desc='返回匹配失败信息' 
   * </pre>
   *
   * Protobuf type {@code pvpmatch.ResMatchFail}
   */
  public static final class ResMatchFail extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.ResMatchFail)
      ResMatchFailOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMatchFail.newBuilder() to construct.
    private ResMatchFail(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMatchFail() {
      info_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMatchFail();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMatchFail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              pvpType_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              info_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchFail_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchFail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.ResMatchFail.class, com.sh.game.protos.PvpmatchProtos.ResMatchFail.Builder.class);
    }

    public static final int PVPTYPE_FIELD_NUMBER = 1;
    private int pvpType_;
    /**
     * <pre>
     *pvp类型
     * </pre>
     *
     * <code>int32 pvpType = 1;</code>
     * @return The pvpType.
     */
    @java.lang.Override
    public int getPvpType() {
      return pvpType_;
    }

    public static final int INFO_FIELD_NUMBER = 2;
    private volatile java.lang.Object info_;
    /**
     * <pre>
     *失败原因
     * </pre>
     *
     * <code>string info = 2;</code>
     * @return The info.
     */
    @java.lang.Override
    public java.lang.String getInfo() {
      java.lang.Object ref = info_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        info_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *失败原因
     * </pre>
     *
     * <code>string info = 2;</code>
     * @return The bytes for info.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getInfoBytes() {
      java.lang.Object ref = info_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        info_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (pvpType_ != 0) {
        output.writeInt32(1, pvpType_);
      }
      if (!getInfoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, info_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (pvpType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, pvpType_);
      }
      if (!getInfoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, info_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.ResMatchFail)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.ResMatchFail other = (com.sh.game.protos.PvpmatchProtos.ResMatchFail) obj;

      if (getPvpType()
          != other.getPvpType()) return false;
      if (!getInfo()
          .equals(other.getInfo())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PVPTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getPvpType();
      hash = (37 * hash) + INFO_FIELD_NUMBER;
      hash = (53 * hash) + getInfo().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.ResMatchFail prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMatchFail' id='41' desc='返回匹配失败信息' 
     * </pre>
     *
     * Protobuf type {@code pvpmatch.ResMatchFail}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.ResMatchFail)
        com.sh.game.protos.PvpmatchProtos.ResMatchFailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchFail_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchFail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.ResMatchFail.class, com.sh.game.protos.PvpmatchProtos.ResMatchFail.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.ResMatchFail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        pvpType_ = 0;

        info_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchFail_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchFail getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.ResMatchFail.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchFail build() {
        com.sh.game.protos.PvpmatchProtos.ResMatchFail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchFail buildPartial() {
        com.sh.game.protos.PvpmatchProtos.ResMatchFail result = new com.sh.game.protos.PvpmatchProtos.ResMatchFail(this);
        result.pvpType_ = pvpType_;
        result.info_ = info_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.ResMatchFail) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.ResMatchFail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.ResMatchFail other) {
        if (other == com.sh.game.protos.PvpmatchProtos.ResMatchFail.getDefaultInstance()) return this;
        if (other.getPvpType() != 0) {
          setPvpType(other.getPvpType());
        }
        if (!other.getInfo().isEmpty()) {
          info_ = other.info_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.ResMatchFail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.ResMatchFail) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int pvpType_ ;
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 1;</code>
       * @return The pvpType.
       */
      @java.lang.Override
      public int getPvpType() {
        return pvpType_;
      }
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 1;</code>
       * @param value The pvpType to set.
       * @return This builder for chaining.
       */
      public Builder setPvpType(int value) {
        
        pvpType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPvpType() {
        
        pvpType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object info_ = "";
      /**
       * <pre>
       *失败原因
       * </pre>
       *
       * <code>string info = 2;</code>
       * @return The info.
       */
      public java.lang.String getInfo() {
        java.lang.Object ref = info_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          info_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *失败原因
       * </pre>
       *
       * <code>string info = 2;</code>
       * @return The bytes for info.
       */
      public com.google.protobuf.ByteString
          getInfoBytes() {
        java.lang.Object ref = info_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          info_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *失败原因
       * </pre>
       *
       * <code>string info = 2;</code>
       * @param value The info to set.
       * @return This builder for chaining.
       */
      public Builder setInfo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        info_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *失败原因
       * </pre>
       *
       * <code>string info = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearInfo() {
        
        info_ = getDefaultInstance().getInfo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *失败原因
       * </pre>
       *
       * <code>string info = 2;</code>
       * @param value The bytes for info to set.
       * @return This builder for chaining.
       */
      public Builder setInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        info_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.ResMatchFail)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.ResMatchFail)
    private static final com.sh.game.protos.PvpmatchProtos.ResMatchFail DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.ResMatchFail();
    }

    public static com.sh.game.protos.PvpmatchProtos.ResMatchFail getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMatchFail>
        PARSER = new com.google.protobuf.AbstractParser<ResMatchFail>() {
      @java.lang.Override
      public ResMatchFail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMatchFail(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMatchFail> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResMatchFail> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.ResMatchFail getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResBattleOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.ResBattle)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *队伍id
     * </pre>
     *
     * <code>int64 battleId = 1;</code>
     * @return The battleId.
     */
    long getBattleId();

    /**
     * <pre>
     *pvp类型
     * </pre>
     *
     * <code>int32 pvpType = 2;</code>
     * @return The pvpType.
     */
    int getPvpType();

    /**
     * <pre>
     *队伍A
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
     * @return Whether the teamA field is set.
     */
    boolean hasTeamA();
    /**
     * <pre>
     *队伍A
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
     * @return The teamA.
     */
    com.sh.game.protos.PvpmatchProtos.MatchTeamBean getTeamA();
    /**
     * <pre>
     *队伍A
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
     */
    com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder getTeamAOrBuilder();

    /**
     * <pre>
     *队伍B
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
     * @return Whether the teamB field is set.
     */
    boolean hasTeamB();
    /**
     * <pre>
     *队伍B
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
     * @return The teamB.
     */
    com.sh.game.protos.PvpmatchProtos.MatchTeamBean getTeamB();
    /**
     * <pre>
     *队伍B
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
     */
    com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder getTeamBOrBuilder();
  }
  /**
   * <pre>
   ** class='ResBattle' id='42' desc='返回对战双方信息' 
   * </pre>
   *
   * Protobuf type {@code pvpmatch.ResBattle}
   */
  public static final class ResBattle extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.ResBattle)
      ResBattleOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResBattle.newBuilder() to construct.
    private ResBattle(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResBattle() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResBattle();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResBattle(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              battleId_ = input.readInt64();
              break;
            }
            case 16: {

              pvpType_ = input.readInt32();
              break;
            }
            case 26: {
              com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder subBuilder = null;
              if (teamA_ != null) {
                subBuilder = teamA_.toBuilder();
              }
              teamA_ = input.readMessage(com.sh.game.protos.PvpmatchProtos.MatchTeamBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(teamA_);
                teamA_ = subBuilder.buildPartial();
              }

              break;
            }
            case 34: {
              com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder subBuilder = null;
              if (teamB_ != null) {
                subBuilder = teamB_.toBuilder();
              }
              teamB_ = input.readMessage(com.sh.game.protos.PvpmatchProtos.MatchTeamBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(teamB_);
                teamB_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResBattle_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResBattle_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.ResBattle.class, com.sh.game.protos.PvpmatchProtos.ResBattle.Builder.class);
    }

    public static final int BATTLEID_FIELD_NUMBER = 1;
    private long battleId_;
    /**
     * <pre>
     *队伍id
     * </pre>
     *
     * <code>int64 battleId = 1;</code>
     * @return The battleId.
     */
    @java.lang.Override
    public long getBattleId() {
      return battleId_;
    }

    public static final int PVPTYPE_FIELD_NUMBER = 2;
    private int pvpType_;
    /**
     * <pre>
     *pvp类型
     * </pre>
     *
     * <code>int32 pvpType = 2;</code>
     * @return The pvpType.
     */
    @java.lang.Override
    public int getPvpType() {
      return pvpType_;
    }

    public static final int TEAMA_FIELD_NUMBER = 3;
    private com.sh.game.protos.PvpmatchProtos.MatchTeamBean teamA_;
    /**
     * <pre>
     *队伍A
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
     * @return Whether the teamA field is set.
     */
    @java.lang.Override
    public boolean hasTeamA() {
      return teamA_ != null;
    }
    /**
     * <pre>
     *队伍A
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
     * @return The teamA.
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchTeamBean getTeamA() {
      return teamA_ == null ? com.sh.game.protos.PvpmatchProtos.MatchTeamBean.getDefaultInstance() : teamA_;
    }
    /**
     * <pre>
     *队伍A
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder getTeamAOrBuilder() {
      return getTeamA();
    }

    public static final int TEAMB_FIELD_NUMBER = 4;
    private com.sh.game.protos.PvpmatchProtos.MatchTeamBean teamB_;
    /**
     * <pre>
     *队伍B
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
     * @return Whether the teamB field is set.
     */
    @java.lang.Override
    public boolean hasTeamB() {
      return teamB_ != null;
    }
    /**
     * <pre>
     *队伍B
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
     * @return The teamB.
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchTeamBean getTeamB() {
      return teamB_ == null ? com.sh.game.protos.PvpmatchProtos.MatchTeamBean.getDefaultInstance() : teamB_;
    }
    /**
     * <pre>
     *队伍B
     * </pre>
     *
     * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder getTeamBOrBuilder() {
      return getTeamB();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (battleId_ != 0L) {
        output.writeInt64(1, battleId_);
      }
      if (pvpType_ != 0) {
        output.writeInt32(2, pvpType_);
      }
      if (teamA_ != null) {
        output.writeMessage(3, getTeamA());
      }
      if (teamB_ != null) {
        output.writeMessage(4, getTeamB());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (battleId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, battleId_);
      }
      if (pvpType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, pvpType_);
      }
      if (teamA_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getTeamA());
      }
      if (teamB_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getTeamB());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.ResBattle)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.ResBattle other = (com.sh.game.protos.PvpmatchProtos.ResBattle) obj;

      if (getBattleId()
          != other.getBattleId()) return false;
      if (getPvpType()
          != other.getPvpType()) return false;
      if (hasTeamA() != other.hasTeamA()) return false;
      if (hasTeamA()) {
        if (!getTeamA()
            .equals(other.getTeamA())) return false;
      }
      if (hasTeamB() != other.hasTeamB()) return false;
      if (hasTeamB()) {
        if (!getTeamB()
            .equals(other.getTeamB())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + BATTLEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getBattleId());
      hash = (37 * hash) + PVPTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getPvpType();
      if (hasTeamA()) {
        hash = (37 * hash) + TEAMA_FIELD_NUMBER;
        hash = (53 * hash) + getTeamA().hashCode();
      }
      if (hasTeamB()) {
        hash = (37 * hash) + TEAMB_FIELD_NUMBER;
        hash = (53 * hash) + getTeamB().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResBattle parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.ResBattle prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResBattle' id='42' desc='返回对战双方信息' 
     * </pre>
     *
     * Protobuf type {@code pvpmatch.ResBattle}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.ResBattle)
        com.sh.game.protos.PvpmatchProtos.ResBattleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResBattle_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResBattle_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.ResBattle.class, com.sh.game.protos.PvpmatchProtos.ResBattle.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.ResBattle.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        battleId_ = 0L;

        pvpType_ = 0;

        if (teamABuilder_ == null) {
          teamA_ = null;
        } else {
          teamA_ = null;
          teamABuilder_ = null;
        }
        if (teamBBuilder_ == null) {
          teamB_ = null;
        } else {
          teamB_ = null;
          teamBBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResBattle_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResBattle getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.ResBattle.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResBattle build() {
        com.sh.game.protos.PvpmatchProtos.ResBattle result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResBattle buildPartial() {
        com.sh.game.protos.PvpmatchProtos.ResBattle result = new com.sh.game.protos.PvpmatchProtos.ResBattle(this);
        result.battleId_ = battleId_;
        result.pvpType_ = pvpType_;
        if (teamABuilder_ == null) {
          result.teamA_ = teamA_;
        } else {
          result.teamA_ = teamABuilder_.build();
        }
        if (teamBBuilder_ == null) {
          result.teamB_ = teamB_;
        } else {
          result.teamB_ = teamBBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.ResBattle) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.ResBattle)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.ResBattle other) {
        if (other == com.sh.game.protos.PvpmatchProtos.ResBattle.getDefaultInstance()) return this;
        if (other.getBattleId() != 0L) {
          setBattleId(other.getBattleId());
        }
        if (other.getPvpType() != 0) {
          setPvpType(other.getPvpType());
        }
        if (other.hasTeamA()) {
          mergeTeamA(other.getTeamA());
        }
        if (other.hasTeamB()) {
          mergeTeamB(other.getTeamB());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.ResBattle parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.ResBattle) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long battleId_ ;
      /**
       * <pre>
       *队伍id
       * </pre>
       *
       * <code>int64 battleId = 1;</code>
       * @return The battleId.
       */
      @java.lang.Override
      public long getBattleId() {
        return battleId_;
      }
      /**
       * <pre>
       *队伍id
       * </pre>
       *
       * <code>int64 battleId = 1;</code>
       * @param value The battleId to set.
       * @return This builder for chaining.
       */
      public Builder setBattleId(long value) {
        
        battleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *队伍id
       * </pre>
       *
       * <code>int64 battleId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBattleId() {
        
        battleId_ = 0L;
        onChanged();
        return this;
      }

      private int pvpType_ ;
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 2;</code>
       * @return The pvpType.
       */
      @java.lang.Override
      public int getPvpType() {
        return pvpType_;
      }
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 2;</code>
       * @param value The pvpType to set.
       * @return This builder for chaining.
       */
      public Builder setPvpType(int value) {
        
        pvpType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *pvp类型
       * </pre>
       *
       * <code>int32 pvpType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPvpType() {
        
        pvpType_ = 0;
        onChanged();
        return this;
      }

      private com.sh.game.protos.PvpmatchProtos.MatchTeamBean teamA_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchTeamBean, com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder> teamABuilder_;
      /**
       * <pre>
       *队伍A
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
       * @return Whether the teamA field is set.
       */
      public boolean hasTeamA() {
        return teamABuilder_ != null || teamA_ != null;
      }
      /**
       * <pre>
       *队伍A
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
       * @return The teamA.
       */
      public com.sh.game.protos.PvpmatchProtos.MatchTeamBean getTeamA() {
        if (teamABuilder_ == null) {
          return teamA_ == null ? com.sh.game.protos.PvpmatchProtos.MatchTeamBean.getDefaultInstance() : teamA_;
        } else {
          return teamABuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *队伍A
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
       */
      public Builder setTeamA(com.sh.game.protos.PvpmatchProtos.MatchTeamBean value) {
        if (teamABuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          teamA_ = value;
          onChanged();
        } else {
          teamABuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *队伍A
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
       */
      public Builder setTeamA(
          com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder builderForValue) {
        if (teamABuilder_ == null) {
          teamA_ = builderForValue.build();
          onChanged();
        } else {
          teamABuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *队伍A
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
       */
      public Builder mergeTeamA(com.sh.game.protos.PvpmatchProtos.MatchTeamBean value) {
        if (teamABuilder_ == null) {
          if (teamA_ != null) {
            teamA_ =
              com.sh.game.protos.PvpmatchProtos.MatchTeamBean.newBuilder(teamA_).mergeFrom(value).buildPartial();
          } else {
            teamA_ = value;
          }
          onChanged();
        } else {
          teamABuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *队伍A
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
       */
      public Builder clearTeamA() {
        if (teamABuilder_ == null) {
          teamA_ = null;
          onChanged();
        } else {
          teamA_ = null;
          teamABuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *队伍A
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder getTeamABuilder() {
        
        onChanged();
        return getTeamAFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *队伍A
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder getTeamAOrBuilder() {
        if (teamABuilder_ != null) {
          return teamABuilder_.getMessageOrBuilder();
        } else {
          return teamA_ == null ?
              com.sh.game.protos.PvpmatchProtos.MatchTeamBean.getDefaultInstance() : teamA_;
        }
      }
      /**
       * <pre>
       *队伍A
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamA = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchTeamBean, com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder> 
          getTeamAFieldBuilder() {
        if (teamABuilder_ == null) {
          teamABuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.PvpmatchProtos.MatchTeamBean, com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder>(
                  getTeamA(),
                  getParentForChildren(),
                  isClean());
          teamA_ = null;
        }
        return teamABuilder_;
      }

      private com.sh.game.protos.PvpmatchProtos.MatchTeamBean teamB_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchTeamBean, com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder> teamBBuilder_;
      /**
       * <pre>
       *队伍B
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
       * @return Whether the teamB field is set.
       */
      public boolean hasTeamB() {
        return teamBBuilder_ != null || teamB_ != null;
      }
      /**
       * <pre>
       *队伍B
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
       * @return The teamB.
       */
      public com.sh.game.protos.PvpmatchProtos.MatchTeamBean getTeamB() {
        if (teamBBuilder_ == null) {
          return teamB_ == null ? com.sh.game.protos.PvpmatchProtos.MatchTeamBean.getDefaultInstance() : teamB_;
        } else {
          return teamBBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *队伍B
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
       */
      public Builder setTeamB(com.sh.game.protos.PvpmatchProtos.MatchTeamBean value) {
        if (teamBBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          teamB_ = value;
          onChanged();
        } else {
          teamBBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *队伍B
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
       */
      public Builder setTeamB(
          com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder builderForValue) {
        if (teamBBuilder_ == null) {
          teamB_ = builderForValue.build();
          onChanged();
        } else {
          teamBBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *队伍B
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
       */
      public Builder mergeTeamB(com.sh.game.protos.PvpmatchProtos.MatchTeamBean value) {
        if (teamBBuilder_ == null) {
          if (teamB_ != null) {
            teamB_ =
              com.sh.game.protos.PvpmatchProtos.MatchTeamBean.newBuilder(teamB_).mergeFrom(value).buildPartial();
          } else {
            teamB_ = value;
          }
          onChanged();
        } else {
          teamBBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *队伍B
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
       */
      public Builder clearTeamB() {
        if (teamBBuilder_ == null) {
          teamB_ = null;
          onChanged();
        } else {
          teamB_ = null;
          teamBBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *队伍B
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder getTeamBBuilder() {
        
        onChanged();
        return getTeamBFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *队伍B
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder getTeamBOrBuilder() {
        if (teamBBuilder_ != null) {
          return teamBBuilder_.getMessageOrBuilder();
        } else {
          return teamB_ == null ?
              com.sh.game.protos.PvpmatchProtos.MatchTeamBean.getDefaultInstance() : teamB_;
        }
      }
      /**
       * <pre>
       *队伍B
       * </pre>
       *
       * <code>.pvpmatch.MatchTeamBean teamB = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchTeamBean, com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder> 
          getTeamBFieldBuilder() {
        if (teamBBuilder_ == null) {
          teamBBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.PvpmatchProtos.MatchTeamBean, com.sh.game.protos.PvpmatchProtos.MatchTeamBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchTeamBeanOrBuilder>(
                  getTeamB(),
                  getParentForChildren(),
                  isClean());
          teamB_ = null;
        }
        return teamBBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.ResBattle)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.ResBattle)
    private static final com.sh.game.protos.PvpmatchProtos.ResBattle DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.ResBattle();
    }

    public static com.sh.game.protos.PvpmatchProtos.ResBattle getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResBattle>
        PARSER = new com.google.protobuf.AbstractParser<ResBattle>() {
      @java.lang.Override
      public ResBattle parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResBattle(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResBattle> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResBattle> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.ResBattle getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMatchRankOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.ReqMatchRank)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *匹配类型
     * </pre>
     *
     * <code>int32 pvpType = 1;</code>
     * @return The pvpType.
     */
    int getPvpType();
  }
  /**
   * <pre>
   ** class='ReqMatchRank' id='43' desc='查看修罗排行' 
   * </pre>
   *
   * Protobuf type {@code pvpmatch.ReqMatchRank}
   */
  public static final class ReqMatchRank extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.ReqMatchRank)
      ReqMatchRankOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMatchRank.newBuilder() to construct.
    private ReqMatchRank(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMatchRank() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMatchRank();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMatchRank(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              pvpType_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ReqMatchRank_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ReqMatchRank_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.ReqMatchRank.class, com.sh.game.protos.PvpmatchProtos.ReqMatchRank.Builder.class);
    }

    public static final int PVPTYPE_FIELD_NUMBER = 1;
    private int pvpType_;
    /**
     * <pre>
     *匹配类型
     * </pre>
     *
     * <code>int32 pvpType = 1;</code>
     * @return The pvpType.
     */
    @java.lang.Override
    public int getPvpType() {
      return pvpType_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (pvpType_ != 0) {
        output.writeInt32(1, pvpType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (pvpType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, pvpType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.ReqMatchRank)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.ReqMatchRank other = (com.sh.game.protos.PvpmatchProtos.ReqMatchRank) obj;

      if (getPvpType()
          != other.getPvpType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PVPTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getPvpType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.ReqMatchRank prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMatchRank' id='43' desc='查看修罗排行' 
     * </pre>
     *
     * Protobuf type {@code pvpmatch.ReqMatchRank}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.ReqMatchRank)
        com.sh.game.protos.PvpmatchProtos.ReqMatchRankOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ReqMatchRank_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ReqMatchRank_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.ReqMatchRank.class, com.sh.game.protos.PvpmatchProtos.ReqMatchRank.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.ReqMatchRank.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        pvpType_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ReqMatchRank_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ReqMatchRank getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.ReqMatchRank.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ReqMatchRank build() {
        com.sh.game.protos.PvpmatchProtos.ReqMatchRank result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ReqMatchRank buildPartial() {
        com.sh.game.protos.PvpmatchProtos.ReqMatchRank result = new com.sh.game.protos.PvpmatchProtos.ReqMatchRank(this);
        result.pvpType_ = pvpType_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.ReqMatchRank) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.ReqMatchRank)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.ReqMatchRank other) {
        if (other == com.sh.game.protos.PvpmatchProtos.ReqMatchRank.getDefaultInstance()) return this;
        if (other.getPvpType() != 0) {
          setPvpType(other.getPvpType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.ReqMatchRank parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.ReqMatchRank) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int pvpType_ ;
      /**
       * <pre>
       *匹配类型
       * </pre>
       *
       * <code>int32 pvpType = 1;</code>
       * @return The pvpType.
       */
      @java.lang.Override
      public int getPvpType() {
        return pvpType_;
      }
      /**
       * <pre>
       *匹配类型
       * </pre>
       *
       * <code>int32 pvpType = 1;</code>
       * @param value The pvpType to set.
       * @return This builder for chaining.
       */
      public Builder setPvpType(int value) {
        
        pvpType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *匹配类型
       * </pre>
       *
       * <code>int32 pvpType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPvpType() {
        
        pvpType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.ReqMatchRank)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.ReqMatchRank)
    private static final com.sh.game.protos.PvpmatchProtos.ReqMatchRank DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.ReqMatchRank();
    }

    public static com.sh.game.protos.PvpmatchProtos.ReqMatchRank getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMatchRank>
        PARSER = new com.google.protobuf.AbstractParser<ReqMatchRank>() {
      @java.lang.Override
      public ReqMatchRank parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMatchRank(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMatchRank> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqMatchRank> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.ReqMatchRank getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMatchRankOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.ResMatchRank)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *匹配类型
     * </pre>
     *
     * <code>int32 pvpType = 1;</code>
     * @return The pvpType.
     */
    int getPvpType();

    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    java.util.List<com.sh.game.protos.PvpmatchProtos.MatchRankBean> 
        getRankListList();
    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    com.sh.game.protos.PvpmatchProtos.MatchRankBean getRankList(int index);
    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    int getRankListCount();
    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    java.util.List<? extends com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder> 
        getRankListOrBuilderList();
    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder getRankListOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResMatchRank' id='44' desc='返回修罗排行' 
   * </pre>
   *
   * Protobuf type {@code pvpmatch.ResMatchRank}
   */
  public static final class ResMatchRank extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.ResMatchRank)
      ResMatchRankOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMatchRank.newBuilder() to construct.
    private ResMatchRank(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMatchRank() {
      rankList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMatchRank();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMatchRank(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              pvpType_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rankList_ = new java.util.ArrayList<com.sh.game.protos.PvpmatchProtos.MatchRankBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              rankList_.add(
                  input.readMessage(com.sh.game.protos.PvpmatchProtos.MatchRankBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          rankList_ = java.util.Collections.unmodifiableList(rankList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchRank_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchRank_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.ResMatchRank.class, com.sh.game.protos.PvpmatchProtos.ResMatchRank.Builder.class);
    }

    public static final int PVPTYPE_FIELD_NUMBER = 1;
    private int pvpType_;
    /**
     * <pre>
     *匹配类型
     * </pre>
     *
     * <code>int32 pvpType = 1;</code>
     * @return The pvpType.
     */
    @java.lang.Override
    public int getPvpType() {
      return pvpType_;
    }

    public static final int RANKLIST_FIELD_NUMBER = 2;
    private java.util.List<com.sh.game.protos.PvpmatchProtos.MatchRankBean> rankList_;
    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.PvpmatchProtos.MatchRankBean> getRankListList() {
      return rankList_;
    }
    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder> 
        getRankListOrBuilderList() {
      return rankList_;
    }
    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    @java.lang.Override
    public int getRankListCount() {
      return rankList_.size();
    }
    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchRankBean getRankList(int index) {
      return rankList_.get(index);
    }
    /**
     * <pre>
     *排行详情
     * </pre>
     *
     * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder getRankListOrBuilder(
        int index) {
      return rankList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (pvpType_ != 0) {
        output.writeInt32(1, pvpType_);
      }
      for (int i = 0; i < rankList_.size(); i++) {
        output.writeMessage(2, rankList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (pvpType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, pvpType_);
      }
      for (int i = 0; i < rankList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, rankList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.ResMatchRank)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.ResMatchRank other = (com.sh.game.protos.PvpmatchProtos.ResMatchRank) obj;

      if (getPvpType()
          != other.getPvpType()) return false;
      if (!getRankListList()
          .equals(other.getRankListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PVPTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getPvpType();
      if (getRankListCount() > 0) {
        hash = (37 * hash) + RANKLIST_FIELD_NUMBER;
        hash = (53 * hash) + getRankListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.ResMatchRank prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMatchRank' id='44' desc='返回修罗排行' 
     * </pre>
     *
     * Protobuf type {@code pvpmatch.ResMatchRank}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.ResMatchRank)
        com.sh.game.protos.PvpmatchProtos.ResMatchRankOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchRank_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchRank_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.ResMatchRank.class, com.sh.game.protos.PvpmatchProtos.ResMatchRank.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.ResMatchRank.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRankListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        pvpType_ = 0;

        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rankListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchRank_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchRank getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.ResMatchRank.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchRank build() {
        com.sh.game.protos.PvpmatchProtos.ResMatchRank result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchRank buildPartial() {
        com.sh.game.protos.PvpmatchProtos.ResMatchRank result = new com.sh.game.protos.PvpmatchProtos.ResMatchRank(this);
        int from_bitField0_ = bitField0_;
        result.pvpType_ = pvpType_;
        if (rankListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rankList_ = java.util.Collections.unmodifiableList(rankList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rankList_ = rankList_;
        } else {
          result.rankList_ = rankListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.ResMatchRank) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.ResMatchRank)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.ResMatchRank other) {
        if (other == com.sh.game.protos.PvpmatchProtos.ResMatchRank.getDefaultInstance()) return this;
        if (other.getPvpType() != 0) {
          setPvpType(other.getPvpType());
        }
        if (rankListBuilder_ == null) {
          if (!other.rankList_.isEmpty()) {
            if (rankList_.isEmpty()) {
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRankListIsMutable();
              rankList_.addAll(other.rankList_);
            }
            onChanged();
          }
        } else {
          if (!other.rankList_.isEmpty()) {
            if (rankListBuilder_.isEmpty()) {
              rankListBuilder_.dispose();
              rankListBuilder_ = null;
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rankListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRankListFieldBuilder() : null;
            } else {
              rankListBuilder_.addAllMessages(other.rankList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.ResMatchRank parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.ResMatchRank) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int pvpType_ ;
      /**
       * <pre>
       *匹配类型
       * </pre>
       *
       * <code>int32 pvpType = 1;</code>
       * @return The pvpType.
       */
      @java.lang.Override
      public int getPvpType() {
        return pvpType_;
      }
      /**
       * <pre>
       *匹配类型
       * </pre>
       *
       * <code>int32 pvpType = 1;</code>
       * @param value The pvpType to set.
       * @return This builder for chaining.
       */
      public Builder setPvpType(int value) {
        
        pvpType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *匹配类型
       * </pre>
       *
       * <code>int32 pvpType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPvpType() {
        
        pvpType_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.PvpmatchProtos.MatchRankBean> rankList_ =
        java.util.Collections.emptyList();
      private void ensureRankListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rankList_ = new java.util.ArrayList<com.sh.game.protos.PvpmatchProtos.MatchRankBean>(rankList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchRankBean, com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder> rankListBuilder_;

      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public java.util.List<com.sh.game.protos.PvpmatchProtos.MatchRankBean> getRankListList() {
        if (rankListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rankList_);
        } else {
          return rankListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public int getRankListCount() {
        if (rankListBuilder_ == null) {
          return rankList_.size();
        } else {
          return rankListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchRankBean getRankList(int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);
        } else {
          return rankListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public Builder setRankList(
          int index, com.sh.game.protos.PvpmatchProtos.MatchRankBean value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.set(index, value);
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public Builder setRankList(
          int index, com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public Builder addRankList(com.sh.game.protos.PvpmatchProtos.MatchRankBean value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public Builder addRankList(
          int index, com.sh.game.protos.PvpmatchProtos.MatchRankBean value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(index, value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public Builder addRankList(
          com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public Builder addRankList(
          int index, com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public Builder addAllRankList(
          java.lang.Iterable<? extends com.sh.game.protos.PvpmatchProtos.MatchRankBean> values) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rankList_);
          onChanged();
        } else {
          rankListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public Builder clearRankList() {
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rankListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public Builder removeRankList(int index) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.remove(index);
          onChanged();
        } else {
          rankListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder getRankListBuilder(
          int index) {
        return getRankListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder getRankListOrBuilder(
          int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);  } else {
          return rankListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public java.util.List<? extends com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder> 
           getRankListOrBuilderList() {
        if (rankListBuilder_ != null) {
          return rankListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rankList_);
        }
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder addRankListBuilder() {
        return getRankListFieldBuilder().addBuilder(
            com.sh.game.protos.PvpmatchProtos.MatchRankBean.getDefaultInstance());
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder addRankListBuilder(
          int index) {
        return getRankListFieldBuilder().addBuilder(
            index, com.sh.game.protos.PvpmatchProtos.MatchRankBean.getDefaultInstance());
      }
      /**
       * <pre>
       *排行详情
       * </pre>
       *
       * <code>repeated .pvpmatch.MatchRankBean rankList = 2;</code>
       */
      public java.util.List<com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder> 
           getRankListBuilderList() {
        return getRankListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.PvpmatchProtos.MatchRankBean, com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder> 
          getRankListFieldBuilder() {
        if (rankListBuilder_ == null) {
          rankListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.PvpmatchProtos.MatchRankBean, com.sh.game.protos.PvpmatchProtos.MatchRankBean.Builder, com.sh.game.protos.PvpmatchProtos.MatchRankBeanOrBuilder>(
                  rankList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rankList_ = null;
        }
        return rankListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.ResMatchRank)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.ResMatchRank)
    private static final com.sh.game.protos.PvpmatchProtos.ResMatchRank DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.ResMatchRank();
    }

    public static com.sh.game.protos.PvpmatchProtos.ResMatchRank getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMatchRank>
        PARSER = new com.google.protobuf.AbstractParser<ResMatchRank>() {
      @java.lang.Override
      public ResMatchRank parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMatchRank(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMatchRank> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResMatchRank> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.ResMatchRank getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMatchStateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.ResMatchState)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *匹配服状态
     * </pre>
     *
     * <code>bool state = 1;</code>
     * @return The state.
     */
    boolean getState();
  }
  /**
   * <pre>
   ** class='ResMatchState' id='45' desc='返回匹配服状态' 
   * </pre>
   *
   * Protobuf type {@code pvpmatch.ResMatchState}
   */
  public static final class ResMatchState extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.ResMatchState)
      ResMatchStateOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMatchState.newBuilder() to construct.
    private ResMatchState(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMatchState() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMatchState();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMatchState(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              state_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchState_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchState_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.ResMatchState.class, com.sh.game.protos.PvpmatchProtos.ResMatchState.Builder.class);
    }

    public static final int STATE_FIELD_NUMBER = 1;
    private boolean state_;
    /**
     * <pre>
     *匹配服状态
     * </pre>
     *
     * <code>bool state = 1;</code>
     * @return The state.
     */
    @java.lang.Override
    public boolean getState() {
      return state_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (state_ != false) {
        output.writeBool(1, state_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (state_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, state_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.ResMatchState)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.ResMatchState other = (com.sh.game.protos.PvpmatchProtos.ResMatchState) obj;

      if (getState()
          != other.getState()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + STATE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getState());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResMatchState parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.ResMatchState prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMatchState' id='45' desc='返回匹配服状态' 
     * </pre>
     *
     * Protobuf type {@code pvpmatch.ResMatchState}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.ResMatchState)
        com.sh.game.protos.PvpmatchProtos.ResMatchStateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchState_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchState_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.ResMatchState.class, com.sh.game.protos.PvpmatchProtos.ResMatchState.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.ResMatchState.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        state_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResMatchState_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchState getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.ResMatchState.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchState build() {
        com.sh.game.protos.PvpmatchProtos.ResMatchState result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResMatchState buildPartial() {
        com.sh.game.protos.PvpmatchProtos.ResMatchState result = new com.sh.game.protos.PvpmatchProtos.ResMatchState(this);
        result.state_ = state_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.ResMatchState) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.ResMatchState)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.ResMatchState other) {
        if (other == com.sh.game.protos.PvpmatchProtos.ResMatchState.getDefaultInstance()) return this;
        if (other.getState() != false) {
          setState(other.getState());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.ResMatchState parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.ResMatchState) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private boolean state_ ;
      /**
       * <pre>
       *匹配服状态
       * </pre>
       *
       * <code>bool state = 1;</code>
       * @return The state.
       */
      @java.lang.Override
      public boolean getState() {
        return state_;
      }
      /**
       * <pre>
       *匹配服状态
       * </pre>
       *
       * <code>bool state = 1;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(boolean value) {
        
        state_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *匹配服状态
       * </pre>
       *
       * <code>bool state = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        
        state_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.ResMatchState)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.ResMatchState)
    private static final com.sh.game.protos.PvpmatchProtos.ResMatchState DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.ResMatchState();
    }

    public static com.sh.game.protos.PvpmatchProtos.ResMatchState getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMatchState>
        PARSER = new com.google.protobuf.AbstractParser<ResMatchState>() {
      @java.lang.Override
      public ResMatchState parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMatchState(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMatchState> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResMatchState> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.ResMatchState getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResCancelMatchOrBuilder extends
      // @@protoc_insertion_point(interface_extends:pvpmatch.ResCancelMatch)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *结果1成功，0失败
     * </pre>
     *
     * <code>int32 ret = 1;</code>
     * @return The ret.
     */
    int getRet();

    /**
     * <pre>
     *提示
     * </pre>
     *
     * <code>string info = 2;</code>
     * @return The info.
     */
    java.lang.String getInfo();
    /**
     * <pre>
     *提示
     * </pre>
     *
     * <code>string info = 2;</code>
     * @return The bytes for info.
     */
    com.google.protobuf.ByteString
        getInfoBytes();

    /**
     * <pre>
     *匹配类型
     * </pre>
     *
     * <code>int32 matchType = 3;</code>
     * @return The matchType.
     */
    int getMatchType();
  }
  /**
   * <pre>
   ** class='ResCancelMatch' id='46' desc='返回匹配取消' 
   * </pre>
   *
   * Protobuf type {@code pvpmatch.ResCancelMatch}
   */
  public static final class ResCancelMatch extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:pvpmatch.ResCancelMatch)
      ResCancelMatchOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResCancelMatch.newBuilder() to construct.
    private ResCancelMatch(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResCancelMatch() {
      info_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResCancelMatch();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResCancelMatch(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              ret_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              info_ = s;
              break;
            }
            case 24: {

              matchType_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResCancelMatch_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResCancelMatch_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.PvpmatchProtos.ResCancelMatch.class, com.sh.game.protos.PvpmatchProtos.ResCancelMatch.Builder.class);
    }

    public static final int RET_FIELD_NUMBER = 1;
    private int ret_;
    /**
     * <pre>
     *结果1成功，0失败
     * </pre>
     *
     * <code>int32 ret = 1;</code>
     * @return The ret.
     */
    @java.lang.Override
    public int getRet() {
      return ret_;
    }

    public static final int INFO_FIELD_NUMBER = 2;
    private volatile java.lang.Object info_;
    /**
     * <pre>
     *提示
     * </pre>
     *
     * <code>string info = 2;</code>
     * @return The info.
     */
    @java.lang.Override
    public java.lang.String getInfo() {
      java.lang.Object ref = info_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        info_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *提示
     * </pre>
     *
     * <code>string info = 2;</code>
     * @return The bytes for info.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getInfoBytes() {
      java.lang.Object ref = info_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        info_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int MATCHTYPE_FIELD_NUMBER = 3;
    private int matchType_;
    /**
     * <pre>
     *匹配类型
     * </pre>
     *
     * <code>int32 matchType = 3;</code>
     * @return The matchType.
     */
    @java.lang.Override
    public int getMatchType() {
      return matchType_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (ret_ != 0) {
        output.writeInt32(1, ret_);
      }
      if (!getInfoBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, info_);
      }
      if (matchType_ != 0) {
        output.writeInt32(3, matchType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (ret_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, ret_);
      }
      if (!getInfoBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, info_);
      }
      if (matchType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, matchType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.PvpmatchProtos.ResCancelMatch)) {
        return super.equals(obj);
      }
      com.sh.game.protos.PvpmatchProtos.ResCancelMatch other = (com.sh.game.protos.PvpmatchProtos.ResCancelMatch) obj;

      if (getRet()
          != other.getRet()) return false;
      if (!getInfo()
          .equals(other.getInfo())) return false;
      if (getMatchType()
          != other.getMatchType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RET_FIELD_NUMBER;
      hash = (53 * hash) + getRet();
      hash = (37 * hash) + INFO_FIELD_NUMBER;
      hash = (53 * hash) + getInfo().hashCode();
      hash = (37 * hash) + MATCHTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getMatchType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.PvpmatchProtos.ResCancelMatch prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResCancelMatch' id='46' desc='返回匹配取消' 
     * </pre>
     *
     * Protobuf type {@code pvpmatch.ResCancelMatch}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:pvpmatch.ResCancelMatch)
        com.sh.game.protos.PvpmatchProtos.ResCancelMatchOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResCancelMatch_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResCancelMatch_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.PvpmatchProtos.ResCancelMatch.class, com.sh.game.protos.PvpmatchProtos.ResCancelMatch.Builder.class);
      }

      // Construct using com.sh.game.protos.PvpmatchProtos.ResCancelMatch.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ret_ = 0;

        info_ = "";

        matchType_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.PvpmatchProtos.internal_static_pvpmatch_ResCancelMatch_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResCancelMatch getDefaultInstanceForType() {
        return com.sh.game.protos.PvpmatchProtos.ResCancelMatch.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResCancelMatch build() {
        com.sh.game.protos.PvpmatchProtos.ResCancelMatch result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.PvpmatchProtos.ResCancelMatch buildPartial() {
        com.sh.game.protos.PvpmatchProtos.ResCancelMatch result = new com.sh.game.protos.PvpmatchProtos.ResCancelMatch(this);
        result.ret_ = ret_;
        result.info_ = info_;
        result.matchType_ = matchType_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.PvpmatchProtos.ResCancelMatch) {
          return mergeFrom((com.sh.game.protos.PvpmatchProtos.ResCancelMatch)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.PvpmatchProtos.ResCancelMatch other) {
        if (other == com.sh.game.protos.PvpmatchProtos.ResCancelMatch.getDefaultInstance()) return this;
        if (other.getRet() != 0) {
          setRet(other.getRet());
        }
        if (!other.getInfo().isEmpty()) {
          info_ = other.info_;
          onChanged();
        }
        if (other.getMatchType() != 0) {
          setMatchType(other.getMatchType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.PvpmatchProtos.ResCancelMatch parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.PvpmatchProtos.ResCancelMatch) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int ret_ ;
      /**
       * <pre>
       *结果1成功，0失败
       * </pre>
       *
       * <code>int32 ret = 1;</code>
       * @return The ret.
       */
      @java.lang.Override
      public int getRet() {
        return ret_;
      }
      /**
       * <pre>
       *结果1成功，0失败
       * </pre>
       *
       * <code>int32 ret = 1;</code>
       * @param value The ret to set.
       * @return This builder for chaining.
       */
      public Builder setRet(int value) {
        
        ret_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *结果1成功，0失败
       * </pre>
       *
       * <code>int32 ret = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRet() {
        
        ret_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object info_ = "";
      /**
       * <pre>
       *提示
       * </pre>
       *
       * <code>string info = 2;</code>
       * @return The info.
       */
      public java.lang.String getInfo() {
        java.lang.Object ref = info_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          info_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *提示
       * </pre>
       *
       * <code>string info = 2;</code>
       * @return The bytes for info.
       */
      public com.google.protobuf.ByteString
          getInfoBytes() {
        java.lang.Object ref = info_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          info_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *提示
       * </pre>
       *
       * <code>string info = 2;</code>
       * @param value The info to set.
       * @return This builder for chaining.
       */
      public Builder setInfo(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        info_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *提示
       * </pre>
       *
       * <code>string info = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearInfo() {
        
        info_ = getDefaultInstance().getInfo();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *提示
       * </pre>
       *
       * <code>string info = 2;</code>
       * @param value The bytes for info to set.
       * @return This builder for chaining.
       */
      public Builder setInfoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        info_ = value;
        onChanged();
        return this;
      }

      private int matchType_ ;
      /**
       * <pre>
       *匹配类型
       * </pre>
       *
       * <code>int32 matchType = 3;</code>
       * @return The matchType.
       */
      @java.lang.Override
      public int getMatchType() {
        return matchType_;
      }
      /**
       * <pre>
       *匹配类型
       * </pre>
       *
       * <code>int32 matchType = 3;</code>
       * @param value The matchType to set.
       * @return This builder for chaining.
       */
      public Builder setMatchType(int value) {
        
        matchType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *匹配类型
       * </pre>
       *
       * <code>int32 matchType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMatchType() {
        
        matchType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:pvpmatch.ResCancelMatch)
    }

    // @@protoc_insertion_point(class_scope:pvpmatch.ResCancelMatch)
    private static final com.sh.game.protos.PvpmatchProtos.ResCancelMatch DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.PvpmatchProtos.ResCancelMatch();
    }

    public static com.sh.game.protos.PvpmatchProtos.ResCancelMatch getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResCancelMatch>
        PARSER = new com.google.protobuf.AbstractParser<ResCancelMatch>() {
      @java.lang.Override
      public ResCancelMatch parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResCancelMatch(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResCancelMatch> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResCancelMatch> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.PvpmatchProtos.ResCancelMatch getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_MatchPlayerBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_MatchPlayerBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_MatchTeamBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_MatchTeamBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_MatchRankBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_MatchRankBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_ResMatchInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_ResMatchInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_ResMatchFail_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_ResMatchFail_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_ResBattle_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_ResBattle_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_ReqMatchRank_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_ReqMatchRank_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_ResMatchRank_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_ResMatchRank_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_ResMatchState_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_ResMatchState_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_pvpmatch_ResCancelMatch_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_pvpmatch_ResCancelMatch_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016pvpmatch.proto\022\010pvpmatch\"\260\001\n\017MatchPlay" +
      "erBean\022\020\n\010playerId\030\001 \001(\003\022\020\n\010roleName\030\002 \001" +
      "(\t\022\022\n\nfightPower\030\003 \001(\005\022\013\n\003sex\030\004 \001(\005\022\016\n\006c" +
      "areer\030\005 \001(\005\022\016\n\006hostId\030\006 \001(\005\022\022\n\nserverNam" +
      "e\030\007 \001(\t\022\023\n\013successRate\030\010 \001(\005\022\017\n\007duanWei\030" +
      "\t \001(\005\"o\n\rMatchTeamBean\022\016\n\006teamId\030\001 \001(\003\022\022" +
      "\n\nfightPower\030\002 \001(\005\022\017\n\007pvpType\030\003 \001(\005\022)\n\006p" +
      "layer\030\004 \003(\0132\031.pvpmatch.MatchPlayerBean\"^" +
      "\n\rMatchRankBean\022\014\n\004rank\030\001 \001(\005\022\013\n\003rid\030\002 \001" +
      "(\003\022\014\n\004name\030\003 \001(\t\022\016\n\006hostId\030\004 \001(\005\022\024\n\014duan" +
      "WeiCfgId\030\005 \001(\005\"n\n\014ResMatchInfo\022\016\n\006teamId" +
      "\030\001 \001(\003\022\022\n\nfightPower\030\002 \001(\005\022\017\n\007pvpType\030\003 " +
      "\001(\005\022)\n\006player\030\004 \003(\0132\031.pvpmatch.MatchPlay" +
      "erBean\"-\n\014ResMatchFail\022\017\n\007pvpType\030\001 \001(\005\022" +
      "\014\n\004info\030\002 \001(\t\"~\n\tResBattle\022\020\n\010battleId\030\001" +
      " \001(\003\022\017\n\007pvpType\030\002 \001(\005\022&\n\005teamA\030\003 \001(\0132\027.p" +
      "vpmatch.MatchTeamBean\022&\n\005teamB\030\004 \001(\0132\027.p" +
      "vpmatch.MatchTeamBean\"\037\n\014ReqMatchRank\022\017\n" +
      "\007pvpType\030\001 \001(\005\"J\n\014ResMatchRank\022\017\n\007pvpTyp" +
      "e\030\001 \001(\005\022)\n\010rankList\030\002 \003(\0132\027.pvpmatch.Mat" +
      "chRankBean\"\036\n\rResMatchState\022\r\n\005state\030\001 \001" +
      "(\010\">\n\016ResCancelMatch\022\013\n\003ret\030\001 \001(\005\022\014\n\004inf" +
      "o\030\002 \001(\t\022\021\n\tmatchType\030\003 \001(\005B$\n\022com.sh.gam" +
      "e.protosB\016PvpmatchProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_pvpmatch_MatchPlayerBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_pvpmatch_MatchPlayerBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_MatchPlayerBean_descriptor,
        new java.lang.String[] { "PlayerId", "RoleName", "FightPower", "Sex", "Career", "HostId", "ServerName", "SuccessRate", "DuanWei", });
    internal_static_pvpmatch_MatchTeamBean_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_pvpmatch_MatchTeamBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_MatchTeamBean_descriptor,
        new java.lang.String[] { "TeamId", "FightPower", "PvpType", "Player", });
    internal_static_pvpmatch_MatchRankBean_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_pvpmatch_MatchRankBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_MatchRankBean_descriptor,
        new java.lang.String[] { "Rank", "Rid", "Name", "HostId", "DuanWeiCfgId", });
    internal_static_pvpmatch_ResMatchInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_pvpmatch_ResMatchInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_ResMatchInfo_descriptor,
        new java.lang.String[] { "TeamId", "FightPower", "PvpType", "Player", });
    internal_static_pvpmatch_ResMatchFail_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_pvpmatch_ResMatchFail_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_ResMatchFail_descriptor,
        new java.lang.String[] { "PvpType", "Info", });
    internal_static_pvpmatch_ResBattle_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_pvpmatch_ResBattle_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_ResBattle_descriptor,
        new java.lang.String[] { "BattleId", "PvpType", "TeamA", "TeamB", });
    internal_static_pvpmatch_ReqMatchRank_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_pvpmatch_ReqMatchRank_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_ReqMatchRank_descriptor,
        new java.lang.String[] { "PvpType", });
    internal_static_pvpmatch_ResMatchRank_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_pvpmatch_ResMatchRank_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_ResMatchRank_descriptor,
        new java.lang.String[] { "PvpType", "RankList", });
    internal_static_pvpmatch_ResMatchState_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_pvpmatch_ResMatchState_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_ResMatchState_descriptor,
        new java.lang.String[] { "State", });
    internal_static_pvpmatch_ResCancelMatch_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_pvpmatch_ResCancelMatch_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_pvpmatch_ResCancelMatch_descriptor,
        new java.lang.String[] { "Ret", "Info", "MatchType", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
