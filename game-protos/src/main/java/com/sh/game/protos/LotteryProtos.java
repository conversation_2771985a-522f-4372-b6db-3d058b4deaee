// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lottery.proto

package com.sh.game.protos;

public final class LotteryProtos {
  private LotteryProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqLotteryOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lottery.ReqLottery)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 抽奖配置id
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     * @return The configId.
     */
    int getConfigId();

    /**
     * <pre>
     * 抽奖次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    int getCount();
  }
  /**
   * <pre>
   ** class='ReqLottery' id='1' desc='请求抽奖' 
   * </pre>
   *
   * Protobuf type {@code lottery.ReqLottery}
   */
  public static final class ReqLottery extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lottery.ReqLottery)
      ReqLotteryOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqLottery.newBuilder() to construct.
    private ReqLottery(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqLottery() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqLottery();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqLottery(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              configId_ = input.readInt32();
              break;
            }
            case 16: {

              count_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLottery_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLottery_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LotteryProtos.ReqLottery.class, com.sh.game.protos.LotteryProtos.ReqLottery.Builder.class);
    }

    public static final int CONFIGID_FIELD_NUMBER = 1;
    private int configId_;
    /**
     * <pre>
     * 抽奖配置id
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private int count_;
    /**
     * <pre>
     * 抽奖次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (configId_ != 0) {
        output.writeInt32(1, configId_);
      }
      if (count_ != 0) {
        output.writeInt32(2, count_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, configId_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, count_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LotteryProtos.ReqLottery)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LotteryProtos.ReqLottery other = (com.sh.game.protos.LotteryProtos.ReqLottery) obj;

      if (getConfigId()
          != other.getConfigId()) return false;
      if (getCount()
          != other.getCount()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLottery parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LotteryProtos.ReqLottery prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqLottery' id='1' desc='请求抽奖' 
     * </pre>
     *
     * Protobuf type {@code lottery.ReqLottery}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lottery.ReqLottery)
        com.sh.game.protos.LotteryProtos.ReqLotteryOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLottery_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLottery_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LotteryProtos.ReqLottery.class, com.sh.game.protos.LotteryProtos.ReqLottery.Builder.class);
      }

      // Construct using com.sh.game.protos.LotteryProtos.ReqLottery.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        configId_ = 0;

        count_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLottery_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ReqLottery getDefaultInstanceForType() {
        return com.sh.game.protos.LotteryProtos.ReqLottery.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ReqLottery build() {
        com.sh.game.protos.LotteryProtos.ReqLottery result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ReqLottery buildPartial() {
        com.sh.game.protos.LotteryProtos.ReqLottery result = new com.sh.game.protos.LotteryProtos.ReqLottery(this);
        result.configId_ = configId_;
        result.count_ = count_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LotteryProtos.ReqLottery) {
          return mergeFrom((com.sh.game.protos.LotteryProtos.ReqLottery)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LotteryProtos.ReqLottery other) {
        if (other == com.sh.game.protos.LotteryProtos.ReqLottery.getDefaultInstance()) return this;
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LotteryProtos.ReqLottery parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LotteryProtos.ReqLottery) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int configId_ ;
      /**
       * <pre>
       * 抽奖配置id
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <pre>
       * 抽奖配置id
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 抽奖配置id
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       * 抽奖次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       * 抽奖次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 抽奖次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lottery.ReqLottery)
    }

    // @@protoc_insertion_point(class_scope:lottery.ReqLottery)
    private static final com.sh.game.protos.LotteryProtos.ReqLottery DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LotteryProtos.ReqLottery();
    }

    public static com.sh.game.protos.LotteryProtos.ReqLottery getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqLottery>
        PARSER = new com.google.protobuf.AbstractParser<ReqLottery>() {
      @java.lang.Override
      public ReqLottery parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqLottery(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqLottery> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqLottery> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LotteryProtos.ReqLottery getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResLotteryOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lottery.ResLottery)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *活动
     * </pre>
     *
     * <code>int32 actType = 1;</code>
     * @return The actType.
     */
    int getActType();

    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> 
        getRewardCountsList();
    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    com.sh.game.protos.LotteryProtos.RewardCountBean getRewardCounts(int index);
    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    int getRewardCountsCount();
    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    java.util.List<? extends com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> 
        getRewardCountsOrBuilderList();
    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder getRewardCountsOrBuilder(
        int index);

    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> 
        getSelfMaxCountsList();
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBean getSelfMaxCounts(int index);
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    int getSelfMaxCountsCount();
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getSelfMaxCountsOrBuilderList();
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getSelfMaxCountsOrBuilder(
        int index);

    /**
     * <pre>
     *活动id
     * </pre>
     *
     * <code>int32 activityId = 4;</code>
     * @return The activityId.
     */
    int getActivityId();

    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> 
        getItemsList();
    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBean getItems(int index);
    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    int getItemsCount();
    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getItemsOrBuilderList();
    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getItemsOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResLottery' id='2' desc='返回抽奖结果信息' 
   * </pre>
   *
   * Protobuf type {@code lottery.ResLottery}
   */
  public static final class ResLottery extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lottery.ResLottery)
      ResLotteryOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResLottery.newBuilder() to construct.
    private ResLottery(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResLottery() {
      rewardCounts_ = java.util.Collections.emptyList();
      selfMaxCounts_ = java.util.Collections.emptyList();
      items_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResLottery();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResLottery(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              actType_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rewardCounts_ = new java.util.ArrayList<com.sh.game.protos.LotteryProtos.RewardCountBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              rewardCounts_.add(
                  input.readMessage(com.sh.game.protos.LotteryProtos.RewardCountBean.parser(), extensionRegistry));
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                selfMaxCounts_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>();
                mutable_bitField0_ |= 0x00000002;
              }
              selfMaxCounts_.add(
                  input.readMessage(com.sh.game.protos.AbcProtos.CommonKeyValueBean.parser(), extensionRegistry));
              break;
            }
            case 32: {

              activityId_ = input.readInt32();
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                items_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>();
                mutable_bitField0_ |= 0x00000004;
              }
              items_.add(
                  input.readMessage(com.sh.game.protos.AbcProtos.CommonKeyValueBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          rewardCounts_ = java.util.Collections.unmodifiableList(rewardCounts_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          selfMaxCounts_ = java.util.Collections.unmodifiableList(selfMaxCounts_);
        }
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          items_ = java.util.Collections.unmodifiableList(items_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLottery_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLottery_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LotteryProtos.ResLottery.class, com.sh.game.protos.LotteryProtos.ResLottery.Builder.class);
    }

    public static final int ACTTYPE_FIELD_NUMBER = 1;
    private int actType_;
    /**
     * <pre>
     *活动
     * </pre>
     *
     * <code>int32 actType = 1;</code>
     * @return The actType.
     */
    @java.lang.Override
    public int getActType() {
      return actType_;
    }

    public static final int REWARDCOUNTS_FIELD_NUMBER = 2;
    private java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> rewardCounts_;
    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> getRewardCountsList() {
      return rewardCounts_;
    }
    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> 
        getRewardCountsOrBuilderList() {
      return rewardCounts_;
    }
    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    @java.lang.Override
    public int getRewardCountsCount() {
      return rewardCounts_.size();
    }
    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.LotteryProtos.RewardCountBean getRewardCounts(int index) {
      return rewardCounts_.get(index);
    }
    /**
     * <pre>
     *已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder getRewardCountsOrBuilder(
        int index) {
      return rewardCounts_.get(index);
    }

    public static final int SELFMAXCOUNTS_FIELD_NUMBER = 3;
    private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> selfMaxCounts_;
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getSelfMaxCountsList() {
      return selfMaxCounts_;
    }
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getSelfMaxCountsOrBuilderList() {
      return selfMaxCounts_;
    }
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    @java.lang.Override
    public int getSelfMaxCountsCount() {
      return selfMaxCounts_.size();
    }
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBean getSelfMaxCounts(int index) {
      return selfMaxCounts_.get(index);
    }
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getSelfMaxCountsOrBuilder(
        int index) {
      return selfMaxCounts_.get(index);
    }

    public static final int ACTIVITYID_FIELD_NUMBER = 4;
    private int activityId_;
    /**
     * <pre>
     *活动id
     * </pre>
     *
     * <code>int32 activityId = 4;</code>
     * @return The activityId.
     */
    @java.lang.Override
    public int getActivityId() {
      return activityId_;
    }

    public static final int ITEMS_FIELD_NUMBER = 5;
    private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> items_;
    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getItemsList() {
      return items_;
    }
    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getItemsOrBuilderList() {
      return items_;
    }
    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    @java.lang.Override
    public int getItemsCount() {
      return items_.size();
    }
    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBean getItems(int index) {
      return items_.get(index);
    }
    /**
     * <pre>
     * 抽取到的道具
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getItemsOrBuilder(
        int index) {
      return items_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (actType_ != 0) {
        output.writeInt32(1, actType_);
      }
      for (int i = 0; i < rewardCounts_.size(); i++) {
        output.writeMessage(2, rewardCounts_.get(i));
      }
      for (int i = 0; i < selfMaxCounts_.size(); i++) {
        output.writeMessage(3, selfMaxCounts_.get(i));
      }
      if (activityId_ != 0) {
        output.writeInt32(4, activityId_);
      }
      for (int i = 0; i < items_.size(); i++) {
        output.writeMessage(5, items_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (actType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, actType_);
      }
      for (int i = 0; i < rewardCounts_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, rewardCounts_.get(i));
      }
      for (int i = 0; i < selfMaxCounts_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, selfMaxCounts_.get(i));
      }
      if (activityId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, activityId_);
      }
      for (int i = 0; i < items_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, items_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LotteryProtos.ResLottery)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LotteryProtos.ResLottery other = (com.sh.game.protos.LotteryProtos.ResLottery) obj;

      if (getActType()
          != other.getActType()) return false;
      if (!getRewardCountsList()
          .equals(other.getRewardCountsList())) return false;
      if (!getSelfMaxCountsList()
          .equals(other.getSelfMaxCountsList())) return false;
      if (getActivityId()
          != other.getActivityId()) return false;
      if (!getItemsList()
          .equals(other.getItemsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ACTTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getActType();
      if (getRewardCountsCount() > 0) {
        hash = (37 * hash) + REWARDCOUNTS_FIELD_NUMBER;
        hash = (53 * hash) + getRewardCountsList().hashCode();
      }
      if (getSelfMaxCountsCount() > 0) {
        hash = (37 * hash) + SELFMAXCOUNTS_FIELD_NUMBER;
        hash = (53 * hash) + getSelfMaxCountsList().hashCode();
      }
      hash = (37 * hash) + ACTIVITYID_FIELD_NUMBER;
      hash = (53 * hash) + getActivityId();
      if (getItemsCount() > 0) {
        hash = (37 * hash) + ITEMS_FIELD_NUMBER;
        hash = (53 * hash) + getItemsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ResLottery parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LotteryProtos.ResLottery prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResLottery' id='2' desc='返回抽奖结果信息' 
     * </pre>
     *
     * Protobuf type {@code lottery.ResLottery}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lottery.ResLottery)
        com.sh.game.protos.LotteryProtos.ResLotteryOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLottery_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLottery_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LotteryProtos.ResLottery.class, com.sh.game.protos.LotteryProtos.ResLottery.Builder.class);
      }

      // Construct using com.sh.game.protos.LotteryProtos.ResLottery.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardCountsFieldBuilder();
          getSelfMaxCountsFieldBuilder();
          getItemsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        actType_ = 0;

        if (rewardCountsBuilder_ == null) {
          rewardCounts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rewardCountsBuilder_.clear();
        }
        if (selfMaxCountsBuilder_ == null) {
          selfMaxCounts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          selfMaxCountsBuilder_.clear();
        }
        activityId_ = 0;

        if (itemsBuilder_ == null) {
          items_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          itemsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLottery_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ResLottery getDefaultInstanceForType() {
        return com.sh.game.protos.LotteryProtos.ResLottery.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ResLottery build() {
        com.sh.game.protos.LotteryProtos.ResLottery result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ResLottery buildPartial() {
        com.sh.game.protos.LotteryProtos.ResLottery result = new com.sh.game.protos.LotteryProtos.ResLottery(this);
        int from_bitField0_ = bitField0_;
        result.actType_ = actType_;
        if (rewardCountsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rewardCounts_ = java.util.Collections.unmodifiableList(rewardCounts_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rewardCounts_ = rewardCounts_;
        } else {
          result.rewardCounts_ = rewardCountsBuilder_.build();
        }
        if (selfMaxCountsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            selfMaxCounts_ = java.util.Collections.unmodifiableList(selfMaxCounts_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.selfMaxCounts_ = selfMaxCounts_;
        } else {
          result.selfMaxCounts_ = selfMaxCountsBuilder_.build();
        }
        result.activityId_ = activityId_;
        if (itemsBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            items_ = java.util.Collections.unmodifiableList(items_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.items_ = items_;
        } else {
          result.items_ = itemsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LotteryProtos.ResLottery) {
          return mergeFrom((com.sh.game.protos.LotteryProtos.ResLottery)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LotteryProtos.ResLottery other) {
        if (other == com.sh.game.protos.LotteryProtos.ResLottery.getDefaultInstance()) return this;
        if (other.getActType() != 0) {
          setActType(other.getActType());
        }
        if (rewardCountsBuilder_ == null) {
          if (!other.rewardCounts_.isEmpty()) {
            if (rewardCounts_.isEmpty()) {
              rewardCounts_ = other.rewardCounts_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardCountsIsMutable();
              rewardCounts_.addAll(other.rewardCounts_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardCounts_.isEmpty()) {
            if (rewardCountsBuilder_.isEmpty()) {
              rewardCountsBuilder_.dispose();
              rewardCountsBuilder_ = null;
              rewardCounts_ = other.rewardCounts_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardCountsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardCountsFieldBuilder() : null;
            } else {
              rewardCountsBuilder_.addAllMessages(other.rewardCounts_);
            }
          }
        }
        if (selfMaxCountsBuilder_ == null) {
          if (!other.selfMaxCounts_.isEmpty()) {
            if (selfMaxCounts_.isEmpty()) {
              selfMaxCounts_ = other.selfMaxCounts_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureSelfMaxCountsIsMutable();
              selfMaxCounts_.addAll(other.selfMaxCounts_);
            }
            onChanged();
          }
        } else {
          if (!other.selfMaxCounts_.isEmpty()) {
            if (selfMaxCountsBuilder_.isEmpty()) {
              selfMaxCountsBuilder_.dispose();
              selfMaxCountsBuilder_ = null;
              selfMaxCounts_ = other.selfMaxCounts_;
              bitField0_ = (bitField0_ & ~0x00000002);
              selfMaxCountsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSelfMaxCountsFieldBuilder() : null;
            } else {
              selfMaxCountsBuilder_.addAllMessages(other.selfMaxCounts_);
            }
          }
        }
        if (other.getActivityId() != 0) {
          setActivityId(other.getActivityId());
        }
        if (itemsBuilder_ == null) {
          if (!other.items_.isEmpty()) {
            if (items_.isEmpty()) {
              items_ = other.items_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureItemsIsMutable();
              items_.addAll(other.items_);
            }
            onChanged();
          }
        } else {
          if (!other.items_.isEmpty()) {
            if (itemsBuilder_.isEmpty()) {
              itemsBuilder_.dispose();
              itemsBuilder_ = null;
              items_ = other.items_;
              bitField0_ = (bitField0_ & ~0x00000004);
              itemsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getItemsFieldBuilder() : null;
            } else {
              itemsBuilder_.addAllMessages(other.items_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LotteryProtos.ResLottery parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LotteryProtos.ResLottery) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int actType_ ;
      /**
       * <pre>
       *活动
       * </pre>
       *
       * <code>int32 actType = 1;</code>
       * @return The actType.
       */
      @java.lang.Override
      public int getActType() {
        return actType_;
      }
      /**
       * <pre>
       *活动
       * </pre>
       *
       * <code>int32 actType = 1;</code>
       * @param value The actType to set.
       * @return This builder for chaining.
       */
      public Builder setActType(int value) {
        
        actType_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *活动
       * </pre>
       *
       * <code>int32 actType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActType() {
        
        actType_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> rewardCounts_ =
        java.util.Collections.emptyList();
      private void ensureRewardCountsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rewardCounts_ = new java.util.ArrayList<com.sh.game.protos.LotteryProtos.RewardCountBean>(rewardCounts_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.LotteryProtos.RewardCountBean, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder, com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> rewardCountsBuilder_;

      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> getRewardCountsList() {
        if (rewardCountsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardCounts_);
        } else {
          return rewardCountsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public int getRewardCountsCount() {
        if (rewardCountsBuilder_ == null) {
          return rewardCounts_.size();
        } else {
          return rewardCountsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBean getRewardCounts(int index) {
        if (rewardCountsBuilder_ == null) {
          return rewardCounts_.get(index);
        } else {
          return rewardCountsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public Builder setRewardCounts(
          int index, com.sh.game.protos.LotteryProtos.RewardCountBean value) {
        if (rewardCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardCountsIsMutable();
          rewardCounts_.set(index, value);
          onChanged();
        } else {
          rewardCountsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public Builder setRewardCounts(
          int index, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder builderForValue) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          rewardCounts_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardCountsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public Builder addRewardCounts(com.sh.game.protos.LotteryProtos.RewardCountBean value) {
        if (rewardCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardCountsIsMutable();
          rewardCounts_.add(value);
          onChanged();
        } else {
          rewardCountsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public Builder addRewardCounts(
          int index, com.sh.game.protos.LotteryProtos.RewardCountBean value) {
        if (rewardCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardCountsIsMutable();
          rewardCounts_.add(index, value);
          onChanged();
        } else {
          rewardCountsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public Builder addRewardCounts(
          com.sh.game.protos.LotteryProtos.RewardCountBean.Builder builderForValue) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          rewardCounts_.add(builderForValue.build());
          onChanged();
        } else {
          rewardCountsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public Builder addRewardCounts(
          int index, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder builderForValue) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          rewardCounts_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardCountsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public Builder addAllRewardCounts(
          java.lang.Iterable<? extends com.sh.game.protos.LotteryProtos.RewardCountBean> values) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardCounts_);
          onChanged();
        } else {
          rewardCountsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public Builder clearRewardCounts() {
        if (rewardCountsBuilder_ == null) {
          rewardCounts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardCountsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public Builder removeRewardCounts(int index) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          rewardCounts_.remove(index);
          onChanged();
        } else {
          rewardCountsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBean.Builder getRewardCountsBuilder(
          int index) {
        return getRewardCountsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder getRewardCountsOrBuilder(
          int index) {
        if (rewardCountsBuilder_ == null) {
          return rewardCounts_.get(index);  } else {
          return rewardCountsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public java.util.List<? extends com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> 
           getRewardCountsOrBuilderList() {
        if (rewardCountsBuilder_ != null) {
          return rewardCountsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardCounts_);
        }
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBean.Builder addRewardCountsBuilder() {
        return getRewardCountsFieldBuilder().addBuilder(
            com.sh.game.protos.LotteryProtos.RewardCountBean.getDefaultInstance());
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBean.Builder addRewardCountsBuilder(
          int index) {
        return getRewardCountsFieldBuilder().addBuilder(
            index, com.sh.game.protos.LotteryProtos.RewardCountBean.getDefaultInstance());
      }
      /**
       * <pre>
       *已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 2;</code>
       */
      public java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean.Builder> 
           getRewardCountsBuilderList() {
        return getRewardCountsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.LotteryProtos.RewardCountBean, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder, com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> 
          getRewardCountsFieldBuilder() {
        if (rewardCountsBuilder_ == null) {
          rewardCountsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.LotteryProtos.RewardCountBean, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder, com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder>(
                  rewardCounts_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rewardCounts_ = null;
        }
        return rewardCountsBuilder_;
      }

      private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> selfMaxCounts_ =
        java.util.Collections.emptyList();
      private void ensureSelfMaxCountsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          selfMaxCounts_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>(selfMaxCounts_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> selfMaxCountsBuilder_;

      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getSelfMaxCountsList() {
        if (selfMaxCountsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(selfMaxCounts_);
        } else {
          return selfMaxCountsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public int getSelfMaxCountsCount() {
        if (selfMaxCountsBuilder_ == null) {
          return selfMaxCounts_.size();
        } else {
          return selfMaxCountsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean getSelfMaxCounts(int index) {
        if (selfMaxCountsBuilder_ == null) {
          return selfMaxCounts_.get(index);
        } else {
          return selfMaxCountsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public Builder setSelfMaxCounts(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (selfMaxCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.set(index, value);
          onChanged();
        } else {
          selfMaxCountsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public Builder setSelfMaxCounts(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.set(index, builderForValue.build());
          onChanged();
        } else {
          selfMaxCountsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public Builder addSelfMaxCounts(com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (selfMaxCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.add(value);
          onChanged();
        } else {
          selfMaxCountsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public Builder addSelfMaxCounts(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (selfMaxCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.add(index, value);
          onChanged();
        } else {
          selfMaxCountsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public Builder addSelfMaxCounts(
          com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.add(builderForValue.build());
          onChanged();
        } else {
          selfMaxCountsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public Builder addSelfMaxCounts(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.add(index, builderForValue.build());
          onChanged();
        } else {
          selfMaxCountsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public Builder addAllSelfMaxCounts(
          java.lang.Iterable<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBean> values) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, selfMaxCounts_);
          onChanged();
        } else {
          selfMaxCountsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public Builder clearSelfMaxCounts() {
        if (selfMaxCountsBuilder_ == null) {
          selfMaxCounts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          selfMaxCountsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public Builder removeSelfMaxCounts(int index) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.remove(index);
          onChanged();
        } else {
          selfMaxCountsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder getSelfMaxCountsBuilder(
          int index) {
        return getSelfMaxCountsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getSelfMaxCountsOrBuilder(
          int index) {
        if (selfMaxCountsBuilder_ == null) {
          return selfMaxCounts_.get(index);  } else {
          return selfMaxCountsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
           getSelfMaxCountsOrBuilderList() {
        if (selfMaxCountsBuilder_ != null) {
          return selfMaxCountsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(selfMaxCounts_);
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addSelfMaxCountsBuilder() {
        return getSelfMaxCountsFieldBuilder().addBuilder(
            com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addSelfMaxCountsBuilder(
          int index) {
        return getSelfMaxCountsFieldBuilder().addBuilder(
            index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 3;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder> 
           getSelfMaxCountsBuilderList() {
        return getSelfMaxCountsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
          getSelfMaxCountsFieldBuilder() {
        if (selfMaxCountsBuilder_ == null) {
          selfMaxCountsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder>(
                  selfMaxCounts_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          selfMaxCounts_ = null;
        }
        return selfMaxCountsBuilder_;
      }

      private int activityId_ ;
      /**
       * <pre>
       *活动id
       * </pre>
       *
       * <code>int32 activityId = 4;</code>
       * @return The activityId.
       */
      @java.lang.Override
      public int getActivityId() {
        return activityId_;
      }
      /**
       * <pre>
       *活动id
       * </pre>
       *
       * <code>int32 activityId = 4;</code>
       * @param value The activityId to set.
       * @return This builder for chaining.
       */
      public Builder setActivityId(int value) {
        
        activityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *活动id
       * </pre>
       *
       * <code>int32 activityId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearActivityId() {
        
        activityId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> items_ =
        java.util.Collections.emptyList();
      private void ensureItemsIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          items_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>(items_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> itemsBuilder_;

      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getItemsList() {
        if (itemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(items_);
        } else {
          return itemsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public int getItemsCount() {
        if (itemsBuilder_ == null) {
          return items_.size();
        } else {
          return itemsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean getItems(int index) {
        if (itemsBuilder_ == null) {
          return items_.get(index);
        } else {
          return itemsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public Builder setItems(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (itemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemsIsMutable();
          items_.set(index, value);
          onChanged();
        } else {
          itemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public Builder setItems(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          items_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public Builder addItems(com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (itemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemsIsMutable();
          items_.add(value);
          onChanged();
        } else {
          itemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public Builder addItems(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (itemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemsIsMutable();
          items_.add(index, value);
          onChanged();
        } else {
          itemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public Builder addItems(
          com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          items_.add(builderForValue.build());
          onChanged();
        } else {
          itemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public Builder addItems(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          items_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public Builder addAllItems(
          java.lang.Iterable<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBean> values) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, items_);
          onChanged();
        } else {
          itemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public Builder clearItems() {
        if (itemsBuilder_ == null) {
          items_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          itemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public Builder removeItems(int index) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          items_.remove(index);
          onChanged();
        } else {
          itemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder getItemsBuilder(
          int index) {
        return getItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getItemsOrBuilder(
          int index) {
        if (itemsBuilder_ == null) {
          return items_.get(index);  } else {
          return itemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
           getItemsOrBuilderList() {
        if (itemsBuilder_ != null) {
          return itemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(items_);
        }
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addItemsBuilder() {
        return getItemsFieldBuilder().addBuilder(
            com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addItemsBuilder(
          int index) {
        return getItemsFieldBuilder().addBuilder(
            index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       * 抽取到的道具
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean items = 5;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder> 
           getItemsBuilderList() {
        return getItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
          getItemsFieldBuilder() {
        if (itemsBuilder_ == null) {
          itemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder>(
                  items_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          items_ = null;
        }
        return itemsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lottery.ResLottery)
    }

    // @@protoc_insertion_point(class_scope:lottery.ResLottery)
    private static final com.sh.game.protos.LotteryProtos.ResLottery DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LotteryProtos.ResLottery();
    }

    public static com.sh.game.protos.LotteryProtos.ResLottery getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResLottery>
        PARSER = new com.google.protobuf.AbstractParser<ResLottery>() {
      @java.lang.Override
      public ResLottery parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResLottery(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResLottery> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResLottery> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LotteryProtos.ResLottery getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqLotteryInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lottery.ReqLotteryInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqLotteryInfo' id='3' desc='请求抽奖详情' 
   * </pre>
   *
   * Protobuf type {@code lottery.ReqLotteryInfo}
   */
  public static final class ReqLotteryInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lottery.ReqLotteryInfo)
      ReqLotteryInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqLotteryInfo.newBuilder() to construct.
    private ReqLotteryInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqLotteryInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqLotteryInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqLotteryInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLotteryInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLotteryInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LotteryProtos.ReqLotteryInfo.class, com.sh.game.protos.LotteryProtos.ReqLotteryInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LotteryProtos.ReqLotteryInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LotteryProtos.ReqLotteryInfo other = (com.sh.game.protos.LotteryProtos.ReqLotteryInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LotteryProtos.ReqLotteryInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqLotteryInfo' id='3' desc='请求抽奖详情' 
     * </pre>
     *
     * Protobuf type {@code lottery.ReqLotteryInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lottery.ReqLotteryInfo)
        com.sh.game.protos.LotteryProtos.ReqLotteryInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLotteryInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLotteryInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LotteryProtos.ReqLotteryInfo.class, com.sh.game.protos.LotteryProtos.ReqLotteryInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.LotteryProtos.ReqLotteryInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ReqLotteryInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ReqLotteryInfo getDefaultInstanceForType() {
        return com.sh.game.protos.LotteryProtos.ReqLotteryInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ReqLotteryInfo build() {
        com.sh.game.protos.LotteryProtos.ReqLotteryInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ReqLotteryInfo buildPartial() {
        com.sh.game.protos.LotteryProtos.ReqLotteryInfo result = new com.sh.game.protos.LotteryProtos.ReqLotteryInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LotteryProtos.ReqLotteryInfo) {
          return mergeFrom((com.sh.game.protos.LotteryProtos.ReqLotteryInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LotteryProtos.ReqLotteryInfo other) {
        if (other == com.sh.game.protos.LotteryProtos.ReqLotteryInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LotteryProtos.ReqLotteryInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LotteryProtos.ReqLotteryInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lottery.ReqLotteryInfo)
    }

    // @@protoc_insertion_point(class_scope:lottery.ReqLotteryInfo)
    private static final com.sh.game.protos.LotteryProtos.ReqLotteryInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LotteryProtos.ReqLotteryInfo();
    }

    public static com.sh.game.protos.LotteryProtos.ReqLotteryInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqLotteryInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqLotteryInfo>() {
      @java.lang.Override
      public ReqLotteryInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqLotteryInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqLotteryInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqLotteryInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LotteryProtos.ReqLotteryInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResLotteryInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lottery.ResLotteryInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> 
        getRewardCountsList();
    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    com.sh.game.protos.LotteryProtos.RewardCountBean getRewardCounts(int index);
    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    int getRewardCountsCount();
    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    java.util.List<? extends com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> 
        getRewardCountsOrBuilderList();
    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder getRewardCountsOrBuilder(
        int index);

    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> 
        getSelfMaxCountsList();
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBean getSelfMaxCounts(int index);
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    int getSelfMaxCountsCount();
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getSelfMaxCountsOrBuilderList();
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getSelfMaxCountsOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResLotteryInfo' id='4' desc='返回抽奖详情' 
   * </pre>
   *
   * Protobuf type {@code lottery.ResLotteryInfo}
   */
  public static final class ResLotteryInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lottery.ResLotteryInfo)
      ResLotteryInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResLotteryInfo.newBuilder() to construct.
    private ResLotteryInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResLotteryInfo() {
      rewardCounts_ = java.util.Collections.emptyList();
      selfMaxCounts_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResLotteryInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResLotteryInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rewardCounts_ = new java.util.ArrayList<com.sh.game.protos.LotteryProtos.RewardCountBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              rewardCounts_.add(
                  input.readMessage(com.sh.game.protos.LotteryProtos.RewardCountBean.parser(), extensionRegistry));
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                selfMaxCounts_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>();
                mutable_bitField0_ |= 0x00000002;
              }
              selfMaxCounts_.add(
                  input.readMessage(com.sh.game.protos.AbcProtos.CommonKeyValueBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          rewardCounts_ = java.util.Collections.unmodifiableList(rewardCounts_);
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          selfMaxCounts_ = java.util.Collections.unmodifiableList(selfMaxCounts_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLotteryInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLotteryInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LotteryProtos.ResLotteryInfo.class, com.sh.game.protos.LotteryProtos.ResLotteryInfo.Builder.class);
    }

    public static final int REWARDCOUNTS_FIELD_NUMBER = 1;
    private java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> rewardCounts_;
    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> getRewardCountsList() {
      return rewardCounts_;
    }
    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> 
        getRewardCountsOrBuilderList() {
      return rewardCounts_;
    }
    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    @java.lang.Override
    public int getRewardCountsCount() {
      return rewardCounts_.size();
    }
    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.LotteryProtos.RewardCountBean getRewardCounts(int index) {
      return rewardCounts_.get(index);
    }
    /**
     * <pre>
     * 已经挖取的次数，会重置
     * </pre>
     *
     * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder getRewardCountsOrBuilder(
        int index) {
      return rewardCounts_.get(index);
    }

    public static final int SELFMAXCOUNTS_FIELD_NUMBER = 2;
    private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> selfMaxCounts_;
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getSelfMaxCountsList() {
      return selfMaxCounts_;
    }
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getSelfMaxCountsOrBuilderList() {
      return selfMaxCounts_;
    }
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    @java.lang.Override
    public int getSelfMaxCountsCount() {
      return selfMaxCounts_.size();
    }
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBean getSelfMaxCounts(int index) {
      return selfMaxCounts_.get(index);
    }
    /**
     * <pre>
     *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getSelfMaxCountsOrBuilder(
        int index) {
      return selfMaxCounts_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < rewardCounts_.size(); i++) {
        output.writeMessage(1, rewardCounts_.get(i));
      }
      for (int i = 0; i < selfMaxCounts_.size(); i++) {
        output.writeMessage(2, selfMaxCounts_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < rewardCounts_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, rewardCounts_.get(i));
      }
      for (int i = 0; i < selfMaxCounts_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, selfMaxCounts_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LotteryProtos.ResLotteryInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LotteryProtos.ResLotteryInfo other = (com.sh.game.protos.LotteryProtos.ResLotteryInfo) obj;

      if (!getRewardCountsList()
          .equals(other.getRewardCountsList())) return false;
      if (!getSelfMaxCountsList()
          .equals(other.getSelfMaxCountsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRewardCountsCount() > 0) {
        hash = (37 * hash) + REWARDCOUNTS_FIELD_NUMBER;
        hash = (53 * hash) + getRewardCountsList().hashCode();
      }
      if (getSelfMaxCountsCount() > 0) {
        hash = (37 * hash) + SELFMAXCOUNTS_FIELD_NUMBER;
        hash = (53 * hash) + getSelfMaxCountsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LotteryProtos.ResLotteryInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResLotteryInfo' id='4' desc='返回抽奖详情' 
     * </pre>
     *
     * Protobuf type {@code lottery.ResLotteryInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lottery.ResLotteryInfo)
        com.sh.game.protos.LotteryProtos.ResLotteryInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLotteryInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLotteryInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LotteryProtos.ResLotteryInfo.class, com.sh.game.protos.LotteryProtos.ResLotteryInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.LotteryProtos.ResLotteryInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardCountsFieldBuilder();
          getSelfMaxCountsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (rewardCountsBuilder_ == null) {
          rewardCounts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rewardCountsBuilder_.clear();
        }
        if (selfMaxCountsBuilder_ == null) {
          selfMaxCounts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          selfMaxCountsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_ResLotteryInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ResLotteryInfo getDefaultInstanceForType() {
        return com.sh.game.protos.LotteryProtos.ResLotteryInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ResLotteryInfo build() {
        com.sh.game.protos.LotteryProtos.ResLotteryInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.ResLotteryInfo buildPartial() {
        com.sh.game.protos.LotteryProtos.ResLotteryInfo result = new com.sh.game.protos.LotteryProtos.ResLotteryInfo(this);
        int from_bitField0_ = bitField0_;
        if (rewardCountsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rewardCounts_ = java.util.Collections.unmodifiableList(rewardCounts_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rewardCounts_ = rewardCounts_;
        } else {
          result.rewardCounts_ = rewardCountsBuilder_.build();
        }
        if (selfMaxCountsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            selfMaxCounts_ = java.util.Collections.unmodifiableList(selfMaxCounts_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.selfMaxCounts_ = selfMaxCounts_;
        } else {
          result.selfMaxCounts_ = selfMaxCountsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LotteryProtos.ResLotteryInfo) {
          return mergeFrom((com.sh.game.protos.LotteryProtos.ResLotteryInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LotteryProtos.ResLotteryInfo other) {
        if (other == com.sh.game.protos.LotteryProtos.ResLotteryInfo.getDefaultInstance()) return this;
        if (rewardCountsBuilder_ == null) {
          if (!other.rewardCounts_.isEmpty()) {
            if (rewardCounts_.isEmpty()) {
              rewardCounts_ = other.rewardCounts_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardCountsIsMutable();
              rewardCounts_.addAll(other.rewardCounts_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardCounts_.isEmpty()) {
            if (rewardCountsBuilder_.isEmpty()) {
              rewardCountsBuilder_.dispose();
              rewardCountsBuilder_ = null;
              rewardCounts_ = other.rewardCounts_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardCountsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardCountsFieldBuilder() : null;
            } else {
              rewardCountsBuilder_.addAllMessages(other.rewardCounts_);
            }
          }
        }
        if (selfMaxCountsBuilder_ == null) {
          if (!other.selfMaxCounts_.isEmpty()) {
            if (selfMaxCounts_.isEmpty()) {
              selfMaxCounts_ = other.selfMaxCounts_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureSelfMaxCountsIsMutable();
              selfMaxCounts_.addAll(other.selfMaxCounts_);
            }
            onChanged();
          }
        } else {
          if (!other.selfMaxCounts_.isEmpty()) {
            if (selfMaxCountsBuilder_.isEmpty()) {
              selfMaxCountsBuilder_.dispose();
              selfMaxCountsBuilder_ = null;
              selfMaxCounts_ = other.selfMaxCounts_;
              bitField0_ = (bitField0_ & ~0x00000002);
              selfMaxCountsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSelfMaxCountsFieldBuilder() : null;
            } else {
              selfMaxCountsBuilder_.addAllMessages(other.selfMaxCounts_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LotteryProtos.ResLotteryInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LotteryProtos.ResLotteryInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> rewardCounts_ =
        java.util.Collections.emptyList();
      private void ensureRewardCountsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rewardCounts_ = new java.util.ArrayList<com.sh.game.protos.LotteryProtos.RewardCountBean>(rewardCounts_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.LotteryProtos.RewardCountBean, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder, com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> rewardCountsBuilder_;

      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean> getRewardCountsList() {
        if (rewardCountsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardCounts_);
        } else {
          return rewardCountsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public int getRewardCountsCount() {
        if (rewardCountsBuilder_ == null) {
          return rewardCounts_.size();
        } else {
          return rewardCountsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBean getRewardCounts(int index) {
        if (rewardCountsBuilder_ == null) {
          return rewardCounts_.get(index);
        } else {
          return rewardCountsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public Builder setRewardCounts(
          int index, com.sh.game.protos.LotteryProtos.RewardCountBean value) {
        if (rewardCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardCountsIsMutable();
          rewardCounts_.set(index, value);
          onChanged();
        } else {
          rewardCountsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public Builder setRewardCounts(
          int index, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder builderForValue) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          rewardCounts_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardCountsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public Builder addRewardCounts(com.sh.game.protos.LotteryProtos.RewardCountBean value) {
        if (rewardCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardCountsIsMutable();
          rewardCounts_.add(value);
          onChanged();
        } else {
          rewardCountsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public Builder addRewardCounts(
          int index, com.sh.game.protos.LotteryProtos.RewardCountBean value) {
        if (rewardCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardCountsIsMutable();
          rewardCounts_.add(index, value);
          onChanged();
        } else {
          rewardCountsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public Builder addRewardCounts(
          com.sh.game.protos.LotteryProtos.RewardCountBean.Builder builderForValue) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          rewardCounts_.add(builderForValue.build());
          onChanged();
        } else {
          rewardCountsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public Builder addRewardCounts(
          int index, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder builderForValue) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          rewardCounts_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardCountsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public Builder addAllRewardCounts(
          java.lang.Iterable<? extends com.sh.game.protos.LotteryProtos.RewardCountBean> values) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardCounts_);
          onChanged();
        } else {
          rewardCountsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public Builder clearRewardCounts() {
        if (rewardCountsBuilder_ == null) {
          rewardCounts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardCountsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public Builder removeRewardCounts(int index) {
        if (rewardCountsBuilder_ == null) {
          ensureRewardCountsIsMutable();
          rewardCounts_.remove(index);
          onChanged();
        } else {
          rewardCountsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBean.Builder getRewardCountsBuilder(
          int index) {
        return getRewardCountsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder getRewardCountsOrBuilder(
          int index) {
        if (rewardCountsBuilder_ == null) {
          return rewardCounts_.get(index);  } else {
          return rewardCountsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public java.util.List<? extends com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> 
           getRewardCountsOrBuilderList() {
        if (rewardCountsBuilder_ != null) {
          return rewardCountsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardCounts_);
        }
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBean.Builder addRewardCountsBuilder() {
        return getRewardCountsFieldBuilder().addBuilder(
            com.sh.game.protos.LotteryProtos.RewardCountBean.getDefaultInstance());
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public com.sh.game.protos.LotteryProtos.RewardCountBean.Builder addRewardCountsBuilder(
          int index) {
        return getRewardCountsFieldBuilder().addBuilder(
            index, com.sh.game.protos.LotteryProtos.RewardCountBean.getDefaultInstance());
      }
      /**
       * <pre>
       * 已经挖取的次数，会重置
       * </pre>
       *
       * <code>repeated .lottery.RewardCountBean rewardCounts = 1;</code>
       */
      public java.util.List<com.sh.game.protos.LotteryProtos.RewardCountBean.Builder> 
           getRewardCountsBuilderList() {
        return getRewardCountsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.LotteryProtos.RewardCountBean, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder, com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder> 
          getRewardCountsFieldBuilder() {
        if (rewardCountsBuilder_ == null) {
          rewardCountsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.LotteryProtos.RewardCountBean, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder, com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder>(
                  rewardCounts_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rewardCounts_ = null;
        }
        return rewardCountsBuilder_;
      }

      private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> selfMaxCounts_ =
        java.util.Collections.emptyList();
      private void ensureSelfMaxCountsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          selfMaxCounts_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>(selfMaxCounts_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> selfMaxCountsBuilder_;

      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getSelfMaxCountsList() {
        if (selfMaxCountsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(selfMaxCounts_);
        } else {
          return selfMaxCountsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public int getSelfMaxCountsCount() {
        if (selfMaxCountsBuilder_ == null) {
          return selfMaxCounts_.size();
        } else {
          return selfMaxCountsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean getSelfMaxCounts(int index) {
        if (selfMaxCountsBuilder_ == null) {
          return selfMaxCounts_.get(index);
        } else {
          return selfMaxCountsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public Builder setSelfMaxCounts(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (selfMaxCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.set(index, value);
          onChanged();
        } else {
          selfMaxCountsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public Builder setSelfMaxCounts(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.set(index, builderForValue.build());
          onChanged();
        } else {
          selfMaxCountsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public Builder addSelfMaxCounts(com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (selfMaxCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.add(value);
          onChanged();
        } else {
          selfMaxCountsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public Builder addSelfMaxCounts(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (selfMaxCountsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.add(index, value);
          onChanged();
        } else {
          selfMaxCountsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public Builder addSelfMaxCounts(
          com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.add(builderForValue.build());
          onChanged();
        } else {
          selfMaxCountsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public Builder addSelfMaxCounts(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.add(index, builderForValue.build());
          onChanged();
        } else {
          selfMaxCountsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public Builder addAllSelfMaxCounts(
          java.lang.Iterable<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBean> values) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, selfMaxCounts_);
          onChanged();
        } else {
          selfMaxCountsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public Builder clearSelfMaxCounts() {
        if (selfMaxCountsBuilder_ == null) {
          selfMaxCounts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          selfMaxCountsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public Builder removeSelfMaxCounts(int index) {
        if (selfMaxCountsBuilder_ == null) {
          ensureSelfMaxCountsIsMutable();
          selfMaxCounts_.remove(index);
          onChanged();
        } else {
          selfMaxCountsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder getSelfMaxCountsBuilder(
          int index) {
        return getSelfMaxCountsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getSelfMaxCountsOrBuilder(
          int index) {
        if (selfMaxCountsBuilder_ == null) {
          return selfMaxCounts_.get(index);  } else {
          return selfMaxCountsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
           getSelfMaxCountsOrBuilderList() {
        if (selfMaxCountsBuilder_ != null) {
          return selfMaxCountsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(selfMaxCounts_);
        }
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addSelfMaxCountsBuilder() {
        return getSelfMaxCountsFieldBuilder().addBuilder(
            com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addSelfMaxCountsBuilder(
          int index) {
        return getSelfMaxCountsFieldBuilder().addBuilder(
            index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       *个人已挖总次数，只记录配置个人获取上限的，k 对应cfg_lottery_reward表的Id v 次数
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean selfMaxCounts = 2;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder> 
           getSelfMaxCountsBuilderList() {
        return getSelfMaxCountsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
          getSelfMaxCountsFieldBuilder() {
        if (selfMaxCountsBuilder_ == null) {
          selfMaxCountsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder>(
                  selfMaxCounts_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          selfMaxCounts_ = null;
        }
        return selfMaxCountsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lottery.ResLotteryInfo)
    }

    // @@protoc_insertion_point(class_scope:lottery.ResLotteryInfo)
    private static final com.sh.game.protos.LotteryProtos.ResLotteryInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LotteryProtos.ResLotteryInfo();
    }

    public static com.sh.game.protos.LotteryProtos.ResLotteryInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResLotteryInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResLotteryInfo>() {
      @java.lang.Override
      public ResLotteryInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResLotteryInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResLotteryInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResLotteryInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LotteryProtos.ResLotteryInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RewardCountBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:lottery.RewardCountBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *对应cfg_lottery_reward表的Id
     * </pre>
     *
     * <code>int32 rewardConfigId = 1;</code>
     * @return The rewardConfigId.
     */
    int getRewardConfigId();

    /**
     * <pre>
     *次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    int getCount();
  }
  /**
   * Protobuf type {@code lottery.RewardCountBean}
   */
  public static final class RewardCountBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:lottery.RewardCountBean)
      RewardCountBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RewardCountBean.newBuilder() to construct.
    private RewardCountBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RewardCountBean() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RewardCountBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RewardCountBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              rewardConfigId_ = input.readInt32();
              break;
            }
            case 16: {

              count_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_RewardCountBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.LotteryProtos.internal_static_lottery_RewardCountBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.LotteryProtos.RewardCountBean.class, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder.class);
    }

    public static final int REWARDCONFIGID_FIELD_NUMBER = 1;
    private int rewardConfigId_;
    /**
     * <pre>
     *对应cfg_lottery_reward表的Id
     * </pre>
     *
     * <code>int32 rewardConfigId = 1;</code>
     * @return The rewardConfigId.
     */
    @java.lang.Override
    public int getRewardConfigId() {
      return rewardConfigId_;
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private int count_;
    /**
     * <pre>
     *次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (rewardConfigId_ != 0) {
        output.writeInt32(1, rewardConfigId_);
      }
      if (count_ != 0) {
        output.writeInt32(2, count_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rewardConfigId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rewardConfigId_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, count_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.LotteryProtos.RewardCountBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.LotteryProtos.RewardCountBean other = (com.sh.game.protos.LotteryProtos.RewardCountBean) obj;

      if (getRewardConfigId()
          != other.getRewardConfigId()) return false;
      if (getCount()
          != other.getCount()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + REWARDCONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getRewardConfigId();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.LotteryProtos.RewardCountBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.LotteryProtos.RewardCountBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code lottery.RewardCountBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:lottery.RewardCountBean)
        com.sh.game.protos.LotteryProtos.RewardCountBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_RewardCountBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_RewardCountBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.LotteryProtos.RewardCountBean.class, com.sh.game.protos.LotteryProtos.RewardCountBean.Builder.class);
      }

      // Construct using com.sh.game.protos.LotteryProtos.RewardCountBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        rewardConfigId_ = 0;

        count_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.LotteryProtos.internal_static_lottery_RewardCountBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.RewardCountBean getDefaultInstanceForType() {
        return com.sh.game.protos.LotteryProtos.RewardCountBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.RewardCountBean build() {
        com.sh.game.protos.LotteryProtos.RewardCountBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.LotteryProtos.RewardCountBean buildPartial() {
        com.sh.game.protos.LotteryProtos.RewardCountBean result = new com.sh.game.protos.LotteryProtos.RewardCountBean(this);
        result.rewardConfigId_ = rewardConfigId_;
        result.count_ = count_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.LotteryProtos.RewardCountBean) {
          return mergeFrom((com.sh.game.protos.LotteryProtos.RewardCountBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.LotteryProtos.RewardCountBean other) {
        if (other == com.sh.game.protos.LotteryProtos.RewardCountBean.getDefaultInstance()) return this;
        if (other.getRewardConfigId() != 0) {
          setRewardConfigId(other.getRewardConfigId());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.LotteryProtos.RewardCountBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.LotteryProtos.RewardCountBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int rewardConfigId_ ;
      /**
       * <pre>
       *对应cfg_lottery_reward表的Id
       * </pre>
       *
       * <code>int32 rewardConfigId = 1;</code>
       * @return The rewardConfigId.
       */
      @java.lang.Override
      public int getRewardConfigId() {
        return rewardConfigId_;
      }
      /**
       * <pre>
       *对应cfg_lottery_reward表的Id
       * </pre>
       *
       * <code>int32 rewardConfigId = 1;</code>
       * @param value The rewardConfigId to set.
       * @return This builder for chaining.
       */
      public Builder setRewardConfigId(int value) {
        
        rewardConfigId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *对应cfg_lottery_reward表的Id
       * </pre>
       *
       * <code>int32 rewardConfigId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRewardConfigId() {
        
        rewardConfigId_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       *次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       *次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:lottery.RewardCountBean)
    }

    // @@protoc_insertion_point(class_scope:lottery.RewardCountBean)
    private static final com.sh.game.protos.LotteryProtos.RewardCountBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.LotteryProtos.RewardCountBean();
    }

    public static com.sh.game.protos.LotteryProtos.RewardCountBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RewardCountBean>
        PARSER = new com.google.protobuf.AbstractParser<RewardCountBean>() {
      @java.lang.Override
      public RewardCountBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RewardCountBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RewardCountBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RewardCountBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.LotteryProtos.RewardCountBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lottery_ReqLottery_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lottery_ReqLottery_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lottery_ResLottery_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lottery_ResLottery_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lottery_ReqLotteryInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lottery_ReqLotteryInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lottery_ResLotteryInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lottery_ResLotteryInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_lottery_RewardCountBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_lottery_RewardCountBean_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rlottery.proto\022\007lottery\032\tabc.proto\"-\n\nR" +
      "eqLottery\022\020\n\010configId\030\001 \001(\005\022\r\n\005count\030\002 \001" +
      "(\005\"\271\001\n\nResLottery\022\017\n\007actType\030\001 \001(\005\022.\n\014re" +
      "wardCounts\030\002 \003(\0132\030.lottery.RewardCountBe" +
      "an\022.\n\rselfMaxCounts\030\003 \003(\0132\027.abc.CommonKe" +
      "yValueBean\022\022\n\nactivityId\030\004 \001(\005\022&\n\005items\030" +
      "\005 \003(\0132\027.abc.CommonKeyValueBean\"\020\n\016ReqLot" +
      "teryInfo\"p\n\016ResLotteryInfo\022.\n\014rewardCoun" +
      "ts\030\001 \003(\0132\030.lottery.RewardCountBean\022.\n\rse" +
      "lfMaxCounts\030\002 \003(\0132\027.abc.CommonKeyValueBe" +
      "an\"8\n\017RewardCountBean\022\026\n\016rewardConfigId\030" +
      "\001 \001(\005\022\r\n\005count\030\002 \001(\005B#\n\022com.sh.game.prot" +
      "osB\rLotteryProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.sh.game.protos.AbcProtos.getDescriptor(),
        });
    internal_static_lottery_ReqLottery_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_lottery_ReqLottery_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lottery_ReqLottery_descriptor,
        new java.lang.String[] { "ConfigId", "Count", });
    internal_static_lottery_ResLottery_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_lottery_ResLottery_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lottery_ResLottery_descriptor,
        new java.lang.String[] { "ActType", "RewardCounts", "SelfMaxCounts", "ActivityId", "Items", });
    internal_static_lottery_ReqLotteryInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_lottery_ReqLotteryInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lottery_ReqLotteryInfo_descriptor,
        new java.lang.String[] { });
    internal_static_lottery_ResLotteryInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_lottery_ResLotteryInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lottery_ResLotteryInfo_descriptor,
        new java.lang.String[] { "RewardCounts", "SelfMaxCounts", });
    internal_static_lottery_RewardCountBean_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_lottery_RewardCountBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_lottery_RewardCountBean_descriptor,
        new java.lang.String[] { "RewardConfigId", "Count", });
    com.sh.game.protos.AbcProtos.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
