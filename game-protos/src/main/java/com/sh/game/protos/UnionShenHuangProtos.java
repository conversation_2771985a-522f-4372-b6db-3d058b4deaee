// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: unionShenHuang.proto

package com.sh.game.protos;

public final class UnionShenHuangProtos {
  private UnionShenHuangProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ResUnionShenHuangInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:unionShenHuang.ResUnionShenHuangInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *行会id
     * </pre>
     *
     * <code>int64 unionId = 1;</code>
     * @return The unionId.
     */
    long getUnionId();

    /**
     * <pre>
     *行会神皇id
     * </pre>
     *
     * <code>int32 unionShenHuangId = 2;</code>
     * @return The unionShenHuangId.
     */
    int getUnionShenHuangId();

    /**
     * <pre>
     *行会神皇经验
     * </pre>
     *
     * <code>int32 unionShenHuangExp = 3;</code>
     * @return The unionShenHuangExp.
     */
    int getUnionShenHuangExp();
  }
  /**
   * <pre>
   ** class='ResUnionShenHuangInfo' id='1' desc='返回行会神皇信息' 
   * </pre>
   *
   * Protobuf type {@code unionShenHuang.ResUnionShenHuangInfo}
   */
  public static final class ResUnionShenHuangInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:unionShenHuang.ResUnionShenHuangInfo)
      ResUnionShenHuangInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResUnionShenHuangInfo.newBuilder() to construct.
    private ResUnionShenHuangInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResUnionShenHuangInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResUnionShenHuangInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResUnionShenHuangInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              unionId_ = input.readInt64();
              break;
            }
            case 16: {

              unionShenHuangId_ = input.readInt32();
              break;
            }
            case 24: {

              unionShenHuangExp_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ResUnionShenHuangInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ResUnionShenHuangInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo.class, com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo.Builder.class);
    }

    public static final int UNIONID_FIELD_NUMBER = 1;
    private long unionId_;
    /**
     * <pre>
     *行会id
     * </pre>
     *
     * <code>int64 unionId = 1;</code>
     * @return The unionId.
     */
    @java.lang.Override
    public long getUnionId() {
      return unionId_;
    }

    public static final int UNIONSHENHUANGID_FIELD_NUMBER = 2;
    private int unionShenHuangId_;
    /**
     * <pre>
     *行会神皇id
     * </pre>
     *
     * <code>int32 unionShenHuangId = 2;</code>
     * @return The unionShenHuangId.
     */
    @java.lang.Override
    public int getUnionShenHuangId() {
      return unionShenHuangId_;
    }

    public static final int UNIONSHENHUANGEXP_FIELD_NUMBER = 3;
    private int unionShenHuangExp_;
    /**
     * <pre>
     *行会神皇经验
     * </pre>
     *
     * <code>int32 unionShenHuangExp = 3;</code>
     * @return The unionShenHuangExp.
     */
    @java.lang.Override
    public int getUnionShenHuangExp() {
      return unionShenHuangExp_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (unionId_ != 0L) {
        output.writeInt64(1, unionId_);
      }
      if (unionShenHuangId_ != 0) {
        output.writeInt32(2, unionShenHuangId_);
      }
      if (unionShenHuangExp_ != 0) {
        output.writeInt32(3, unionShenHuangExp_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (unionId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, unionId_);
      }
      if (unionShenHuangId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, unionShenHuangId_);
      }
      if (unionShenHuangExp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, unionShenHuangExp_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo other = (com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo) obj;

      if (getUnionId()
          != other.getUnionId()) return false;
      if (getUnionShenHuangId()
          != other.getUnionShenHuangId()) return false;
      if (getUnionShenHuangExp()
          != other.getUnionShenHuangExp()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UNIONID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUnionId());
      hash = (37 * hash) + UNIONSHENHUANGID_FIELD_NUMBER;
      hash = (53 * hash) + getUnionShenHuangId();
      hash = (37 * hash) + UNIONSHENHUANGEXP_FIELD_NUMBER;
      hash = (53 * hash) + getUnionShenHuangExp();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResUnionShenHuangInfo' id='1' desc='返回行会神皇信息' 
     * </pre>
     *
     * Protobuf type {@code unionShenHuang.ResUnionShenHuangInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:unionShenHuang.ResUnionShenHuangInfo)
        com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ResUnionShenHuangInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ResUnionShenHuangInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo.class, com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        unionId_ = 0L;

        unionShenHuangId_ = 0;

        unionShenHuangExp_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ResUnionShenHuangInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo getDefaultInstanceForType() {
        return com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo build() {
        com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo buildPartial() {
        com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo result = new com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo(this);
        result.unionId_ = unionId_;
        result.unionShenHuangId_ = unionShenHuangId_;
        result.unionShenHuangExp_ = unionShenHuangExp_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo) {
          return mergeFrom((com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo other) {
        if (other == com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo.getDefaultInstance()) return this;
        if (other.getUnionId() != 0L) {
          setUnionId(other.getUnionId());
        }
        if (other.getUnionShenHuangId() != 0) {
          setUnionShenHuangId(other.getUnionShenHuangId());
        }
        if (other.getUnionShenHuangExp() != 0) {
          setUnionShenHuangExp(other.getUnionShenHuangExp());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long unionId_ ;
      /**
       * <pre>
       *行会id
       * </pre>
       *
       * <code>int64 unionId = 1;</code>
       * @return The unionId.
       */
      @java.lang.Override
      public long getUnionId() {
        return unionId_;
      }
      /**
       * <pre>
       *行会id
       * </pre>
       *
       * <code>int64 unionId = 1;</code>
       * @param value The unionId to set.
       * @return This builder for chaining.
       */
      public Builder setUnionId(long value) {
        
        unionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *行会id
       * </pre>
       *
       * <code>int64 unionId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnionId() {
        
        unionId_ = 0L;
        onChanged();
        return this;
      }

      private int unionShenHuangId_ ;
      /**
       * <pre>
       *行会神皇id
       * </pre>
       *
       * <code>int32 unionShenHuangId = 2;</code>
       * @return The unionShenHuangId.
       */
      @java.lang.Override
      public int getUnionShenHuangId() {
        return unionShenHuangId_;
      }
      /**
       * <pre>
       *行会神皇id
       * </pre>
       *
       * <code>int32 unionShenHuangId = 2;</code>
       * @param value The unionShenHuangId to set.
       * @return This builder for chaining.
       */
      public Builder setUnionShenHuangId(int value) {
        
        unionShenHuangId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *行会神皇id
       * </pre>
       *
       * <code>int32 unionShenHuangId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnionShenHuangId() {
        
        unionShenHuangId_ = 0;
        onChanged();
        return this;
      }

      private int unionShenHuangExp_ ;
      /**
       * <pre>
       *行会神皇经验
       * </pre>
       *
       * <code>int32 unionShenHuangExp = 3;</code>
       * @return The unionShenHuangExp.
       */
      @java.lang.Override
      public int getUnionShenHuangExp() {
        return unionShenHuangExp_;
      }
      /**
       * <pre>
       *行会神皇经验
       * </pre>
       *
       * <code>int32 unionShenHuangExp = 3;</code>
       * @param value The unionShenHuangExp to set.
       * @return This builder for chaining.
       */
      public Builder setUnionShenHuangExp(int value) {
        
        unionShenHuangExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *行会神皇经验
       * </pre>
       *
       * <code>int32 unionShenHuangExp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnionShenHuangExp() {
        
        unionShenHuangExp_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:unionShenHuang.ResUnionShenHuangInfo)
    }

    // @@protoc_insertion_point(class_scope:unionShenHuang.ResUnionShenHuangInfo)
    private static final com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo();
    }

    public static com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResUnionShenHuangInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResUnionShenHuangInfo>() {
      @java.lang.Override
      public ResUnionShenHuangInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResUnionShenHuangInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResUnionShenHuangInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResUnionShenHuangInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.UnionShenHuangProtos.ResUnionShenHuangInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqUnionShenHuangInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:unionShenHuang.ReqUnionShenHuangInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqUnionShenHuangInfo' id='2' desc='请求行会神皇信息' 
   * </pre>
   *
   * Protobuf type {@code unionShenHuang.ReqUnionShenHuangInfo}
   */
  public static final class ReqUnionShenHuangInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:unionShenHuang.ReqUnionShenHuangInfo)
      ReqUnionShenHuangInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqUnionShenHuangInfo.newBuilder() to construct.
    private ReqUnionShenHuangInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqUnionShenHuangInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqUnionShenHuangInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqUnionShenHuangInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo.class, com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo other = (com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqUnionShenHuangInfo' id='2' desc='请求行会神皇信息' 
     * </pre>
     *
     * Protobuf type {@code unionShenHuang.ReqUnionShenHuangInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:unionShenHuang.ReqUnionShenHuangInfo)
        com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo.class, com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo getDefaultInstanceForType() {
        return com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo build() {
        com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo buildPartial() {
        com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo result = new com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo) {
          return mergeFrom((com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo other) {
        if (other == com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:unionShenHuang.ReqUnionShenHuangInfo)
    }

    // @@protoc_insertion_point(class_scope:unionShenHuang.ReqUnionShenHuangInfo)
    private static final com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo();
    }

    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqUnionShenHuangInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqUnionShenHuangInfo>() {
      @java.lang.Override
      public ReqUnionShenHuangInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqUnionShenHuangInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqUnionShenHuangInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqUnionShenHuangInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqUnionShenHuangUpOrBuilder extends
      // @@protoc_insertion_point(interface_extends:unionShenHuang.ReqUnionShenHuangUp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *次数
     * </pre>
     *
     * <code>int32 count = 1;</code>
     * @return The count.
     */
    int getCount();
  }
  /**
   * <pre>
   ** class='ReqUnionShenHuangUp' id='3' desc='请求解锁特戒灵石槽位' 
   * </pre>
   *
   * Protobuf type {@code unionShenHuang.ReqUnionShenHuangUp}
   */
  public static final class ReqUnionShenHuangUp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:unionShenHuang.ReqUnionShenHuangUp)
      ReqUnionShenHuangUpOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqUnionShenHuangUp.newBuilder() to construct.
    private ReqUnionShenHuangUp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqUnionShenHuangUp() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqUnionShenHuangUp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqUnionShenHuangUp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              count_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangUp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangUp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.class, com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.Builder.class);
    }

    public static final int COUNT_FIELD_NUMBER = 1;
    private int count_;
    /**
     * <pre>
     *次数
     * </pre>
     *
     * <code>int32 count = 1;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (count_ != 0) {
        output.writeInt32(1, count_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, count_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp)) {
        return super.equals(obj);
      }
      com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp other = (com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp) obj;

      if (getCount()
          != other.getCount()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqUnionShenHuangUp' id='3' desc='请求解锁特戒灵石槽位' 
     * </pre>
     *
     * Protobuf type {@code unionShenHuang.ReqUnionShenHuangUp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:unionShenHuang.ReqUnionShenHuangUp)
        com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangUp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangUp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.class, com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.Builder.class);
      }

      // Construct using com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        count_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.UnionShenHuangProtos.internal_static_unionShenHuang_ReqUnionShenHuangUp_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp getDefaultInstanceForType() {
        return com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp build() {
        com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp buildPartial() {
        com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp result = new com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp(this);
        result.count_ = count_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp) {
          return mergeFrom((com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp other) {
        if (other == com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp.getDefaultInstance()) return this;
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       *次数
       * </pre>
       *
       * <code>int32 count = 1;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       *次数
       * </pre>
       *
       * <code>int32 count = 1;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *次数
       * </pre>
       *
       * <code>int32 count = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:unionShenHuang.ReqUnionShenHuangUp)
    }

    // @@protoc_insertion_point(class_scope:unionShenHuang.ReqUnionShenHuangUp)
    private static final com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp();
    }

    public static com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqUnionShenHuangUp>
        PARSER = new com.google.protobuf.AbstractParser<ReqUnionShenHuangUp>() {
      @java.lang.Override
      public ReqUnionShenHuangUp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqUnionShenHuangUp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqUnionShenHuangUp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqUnionShenHuangUp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.UnionShenHuangProtos.ReqUnionShenHuangUp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_unionShenHuang_ResUnionShenHuangInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_unionShenHuang_ResUnionShenHuangInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_unionShenHuang_ReqUnionShenHuangInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_unionShenHuang_ReqUnionShenHuangInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_unionShenHuang_ReqUnionShenHuangUp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_unionShenHuang_ReqUnionShenHuangUp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024unionShenHuang.proto\022\016unionShenHuang\"]" +
      "\n\025ResUnionShenHuangInfo\022\017\n\007unionId\030\001 \001(\003" +
      "\022\030\n\020unionShenHuangId\030\002 \001(\005\022\031\n\021unionShenH" +
      "uangExp\030\003 \001(\005\"\027\n\025ReqUnionShenHuangInfo\"$" +
      "\n\023ReqUnionShenHuangUp\022\r\n\005count\030\001 \001(\005B*\n\022" +
      "com.sh.game.protosB\024UnionShenHuangProtos" +
      "b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_unionShenHuang_ResUnionShenHuangInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_unionShenHuang_ResUnionShenHuangInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_unionShenHuang_ResUnionShenHuangInfo_descriptor,
        new java.lang.String[] { "UnionId", "UnionShenHuangId", "UnionShenHuangExp", });
    internal_static_unionShenHuang_ReqUnionShenHuangInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_unionShenHuang_ReqUnionShenHuangInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_unionShenHuang_ReqUnionShenHuangInfo_descriptor,
        new java.lang.String[] { });
    internal_static_unionShenHuang_ReqUnionShenHuangUp_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_unionShenHuang_ReqUnionShenHuangUp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_unionShenHuang_ReqUnionShenHuangUp_descriptor,
        new java.lang.String[] { "Count", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
