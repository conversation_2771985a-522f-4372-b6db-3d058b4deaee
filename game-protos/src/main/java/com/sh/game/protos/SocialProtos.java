// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: social.proto

package com.sh.game.protos;

public final class SocialProtos {
  private SocialProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SocialBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:social.SocialBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *角色id
     * </pre>
     *
     * <code>int64 uid = 1;</code>
     * @return The uid.
     */
    long getUid();

    /**
     * <pre>
     *角色名
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     *角色名
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <pre>
     *职业
     * </pre>
     *
     * <code>int32 career = 3;</code>
     * @return The career.
     */
    int getCareer();

    /**
     * <pre>
     *等级
     * </pre>
     *
     * <code>int32 level = 4;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <pre>
     *行会名
     * </pre>
     *
     * <code>string unionName = 5;</code>
     * @return The unionName.
     */
    java.lang.String getUnionName();
    /**
     * <pre>
     *行会名
     * </pre>
     *
     * <code>string unionName = 5;</code>
     * @return The bytes for unionName.
     */
    com.google.protobuf.ByteString
        getUnionNameBytes();

    /**
     * <pre>
     *离线时间戳 0表示在线
     * </pre>
     *
     * <code>int32 offline = 6;</code>
     * @return The offline.
     */
    int getOffline();

    /**
     * <pre>
     *时间戳秒
     * </pre>
     *
     * <code>int32 time = 7;</code>
     * @return The time.
     */
    int getTime();

    /**
     * <pre>
     *转生等级
     * </pre>
     *
     * <code>int32 zhuansheng = 8;</code>
     * @return The zhuansheng.
     */
    int getZhuansheng();

    /**
     * <pre>
     *地图id
     * </pre>
     *
     * <code>int32 mapId = 9;</code>
     * @return The mapId.
     */
    int getMapId();
  }
  /**
   * Protobuf type {@code social.SocialBean}
   */
  public static final class SocialBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:social.SocialBean)
      SocialBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SocialBean.newBuilder() to construct.
    private SocialBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SocialBean() {
      name_ = "";
      unionName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SocialBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SocialBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              uid_ = input.readInt64();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 24: {

              career_ = input.readInt32();
              break;
            }
            case 32: {

              level_ = input.readInt32();
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              unionName_ = s;
              break;
            }
            case 48: {

              offline_ = input.readInt32();
              break;
            }
            case 56: {

              time_ = input.readInt32();
              break;
            }
            case 64: {

              zhuansheng_ = input.readInt32();
              break;
            }
            case 72: {

              mapId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SocialProtos.internal_static_social_SocialBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SocialProtos.internal_static_social_SocialBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SocialProtos.SocialBean.class, com.sh.game.protos.SocialProtos.SocialBean.Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private long uid_;
    /**
     * <pre>
     *角色id
     * </pre>
     *
     * <code>int64 uid = 1;</code>
     * @return The uid.
     */
    @java.lang.Override
    public long getUid() {
      return uid_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     *角色名
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *角色名
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CAREER_FIELD_NUMBER = 3;
    private int career_;
    /**
     * <pre>
     *职业
     * </pre>
     *
     * <code>int32 career = 3;</code>
     * @return The career.
     */
    @java.lang.Override
    public int getCareer() {
      return career_;
    }

    public static final int LEVEL_FIELD_NUMBER = 4;
    private int level_;
    /**
     * <pre>
     *等级
     * </pre>
     *
     * <code>int32 level = 4;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int UNIONNAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object unionName_;
    /**
     * <pre>
     *行会名
     * </pre>
     *
     * <code>string unionName = 5;</code>
     * @return The unionName.
     */
    @java.lang.Override
    public java.lang.String getUnionName() {
      java.lang.Object ref = unionName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        unionName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *行会名
     * </pre>
     *
     * <code>string unionName = 5;</code>
     * @return The bytes for unionName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUnionNameBytes() {
      java.lang.Object ref = unionName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        unionName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OFFLINE_FIELD_NUMBER = 6;
    private int offline_;
    /**
     * <pre>
     *离线时间戳 0表示在线
     * </pre>
     *
     * <code>int32 offline = 6;</code>
     * @return The offline.
     */
    @java.lang.Override
    public int getOffline() {
      return offline_;
    }

    public static final int TIME_FIELD_NUMBER = 7;
    private int time_;
    /**
     * <pre>
     *时间戳秒
     * </pre>
     *
     * <code>int32 time = 7;</code>
     * @return The time.
     */
    @java.lang.Override
    public int getTime() {
      return time_;
    }

    public static final int ZHUANSHENG_FIELD_NUMBER = 8;
    private int zhuansheng_;
    /**
     * <pre>
     *转生等级
     * </pre>
     *
     * <code>int32 zhuansheng = 8;</code>
     * @return The zhuansheng.
     */
    @java.lang.Override
    public int getZhuansheng() {
      return zhuansheng_;
    }

    public static final int MAPID_FIELD_NUMBER = 9;
    private int mapId_;
    /**
     * <pre>
     *地图id
     * </pre>
     *
     * <code>int32 mapId = 9;</code>
     * @return The mapId.
     */
    @java.lang.Override
    public int getMapId() {
      return mapId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (uid_ != 0L) {
        output.writeInt64(1, uid_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      if (career_ != 0) {
        output.writeInt32(3, career_);
      }
      if (level_ != 0) {
        output.writeInt32(4, level_);
      }
      if (!getUnionNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, unionName_);
      }
      if (offline_ != 0) {
        output.writeInt32(6, offline_);
      }
      if (time_ != 0) {
        output.writeInt32(7, time_);
      }
      if (zhuansheng_ != 0) {
        output.writeInt32(8, zhuansheng_);
      }
      if (mapId_ != 0) {
        output.writeInt32(9, mapId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (uid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, uid_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      if (career_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, career_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, level_);
      }
      if (!getUnionNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, unionName_);
      }
      if (offline_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, offline_);
      }
      if (time_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, time_);
      }
      if (zhuansheng_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, zhuansheng_);
      }
      if (mapId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, mapId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SocialProtos.SocialBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SocialProtos.SocialBean other = (com.sh.game.protos.SocialProtos.SocialBean) obj;

      if (getUid()
          != other.getUid()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (getCareer()
          != other.getCareer()) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (!getUnionName()
          .equals(other.getUnionName())) return false;
      if (getOffline()
          != other.getOffline()) return false;
      if (getTime()
          != other.getTime()) return false;
      if (getZhuansheng()
          != other.getZhuansheng()) return false;
      if (getMapId()
          != other.getMapId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUid());
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + CAREER_FIELD_NUMBER;
      hash = (53 * hash) + getCareer();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (37 * hash) + UNIONNAME_FIELD_NUMBER;
      hash = (53 * hash) + getUnionName().hashCode();
      hash = (37 * hash) + OFFLINE_FIELD_NUMBER;
      hash = (53 * hash) + getOffline();
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + getTime();
      hash = (37 * hash) + ZHUANSHENG_FIELD_NUMBER;
      hash = (53 * hash) + getZhuansheng();
      hash = (37 * hash) + MAPID_FIELD_NUMBER;
      hash = (53 * hash) + getMapId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.SocialBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SocialProtos.SocialBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code social.SocialBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:social.SocialBean)
        com.sh.game.protos.SocialProtos.SocialBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SocialProtos.internal_static_social_SocialBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SocialProtos.internal_static_social_SocialBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SocialProtos.SocialBean.class, com.sh.game.protos.SocialProtos.SocialBean.Builder.class);
      }

      // Construct using com.sh.game.protos.SocialProtos.SocialBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        uid_ = 0L;

        name_ = "";

        career_ = 0;

        level_ = 0;

        unionName_ = "";

        offline_ = 0;

        time_ = 0;

        zhuansheng_ = 0;

        mapId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SocialProtos.internal_static_social_SocialBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.SocialBean getDefaultInstanceForType() {
        return com.sh.game.protos.SocialProtos.SocialBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.SocialBean build() {
        com.sh.game.protos.SocialProtos.SocialBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.SocialBean buildPartial() {
        com.sh.game.protos.SocialProtos.SocialBean result = new com.sh.game.protos.SocialProtos.SocialBean(this);
        result.uid_ = uid_;
        result.name_ = name_;
        result.career_ = career_;
        result.level_ = level_;
        result.unionName_ = unionName_;
        result.offline_ = offline_;
        result.time_ = time_;
        result.zhuansheng_ = zhuansheng_;
        result.mapId_ = mapId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SocialProtos.SocialBean) {
          return mergeFrom((com.sh.game.protos.SocialProtos.SocialBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SocialProtos.SocialBean other) {
        if (other == com.sh.game.protos.SocialProtos.SocialBean.getDefaultInstance()) return this;
        if (other.getUid() != 0L) {
          setUid(other.getUid());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.getCareer() != 0) {
          setCareer(other.getCareer());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (!other.getUnionName().isEmpty()) {
          unionName_ = other.unionName_;
          onChanged();
        }
        if (other.getOffline() != 0) {
          setOffline(other.getOffline());
        }
        if (other.getTime() != 0) {
          setTime(other.getTime());
        }
        if (other.getZhuansheng() != 0) {
          setZhuansheng(other.getZhuansheng());
        }
        if (other.getMapId() != 0) {
          setMapId(other.getMapId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SocialProtos.SocialBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SocialProtos.SocialBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long uid_ ;
      /**
       * <pre>
       *角色id
       * </pre>
       *
       * <code>int64 uid = 1;</code>
       * @return The uid.
       */
      @java.lang.Override
      public long getUid() {
        return uid_;
      }
      /**
       * <pre>
       *角色id
       * </pre>
       *
       * <code>int64 uid = 1;</code>
       * @param value The uid to set.
       * @return This builder for chaining.
       */
      public Builder setUid(long value) {
        
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *角色id
       * </pre>
       *
       * <code>int64 uid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUid() {
        
        uid_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private int career_ ;
      /**
       * <pre>
       *职业
       * </pre>
       *
       * <code>int32 career = 3;</code>
       * @return The career.
       */
      @java.lang.Override
      public int getCareer() {
        return career_;
      }
      /**
       * <pre>
       *职业
       * </pre>
       *
       * <code>int32 career = 3;</code>
       * @param value The career to set.
       * @return This builder for chaining.
       */
      public Builder setCareer(int value) {
        
        career_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *职业
       * </pre>
       *
       * <code>int32 career = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCareer() {
        
        career_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       *等级
       * </pre>
       *
       * <code>int32 level = 4;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       *等级
       * </pre>
       *
       * <code>int32 level = 4;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *等级
       * </pre>
       *
       * <code>int32 level = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object unionName_ = "";
      /**
       * <pre>
       *行会名
       * </pre>
       *
       * <code>string unionName = 5;</code>
       * @return The unionName.
       */
      public java.lang.String getUnionName() {
        java.lang.Object ref = unionName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          unionName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *行会名
       * </pre>
       *
       * <code>string unionName = 5;</code>
       * @return The bytes for unionName.
       */
      public com.google.protobuf.ByteString
          getUnionNameBytes() {
        java.lang.Object ref = unionName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          unionName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *行会名
       * </pre>
       *
       * <code>string unionName = 5;</code>
       * @param value The unionName to set.
       * @return This builder for chaining.
       */
      public Builder setUnionName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        unionName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *行会名
       * </pre>
       *
       * <code>string unionName = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearUnionName() {
        
        unionName_ = getDefaultInstance().getUnionName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *行会名
       * </pre>
       *
       * <code>string unionName = 5;</code>
       * @param value The bytes for unionName to set.
       * @return This builder for chaining.
       */
      public Builder setUnionNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        unionName_ = value;
        onChanged();
        return this;
      }

      private int offline_ ;
      /**
       * <pre>
       *离线时间戳 0表示在线
       * </pre>
       *
       * <code>int32 offline = 6;</code>
       * @return The offline.
       */
      @java.lang.Override
      public int getOffline() {
        return offline_;
      }
      /**
       * <pre>
       *离线时间戳 0表示在线
       * </pre>
       *
       * <code>int32 offline = 6;</code>
       * @param value The offline to set.
       * @return This builder for chaining.
       */
      public Builder setOffline(int value) {
        
        offline_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *离线时间戳 0表示在线
       * </pre>
       *
       * <code>int32 offline = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearOffline() {
        
        offline_ = 0;
        onChanged();
        return this;
      }

      private int time_ ;
      /**
       * <pre>
       *时间戳秒
       * </pre>
       *
       * <code>int32 time = 7;</code>
       * @return The time.
       */
      @java.lang.Override
      public int getTime() {
        return time_;
      }
      /**
       * <pre>
       *时间戳秒
       * </pre>
       *
       * <code>int32 time = 7;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(int value) {
        
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *时间戳秒
       * </pre>
       *
       * <code>int32 time = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        
        time_ = 0;
        onChanged();
        return this;
      }

      private int zhuansheng_ ;
      /**
       * <pre>
       *转生等级
       * </pre>
       *
       * <code>int32 zhuansheng = 8;</code>
       * @return The zhuansheng.
       */
      @java.lang.Override
      public int getZhuansheng() {
        return zhuansheng_;
      }
      /**
       * <pre>
       *转生等级
       * </pre>
       *
       * <code>int32 zhuansheng = 8;</code>
       * @param value The zhuansheng to set.
       * @return This builder for chaining.
       */
      public Builder setZhuansheng(int value) {
        
        zhuansheng_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *转生等级
       * </pre>
       *
       * <code>int32 zhuansheng = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearZhuansheng() {
        
        zhuansheng_ = 0;
        onChanged();
        return this;
      }

      private int mapId_ ;
      /**
       * <pre>
       *地图id
       * </pre>
       *
       * <code>int32 mapId = 9;</code>
       * @return The mapId.
       */
      @java.lang.Override
      public int getMapId() {
        return mapId_;
      }
      /**
       * <pre>
       *地图id
       * </pre>
       *
       * <code>int32 mapId = 9;</code>
       * @param value The mapId to set.
       * @return This builder for chaining.
       */
      public Builder setMapId(int value) {
        
        mapId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *地图id
       * </pre>
       *
       * <code>int32 mapId = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearMapId() {
        
        mapId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:social.SocialBean)
    }

    // @@protoc_insertion_point(class_scope:social.SocialBean)
    private static final com.sh.game.protos.SocialProtos.SocialBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SocialProtos.SocialBean();
    }

    public static com.sh.game.protos.SocialProtos.SocialBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SocialBean>
        PARSER = new com.google.protobuf.AbstractParser<SocialBean>() {
      @java.lang.Override
      public SocialBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SocialBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SocialBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SocialBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SocialProtos.SocialBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ApplyBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:social.ApplyBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *角色id
     * </pre>
     *
     * <code>int64 uid = 1;</code>
     * @return The uid.
     */
    long getUid();

    /**
     * <pre>
     *角色名
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     *角色名
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code social.ApplyBean}
   */
  public static final class ApplyBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:social.ApplyBean)
      ApplyBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ApplyBean.newBuilder() to construct.
    private ApplyBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ApplyBean() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ApplyBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ApplyBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              uid_ = input.readInt64();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ApplyBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ApplyBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SocialProtos.ApplyBean.class, com.sh.game.protos.SocialProtos.ApplyBean.Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    private long uid_;
    /**
     * <pre>
     *角色id
     * </pre>
     *
     * <code>int64 uid = 1;</code>
     * @return The uid.
     */
    @java.lang.Override
    public long getUid() {
      return uid_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     *角色名
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *角色名
     * </pre>
     *
     * <code>string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (uid_ != 0L) {
        output.writeInt64(1, uid_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (uid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, uid_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SocialProtos.ApplyBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SocialProtos.ApplyBean other = (com.sh.game.protos.SocialProtos.ApplyBean) obj;

      if (getUid()
          != other.getUid()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUid());
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ApplyBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SocialProtos.ApplyBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code social.ApplyBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:social.ApplyBean)
        com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ApplyBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ApplyBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SocialProtos.ApplyBean.class, com.sh.game.protos.SocialProtos.ApplyBean.Builder.class);
      }

      // Construct using com.sh.game.protos.SocialProtos.ApplyBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        uid_ = 0L;

        name_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ApplyBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ApplyBean getDefaultInstanceForType() {
        return com.sh.game.protos.SocialProtos.ApplyBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ApplyBean build() {
        com.sh.game.protos.SocialProtos.ApplyBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ApplyBean buildPartial() {
        com.sh.game.protos.SocialProtos.ApplyBean result = new com.sh.game.protos.SocialProtos.ApplyBean(this);
        result.uid_ = uid_;
        result.name_ = name_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SocialProtos.ApplyBean) {
          return mergeFrom((com.sh.game.protos.SocialProtos.ApplyBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SocialProtos.ApplyBean other) {
        if (other == com.sh.game.protos.SocialProtos.ApplyBean.getDefaultInstance()) return this;
        if (other.getUid() != 0L) {
          setUid(other.getUid());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SocialProtos.ApplyBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SocialProtos.ApplyBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long uid_ ;
      /**
       * <pre>
       *角色id
       * </pre>
       *
       * <code>int64 uid = 1;</code>
       * @return The uid.
       */
      @java.lang.Override
      public long getUid() {
        return uid_;
      }
      /**
       * <pre>
       *角色id
       * </pre>
       *
       * <code>int64 uid = 1;</code>
       * @param value The uid to set.
       * @return This builder for chaining.
       */
      public Builder setUid(long value) {
        
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *角色id
       * </pre>
       *
       * <code>int64 uid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUid() {
        
        uid_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *角色名
       * </pre>
       *
       * <code>string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:social.ApplyBean)
    }

    // @@protoc_insertion_point(class_scope:social.ApplyBean)
    private static final com.sh.game.protos.SocialProtos.ApplyBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SocialProtos.ApplyBean();
    }

    public static com.sh.game.protos.SocialProtos.ApplyBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ApplyBean>
        PARSER = new com.google.protobuf.AbstractParser<ApplyBean>() {
      @java.lang.Override
      public ApplyBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ApplyBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ApplyBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ApplyBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ApplyBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqSocialOpenPanelOrBuilder extends
      // @@protoc_insertion_point(interface_extends:social.ReqSocialOpenPanel)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ReqSocialOpenPanel' id='1' desc='请求打开社交面板' 
   * </pre>
   *
   * Protobuf type {@code social.ReqSocialOpenPanel}
   */
  public static final class ReqSocialOpenPanel extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:social.ReqSocialOpenPanel)
      ReqSocialOpenPanelOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqSocialOpenPanel.newBuilder() to construct.
    private ReqSocialOpenPanel(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqSocialOpenPanel() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqSocialOpenPanel();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqSocialOpenPanel(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialOpenPanel_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialOpenPanel_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SocialProtos.ReqSocialOpenPanel.class, com.sh.game.protos.SocialProtos.ReqSocialOpenPanel.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SocialProtos.ReqSocialOpenPanel)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SocialProtos.ReqSocialOpenPanel other = (com.sh.game.protos.SocialProtos.ReqSocialOpenPanel) obj;

      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SocialProtos.ReqSocialOpenPanel prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqSocialOpenPanel' id='1' desc='请求打开社交面板' 
     * </pre>
     *
     * Protobuf type {@code social.ReqSocialOpenPanel}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:social.ReqSocialOpenPanel)
        com.sh.game.protos.SocialProtos.ReqSocialOpenPanelOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialOpenPanel_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialOpenPanel_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SocialProtos.ReqSocialOpenPanel.class, com.sh.game.protos.SocialProtos.ReqSocialOpenPanel.Builder.class);
      }

      // Construct using com.sh.game.protos.SocialProtos.ReqSocialOpenPanel.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialOpenPanel_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqSocialOpenPanel getDefaultInstanceForType() {
        return com.sh.game.protos.SocialProtos.ReqSocialOpenPanel.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqSocialOpenPanel build() {
        com.sh.game.protos.SocialProtos.ReqSocialOpenPanel result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqSocialOpenPanel buildPartial() {
        com.sh.game.protos.SocialProtos.ReqSocialOpenPanel result = new com.sh.game.protos.SocialProtos.ReqSocialOpenPanel(this);
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SocialProtos.ReqSocialOpenPanel) {
          return mergeFrom((com.sh.game.protos.SocialProtos.ReqSocialOpenPanel)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SocialProtos.ReqSocialOpenPanel other) {
        if (other == com.sh.game.protos.SocialProtos.ReqSocialOpenPanel.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SocialProtos.ReqSocialOpenPanel parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SocialProtos.ReqSocialOpenPanel) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:social.ReqSocialOpenPanel)
    }

    // @@protoc_insertion_point(class_scope:social.ReqSocialOpenPanel)
    private static final com.sh.game.protos.SocialProtos.ReqSocialOpenPanel DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SocialProtos.ReqSocialOpenPanel();
    }

    public static com.sh.game.protos.SocialProtos.ReqSocialOpenPanel getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqSocialOpenPanel>
        PARSER = new com.google.protobuf.AbstractParser<ReqSocialOpenPanel>() {
      @java.lang.Override
      public ReqSocialOpenPanel parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqSocialOpenPanel(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqSocialOpenPanel> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqSocialOpenPanel> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ReqSocialOpenPanel getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResSocialInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:social.ResSocialInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    java.util.List<com.sh.game.protos.SocialProtos.SocialBean> 
        getSocialListList();
    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    com.sh.game.protos.SocialProtos.SocialBean getSocialList(int index);
    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    int getSocialListCount();
    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    java.util.List<? extends com.sh.game.protos.SocialProtos.SocialBeanOrBuilder> 
        getSocialListOrBuilderList();
    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    com.sh.game.protos.SocialProtos.SocialBeanOrBuilder getSocialListOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResSocialInfo' id='2' desc='返回社交列表' 
   * </pre>
   *
   * Protobuf type {@code social.ResSocialInfo}
   */
  public static final class ResSocialInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:social.ResSocialInfo)
      ResSocialInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResSocialInfo.newBuilder() to construct.
    private ResSocialInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResSocialInfo() {
      socialList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResSocialInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResSocialInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                socialList_ = new java.util.ArrayList<com.sh.game.protos.SocialProtos.SocialBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              socialList_.add(
                  input.readMessage(com.sh.game.protos.SocialProtos.SocialBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          socialList_ = java.util.Collections.unmodifiableList(socialList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SocialProtos.ResSocialInfo.class, com.sh.game.protos.SocialProtos.ResSocialInfo.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int SOCIALLIST_FIELD_NUMBER = 2;
    private java.util.List<com.sh.game.protos.SocialProtos.SocialBean> socialList_;
    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.SocialProtos.SocialBean> getSocialListList() {
      return socialList_;
    }
    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.SocialProtos.SocialBeanOrBuilder> 
        getSocialListOrBuilderList() {
      return socialList_;
    }
    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    @java.lang.Override
    public int getSocialListCount() {
      return socialList_.size();
    }
    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.SocialProtos.SocialBean getSocialList(int index) {
      return socialList_.get(index);
    }
    /**
     * <pre>
     *社交名单
     * </pre>
     *
     * <code>repeated .social.SocialBean socialList = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.SocialProtos.SocialBeanOrBuilder getSocialListOrBuilder(
        int index) {
      return socialList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      for (int i = 0; i < socialList_.size(); i++) {
        output.writeMessage(2, socialList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      for (int i = 0; i < socialList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, socialList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SocialProtos.ResSocialInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SocialProtos.ResSocialInfo other = (com.sh.game.protos.SocialProtos.ResSocialInfo) obj;

      if (getType()
          != other.getType()) return false;
      if (!getSocialListList()
          .equals(other.getSocialListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      if (getSocialListCount() > 0) {
        hash = (37 * hash) + SOCIALLIST_FIELD_NUMBER;
        hash = (53 * hash) + getSocialListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SocialProtos.ResSocialInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResSocialInfo' id='2' desc='返回社交列表' 
     * </pre>
     *
     * Protobuf type {@code social.ResSocialInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:social.ResSocialInfo)
        com.sh.game.protos.SocialProtos.ResSocialInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SocialProtos.ResSocialInfo.class, com.sh.game.protos.SocialProtos.ResSocialInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.SocialProtos.ResSocialInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSocialListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        if (socialListBuilder_ == null) {
          socialList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          socialListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ResSocialInfo getDefaultInstanceForType() {
        return com.sh.game.protos.SocialProtos.ResSocialInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ResSocialInfo build() {
        com.sh.game.protos.SocialProtos.ResSocialInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ResSocialInfo buildPartial() {
        com.sh.game.protos.SocialProtos.ResSocialInfo result = new com.sh.game.protos.SocialProtos.ResSocialInfo(this);
        int from_bitField0_ = bitField0_;
        result.type_ = type_;
        if (socialListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            socialList_ = java.util.Collections.unmodifiableList(socialList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.socialList_ = socialList_;
        } else {
          result.socialList_ = socialListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SocialProtos.ResSocialInfo) {
          return mergeFrom((com.sh.game.protos.SocialProtos.ResSocialInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SocialProtos.ResSocialInfo other) {
        if (other == com.sh.game.protos.SocialProtos.ResSocialInfo.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (socialListBuilder_ == null) {
          if (!other.socialList_.isEmpty()) {
            if (socialList_.isEmpty()) {
              socialList_ = other.socialList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSocialListIsMutable();
              socialList_.addAll(other.socialList_);
            }
            onChanged();
          }
        } else {
          if (!other.socialList_.isEmpty()) {
            if (socialListBuilder_.isEmpty()) {
              socialListBuilder_.dispose();
              socialListBuilder_ = null;
              socialList_ = other.socialList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              socialListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSocialListFieldBuilder() : null;
            } else {
              socialListBuilder_.addAllMessages(other.socialList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SocialProtos.ResSocialInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SocialProtos.ResSocialInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.SocialProtos.SocialBean> socialList_ =
        java.util.Collections.emptyList();
      private void ensureSocialListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          socialList_ = new java.util.ArrayList<com.sh.game.protos.SocialProtos.SocialBean>(socialList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.SocialProtos.SocialBean, com.sh.game.protos.SocialProtos.SocialBean.Builder, com.sh.game.protos.SocialProtos.SocialBeanOrBuilder> socialListBuilder_;

      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public java.util.List<com.sh.game.protos.SocialProtos.SocialBean> getSocialListList() {
        if (socialListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(socialList_);
        } else {
          return socialListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public int getSocialListCount() {
        if (socialListBuilder_ == null) {
          return socialList_.size();
        } else {
          return socialListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public com.sh.game.protos.SocialProtos.SocialBean getSocialList(int index) {
        if (socialListBuilder_ == null) {
          return socialList_.get(index);
        } else {
          return socialListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public Builder setSocialList(
          int index, com.sh.game.protos.SocialProtos.SocialBean value) {
        if (socialListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSocialListIsMutable();
          socialList_.set(index, value);
          onChanged();
        } else {
          socialListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public Builder setSocialList(
          int index, com.sh.game.protos.SocialProtos.SocialBean.Builder builderForValue) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          socialList_.set(index, builderForValue.build());
          onChanged();
        } else {
          socialListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public Builder addSocialList(com.sh.game.protos.SocialProtos.SocialBean value) {
        if (socialListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSocialListIsMutable();
          socialList_.add(value);
          onChanged();
        } else {
          socialListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public Builder addSocialList(
          int index, com.sh.game.protos.SocialProtos.SocialBean value) {
        if (socialListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSocialListIsMutable();
          socialList_.add(index, value);
          onChanged();
        } else {
          socialListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public Builder addSocialList(
          com.sh.game.protos.SocialProtos.SocialBean.Builder builderForValue) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          socialList_.add(builderForValue.build());
          onChanged();
        } else {
          socialListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public Builder addSocialList(
          int index, com.sh.game.protos.SocialProtos.SocialBean.Builder builderForValue) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          socialList_.add(index, builderForValue.build());
          onChanged();
        } else {
          socialListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public Builder addAllSocialList(
          java.lang.Iterable<? extends com.sh.game.protos.SocialProtos.SocialBean> values) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, socialList_);
          onChanged();
        } else {
          socialListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public Builder clearSocialList() {
        if (socialListBuilder_ == null) {
          socialList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          socialListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public Builder removeSocialList(int index) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          socialList_.remove(index);
          onChanged();
        } else {
          socialListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public com.sh.game.protos.SocialProtos.SocialBean.Builder getSocialListBuilder(
          int index) {
        return getSocialListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public com.sh.game.protos.SocialProtos.SocialBeanOrBuilder getSocialListOrBuilder(
          int index) {
        if (socialListBuilder_ == null) {
          return socialList_.get(index);  } else {
          return socialListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public java.util.List<? extends com.sh.game.protos.SocialProtos.SocialBeanOrBuilder> 
           getSocialListOrBuilderList() {
        if (socialListBuilder_ != null) {
          return socialListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(socialList_);
        }
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public com.sh.game.protos.SocialProtos.SocialBean.Builder addSocialListBuilder() {
        return getSocialListFieldBuilder().addBuilder(
            com.sh.game.protos.SocialProtos.SocialBean.getDefaultInstance());
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public com.sh.game.protos.SocialProtos.SocialBean.Builder addSocialListBuilder(
          int index) {
        return getSocialListFieldBuilder().addBuilder(
            index, com.sh.game.protos.SocialProtos.SocialBean.getDefaultInstance());
      }
      /**
       * <pre>
       *社交名单
       * </pre>
       *
       * <code>repeated .social.SocialBean socialList = 2;</code>
       */
      public java.util.List<com.sh.game.protos.SocialProtos.SocialBean.Builder> 
           getSocialListBuilderList() {
        return getSocialListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.SocialProtos.SocialBean, com.sh.game.protos.SocialProtos.SocialBean.Builder, com.sh.game.protos.SocialProtos.SocialBeanOrBuilder> 
          getSocialListFieldBuilder() {
        if (socialListBuilder_ == null) {
          socialListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.SocialProtos.SocialBean, com.sh.game.protos.SocialProtos.SocialBean.Builder, com.sh.game.protos.SocialProtos.SocialBeanOrBuilder>(
                  socialList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          socialList_ = null;
        }
        return socialListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:social.ResSocialInfo)
    }

    // @@protoc_insertion_point(class_scope:social.ResSocialInfo)
    private static final com.sh.game.protos.SocialProtos.ResSocialInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SocialProtos.ResSocialInfo();
    }

    public static com.sh.game.protos.SocialProtos.ResSocialInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResSocialInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResSocialInfo>() {
      @java.lang.Override
      public ResSocialInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResSocialInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResSocialInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResSocialInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ResSocialInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqSocialAddOrBuilder extends
      // @@protoc_insertion_point(interface_extends:social.ReqSocialAdd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *参数，玩家ID或玩家名
     * </pre>
     *
     * <code>string idOrName = 2;</code>
     * @return The idOrName.
     */
    java.lang.String getIdOrName();
    /**
     * <pre>
     *参数，玩家ID或玩家名
     * </pre>
     *
     * <code>string idOrName = 2;</code>
     * @return The bytes for idOrName.
     */
    com.google.protobuf.ByteString
        getIdOrNameBytes();
  }
  /**
   * <pre>
   ** class='ReqSocialAdd' id='3' desc='请求添加社交' 
   * </pre>
   *
   * Protobuf type {@code social.ReqSocialAdd}
   */
  public static final class ReqSocialAdd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:social.ReqSocialAdd)
      ReqSocialAddOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqSocialAdd.newBuilder() to construct.
    private ReqSocialAdd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqSocialAdd() {
      idOrName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqSocialAdd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqSocialAdd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              idOrName_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialAdd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialAdd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SocialProtos.ReqSocialAdd.class, com.sh.game.protos.SocialProtos.ReqSocialAdd.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int IDORNAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object idOrName_;
    /**
     * <pre>
     *参数，玩家ID或玩家名
     * </pre>
     *
     * <code>string idOrName = 2;</code>
     * @return The idOrName.
     */
    @java.lang.Override
    public java.lang.String getIdOrName() {
      java.lang.Object ref = idOrName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        idOrName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *参数，玩家ID或玩家名
     * </pre>
     *
     * <code>string idOrName = 2;</code>
     * @return The bytes for idOrName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIdOrNameBytes() {
      java.lang.Object ref = idOrName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        idOrName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (!getIdOrNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, idOrName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (!getIdOrNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, idOrName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SocialProtos.ReqSocialAdd)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SocialProtos.ReqSocialAdd other = (com.sh.game.protos.SocialProtos.ReqSocialAdd) obj;

      if (getType()
          != other.getType()) return false;
      if (!getIdOrName()
          .equals(other.getIdOrName())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + IDORNAME_FIELD_NUMBER;
      hash = (53 * hash) + getIdOrName().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialAdd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SocialProtos.ReqSocialAdd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqSocialAdd' id='3' desc='请求添加社交' 
     * </pre>
     *
     * Protobuf type {@code social.ReqSocialAdd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:social.ReqSocialAdd)
        com.sh.game.protos.SocialProtos.ReqSocialAddOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialAdd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialAdd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SocialProtos.ReqSocialAdd.class, com.sh.game.protos.SocialProtos.ReqSocialAdd.Builder.class);
      }

      // Construct using com.sh.game.protos.SocialProtos.ReqSocialAdd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        idOrName_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialAdd_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqSocialAdd getDefaultInstanceForType() {
        return com.sh.game.protos.SocialProtos.ReqSocialAdd.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqSocialAdd build() {
        com.sh.game.protos.SocialProtos.ReqSocialAdd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqSocialAdd buildPartial() {
        com.sh.game.protos.SocialProtos.ReqSocialAdd result = new com.sh.game.protos.SocialProtos.ReqSocialAdd(this);
        result.type_ = type_;
        result.idOrName_ = idOrName_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SocialProtos.ReqSocialAdd) {
          return mergeFrom((com.sh.game.protos.SocialProtos.ReqSocialAdd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SocialProtos.ReqSocialAdd other) {
        if (other == com.sh.game.protos.SocialProtos.ReqSocialAdd.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (!other.getIdOrName().isEmpty()) {
          idOrName_ = other.idOrName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SocialProtos.ReqSocialAdd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SocialProtos.ReqSocialAdd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object idOrName_ = "";
      /**
       * <pre>
       *参数，玩家ID或玩家名
       * </pre>
       *
       * <code>string idOrName = 2;</code>
       * @return The idOrName.
       */
      public java.lang.String getIdOrName() {
        java.lang.Object ref = idOrName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          idOrName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *参数，玩家ID或玩家名
       * </pre>
       *
       * <code>string idOrName = 2;</code>
       * @return The bytes for idOrName.
       */
      public com.google.protobuf.ByteString
          getIdOrNameBytes() {
        java.lang.Object ref = idOrName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          idOrName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *参数，玩家ID或玩家名
       * </pre>
       *
       * <code>string idOrName = 2;</code>
       * @param value The idOrName to set.
       * @return This builder for chaining.
       */
      public Builder setIdOrName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        idOrName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *参数，玩家ID或玩家名
       * </pre>
       *
       * <code>string idOrName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIdOrName() {
        
        idOrName_ = getDefaultInstance().getIdOrName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *参数，玩家ID或玩家名
       * </pre>
       *
       * <code>string idOrName = 2;</code>
       * @param value The bytes for idOrName to set.
       * @return This builder for chaining.
       */
      public Builder setIdOrNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        idOrName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:social.ReqSocialAdd)
    }

    // @@protoc_insertion_point(class_scope:social.ReqSocialAdd)
    private static final com.sh.game.protos.SocialProtos.ReqSocialAdd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SocialProtos.ReqSocialAdd();
    }

    public static com.sh.game.protos.SocialProtos.ReqSocialAdd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqSocialAdd>
        PARSER = new com.google.protobuf.AbstractParser<ReqSocialAdd>() {
      @java.lang.Override
      public ReqSocialAdd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqSocialAdd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqSocialAdd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqSocialAdd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ReqSocialAdd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqSocialDeleteOrBuilder extends
      // @@protoc_insertion_point(interface_extends:social.ReqSocialDelete)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *玩家id
     * </pre>
     *
     * <code>int64 uid = 2;</code>
     * @return The uid.
     */
    long getUid();
  }
  /**
   * <pre>
   ** class='ReqSocialDelete' id='4' desc='请求删除社交' 
   * </pre>
   *
   * Protobuf type {@code social.ReqSocialDelete}
   */
  public static final class ReqSocialDelete extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:social.ReqSocialDelete)
      ReqSocialDeleteOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqSocialDelete.newBuilder() to construct.
    private ReqSocialDelete(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqSocialDelete() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqSocialDelete();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqSocialDelete(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 16: {

              uid_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialDelete_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialDelete_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SocialProtos.ReqSocialDelete.class, com.sh.game.protos.SocialProtos.ReqSocialDelete.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int UID_FIELD_NUMBER = 2;
    private long uid_;
    /**
     * <pre>
     *玩家id
     * </pre>
     *
     * <code>int64 uid = 2;</code>
     * @return The uid.
     */
    @java.lang.Override
    public long getUid() {
      return uid_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (uid_ != 0L) {
        output.writeInt64(2, uid_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (uid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, uid_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SocialProtos.ReqSocialDelete)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SocialProtos.ReqSocialDelete other = (com.sh.game.protos.SocialProtos.ReqSocialDelete) obj;

      if (getType()
          != other.getType()) return false;
      if (getUid()
          != other.getUid()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUid());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqSocialDelete parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SocialProtos.ReqSocialDelete prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqSocialDelete' id='4' desc='请求删除社交' 
     * </pre>
     *
     * Protobuf type {@code social.ReqSocialDelete}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:social.ReqSocialDelete)
        com.sh.game.protos.SocialProtos.ReqSocialDeleteOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialDelete_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialDelete_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SocialProtos.ReqSocialDelete.class, com.sh.game.protos.SocialProtos.ReqSocialDelete.Builder.class);
      }

      // Construct using com.sh.game.protos.SocialProtos.ReqSocialDelete.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        uid_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqSocialDelete_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqSocialDelete getDefaultInstanceForType() {
        return com.sh.game.protos.SocialProtos.ReqSocialDelete.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqSocialDelete build() {
        com.sh.game.protos.SocialProtos.ReqSocialDelete result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqSocialDelete buildPartial() {
        com.sh.game.protos.SocialProtos.ReqSocialDelete result = new com.sh.game.protos.SocialProtos.ReqSocialDelete(this);
        result.type_ = type_;
        result.uid_ = uid_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SocialProtos.ReqSocialDelete) {
          return mergeFrom((com.sh.game.protos.SocialProtos.ReqSocialDelete)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SocialProtos.ReqSocialDelete other) {
        if (other == com.sh.game.protos.SocialProtos.ReqSocialDelete.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getUid() != 0L) {
          setUid(other.getUid());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SocialProtos.ReqSocialDelete parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SocialProtos.ReqSocialDelete) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private long uid_ ;
      /**
       * <pre>
       *玩家id
       * </pre>
       *
       * <code>int64 uid = 2;</code>
       * @return The uid.
       */
      @java.lang.Override
      public long getUid() {
        return uid_;
      }
      /**
       * <pre>
       *玩家id
       * </pre>
       *
       * <code>int64 uid = 2;</code>
       * @param value The uid to set.
       * @return This builder for chaining.
       */
      public Builder setUid(long value) {
        
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家id
       * </pre>
       *
       * <code>int64 uid = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUid() {
        
        uid_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:social.ReqSocialDelete)
    }

    // @@protoc_insertion_point(class_scope:social.ReqSocialDelete)
    private static final com.sh.game.protos.SocialProtos.ReqSocialDelete DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SocialProtos.ReqSocialDelete();
    }

    public static com.sh.game.protos.SocialProtos.ReqSocialDelete getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqSocialDelete>
        PARSER = new com.google.protobuf.AbstractParser<ReqSocialDelete>() {
      @java.lang.Override
      public ReqSocialDelete parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqSocialDelete(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqSocialDelete> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqSocialDelete> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ReqSocialDelete getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResSocialChangeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:social.ResSocialChange)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *1添加，2移除
     * </pre>
     *
     * <code>int32 addOrRemove = 2;</code>
     * @return The addOrRemove.
     */
    int getAddOrRemove();

    /**
     * <pre>
     *社交
     * </pre>
     *
     * <code>.social.SocialBean friend = 3;</code>
     * @return Whether the friend field is set.
     */
    boolean hasFriend();
    /**
     * <pre>
     *社交
     * </pre>
     *
     * <code>.social.SocialBean friend = 3;</code>
     * @return The friend.
     */
    com.sh.game.protos.SocialProtos.SocialBean getFriend();
    /**
     * <pre>
     *社交
     * </pre>
     *
     * <code>.social.SocialBean friend = 3;</code>
     */
    com.sh.game.protos.SocialProtos.SocialBeanOrBuilder getFriendOrBuilder();
  }
  /**
   * <pre>
   ** class='ResSocialChange' id='5' desc='返回社交变化' 
   * </pre>
   *
   * Protobuf type {@code social.ResSocialChange}
   */
  public static final class ResSocialChange extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:social.ResSocialChange)
      ResSocialChangeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResSocialChange.newBuilder() to construct.
    private ResSocialChange(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResSocialChange() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResSocialChange();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResSocialChange(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 16: {

              addOrRemove_ = input.readInt32();
              break;
            }
            case 26: {
              com.sh.game.protos.SocialProtos.SocialBean.Builder subBuilder = null;
              if (friend_ != null) {
                subBuilder = friend_.toBuilder();
              }
              friend_ = input.readMessage(com.sh.game.protos.SocialProtos.SocialBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(friend_);
                friend_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialChange_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialChange_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SocialProtos.ResSocialChange.class, com.sh.game.protos.SocialProtos.ResSocialChange.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *1好友，2黑名单
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int ADDORREMOVE_FIELD_NUMBER = 2;
    private int addOrRemove_;
    /**
     * <pre>
     *1添加，2移除
     * </pre>
     *
     * <code>int32 addOrRemove = 2;</code>
     * @return The addOrRemove.
     */
    @java.lang.Override
    public int getAddOrRemove() {
      return addOrRemove_;
    }

    public static final int FRIEND_FIELD_NUMBER = 3;
    private com.sh.game.protos.SocialProtos.SocialBean friend_;
    /**
     * <pre>
     *社交
     * </pre>
     *
     * <code>.social.SocialBean friend = 3;</code>
     * @return Whether the friend field is set.
     */
    @java.lang.Override
    public boolean hasFriend() {
      return friend_ != null;
    }
    /**
     * <pre>
     *社交
     * </pre>
     *
     * <code>.social.SocialBean friend = 3;</code>
     * @return The friend.
     */
    @java.lang.Override
    public com.sh.game.protos.SocialProtos.SocialBean getFriend() {
      return friend_ == null ? com.sh.game.protos.SocialProtos.SocialBean.getDefaultInstance() : friend_;
    }
    /**
     * <pre>
     *社交
     * </pre>
     *
     * <code>.social.SocialBean friend = 3;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.SocialProtos.SocialBeanOrBuilder getFriendOrBuilder() {
      return getFriend();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (addOrRemove_ != 0) {
        output.writeInt32(2, addOrRemove_);
      }
      if (friend_ != null) {
        output.writeMessage(3, getFriend());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (addOrRemove_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, addOrRemove_);
      }
      if (friend_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getFriend());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SocialProtos.ResSocialChange)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SocialProtos.ResSocialChange other = (com.sh.game.protos.SocialProtos.ResSocialChange) obj;

      if (getType()
          != other.getType()) return false;
      if (getAddOrRemove()
          != other.getAddOrRemove()) return false;
      if (hasFriend() != other.hasFriend()) return false;
      if (hasFriend()) {
        if (!getFriend()
            .equals(other.getFriend())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + ADDORREMOVE_FIELD_NUMBER;
      hash = (53 * hash) + getAddOrRemove();
      if (hasFriend()) {
        hash = (37 * hash) + FRIEND_FIELD_NUMBER;
        hash = (53 * hash) + getFriend().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ResSocialChange parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SocialProtos.ResSocialChange prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResSocialChange' id='5' desc='返回社交变化' 
     * </pre>
     *
     * Protobuf type {@code social.ResSocialChange}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:social.ResSocialChange)
        com.sh.game.protos.SocialProtos.ResSocialChangeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialChange_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialChange_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SocialProtos.ResSocialChange.class, com.sh.game.protos.SocialProtos.ResSocialChange.Builder.class);
      }

      // Construct using com.sh.game.protos.SocialProtos.ResSocialChange.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        addOrRemove_ = 0;

        if (friendBuilder_ == null) {
          friend_ = null;
        } else {
          friend_ = null;
          friendBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ResSocialChange_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ResSocialChange getDefaultInstanceForType() {
        return com.sh.game.protos.SocialProtos.ResSocialChange.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ResSocialChange build() {
        com.sh.game.protos.SocialProtos.ResSocialChange result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ResSocialChange buildPartial() {
        com.sh.game.protos.SocialProtos.ResSocialChange result = new com.sh.game.protos.SocialProtos.ResSocialChange(this);
        result.type_ = type_;
        result.addOrRemove_ = addOrRemove_;
        if (friendBuilder_ == null) {
          result.friend_ = friend_;
        } else {
          result.friend_ = friendBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SocialProtos.ResSocialChange) {
          return mergeFrom((com.sh.game.protos.SocialProtos.ResSocialChange)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SocialProtos.ResSocialChange other) {
        if (other == com.sh.game.protos.SocialProtos.ResSocialChange.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getAddOrRemove() != 0) {
          setAddOrRemove(other.getAddOrRemove());
        }
        if (other.hasFriend()) {
          mergeFriend(other.getFriend());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SocialProtos.ResSocialChange parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SocialProtos.ResSocialChange) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1好友，2黑名单
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int addOrRemove_ ;
      /**
       * <pre>
       *1添加，2移除
       * </pre>
       *
       * <code>int32 addOrRemove = 2;</code>
       * @return The addOrRemove.
       */
      @java.lang.Override
      public int getAddOrRemove() {
        return addOrRemove_;
      }
      /**
       * <pre>
       *1添加，2移除
       * </pre>
       *
       * <code>int32 addOrRemove = 2;</code>
       * @param value The addOrRemove to set.
       * @return This builder for chaining.
       */
      public Builder setAddOrRemove(int value) {
        
        addOrRemove_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *1添加，2移除
       * </pre>
       *
       * <code>int32 addOrRemove = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAddOrRemove() {
        
        addOrRemove_ = 0;
        onChanged();
        return this;
      }

      private com.sh.game.protos.SocialProtos.SocialBean friend_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.SocialProtos.SocialBean, com.sh.game.protos.SocialProtos.SocialBean.Builder, com.sh.game.protos.SocialProtos.SocialBeanOrBuilder> friendBuilder_;
      /**
       * <pre>
       *社交
       * </pre>
       *
       * <code>.social.SocialBean friend = 3;</code>
       * @return Whether the friend field is set.
       */
      public boolean hasFriend() {
        return friendBuilder_ != null || friend_ != null;
      }
      /**
       * <pre>
       *社交
       * </pre>
       *
       * <code>.social.SocialBean friend = 3;</code>
       * @return The friend.
       */
      public com.sh.game.protos.SocialProtos.SocialBean getFriend() {
        if (friendBuilder_ == null) {
          return friend_ == null ? com.sh.game.protos.SocialProtos.SocialBean.getDefaultInstance() : friend_;
        } else {
          return friendBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *社交
       * </pre>
       *
       * <code>.social.SocialBean friend = 3;</code>
       */
      public Builder setFriend(com.sh.game.protos.SocialProtos.SocialBean value) {
        if (friendBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          friend_ = value;
          onChanged();
        } else {
          friendBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *社交
       * </pre>
       *
       * <code>.social.SocialBean friend = 3;</code>
       */
      public Builder setFriend(
          com.sh.game.protos.SocialProtos.SocialBean.Builder builderForValue) {
        if (friendBuilder_ == null) {
          friend_ = builderForValue.build();
          onChanged();
        } else {
          friendBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *社交
       * </pre>
       *
       * <code>.social.SocialBean friend = 3;</code>
       */
      public Builder mergeFriend(com.sh.game.protos.SocialProtos.SocialBean value) {
        if (friendBuilder_ == null) {
          if (friend_ != null) {
            friend_ =
              com.sh.game.protos.SocialProtos.SocialBean.newBuilder(friend_).mergeFrom(value).buildPartial();
          } else {
            friend_ = value;
          }
          onChanged();
        } else {
          friendBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *社交
       * </pre>
       *
       * <code>.social.SocialBean friend = 3;</code>
       */
      public Builder clearFriend() {
        if (friendBuilder_ == null) {
          friend_ = null;
          onChanged();
        } else {
          friend_ = null;
          friendBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *社交
       * </pre>
       *
       * <code>.social.SocialBean friend = 3;</code>
       */
      public com.sh.game.protos.SocialProtos.SocialBean.Builder getFriendBuilder() {
        
        onChanged();
        return getFriendFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *社交
       * </pre>
       *
       * <code>.social.SocialBean friend = 3;</code>
       */
      public com.sh.game.protos.SocialProtos.SocialBeanOrBuilder getFriendOrBuilder() {
        if (friendBuilder_ != null) {
          return friendBuilder_.getMessageOrBuilder();
        } else {
          return friend_ == null ?
              com.sh.game.protos.SocialProtos.SocialBean.getDefaultInstance() : friend_;
        }
      }
      /**
       * <pre>
       *社交
       * </pre>
       *
       * <code>.social.SocialBean friend = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.SocialProtos.SocialBean, com.sh.game.protos.SocialProtos.SocialBean.Builder, com.sh.game.protos.SocialProtos.SocialBeanOrBuilder> 
          getFriendFieldBuilder() {
        if (friendBuilder_ == null) {
          friendBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.SocialProtos.SocialBean, com.sh.game.protos.SocialProtos.SocialBean.Builder, com.sh.game.protos.SocialProtos.SocialBeanOrBuilder>(
                  getFriend(),
                  getParentForChildren(),
                  isClean());
          friend_ = null;
        }
        return friendBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:social.ResSocialChange)
    }

    // @@protoc_insertion_point(class_scope:social.ResSocialChange)
    private static final com.sh.game.protos.SocialProtos.ResSocialChange DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SocialProtos.ResSocialChange();
    }

    public static com.sh.game.protos.SocialProtos.ResSocialChange getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResSocialChange>
        PARSER = new com.google.protobuf.AbstractParser<ResSocialChange>() {
      @java.lang.Override
      public ResSocialChange parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResSocialChange(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResSocialChange> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResSocialChange> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ResSocialChange getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResFriendApplyListOrBuilder extends
      // @@protoc_insertion_point(interface_extends:social.ResFriendApplyList)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    java.util.List<com.sh.game.protos.SocialProtos.ApplyBean> 
        getSocialListList();
    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    com.sh.game.protos.SocialProtos.ApplyBean getSocialList(int index);
    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    int getSocialListCount();
    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    java.util.List<? extends com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder> 
        getSocialListOrBuilderList();
    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder getSocialListOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResFriendApplyList' id='6' desc='返回好友申请列表' 
   * </pre>
   *
   * Protobuf type {@code social.ResFriendApplyList}
   */
  public static final class ResFriendApplyList extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:social.ResFriendApplyList)
      ResFriendApplyListOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResFriendApplyList.newBuilder() to construct.
    private ResFriendApplyList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResFriendApplyList() {
      socialList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResFriendApplyList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResFriendApplyList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                socialList_ = new java.util.ArrayList<com.sh.game.protos.SocialProtos.ApplyBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              socialList_.add(
                  input.readMessage(com.sh.game.protos.SocialProtos.ApplyBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          socialList_ = java.util.Collections.unmodifiableList(socialList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ResFriendApplyList_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ResFriendApplyList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SocialProtos.ResFriendApplyList.class, com.sh.game.protos.SocialProtos.ResFriendApplyList.Builder.class);
    }

    public static final int SOCIALLIST_FIELD_NUMBER = 1;
    private java.util.List<com.sh.game.protos.SocialProtos.ApplyBean> socialList_;
    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.SocialProtos.ApplyBean> getSocialListList() {
      return socialList_;
    }
    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder> 
        getSocialListOrBuilderList() {
      return socialList_;
    }
    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    @java.lang.Override
    public int getSocialListCount() {
      return socialList_.size();
    }
    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ApplyBean getSocialList(int index) {
      return socialList_.get(index);
    }
    /**
     * <pre>
     *申请名单
     * </pre>
     *
     * <code>repeated .social.ApplyBean socialList = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder getSocialListOrBuilder(
        int index) {
      return socialList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < socialList_.size(); i++) {
        output.writeMessage(1, socialList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < socialList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, socialList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SocialProtos.ResFriendApplyList)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SocialProtos.ResFriendApplyList other = (com.sh.game.protos.SocialProtos.ResFriendApplyList) obj;

      if (!getSocialListList()
          .equals(other.getSocialListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getSocialListCount() > 0) {
        hash = (37 * hash) + SOCIALLIST_FIELD_NUMBER;
        hash = (53 * hash) + getSocialListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ResFriendApplyList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SocialProtos.ResFriendApplyList prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResFriendApplyList' id='6' desc='返回好友申请列表' 
     * </pre>
     *
     * Protobuf type {@code social.ResFriendApplyList}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:social.ResFriendApplyList)
        com.sh.game.protos.SocialProtos.ResFriendApplyListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ResFriendApplyList_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ResFriendApplyList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SocialProtos.ResFriendApplyList.class, com.sh.game.protos.SocialProtos.ResFriendApplyList.Builder.class);
      }

      // Construct using com.sh.game.protos.SocialProtos.ResFriendApplyList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSocialListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (socialListBuilder_ == null) {
          socialList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          socialListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ResFriendApplyList_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ResFriendApplyList getDefaultInstanceForType() {
        return com.sh.game.protos.SocialProtos.ResFriendApplyList.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ResFriendApplyList build() {
        com.sh.game.protos.SocialProtos.ResFriendApplyList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ResFriendApplyList buildPartial() {
        com.sh.game.protos.SocialProtos.ResFriendApplyList result = new com.sh.game.protos.SocialProtos.ResFriendApplyList(this);
        int from_bitField0_ = bitField0_;
        if (socialListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            socialList_ = java.util.Collections.unmodifiableList(socialList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.socialList_ = socialList_;
        } else {
          result.socialList_ = socialListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SocialProtos.ResFriendApplyList) {
          return mergeFrom((com.sh.game.protos.SocialProtos.ResFriendApplyList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SocialProtos.ResFriendApplyList other) {
        if (other == com.sh.game.protos.SocialProtos.ResFriendApplyList.getDefaultInstance()) return this;
        if (socialListBuilder_ == null) {
          if (!other.socialList_.isEmpty()) {
            if (socialList_.isEmpty()) {
              socialList_ = other.socialList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSocialListIsMutable();
              socialList_.addAll(other.socialList_);
            }
            onChanged();
          }
        } else {
          if (!other.socialList_.isEmpty()) {
            if (socialListBuilder_.isEmpty()) {
              socialListBuilder_.dispose();
              socialListBuilder_ = null;
              socialList_ = other.socialList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              socialListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSocialListFieldBuilder() : null;
            } else {
              socialListBuilder_.addAllMessages(other.socialList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SocialProtos.ResFriendApplyList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SocialProtos.ResFriendApplyList) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.sh.game.protos.SocialProtos.ApplyBean> socialList_ =
        java.util.Collections.emptyList();
      private void ensureSocialListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          socialList_ = new java.util.ArrayList<com.sh.game.protos.SocialProtos.ApplyBean>(socialList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.SocialProtos.ApplyBean, com.sh.game.protos.SocialProtos.ApplyBean.Builder, com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder> socialListBuilder_;

      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public java.util.List<com.sh.game.protos.SocialProtos.ApplyBean> getSocialListList() {
        if (socialListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(socialList_);
        } else {
          return socialListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public int getSocialListCount() {
        if (socialListBuilder_ == null) {
          return socialList_.size();
        } else {
          return socialListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public com.sh.game.protos.SocialProtos.ApplyBean getSocialList(int index) {
        if (socialListBuilder_ == null) {
          return socialList_.get(index);
        } else {
          return socialListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public Builder setSocialList(
          int index, com.sh.game.protos.SocialProtos.ApplyBean value) {
        if (socialListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSocialListIsMutable();
          socialList_.set(index, value);
          onChanged();
        } else {
          socialListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public Builder setSocialList(
          int index, com.sh.game.protos.SocialProtos.ApplyBean.Builder builderForValue) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          socialList_.set(index, builderForValue.build());
          onChanged();
        } else {
          socialListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public Builder addSocialList(com.sh.game.protos.SocialProtos.ApplyBean value) {
        if (socialListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSocialListIsMutable();
          socialList_.add(value);
          onChanged();
        } else {
          socialListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public Builder addSocialList(
          int index, com.sh.game.protos.SocialProtos.ApplyBean value) {
        if (socialListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSocialListIsMutable();
          socialList_.add(index, value);
          onChanged();
        } else {
          socialListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public Builder addSocialList(
          com.sh.game.protos.SocialProtos.ApplyBean.Builder builderForValue) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          socialList_.add(builderForValue.build());
          onChanged();
        } else {
          socialListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public Builder addSocialList(
          int index, com.sh.game.protos.SocialProtos.ApplyBean.Builder builderForValue) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          socialList_.add(index, builderForValue.build());
          onChanged();
        } else {
          socialListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public Builder addAllSocialList(
          java.lang.Iterable<? extends com.sh.game.protos.SocialProtos.ApplyBean> values) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, socialList_);
          onChanged();
        } else {
          socialListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public Builder clearSocialList() {
        if (socialListBuilder_ == null) {
          socialList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          socialListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public Builder removeSocialList(int index) {
        if (socialListBuilder_ == null) {
          ensureSocialListIsMutable();
          socialList_.remove(index);
          onChanged();
        } else {
          socialListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public com.sh.game.protos.SocialProtos.ApplyBean.Builder getSocialListBuilder(
          int index) {
        return getSocialListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder getSocialListOrBuilder(
          int index) {
        if (socialListBuilder_ == null) {
          return socialList_.get(index);  } else {
          return socialListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public java.util.List<? extends com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder> 
           getSocialListOrBuilderList() {
        if (socialListBuilder_ != null) {
          return socialListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(socialList_);
        }
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public com.sh.game.protos.SocialProtos.ApplyBean.Builder addSocialListBuilder() {
        return getSocialListFieldBuilder().addBuilder(
            com.sh.game.protos.SocialProtos.ApplyBean.getDefaultInstance());
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public com.sh.game.protos.SocialProtos.ApplyBean.Builder addSocialListBuilder(
          int index) {
        return getSocialListFieldBuilder().addBuilder(
            index, com.sh.game.protos.SocialProtos.ApplyBean.getDefaultInstance());
      }
      /**
       * <pre>
       *申请名单
       * </pre>
       *
       * <code>repeated .social.ApplyBean socialList = 1;</code>
       */
      public java.util.List<com.sh.game.protos.SocialProtos.ApplyBean.Builder> 
           getSocialListBuilderList() {
        return getSocialListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.SocialProtos.ApplyBean, com.sh.game.protos.SocialProtos.ApplyBean.Builder, com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder> 
          getSocialListFieldBuilder() {
        if (socialListBuilder_ == null) {
          socialListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.SocialProtos.ApplyBean, com.sh.game.protos.SocialProtos.ApplyBean.Builder, com.sh.game.protos.SocialProtos.ApplyBeanOrBuilder>(
                  socialList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          socialList_ = null;
        }
        return socialListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:social.ResFriendApplyList)
    }

    // @@protoc_insertion_point(class_scope:social.ResFriendApplyList)
    private static final com.sh.game.protos.SocialProtos.ResFriendApplyList DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SocialProtos.ResFriendApplyList();
    }

    public static com.sh.game.protos.SocialProtos.ResFriendApplyList getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResFriendApplyList>
        PARSER = new com.google.protobuf.AbstractParser<ResFriendApplyList>() {
      @java.lang.Override
      public ResFriendApplyList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResFriendApplyList(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResFriendApplyList> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResFriendApplyList> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ResFriendApplyList getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFriendApplyOpOrBuilder extends
      // @@protoc_insertion_point(interface_extends:social.ReqFriendApplyOp)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *0同意 1拒绝
     * </pre>
     *
     * <code>int32 op = 1;</code>
     * @return The op.
     */
    int getOp();

    /**
     * <pre>
     *列表玩家id
     * </pre>
     *
     * <code>repeated int64 targetId = 2;</code>
     * @return A list containing the targetId.
     */
    java.util.List<java.lang.Long> getTargetIdList();
    /**
     * <pre>
     *列表玩家id
     * </pre>
     *
     * <code>repeated int64 targetId = 2;</code>
     * @return The count of targetId.
     */
    int getTargetIdCount();
    /**
     * <pre>
     *列表玩家id
     * </pre>
     *
     * <code>repeated int64 targetId = 2;</code>
     * @param index The index of the element to return.
     * @return The targetId at the given index.
     */
    long getTargetId(int index);
  }
  /**
   * <pre>
   ** class='ReqFriendApplyOp' id='7' desc='好友申请列表操作' 
   * </pre>
   *
   * Protobuf type {@code social.ReqFriendApplyOp}
   */
  public static final class ReqFriendApplyOp extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:social.ReqFriendApplyOp)
      ReqFriendApplyOpOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFriendApplyOp.newBuilder() to construct.
    private ReqFriendApplyOp(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFriendApplyOp() {
      targetId_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFriendApplyOp();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFriendApplyOp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              op_ = input.readInt32();
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                targetId_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              targetId_.addLong(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                targetId_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                targetId_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          targetId_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ReqFriendApplyOp_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SocialProtos.internal_static_social_ReqFriendApplyOp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SocialProtos.ReqFriendApplyOp.class, com.sh.game.protos.SocialProtos.ReqFriendApplyOp.Builder.class);
    }

    public static final int OP_FIELD_NUMBER = 1;
    private int op_;
    /**
     * <pre>
     *0同意 1拒绝
     * </pre>
     *
     * <code>int32 op = 1;</code>
     * @return The op.
     */
    @java.lang.Override
    public int getOp() {
      return op_;
    }

    public static final int TARGETID_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList targetId_;
    /**
     * <pre>
     *列表玩家id
     * </pre>
     *
     * <code>repeated int64 targetId = 2;</code>
     * @return A list containing the targetId.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getTargetIdList() {
      return targetId_;
    }
    /**
     * <pre>
     *列表玩家id
     * </pre>
     *
     * <code>repeated int64 targetId = 2;</code>
     * @return The count of targetId.
     */
    public int getTargetIdCount() {
      return targetId_.size();
    }
    /**
     * <pre>
     *列表玩家id
     * </pre>
     *
     * <code>repeated int64 targetId = 2;</code>
     * @param index The index of the element to return.
     * @return The targetId at the given index.
     */
    public long getTargetId(int index) {
      return targetId_.getLong(index);
    }
    private int targetIdMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (op_ != 0) {
        output.writeInt32(1, op_);
      }
      if (getTargetIdList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(targetIdMemoizedSerializedSize);
      }
      for (int i = 0; i < targetId_.size(); i++) {
        output.writeInt64NoTag(targetId_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (op_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, op_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < targetId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(targetId_.getLong(i));
        }
        size += dataSize;
        if (!getTargetIdList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        targetIdMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SocialProtos.ReqFriendApplyOp)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SocialProtos.ReqFriendApplyOp other = (com.sh.game.protos.SocialProtos.ReqFriendApplyOp) obj;

      if (getOp()
          != other.getOp()) return false;
      if (!getTargetIdList()
          .equals(other.getTargetIdList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + OP_FIELD_NUMBER;
      hash = (53 * hash) + getOp();
      if (getTargetIdCount() > 0) {
        hash = (37 * hash) + TARGETID_FIELD_NUMBER;
        hash = (53 * hash) + getTargetIdList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SocialProtos.ReqFriendApplyOp prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFriendApplyOp' id='7' desc='好友申请列表操作' 
     * </pre>
     *
     * Protobuf type {@code social.ReqFriendApplyOp}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:social.ReqFriendApplyOp)
        com.sh.game.protos.SocialProtos.ReqFriendApplyOpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqFriendApplyOp_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqFriendApplyOp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SocialProtos.ReqFriendApplyOp.class, com.sh.game.protos.SocialProtos.ReqFriendApplyOp.Builder.class);
      }

      // Construct using com.sh.game.protos.SocialProtos.ReqFriendApplyOp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        op_ = 0;

        targetId_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SocialProtos.internal_static_social_ReqFriendApplyOp_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqFriendApplyOp getDefaultInstanceForType() {
        return com.sh.game.protos.SocialProtos.ReqFriendApplyOp.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqFriendApplyOp build() {
        com.sh.game.protos.SocialProtos.ReqFriendApplyOp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SocialProtos.ReqFriendApplyOp buildPartial() {
        com.sh.game.protos.SocialProtos.ReqFriendApplyOp result = new com.sh.game.protos.SocialProtos.ReqFriendApplyOp(this);
        int from_bitField0_ = bitField0_;
        result.op_ = op_;
        if (((bitField0_ & 0x00000001) != 0)) {
          targetId_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.targetId_ = targetId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SocialProtos.ReqFriendApplyOp) {
          return mergeFrom((com.sh.game.protos.SocialProtos.ReqFriendApplyOp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SocialProtos.ReqFriendApplyOp other) {
        if (other == com.sh.game.protos.SocialProtos.ReqFriendApplyOp.getDefaultInstance()) return this;
        if (other.getOp() != 0) {
          setOp(other.getOp());
        }
        if (!other.targetId_.isEmpty()) {
          if (targetId_.isEmpty()) {
            targetId_ = other.targetId_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureTargetIdIsMutable();
            targetId_.addAll(other.targetId_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SocialProtos.ReqFriendApplyOp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SocialProtos.ReqFriendApplyOp) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int op_ ;
      /**
       * <pre>
       *0同意 1拒绝
       * </pre>
       *
       * <code>int32 op = 1;</code>
       * @return The op.
       */
      @java.lang.Override
      public int getOp() {
        return op_;
      }
      /**
       * <pre>
       *0同意 1拒绝
       * </pre>
       *
       * <code>int32 op = 1;</code>
       * @param value The op to set.
       * @return This builder for chaining.
       */
      public Builder setOp(int value) {
        
        op_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *0同意 1拒绝
       * </pre>
       *
       * <code>int32 op = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOp() {
        
        op_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList targetId_ = emptyLongList();
      private void ensureTargetIdIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          targetId_ = mutableCopy(targetId_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       *列表玩家id
       * </pre>
       *
       * <code>repeated int64 targetId = 2;</code>
       * @return A list containing the targetId.
       */
      public java.util.List<java.lang.Long>
          getTargetIdList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(targetId_) : targetId_;
      }
      /**
       * <pre>
       *列表玩家id
       * </pre>
       *
       * <code>repeated int64 targetId = 2;</code>
       * @return The count of targetId.
       */
      public int getTargetIdCount() {
        return targetId_.size();
      }
      /**
       * <pre>
       *列表玩家id
       * </pre>
       *
       * <code>repeated int64 targetId = 2;</code>
       * @param index The index of the element to return.
       * @return The targetId at the given index.
       */
      public long getTargetId(int index) {
        return targetId_.getLong(index);
      }
      /**
       * <pre>
       *列表玩家id
       * </pre>
       *
       * <code>repeated int64 targetId = 2;</code>
       * @param index The index to set the value at.
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(
          int index, long value) {
        ensureTargetIdIsMutable();
        targetId_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *列表玩家id
       * </pre>
       *
       * <code>repeated int64 targetId = 2;</code>
       * @param value The targetId to add.
       * @return This builder for chaining.
       */
      public Builder addTargetId(long value) {
        ensureTargetIdIsMutable();
        targetId_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *列表玩家id
       * </pre>
       *
       * <code>repeated int64 targetId = 2;</code>
       * @param values The targetId to add.
       * @return This builder for chaining.
       */
      public Builder addAllTargetId(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureTargetIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, targetId_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *列表玩家id
       * </pre>
       *
       * <code>repeated int64 targetId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        targetId_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:social.ReqFriendApplyOp)
    }

    // @@protoc_insertion_point(class_scope:social.ReqFriendApplyOp)
    private static final com.sh.game.protos.SocialProtos.ReqFriendApplyOp DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SocialProtos.ReqFriendApplyOp();
    }

    public static com.sh.game.protos.SocialProtos.ReqFriendApplyOp getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFriendApplyOp>
        PARSER = new com.google.protobuf.AbstractParser<ReqFriendApplyOp>() {
      @java.lang.Override
      public ReqFriendApplyOp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFriendApplyOp(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFriendApplyOp> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFriendApplyOp> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SocialProtos.ReqFriendApplyOp getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_social_SocialBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_social_SocialBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_social_ApplyBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_social_ApplyBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_social_ReqSocialOpenPanel_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_social_ReqSocialOpenPanel_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_social_ResSocialInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_social_ResSocialInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_social_ReqSocialAdd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_social_ReqSocialAdd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_social_ReqSocialDelete_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_social_ReqSocialDelete_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_social_ResSocialChange_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_social_ResSocialChange_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_social_ResFriendApplyList_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_social_ResFriendApplyList_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_social_ReqFriendApplyOp_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_social_ReqFriendApplyOp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014social.proto\022\006social\"\233\001\n\nSocialBean\022\013\n" +
      "\003uid\030\001 \001(\003\022\014\n\004name\030\002 \001(\t\022\016\n\006career\030\003 \001(\005" +
      "\022\r\n\005level\030\004 \001(\005\022\021\n\tunionName\030\005 \001(\t\022\017\n\007of" +
      "fline\030\006 \001(\005\022\014\n\004time\030\007 \001(\005\022\022\n\nzhuansheng\030" +
      "\010 \001(\005\022\r\n\005mapId\030\t \001(\005\"&\n\tApplyBean\022\013\n\003uid" +
      "\030\001 \001(\003\022\014\n\004name\030\002 \001(\t\"\"\n\022ReqSocialOpenPan" +
      "el\022\014\n\004type\030\001 \001(\005\"E\n\rResSocialInfo\022\014\n\004typ" +
      "e\030\001 \001(\005\022&\n\nsocialList\030\002 \003(\0132\022.social.Soc" +
      "ialBean\".\n\014ReqSocialAdd\022\014\n\004type\030\001 \001(\005\022\020\n" +
      "\010idOrName\030\002 \001(\t\",\n\017ReqSocialDelete\022\014\n\004ty" +
      "pe\030\001 \001(\005\022\013\n\003uid\030\002 \001(\003\"X\n\017ResSocialChange" +
      "\022\014\n\004type\030\001 \001(\005\022\023\n\013addOrRemove\030\002 \001(\005\022\"\n\006f" +
      "riend\030\003 \001(\0132\022.social.SocialBean\";\n\022ResFr" +
      "iendApplyList\022%\n\nsocialList\030\001 \003(\0132\021.soci" +
      "al.ApplyBean\"0\n\020ReqFriendApplyOp\022\n\n\002op\030\001" +
      " \001(\005\022\020\n\010targetId\030\002 \003(\003B\"\n\022com.sh.game.pr" +
      "otosB\014SocialProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_social_SocialBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_social_SocialBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_social_SocialBean_descriptor,
        new java.lang.String[] { "Uid", "Name", "Career", "Level", "UnionName", "Offline", "Time", "Zhuansheng", "MapId", });
    internal_static_social_ApplyBean_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_social_ApplyBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_social_ApplyBean_descriptor,
        new java.lang.String[] { "Uid", "Name", });
    internal_static_social_ReqSocialOpenPanel_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_social_ReqSocialOpenPanel_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_social_ReqSocialOpenPanel_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_social_ResSocialInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_social_ResSocialInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_social_ResSocialInfo_descriptor,
        new java.lang.String[] { "Type", "SocialList", });
    internal_static_social_ReqSocialAdd_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_social_ReqSocialAdd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_social_ReqSocialAdd_descriptor,
        new java.lang.String[] { "Type", "IdOrName", });
    internal_static_social_ReqSocialDelete_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_social_ReqSocialDelete_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_social_ReqSocialDelete_descriptor,
        new java.lang.String[] { "Type", "Uid", });
    internal_static_social_ResSocialChange_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_social_ResSocialChange_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_social_ResSocialChange_descriptor,
        new java.lang.String[] { "Type", "AddOrRemove", "Friend", });
    internal_static_social_ResFriendApplyList_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_social_ResFriendApplyList_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_social_ResFriendApplyList_descriptor,
        new java.lang.String[] { "SocialList", });
    internal_static_social_ReqFriendApplyOp_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_social_ReqFriendApplyOp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_social_ReqFriendApplyOp_descriptor,
        new java.lang.String[] { "Op", "TargetId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
