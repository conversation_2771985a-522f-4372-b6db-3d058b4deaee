// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: openlist.proto

package com.sh.game.protos;

public final class OpenListProtos {
  private OpenListProtos() {}
  public static void registerAllExtensions(
          com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
          com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
            (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqOpenListInfoOrBuilder extends
          // @@protoc_insertion_point(interface_extends:OpenList.ReqOpenListInfo)
          com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqOpenListInfo' id='1' desc='请求信息' 
   * </pre>
   *
   * Protobuf type {@code OpenList.ReqOpenListInfo}
   */
  public static final class ReqOpenListInfo extends
          com.google.protobuf.GeneratedMessageV3 implements
          // @@protoc_insertion_point(message_implements:OpenList.ReqOpenListInfo)
          ReqOpenListInfoOrBuilder {
    private static final long serialVersionUID = 0L;
    // Use ReqOpenListInfo.newBuilder() to construct.
    private ReqOpenListInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqOpenListInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
            UnusedPrivateParameter unused) {
      return new ReqOpenListInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqOpenListInfo(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
              com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                      input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
                e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
    getDescriptor() {
      return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
    internalGetFieldAccessorTable() {
      return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListInfo_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                      com.sh.game.protos.OpenListProtos.ReqOpenListInfo.class, com.sh.game.protos.OpenListProtos.ReqOpenListInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
            throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
        return true;
      }
      if (!(obj instanceof com.sh.game.protos.OpenListProtos.ReqOpenListInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.OpenListProtos.ReqOpenListInfo other = (com.sh.game.protos.OpenListProtos.ReqOpenListInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(java.io.InputStream input)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.OpenListProtos.ReqOpenListInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqOpenListInfo' id='1' desc='请求信息'
     * </pre>
     *
     * Protobuf type {@code OpenList.ReqOpenListInfo}
     */
    public static final class Builder extends
            com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:OpenList.ReqOpenListInfo)
            com.sh.game.protos.OpenListProtos.ReqOpenListInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
        return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
        return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListInfo_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                        com.sh.game.protos.OpenListProtos.ReqOpenListInfo.class, com.sh.game.protos.OpenListProtos.ReqOpenListInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.OpenListProtos.ReqOpenListInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
              com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
      getDescriptorForType() {
        return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.OpenListProtos.ReqOpenListInfo getDefaultInstanceForType() {
        return com.sh.game.protos.OpenListProtos.ReqOpenListInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.OpenListProtos.ReqOpenListInfo build() {
        com.sh.game.protos.OpenListProtos.ReqOpenListInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.OpenListProtos.ReqOpenListInfo buildPartial() {
        com.sh.game.protos.OpenListProtos.ReqOpenListInfo result = new com.sh.game.protos.OpenListProtos.ReqOpenListInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
              com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
              com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.OpenListProtos.ReqOpenListInfo) {
          return mergeFrom((com.sh.game.protos.OpenListProtos.ReqOpenListInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.OpenListProtos.ReqOpenListInfo other) {
        if (other == com.sh.game.protos.OpenListProtos.ReqOpenListInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
        com.sh.game.protos.OpenListProtos.ReqOpenListInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.OpenListProtos.ReqOpenListInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:OpenList.ReqOpenListInfo)
    }

    // @@protoc_insertion_point(class_scope:OpenList.ReqOpenListInfo)
    private static final com.sh.game.protos.OpenListProtos.ReqOpenListInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.OpenListProtos.ReqOpenListInfo();
    }

    public static com.sh.game.protos.OpenListProtos.ReqOpenListInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqOpenListInfo>
            PARSER = new com.google.protobuf.AbstractParser<ReqOpenListInfo>() {
      @java.lang.Override
      public ReqOpenListInfo parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqOpenListInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqOpenListInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqOpenListInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.OpenListProtos.ReqOpenListInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResOpenListInfoOrBuilder extends
          // @@protoc_insertion_point(interface_extends:OpenList.ResOpenListInfo)
          com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *openlist表id
     * </pre>
     *
     * <code>repeated int32 infos = 1;</code>
     * @return A list containing the infos.
     */
    java.util.List<java.lang.Integer> getInfosList();
    /**
     * <pre>
     *openlist表id
     * </pre>
     *
     * <code>repeated int32 infos = 1;</code>
     * @return The count of infos.
     */
    int getInfosCount();
    /**
     * <pre>
     *openlist表id
     * </pre>
     *
     * <code>repeated int32 infos = 1;</code>
     * @param index The index of the element to return.
     * @return The infos at the given index.
     */
    int getInfos(int index);
  }
  /**
   * <pre>
   ** class='ResOpenListInfo' id='2' desc='返回信息'
   * </pre>
   *
   * Protobuf type {@code OpenList.ResOpenListInfo}
   */
  public static final class ResOpenListInfo extends
          com.google.protobuf.GeneratedMessageV3 implements
          // @@protoc_insertion_point(message_implements:OpenList.ResOpenListInfo)
          ResOpenListInfoOrBuilder {
    private static final long serialVersionUID = 0L;
    // Use ResOpenListInfo.newBuilder() to construct.
    private ResOpenListInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResOpenListInfo() {
      infos_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
            UnusedPrivateParameter unused) {
      return new ResOpenListInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResOpenListInfo(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
              com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                infos_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              infos_.addInt(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                infos_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                infos_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                      input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
                e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          infos_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
    getDescriptor() {
      return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ResOpenListInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
    internalGetFieldAccessorTable() {
      return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ResOpenListInfo_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                      com.sh.game.protos.OpenListProtos.ResOpenListInfo.class, com.sh.game.protos.OpenListProtos.ResOpenListInfo.Builder.class);
    }

    public static final int INFOS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.IntList infos_;
    /**
     * <pre>
     *openlist表id
     * </pre>
     *
     * <code>repeated int32 infos = 1;</code>
     * @return A list containing the infos.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
    getInfosList() {
      return infos_;
    }
    /**
     * <pre>
     *openlist表id
     * </pre>
     *
     * <code>repeated int32 infos = 1;</code>
     * @return The count of infos.
     */
    public int getInfosCount() {
      return infos_.size();
    }
    /**
     * <pre>
     *openlist表id
     * </pre>
     *
     * <code>repeated int32 infos = 1;</code>
     * @param index The index of the element to return.
     * @return The infos at the given index.
     */
    public int getInfos(int index) {
      return infos_.getInt(index);
    }
    private int infosMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
            throws java.io.IOException {
      getSerializedSize();
      if (getInfosList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(infosMemoizedSerializedSize);
      }
      for (int i = 0; i < infos_.size(); i++) {
        output.writeInt32NoTag(infos_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < infos_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
                  .computeInt32SizeNoTag(infos_.getInt(i));
        }
        size += dataSize;
        if (!getInfosList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
                  .computeInt32SizeNoTag(dataSize);
        }
        infosMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
        return true;
      }
      if (!(obj instanceof com.sh.game.protos.OpenListProtos.ResOpenListInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.OpenListProtos.ResOpenListInfo other = (com.sh.game.protos.OpenListProtos.ResOpenListInfo) obj;

      if (!getInfosList()
              .equals(other.getInfosList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfosCount() > 0) {
        hash = (37 * hash) + INFOS_FIELD_NUMBER;
        hash = (53 * hash) + getInfosList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(java.io.InputStream input)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.OpenListProtos.ResOpenListInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResOpenListInfo' id='2' desc='返回信息'
     * </pre>
     *
     * Protobuf type {@code OpenList.ResOpenListInfo}
     */
    public static final class Builder extends
            com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:OpenList.ResOpenListInfo)
            com.sh.game.protos.OpenListProtos.ResOpenListInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
        return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ResOpenListInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
        return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ResOpenListInfo_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                        com.sh.game.protos.OpenListProtos.ResOpenListInfo.class, com.sh.game.protos.OpenListProtos.ResOpenListInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.OpenListProtos.ResOpenListInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
              com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        infos_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
      getDescriptorForType() {
        return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ResOpenListInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.OpenListProtos.ResOpenListInfo getDefaultInstanceForType() {
        return com.sh.game.protos.OpenListProtos.ResOpenListInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.OpenListProtos.ResOpenListInfo build() {
        com.sh.game.protos.OpenListProtos.ResOpenListInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.OpenListProtos.ResOpenListInfo buildPartial() {
        com.sh.game.protos.OpenListProtos.ResOpenListInfo result = new com.sh.game.protos.OpenListProtos.ResOpenListInfo(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          infos_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.infos_ = infos_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
              com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
              com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.OpenListProtos.ResOpenListInfo) {
          return mergeFrom((com.sh.game.protos.OpenListProtos.ResOpenListInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.OpenListProtos.ResOpenListInfo other) {
        if (other == com.sh.game.protos.OpenListProtos.ResOpenListInfo.getDefaultInstance()) return this;
        if (!other.infos_.isEmpty()) {
          if (infos_.isEmpty()) {
            infos_ = other.infos_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureInfosIsMutable();
            infos_.addAll(other.infos_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
        com.sh.game.protos.OpenListProtos.ResOpenListInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.OpenListProtos.ResOpenListInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList infos_ = emptyIntList();
      private void ensureInfosIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          infos_ = mutableCopy(infos_);
          bitField0_ |= 0x00000001;
        }
      }
      /**
       * <pre>
       *openlist表id
       * </pre>
       *
       * <code>repeated int32 infos = 1;</code>
       * @return A list containing the infos.
       */
      public java.util.List<java.lang.Integer>
      getInfosList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                java.util.Collections.unmodifiableList(infos_) : infos_;
      }
      /**
       * <pre>
       *openlist表id
       * </pre>
       *
       * <code>repeated int32 infos = 1;</code>
       * @return The count of infos.
       */
      public int getInfosCount() {
        return infos_.size();
      }
      /**
       * <pre>
       *openlist表id
       * </pre>
       *
       * <code>repeated int32 infos = 1;</code>
       * @param index The index of the element to return.
       * @return The infos at the given index.
       */
      public int getInfos(int index) {
        return infos_.getInt(index);
      }
      /**
       * <pre>
       *openlist表id
       * </pre>
       *
       * <code>repeated int32 infos = 1;</code>
       * @param index The index to set the value at.
       * @param value The infos to set.
       * @return This builder for chaining.
       */
      public Builder setInfos(
              int index, int value) {
        ensureInfosIsMutable();
        infos_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *openlist表id
       * </pre>
       *
       * <code>repeated int32 infos = 1;</code>
       * @param value The infos to add.
       * @return This builder for chaining.
       */
      public Builder addInfos(int value) {
        ensureInfosIsMutable();
        infos_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *openlist表id
       * </pre>
       *
       * <code>repeated int32 infos = 1;</code>
       * @param values The infos to add.
       * @return This builder for chaining.
       */
      public Builder addAllInfos(
              java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureInfosIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
                values, infos_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *openlist表id
       * </pre>
       *
       * <code>repeated int32 infos = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearInfos() {
        infos_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:OpenList.ResOpenListInfo)
    }

    // @@protoc_insertion_point(class_scope:OpenList.ResOpenListInfo)
    private static final com.sh.game.protos.OpenListProtos.ResOpenListInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.OpenListProtos.ResOpenListInfo();
    }

    public static com.sh.game.protos.OpenListProtos.ResOpenListInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResOpenListInfo>
            PARSER = new com.google.protobuf.AbstractParser<ResOpenListInfo>() {
      @java.lang.Override
      public ResOpenListInfo parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResOpenListInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResOpenListInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResOpenListInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.OpenListProtos.ResOpenListInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqOpenListRewardOrBuilder extends
          // @@protoc_insertion_point(interface_extends:OpenList.ReqOpenListReward)
          com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * openList表id
     * </pre>
     *
     * <code>int32 cfgId = 1;</code>
     * @return The cfgId.
     */
    int getCfgId();
  }
  /**
   * <pre>
   ** class='ReqOpenListReward' id='3' desc='请求奖励'
   * </pre>
   *
   * Protobuf type {@code OpenList.ReqOpenListReward}
   */
  public static final class ReqOpenListReward extends
          com.google.protobuf.GeneratedMessageV3 implements
          // @@protoc_insertion_point(message_implements:OpenList.ReqOpenListReward)
          ReqOpenListRewardOrBuilder {
    private static final long serialVersionUID = 0L;
    // Use ReqOpenListReward.newBuilder() to construct.
    private ReqOpenListReward(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqOpenListReward() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
            UnusedPrivateParameter unused) {
      return new ReqOpenListReward();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqOpenListReward(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
              com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              cfgId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                      input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
                e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
    getDescriptor() {
      return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListReward_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
    internalGetFieldAccessorTable() {
      return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListReward_fieldAccessorTable
              .ensureFieldAccessorsInitialized(
                      com.sh.game.protos.OpenListProtos.ReqOpenListReward.class, com.sh.game.protos.OpenListProtos.ReqOpenListReward.Builder.class);
    }

    public static final int CFGID_FIELD_NUMBER = 1;
    private int cfgId_;
    /**
     * <pre>
     * openList表id
     * </pre>
     *
     * <code>int32 cfgId = 1;</code>
     * @return The cfgId.
     */
    @java.lang.Override
    public int getCfgId() {
      return cfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
            throws java.io.IOException {
      if (cfgId_ != 0) {
        output.writeInt32(1, cfgId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (cfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
                .computeInt32Size(1, cfgId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
        return true;
      }
      if (!(obj instanceof com.sh.game.protos.OpenListProtos.ReqOpenListReward)) {
        return super.equals(obj);
      }
      com.sh.game.protos.OpenListProtos.ReqOpenListReward other = (com.sh.game.protos.OpenListProtos.ReqOpenListReward) obj;

      if (getCfgId()
              != other.getCfgId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CFGID_FIELD_NUMBER;
      hash = (53 * hash) + getCfgId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(
            java.nio.ByteBuffer data)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(
            java.nio.ByteBuffer data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(
            com.google.protobuf.ByteString data)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(
            com.google.protobuf.ByteString data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(byte[] data)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(
            byte[] data,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(java.io.InputStream input)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseDelimitedFrom(java.io.InputStream input)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseDelimitedFrom(
            java.io.InputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(
            com.google.protobuf.CodedInputStream input)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward parseFrom(
            com.google.protobuf.CodedInputStream input,
            com.google.protobuf.ExtensionRegistryLite extensionRegistry)
            throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
              .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.OpenListProtos.ReqOpenListReward prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
              ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
            com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqOpenListReward' id='3' desc='请求奖励'
     * </pre>
     *
     * Protobuf type {@code OpenList.ReqOpenListReward}
     */
    public static final class Builder extends
            com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
            // @@protoc_insertion_point(builder_implements:OpenList.ReqOpenListReward)
            com.sh.game.protos.OpenListProtos.ReqOpenListRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
        return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListReward_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
        return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListReward_fieldAccessorTable
                .ensureFieldAccessorsInitialized(
                        com.sh.game.protos.OpenListProtos.ReqOpenListReward.class, com.sh.game.protos.OpenListProtos.ReqOpenListReward.Builder.class);
      }

      // Construct using com.sh.game.protos.OpenListProtos.ReqOpenListReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
              com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        cfgId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
      getDescriptorForType() {
        return com.sh.game.protos.OpenListProtos.internal_static_OpenList_ReqOpenListReward_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.OpenListProtos.ReqOpenListReward getDefaultInstanceForType() {
        return com.sh.game.protos.OpenListProtos.ReqOpenListReward.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.OpenListProtos.ReqOpenListReward build() {
        com.sh.game.protos.OpenListProtos.ReqOpenListReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.OpenListProtos.ReqOpenListReward buildPartial() {
        com.sh.game.protos.OpenListProtos.ReqOpenListReward result = new com.sh.game.protos.OpenListProtos.ReqOpenListReward(this);
        result.cfgId_ = cfgId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
              com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
              com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
              com.google.protobuf.Descriptors.FieldDescriptor field,
              java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.OpenListProtos.ReqOpenListReward) {
          return mergeFrom((com.sh.game.protos.OpenListProtos.ReqOpenListReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.OpenListProtos.ReqOpenListReward other) {
        if (other == com.sh.game.protos.OpenListProtos.ReqOpenListReward.getDefaultInstance()) return this;
        if (other.getCfgId() != 0) {
          setCfgId(other.getCfgId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws java.io.IOException {
        com.sh.game.protos.OpenListProtos.ReqOpenListReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.OpenListProtos.ReqOpenListReward) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int cfgId_ ;
      /**
       * <pre>
       * openList表id
       * </pre>
       *
       * <code>int32 cfgId = 1;</code>
       * @return The cfgId.
       */
      @java.lang.Override
      public int getCfgId() {
        return cfgId_;
      }
      /**
       * <pre>
       * openList表id
       * </pre>
       *
       * <code>int32 cfgId = 1;</code>
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(int value) {

        cfgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * openList表id
       * </pre>
       *
       * <code>int32 cfgId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {

        cfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
              final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:OpenList.ReqOpenListReward)
    }

    // @@protoc_insertion_point(class_scope:OpenList.ReqOpenListReward)
    private static final com.sh.game.protos.OpenListProtos.ReqOpenListReward DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.OpenListProtos.ReqOpenListReward();
    }

    public static com.sh.game.protos.OpenListProtos.ReqOpenListReward getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqOpenListReward>
            PARSER = new com.google.protobuf.AbstractParser<ReqOpenListReward>() {
      @java.lang.Override
      public ReqOpenListReward parsePartialFrom(
              com.google.protobuf.CodedInputStream input,
              com.google.protobuf.ExtensionRegistryLite extensionRegistry)
              throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqOpenListReward(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqOpenListReward> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqOpenListReward> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.OpenListProtos.ReqOpenListReward getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
          internal_static_OpenList_ReqOpenListInfo_descriptor;
  private static final
  com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internal_static_OpenList_ReqOpenListInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
          internal_static_OpenList_ResOpenListInfo_descriptor;
  private static final
  com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internal_static_OpenList_ResOpenListInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
          internal_static_OpenList_ReqOpenListReward_descriptor;
  private static final
  com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internal_static_OpenList_ReqOpenListReward_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
  getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
          descriptor;
  static {
    java.lang.String[] descriptorData = {
            "\n\016openlist.proto\022\010OpenList\"\021\n\017ReqOpenLis" +
                    "tInfo\" \n\017ResOpenListInfo\022\r\n\005infos\030\001 \003(\005\"" +
                    "\"\n\021ReqOpenListReward\022\r\n\005cfgId\030\001 \001(\005B$\n\022c" +
                    "om.sh.game.protosB\016OpenListProtosb\006proto" +
                    "3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
            .internalBuildGeneratedFileFrom(descriptorData,
                    new com.google.protobuf.Descriptors.FileDescriptor[] {
                    });
    internal_static_OpenList_ReqOpenListInfo_descriptor =
            getDescriptor().getMessageTypes().get(0);
    internal_static_OpenList_ReqOpenListInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
            internal_static_OpenList_ReqOpenListInfo_descriptor,
            new java.lang.String[] { });
    internal_static_OpenList_ResOpenListInfo_descriptor =
            getDescriptor().getMessageTypes().get(1);
    internal_static_OpenList_ResOpenListInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
            internal_static_OpenList_ResOpenListInfo_descriptor,
            new java.lang.String[] { "Infos", });
    internal_static_OpenList_ReqOpenListReward_descriptor =
            getDescriptor().getMessageTypes().get(2);
    internal_static_OpenList_ReqOpenListReward_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
            internal_static_OpenList_ReqOpenListReward_descriptor,
            new java.lang.String[] { "CfgId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
