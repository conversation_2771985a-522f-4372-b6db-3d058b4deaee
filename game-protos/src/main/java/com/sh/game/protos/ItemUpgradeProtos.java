// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: itemUpgrade.proto

package com.sh.game.protos;

public final class ItemUpgradeProtos {
  private ItemUpgradeProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqItemUpgradeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:itemUpgrade.ReqItemUpgrade)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *cfg_equip_forging配置id
     * </pre>
     *
     * <code>int32 cfgId = 1;</code>
     * @return The cfgId.
     */
    int getCfgId();

    /**
     * <pre>
     *可选材料索引位（从1开始）
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    int getIndex();
  }
  /**
   * <pre>
   ** class='ReqItemUpgrade' id='1' desc='请求道具升级' 
   * </pre>
   *
   * Protobuf type {@code itemUpgrade.ReqItemUpgrade}
   */
  public static final class ReqItemUpgrade extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:itemUpgrade.ReqItemUpgrade)
      ReqItemUpgradeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqItemUpgrade.newBuilder() to construct.
    private ReqItemUpgrade(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqItemUpgrade() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqItemUpgrade();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqItemUpgrade(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              cfgId_ = input.readInt32();
              break;
            }
            case 16: {

              index_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ReqItemUpgrade_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ReqItemUpgrade_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.class, com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.Builder.class);
    }

    public static final int CFGID_FIELD_NUMBER = 1;
    private int cfgId_;
    /**
     * <pre>
     *cfg_equip_forging配置id
     * </pre>
     *
     * <code>int32 cfgId = 1;</code>
     * @return The cfgId.
     */
    @java.lang.Override
    public int getCfgId() {
      return cfgId_;
    }

    public static final int INDEX_FIELD_NUMBER = 2;
    private int index_;
    /**
     * <pre>
     *可选材料索引位（从1开始）
     * </pre>
     *
     * <code>int32 index = 2;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (cfgId_ != 0) {
        output.writeInt32(1, cfgId_);
      }
      if (index_ != 0) {
        output.writeInt32(2, index_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (cfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, cfgId_);
      }
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, index_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade other = (com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade) obj;

      if (getCfgId()
          != other.getCfgId()) return false;
      if (getIndex()
          != other.getIndex()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CFGID_FIELD_NUMBER;
      hash = (53 * hash) + getCfgId();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqItemUpgrade' id='1' desc='请求道具升级' 
     * </pre>
     *
     * Protobuf type {@code itemUpgrade.ReqItemUpgrade}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:itemUpgrade.ReqItemUpgrade)
        com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgradeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ReqItemUpgrade_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ReqItemUpgrade_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.class, com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.Builder.class);
      }

      // Construct using com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        cfgId_ = 0;

        index_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ReqItemUpgrade_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade getDefaultInstanceForType() {
        return com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade build() {
        com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade buildPartial() {
        com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade result = new com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade(this);
        result.cfgId_ = cfgId_;
        result.index_ = index_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade) {
          return mergeFrom((com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade other) {
        if (other == com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade.getDefaultInstance()) return this;
        if (other.getCfgId() != 0) {
          setCfgId(other.getCfgId());
        }
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int cfgId_ ;
      /**
       * <pre>
       *cfg_equip_forging配置id
       * </pre>
       *
       * <code>int32 cfgId = 1;</code>
       * @return The cfgId.
       */
      @java.lang.Override
      public int getCfgId() {
        return cfgId_;
      }
      /**
       * <pre>
       *cfg_equip_forging配置id
       * </pre>
       *
       * <code>int32 cfgId = 1;</code>
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(int value) {
        
        cfgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *cfg_equip_forging配置id
       * </pre>
       *
       * <code>int32 cfgId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {
        
        cfgId_ = 0;
        onChanged();
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *可选材料索引位（从1开始）
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *可选材料索引位（从1开始）
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *可选材料索引位（从1开始）
       * </pre>
       *
       * <code>int32 index = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:itemUpgrade.ReqItemUpgrade)
    }

    // @@protoc_insertion_point(class_scope:itemUpgrade.ReqItemUpgrade)
    private static final com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade();
    }

    public static com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqItemUpgrade>
        PARSER = new com.google.protobuf.AbstractParser<ReqItemUpgrade>() {
      @java.lang.Override
      public ReqItemUpgrade parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqItemUpgrade(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqItemUpgrade> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqItemUpgrade> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ItemUpgradeProtos.ReqItemUpgrade getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResItemUpgradeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:itemUpgrade.ResItemUpgrade)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *cfg_equip_forging配置id
     * </pre>
     *
     * <code>int32 nextCfgId = 1;</code>
     * @return The nextCfgId.
     */
    int getNextCfgId();
  }
  /**
   * <pre>
   ** class='ResItemUpgrade' id='2' desc='返回道具升级信息' 
   * </pre>
   *
   * Protobuf type {@code itemUpgrade.ResItemUpgrade}
   */
  public static final class ResItemUpgrade extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:itemUpgrade.ResItemUpgrade)
      ResItemUpgradeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResItemUpgrade.newBuilder() to construct.
    private ResItemUpgrade(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResItemUpgrade() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResItemUpgrade();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResItemUpgrade(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              nextCfgId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ResItemUpgrade_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ResItemUpgrade_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade.class, com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade.Builder.class);
    }

    public static final int NEXTCFGID_FIELD_NUMBER = 1;
    private int nextCfgId_;
    /**
     * <pre>
     *cfg_equip_forging配置id
     * </pre>
     *
     * <code>int32 nextCfgId = 1;</code>
     * @return The nextCfgId.
     */
    @java.lang.Override
    public int getNextCfgId() {
      return nextCfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (nextCfgId_ != 0) {
        output.writeInt32(1, nextCfgId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (nextCfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, nextCfgId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade other = (com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade) obj;

      if (getNextCfgId()
          != other.getNextCfgId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + NEXTCFGID_FIELD_NUMBER;
      hash = (53 * hash) + getNextCfgId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResItemUpgrade' id='2' desc='返回道具升级信息' 
     * </pre>
     *
     * Protobuf type {@code itemUpgrade.ResItemUpgrade}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:itemUpgrade.ResItemUpgrade)
        com.sh.game.protos.ItemUpgradeProtos.ResItemUpgradeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ResItemUpgrade_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ResItemUpgrade_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade.class, com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade.Builder.class);
      }

      // Construct using com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        nextCfgId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ItemUpgradeProtos.internal_static_itemUpgrade_ResItemUpgrade_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade getDefaultInstanceForType() {
        return com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade build() {
        com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade buildPartial() {
        com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade result = new com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade(this);
        result.nextCfgId_ = nextCfgId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade) {
          return mergeFrom((com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade other) {
        if (other == com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade.getDefaultInstance()) return this;
        if (other.getNextCfgId() != 0) {
          setNextCfgId(other.getNextCfgId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int nextCfgId_ ;
      /**
       * <pre>
       *cfg_equip_forging配置id
       * </pre>
       *
       * <code>int32 nextCfgId = 1;</code>
       * @return The nextCfgId.
       */
      @java.lang.Override
      public int getNextCfgId() {
        return nextCfgId_;
      }
      /**
       * <pre>
       *cfg_equip_forging配置id
       * </pre>
       *
       * <code>int32 nextCfgId = 1;</code>
       * @param value The nextCfgId to set.
       * @return This builder for chaining.
       */
      public Builder setNextCfgId(int value) {
        
        nextCfgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *cfg_equip_forging配置id
       * </pre>
       *
       * <code>int32 nextCfgId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextCfgId() {
        
        nextCfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:itemUpgrade.ResItemUpgrade)
    }

    // @@protoc_insertion_point(class_scope:itemUpgrade.ResItemUpgrade)
    private static final com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade();
    }

    public static com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResItemUpgrade>
        PARSER = new com.google.protobuf.AbstractParser<ResItemUpgrade>() {
      @java.lang.Override
      public ResItemUpgrade parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResItemUpgrade(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResItemUpgrade> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResItemUpgrade> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ItemUpgradeProtos.ResItemUpgrade getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_itemUpgrade_ReqItemUpgrade_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_itemUpgrade_ReqItemUpgrade_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_itemUpgrade_ResItemUpgrade_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_itemUpgrade_ResItemUpgrade_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021itemUpgrade.proto\022\013itemUpgrade\".\n\016ReqI" +
      "temUpgrade\022\r\n\005cfgId\030\001 \001(\005\022\r\n\005index\030\002 \001(\005" +
      "\"#\n\016ResItemUpgrade\022\021\n\tnextCfgId\030\001 \001(\005B\'\n" +
      "\022com.sh.game.protosB\021ItemUpgradeProtosb\006" +
      "proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_itemUpgrade_ReqItemUpgrade_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_itemUpgrade_ReqItemUpgrade_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_itemUpgrade_ReqItemUpgrade_descriptor,
        new java.lang.String[] { "CfgId", "Index", });
    internal_static_itemUpgrade_ResItemUpgrade_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_itemUpgrade_ResItemUpgrade_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_itemUpgrade_ResItemUpgrade_descriptor,
        new java.lang.String[] { "NextCfgId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
