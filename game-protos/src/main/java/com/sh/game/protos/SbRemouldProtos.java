// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sbRemould.proto

package com.sh.game.protos;

public final class SbRemouldProtos {
  private SbRemouldProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RemouldBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:sbRemould.RemouldBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 pos = 1;</code>
     * @return The pos.
     */
    int getPos();

    /**
     * <pre>
     *等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <pre>
     *额外成功率
     * </pre>
     *
     * <code>int32 extraProbability = 3;</code>
     * @return The extraProbability.
     */
    int getExtraProbability();
  }
  /**
   * Protobuf type {@code sbRemould.RemouldBean}
   */
  public static final class RemouldBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:sbRemould.RemouldBean)
      RemouldBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use RemouldBean.newBuilder() to construct.
    private RemouldBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private RemouldBean() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new RemouldBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private RemouldBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              pos_ = input.readInt32();
              break;
            }
            case 16: {

              level_ = input.readInt32();
              break;
            }
            case 24: {

              extraProbability_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_RemouldBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_RemouldBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SbRemouldProtos.RemouldBean.class, com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder.class);
    }

    public static final int POS_FIELD_NUMBER = 1;
    private int pos_;
    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 pos = 1;</code>
     * @return The pos.
     */
    @java.lang.Override
    public int getPos() {
      return pos_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     *等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int EXTRAPROBABILITY_FIELD_NUMBER = 3;
    private int extraProbability_;
    /**
     * <pre>
     *额外成功率
     * </pre>
     *
     * <code>int32 extraProbability = 3;</code>
     * @return The extraProbability.
     */
    @java.lang.Override
    public int getExtraProbability() {
      return extraProbability_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (pos_ != 0) {
        output.writeInt32(1, pos_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      if (extraProbability_ != 0) {
        output.writeInt32(3, extraProbability_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (pos_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, pos_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      if (extraProbability_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, extraProbability_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SbRemouldProtos.RemouldBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SbRemouldProtos.RemouldBean other = (com.sh.game.protos.SbRemouldProtos.RemouldBean) obj;

      if (getPos()
          != other.getPos()) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (getExtraProbability()
          != other.getExtraProbability()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (37 * hash) + EXTRAPROBABILITY_FIELD_NUMBER;
      hash = (53 * hash) + getExtraProbability();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.RemouldBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SbRemouldProtos.RemouldBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code sbRemould.RemouldBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:sbRemould.RemouldBean)
        com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_RemouldBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_RemouldBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SbRemouldProtos.RemouldBean.class, com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder.class);
      }

      // Construct using com.sh.game.protos.SbRemouldProtos.RemouldBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        pos_ = 0;

        level_ = 0;

        extraProbability_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_RemouldBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.RemouldBean getDefaultInstanceForType() {
        return com.sh.game.protos.SbRemouldProtos.RemouldBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.RemouldBean build() {
        com.sh.game.protos.SbRemouldProtos.RemouldBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.RemouldBean buildPartial() {
        com.sh.game.protos.SbRemouldProtos.RemouldBean result = new com.sh.game.protos.SbRemouldProtos.RemouldBean(this);
        result.pos_ = pos_;
        result.level_ = level_;
        result.extraProbability_ = extraProbability_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SbRemouldProtos.RemouldBean) {
          return mergeFrom((com.sh.game.protos.SbRemouldProtos.RemouldBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SbRemouldProtos.RemouldBean other) {
        if (other == com.sh.game.protos.SbRemouldProtos.RemouldBean.getDefaultInstance()) return this;
        if (other.getPos() != 0) {
          setPos(other.getPos());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (other.getExtraProbability() != 0) {
          setExtraProbability(other.getExtraProbability());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SbRemouldProtos.RemouldBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SbRemouldProtos.RemouldBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int pos_ ;
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @return The pos.
       */
      @java.lang.Override
      public int getPos() {
        return pos_;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @param value The pos to set.
       * @return This builder for chaining.
       */
      public Builder setPos(int value) {
        
        pos_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPos() {
        
        pos_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       *等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       *等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private int extraProbability_ ;
      /**
       * <pre>
       *额外成功率
       * </pre>
       *
       * <code>int32 extraProbability = 3;</code>
       * @return The extraProbability.
       */
      @java.lang.Override
      public int getExtraProbability() {
        return extraProbability_;
      }
      /**
       * <pre>
       *额外成功率
       * </pre>
       *
       * <code>int32 extraProbability = 3;</code>
       * @param value The extraProbability to set.
       * @return This builder for chaining.
       */
      public Builder setExtraProbability(int value) {
        
        extraProbability_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *额外成功率
       * </pre>
       *
       * <code>int32 extraProbability = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtraProbability() {
        
        extraProbability_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:sbRemould.RemouldBean)
    }

    // @@protoc_insertion_point(class_scope:sbRemould.RemouldBean)
    private static final com.sh.game.protos.SbRemouldProtos.RemouldBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SbRemouldProtos.RemouldBean();
    }

    public static com.sh.game.protos.SbRemouldProtos.RemouldBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RemouldBean>
        PARSER = new com.google.protobuf.AbstractParser<RemouldBean>() {
      @java.lang.Override
      public RemouldBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RemouldBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<RemouldBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RemouldBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SbRemouldProtos.RemouldBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqShenBingRemouldInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:sbRemould.ReqShenBingRemouldInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqShenBingRemouldInfo' id='1' desc='请求神兵改造信息' 
   * </pre>
   *
   * Protobuf type {@code sbRemould.ReqShenBingRemouldInfo}
   */
  public static final class ReqShenBingRemouldInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:sbRemould.ReqShenBingRemouldInfo)
      ReqShenBingRemouldInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqShenBingRemouldInfo.newBuilder() to construct.
    private ReqShenBingRemouldInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqShenBingRemouldInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqShenBingRemouldInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqShenBingRemouldInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemouldInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemouldInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo.class, com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo other = (com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqShenBingRemouldInfo' id='1' desc='请求神兵改造信息' 
     * </pre>
     *
     * Protobuf type {@code sbRemould.ReqShenBingRemouldInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:sbRemould.ReqShenBingRemouldInfo)
        com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemouldInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemouldInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo.class, com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemouldInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo getDefaultInstanceForType() {
        return com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo build() {
        com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo buildPartial() {
        com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo result = new com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo) {
          return mergeFrom((com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo other) {
        if (other == com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:sbRemould.ReqShenBingRemouldInfo)
    }

    // @@protoc_insertion_point(class_scope:sbRemould.ReqShenBingRemouldInfo)
    private static final com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo();
    }

    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqShenBingRemouldInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqShenBingRemouldInfo>() {
      @java.lang.Override
      public ReqShenBingRemouldInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqShenBingRemouldInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqShenBingRemouldInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqShenBingRemouldInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResShenBingRemouldInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:sbRemould.ResShenBingRemouldInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    java.util.List<com.sh.game.protos.SbRemouldProtos.RemouldBean> 
        getRemouldBeanListList();
    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    com.sh.game.protos.SbRemouldProtos.RemouldBean getRemouldBeanList(int index);
    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    int getRemouldBeanListCount();
    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    java.util.List<? extends com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder> 
        getRemouldBeanListOrBuilderList();
    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder getRemouldBeanListOrBuilder(
        int index);

    /**
     * <pre>
     *总等级
     * </pre>
     *
     * <code>int32 totalLevel = 2;</code>
     * @return The totalLevel.
     */
    int getTotalLevel();
  }
  /**
   * <pre>
   ** class='ResShenBingRemouldInfo' id='2' desc='返回神兵改造信息' 
   * </pre>
   *
   * Protobuf type {@code sbRemould.ResShenBingRemouldInfo}
   */
  public static final class ResShenBingRemouldInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:sbRemould.ResShenBingRemouldInfo)
      ResShenBingRemouldInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResShenBingRemouldInfo.newBuilder() to construct.
    private ResShenBingRemouldInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResShenBingRemouldInfo() {
      remouldBeanList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResShenBingRemouldInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResShenBingRemouldInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                remouldBeanList_ = new java.util.ArrayList<com.sh.game.protos.SbRemouldProtos.RemouldBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              remouldBeanList_.add(
                  input.readMessage(com.sh.game.protos.SbRemouldProtos.RemouldBean.parser(), extensionRegistry));
              break;
            }
            case 16: {

              totalLevel_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          remouldBeanList_ = java.util.Collections.unmodifiableList(remouldBeanList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemouldInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemouldInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo.class, com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo.Builder.class);
    }

    public static final int REMOULDBEANLIST_FIELD_NUMBER = 1;
    private java.util.List<com.sh.game.protos.SbRemouldProtos.RemouldBean> remouldBeanList_;
    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.SbRemouldProtos.RemouldBean> getRemouldBeanListList() {
      return remouldBeanList_;
    }
    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder> 
        getRemouldBeanListOrBuilderList() {
      return remouldBeanList_;
    }
    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    @java.lang.Override
    public int getRemouldBeanListCount() {
      return remouldBeanList_.size();
    }
    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.SbRemouldProtos.RemouldBean getRemouldBeanList(int index) {
      return remouldBeanList_.get(index);
    }
    /**
     * <pre>
     *神兵改造列表
     * </pre>
     *
     * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder getRemouldBeanListOrBuilder(
        int index) {
      return remouldBeanList_.get(index);
    }

    public static final int TOTALLEVEL_FIELD_NUMBER = 2;
    private int totalLevel_;
    /**
     * <pre>
     *总等级
     * </pre>
     *
     * <code>int32 totalLevel = 2;</code>
     * @return The totalLevel.
     */
    @java.lang.Override
    public int getTotalLevel() {
      return totalLevel_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < remouldBeanList_.size(); i++) {
        output.writeMessage(1, remouldBeanList_.get(i));
      }
      if (totalLevel_ != 0) {
        output.writeInt32(2, totalLevel_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < remouldBeanList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, remouldBeanList_.get(i));
      }
      if (totalLevel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, totalLevel_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo other = (com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo) obj;

      if (!getRemouldBeanListList()
          .equals(other.getRemouldBeanListList())) return false;
      if (getTotalLevel()
          != other.getTotalLevel()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRemouldBeanListCount() > 0) {
        hash = (37 * hash) + REMOULDBEANLIST_FIELD_NUMBER;
        hash = (53 * hash) + getRemouldBeanListList().hashCode();
      }
      hash = (37 * hash) + TOTALLEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getTotalLevel();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResShenBingRemouldInfo' id='2' desc='返回神兵改造信息' 
     * </pre>
     *
     * Protobuf type {@code sbRemould.ResShenBingRemouldInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:sbRemould.ResShenBingRemouldInfo)
        com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemouldInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemouldInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo.class, com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRemouldBeanListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (remouldBeanListBuilder_ == null) {
          remouldBeanList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          remouldBeanListBuilder_.clear();
        }
        totalLevel_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemouldInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo getDefaultInstanceForType() {
        return com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo build() {
        com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo buildPartial() {
        com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo result = new com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo(this);
        int from_bitField0_ = bitField0_;
        if (remouldBeanListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            remouldBeanList_ = java.util.Collections.unmodifiableList(remouldBeanList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.remouldBeanList_ = remouldBeanList_;
        } else {
          result.remouldBeanList_ = remouldBeanListBuilder_.build();
        }
        result.totalLevel_ = totalLevel_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo) {
          return mergeFrom((com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo other) {
        if (other == com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo.getDefaultInstance()) return this;
        if (remouldBeanListBuilder_ == null) {
          if (!other.remouldBeanList_.isEmpty()) {
            if (remouldBeanList_.isEmpty()) {
              remouldBeanList_ = other.remouldBeanList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRemouldBeanListIsMutable();
              remouldBeanList_.addAll(other.remouldBeanList_);
            }
            onChanged();
          }
        } else {
          if (!other.remouldBeanList_.isEmpty()) {
            if (remouldBeanListBuilder_.isEmpty()) {
              remouldBeanListBuilder_.dispose();
              remouldBeanListBuilder_ = null;
              remouldBeanList_ = other.remouldBeanList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              remouldBeanListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRemouldBeanListFieldBuilder() : null;
            } else {
              remouldBeanListBuilder_.addAllMessages(other.remouldBeanList_);
            }
          }
        }
        if (other.getTotalLevel() != 0) {
          setTotalLevel(other.getTotalLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.sh.game.protos.SbRemouldProtos.RemouldBean> remouldBeanList_ =
        java.util.Collections.emptyList();
      private void ensureRemouldBeanListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          remouldBeanList_ = new java.util.ArrayList<com.sh.game.protos.SbRemouldProtos.RemouldBean>(remouldBeanList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.SbRemouldProtos.RemouldBean, com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder, com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder> remouldBeanListBuilder_;

      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public java.util.List<com.sh.game.protos.SbRemouldProtos.RemouldBean> getRemouldBeanListList() {
        if (remouldBeanListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(remouldBeanList_);
        } else {
          return remouldBeanListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public int getRemouldBeanListCount() {
        if (remouldBeanListBuilder_ == null) {
          return remouldBeanList_.size();
        } else {
          return remouldBeanListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public com.sh.game.protos.SbRemouldProtos.RemouldBean getRemouldBeanList(int index) {
        if (remouldBeanListBuilder_ == null) {
          return remouldBeanList_.get(index);
        } else {
          return remouldBeanListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public Builder setRemouldBeanList(
          int index, com.sh.game.protos.SbRemouldProtos.RemouldBean value) {
        if (remouldBeanListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRemouldBeanListIsMutable();
          remouldBeanList_.set(index, value);
          onChanged();
        } else {
          remouldBeanListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public Builder setRemouldBeanList(
          int index, com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder builderForValue) {
        if (remouldBeanListBuilder_ == null) {
          ensureRemouldBeanListIsMutable();
          remouldBeanList_.set(index, builderForValue.build());
          onChanged();
        } else {
          remouldBeanListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public Builder addRemouldBeanList(com.sh.game.protos.SbRemouldProtos.RemouldBean value) {
        if (remouldBeanListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRemouldBeanListIsMutable();
          remouldBeanList_.add(value);
          onChanged();
        } else {
          remouldBeanListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public Builder addRemouldBeanList(
          int index, com.sh.game.protos.SbRemouldProtos.RemouldBean value) {
        if (remouldBeanListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRemouldBeanListIsMutable();
          remouldBeanList_.add(index, value);
          onChanged();
        } else {
          remouldBeanListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public Builder addRemouldBeanList(
          com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder builderForValue) {
        if (remouldBeanListBuilder_ == null) {
          ensureRemouldBeanListIsMutable();
          remouldBeanList_.add(builderForValue.build());
          onChanged();
        } else {
          remouldBeanListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public Builder addRemouldBeanList(
          int index, com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder builderForValue) {
        if (remouldBeanListBuilder_ == null) {
          ensureRemouldBeanListIsMutable();
          remouldBeanList_.add(index, builderForValue.build());
          onChanged();
        } else {
          remouldBeanListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public Builder addAllRemouldBeanList(
          java.lang.Iterable<? extends com.sh.game.protos.SbRemouldProtos.RemouldBean> values) {
        if (remouldBeanListBuilder_ == null) {
          ensureRemouldBeanListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, remouldBeanList_);
          onChanged();
        } else {
          remouldBeanListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public Builder clearRemouldBeanList() {
        if (remouldBeanListBuilder_ == null) {
          remouldBeanList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          remouldBeanListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public Builder removeRemouldBeanList(int index) {
        if (remouldBeanListBuilder_ == null) {
          ensureRemouldBeanListIsMutable();
          remouldBeanList_.remove(index);
          onChanged();
        } else {
          remouldBeanListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder getRemouldBeanListBuilder(
          int index) {
        return getRemouldBeanListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder getRemouldBeanListOrBuilder(
          int index) {
        if (remouldBeanListBuilder_ == null) {
          return remouldBeanList_.get(index);  } else {
          return remouldBeanListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public java.util.List<? extends com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder> 
           getRemouldBeanListOrBuilderList() {
        if (remouldBeanListBuilder_ != null) {
          return remouldBeanListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(remouldBeanList_);
        }
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder addRemouldBeanListBuilder() {
        return getRemouldBeanListFieldBuilder().addBuilder(
            com.sh.game.protos.SbRemouldProtos.RemouldBean.getDefaultInstance());
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder addRemouldBeanListBuilder(
          int index) {
        return getRemouldBeanListFieldBuilder().addBuilder(
            index, com.sh.game.protos.SbRemouldProtos.RemouldBean.getDefaultInstance());
      }
      /**
       * <pre>
       *神兵改造列表
       * </pre>
       *
       * <code>repeated .sbRemould.RemouldBean remouldBeanList = 1;</code>
       */
      public java.util.List<com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder> 
           getRemouldBeanListBuilderList() {
        return getRemouldBeanListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.SbRemouldProtos.RemouldBean, com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder, com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder> 
          getRemouldBeanListFieldBuilder() {
        if (remouldBeanListBuilder_ == null) {
          remouldBeanListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.SbRemouldProtos.RemouldBean, com.sh.game.protos.SbRemouldProtos.RemouldBean.Builder, com.sh.game.protos.SbRemouldProtos.RemouldBeanOrBuilder>(
                  remouldBeanList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          remouldBeanList_ = null;
        }
        return remouldBeanListBuilder_;
      }

      private int totalLevel_ ;
      /**
       * <pre>
       *总等级
       * </pre>
       *
       * <code>int32 totalLevel = 2;</code>
       * @return The totalLevel.
       */
      @java.lang.Override
      public int getTotalLevel() {
        return totalLevel_;
      }
      /**
       * <pre>
       *总等级
       * </pre>
       *
       * <code>int32 totalLevel = 2;</code>
       * @param value The totalLevel to set.
       * @return This builder for chaining.
       */
      public Builder setTotalLevel(int value) {
        
        totalLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总等级
       * </pre>
       *
       * <code>int32 totalLevel = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalLevel() {
        
        totalLevel_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:sbRemould.ResShenBingRemouldInfo)
    }

    // @@protoc_insertion_point(class_scope:sbRemould.ResShenBingRemouldInfo)
    private static final com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo();
    }

    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResShenBingRemouldInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResShenBingRemouldInfo>() {
      @java.lang.Override
      public ResShenBingRemouldInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResShenBingRemouldInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResShenBingRemouldInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResShenBingRemouldInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqShenBingRemouldOrBuilder extends
      // @@protoc_insertion_point(interface_extends:sbRemould.ReqShenBingRemould)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 pos = 1;</code>
     * @return The pos.
     */
    int getPos();
  }
  /**
   * <pre>
   ** class='ReqShenBingRemould' id='3' desc='请求神兵改造' 
   * </pre>
   *
   * Protobuf type {@code sbRemould.ReqShenBingRemould}
   */
  public static final class ReqShenBingRemould extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:sbRemould.ReqShenBingRemould)
      ReqShenBingRemouldOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqShenBingRemould.newBuilder() to construct.
    private ReqShenBingRemould(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqShenBingRemould() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqShenBingRemould();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqShenBingRemould(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              pos_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemould_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemould_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould.class, com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould.Builder.class);
    }

    public static final int POS_FIELD_NUMBER = 1;
    private int pos_;
    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 pos = 1;</code>
     * @return The pos.
     */
    @java.lang.Override
    public int getPos() {
      return pos_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (pos_ != 0) {
        output.writeInt32(1, pos_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (pos_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, pos_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould other = (com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould) obj;

      if (getPos()
          != other.getPos()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqShenBingRemould' id='3' desc='请求神兵改造' 
     * </pre>
     *
     * Protobuf type {@code sbRemould.ReqShenBingRemould}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:sbRemould.ReqShenBingRemould)
        com.sh.game.protos.SbRemouldProtos.ReqShenBingRemouldOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemould_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemould_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould.class, com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould.Builder.class);
      }

      // Construct using com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        pos_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ReqShenBingRemould_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould getDefaultInstanceForType() {
        return com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould build() {
        com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould buildPartial() {
        com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould result = new com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould(this);
        result.pos_ = pos_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould) {
          return mergeFrom((com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould other) {
        if (other == com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould.getDefaultInstance()) return this;
        if (other.getPos() != 0) {
          setPos(other.getPos());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int pos_ ;
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @return The pos.
       */
      @java.lang.Override
      public int getPos() {
        return pos_;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @param value The pos to set.
       * @return This builder for chaining.
       */
      public Builder setPos(int value) {
        
        pos_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPos() {
        
        pos_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:sbRemould.ReqShenBingRemould)
    }

    // @@protoc_insertion_point(class_scope:sbRemould.ReqShenBingRemould)
    private static final com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould();
    }

    public static com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqShenBingRemould>
        PARSER = new com.google.protobuf.AbstractParser<ReqShenBingRemould>() {
      @java.lang.Override
      public ReqShenBingRemould parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqShenBingRemould(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqShenBingRemould> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqShenBingRemould> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SbRemouldProtos.ReqShenBingRemould getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResShenBingRemouldOrBuilder extends
      // @@protoc_insertion_point(interface_extends:sbRemould.ResShenBingRemould)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 pos = 1;</code>
     * @return The pos.
     */
    int getPos();

    /**
     * <pre>
     *是否成功
     * </pre>
     *
     * <code>bool flag = 2;</code>
     * @return The flag.
     */
    boolean getFlag();
  }
  /**
   * <pre>
   ** class='ResShenBingRemould' id='4' desc='返回神兵改造结果' 
   * </pre>
   *
   * Protobuf type {@code sbRemould.ResShenBingRemould}
   */
  public static final class ResShenBingRemould extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:sbRemould.ResShenBingRemould)
      ResShenBingRemouldOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResShenBingRemould.newBuilder() to construct.
    private ResShenBingRemould(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResShenBingRemould() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResShenBingRemould();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResShenBingRemould(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              pos_ = input.readInt32();
              break;
            }
            case 16: {

              flag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemould_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemould_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SbRemouldProtos.ResShenBingRemould.class, com.sh.game.protos.SbRemouldProtos.ResShenBingRemould.Builder.class);
    }

    public static final int POS_FIELD_NUMBER = 1;
    private int pos_;
    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 pos = 1;</code>
     * @return The pos.
     */
    @java.lang.Override
    public int getPos() {
      return pos_;
    }

    public static final int FLAG_FIELD_NUMBER = 2;
    private boolean flag_;
    /**
     * <pre>
     *是否成功
     * </pre>
     *
     * <code>bool flag = 2;</code>
     * @return The flag.
     */
    @java.lang.Override
    public boolean getFlag() {
      return flag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (pos_ != 0) {
        output.writeInt32(1, pos_);
      }
      if (flag_ != false) {
        output.writeBool(2, flag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (pos_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, pos_);
      }
      if (flag_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, flag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SbRemouldProtos.ResShenBingRemould)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SbRemouldProtos.ResShenBingRemould other = (com.sh.game.protos.SbRemouldProtos.ResShenBingRemould) obj;

      if (getPos()
          != other.getPos()) return false;
      if (getFlag()
          != other.getFlag()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos();
      hash = (37 * hash) + FLAG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getFlag());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SbRemouldProtos.ResShenBingRemould prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResShenBingRemould' id='4' desc='返回神兵改造结果' 
     * </pre>
     *
     * Protobuf type {@code sbRemould.ResShenBingRemould}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:sbRemould.ResShenBingRemould)
        com.sh.game.protos.SbRemouldProtos.ResShenBingRemouldOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemould_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemould_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SbRemouldProtos.ResShenBingRemould.class, com.sh.game.protos.SbRemouldProtos.ResShenBingRemould.Builder.class);
      }

      // Construct using com.sh.game.protos.SbRemouldProtos.ResShenBingRemould.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        pos_ = 0;

        flag_ = false;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SbRemouldProtos.internal_static_sbRemould_ResShenBingRemould_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ResShenBingRemould getDefaultInstanceForType() {
        return com.sh.game.protos.SbRemouldProtos.ResShenBingRemould.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ResShenBingRemould build() {
        com.sh.game.protos.SbRemouldProtos.ResShenBingRemould result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SbRemouldProtos.ResShenBingRemould buildPartial() {
        com.sh.game.protos.SbRemouldProtos.ResShenBingRemould result = new com.sh.game.protos.SbRemouldProtos.ResShenBingRemould(this);
        result.pos_ = pos_;
        result.flag_ = flag_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SbRemouldProtos.ResShenBingRemould) {
          return mergeFrom((com.sh.game.protos.SbRemouldProtos.ResShenBingRemould)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SbRemouldProtos.ResShenBingRemould other) {
        if (other == com.sh.game.protos.SbRemouldProtos.ResShenBingRemould.getDefaultInstance()) return this;
        if (other.getPos() != 0) {
          setPos(other.getPos());
        }
        if (other.getFlag() != false) {
          setFlag(other.getFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SbRemouldProtos.ResShenBingRemould parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SbRemouldProtos.ResShenBingRemould) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int pos_ ;
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @return The pos.
       */
      @java.lang.Override
      public int getPos() {
        return pos_;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @param value The pos to set.
       * @return This builder for chaining.
       */
      public Builder setPos(int value) {
        
        pos_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPos() {
        
        pos_ = 0;
        onChanged();
        return this;
      }

      private boolean flag_ ;
      /**
       * <pre>
       *是否成功
       * </pre>
       *
       * <code>bool flag = 2;</code>
       * @return The flag.
       */
      @java.lang.Override
      public boolean getFlag() {
        return flag_;
      }
      /**
       * <pre>
       *是否成功
       * </pre>
       *
       * <code>bool flag = 2;</code>
       * @param value The flag to set.
       * @return This builder for chaining.
       */
      public Builder setFlag(boolean value) {
        
        flag_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否成功
       * </pre>
       *
       * <code>bool flag = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlag() {
        
        flag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:sbRemould.ResShenBingRemould)
    }

    // @@protoc_insertion_point(class_scope:sbRemould.ResShenBingRemould)
    private static final com.sh.game.protos.SbRemouldProtos.ResShenBingRemould DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SbRemouldProtos.ResShenBingRemould();
    }

    public static com.sh.game.protos.SbRemouldProtos.ResShenBingRemould getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResShenBingRemould>
        PARSER = new com.google.protobuf.AbstractParser<ResShenBingRemould>() {
      @java.lang.Override
      public ResShenBingRemould parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResShenBingRemould(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResShenBingRemould> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResShenBingRemould> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SbRemouldProtos.ResShenBingRemould getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_sbRemould_RemouldBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_sbRemould_RemouldBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_sbRemould_ReqShenBingRemouldInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_sbRemould_ReqShenBingRemouldInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_sbRemould_ResShenBingRemouldInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_sbRemould_ResShenBingRemouldInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_sbRemould_ReqShenBingRemould_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_sbRemould_ReqShenBingRemould_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_sbRemould_ResShenBingRemould_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_sbRemould_ResShenBingRemould_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017sbRemould.proto\022\tsbRemould\"C\n\013RemouldB" +
      "ean\022\013\n\003pos\030\001 \001(\005\022\r\n\005level\030\002 \001(\005\022\030\n\020extra" +
      "Probability\030\003 \001(\005\"\030\n\026ReqShenBingRemouldI" +
      "nfo\"]\n\026ResShenBingRemouldInfo\022/\n\017remould" +
      "BeanList\030\001 \003(\0132\026.sbRemould.RemouldBean\022\022" +
      "\n\ntotalLevel\030\002 \001(\005\"!\n\022ReqShenBingRemould" +
      "\022\013\n\003pos\030\001 \001(\005\"/\n\022ResShenBingRemould\022\013\n\003p" +
      "os\030\001 \001(\005\022\014\n\004flag\030\002 \001(\010B%\n\022com.sh.game.pr" +
      "otosB\017SbRemouldProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_sbRemould_RemouldBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_sbRemould_RemouldBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_sbRemould_RemouldBean_descriptor,
        new java.lang.String[] { "Pos", "Level", "ExtraProbability", });
    internal_static_sbRemould_ReqShenBingRemouldInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_sbRemould_ReqShenBingRemouldInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_sbRemould_ReqShenBingRemouldInfo_descriptor,
        new java.lang.String[] { });
    internal_static_sbRemould_ResShenBingRemouldInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_sbRemould_ResShenBingRemouldInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_sbRemould_ResShenBingRemouldInfo_descriptor,
        new java.lang.String[] { "RemouldBeanList", "TotalLevel", });
    internal_static_sbRemould_ReqShenBingRemould_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_sbRemould_ReqShenBingRemould_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_sbRemould_ReqShenBingRemould_descriptor,
        new java.lang.String[] { "Pos", });
    internal_static_sbRemould_ResShenBingRemould_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_sbRemould_ResShenBingRemould_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_sbRemould_ResShenBingRemould_descriptor,
        new java.lang.String[] { "Pos", "Flag", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
