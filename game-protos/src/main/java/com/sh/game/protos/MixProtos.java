// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: mix.proto

package com.sh.game.protos;

public final class MixProtos {
  private MixProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MixInfoBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:mix.MixInfoBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    int getIndex();

    /**
     * <pre>
     *道具id
     * </pre>
     *
     * <code>int32 id = 2;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * Protobuf type {@code mix.MixInfoBean}
   */
  public static final class MixInfoBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:mix.MixInfoBean)
      MixInfoBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MixInfoBean.newBuilder() to construct.
    private MixInfoBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MixInfoBean() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MixInfoBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MixInfoBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              index_ = input.readInt32();
              break;
            }
            case 16: {

              id_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MixProtos.internal_static_mix_MixInfoBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MixProtos.internal_static_mix_MixInfoBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MixProtos.MixInfoBean.class, com.sh.game.protos.MixProtos.MixInfoBean.Builder.class);
    }

    public static final int INDEX_FIELD_NUMBER = 1;
    private int index_;
    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    public static final int ID_FIELD_NUMBER = 2;
    private int id_;
    /**
     * <pre>
     *道具id
     * </pre>
     *
     * <code>int32 id = 2;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (index_ != 0) {
        output.writeInt32(1, index_);
      }
      if (id_ != 0) {
        output.writeInt32(2, id_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, index_);
      }
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, id_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MixProtos.MixInfoBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MixProtos.MixInfoBean other = (com.sh.game.protos.MixProtos.MixInfoBean) obj;

      if (getIndex()
          != other.getIndex()) return false;
      if (getId()
          != other.getId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.MixInfoBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MixProtos.MixInfoBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code mix.MixInfoBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:mix.MixInfoBean)
        com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MixProtos.internal_static_mix_MixInfoBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MixProtos.internal_static_mix_MixInfoBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MixProtos.MixInfoBean.class, com.sh.game.protos.MixProtos.MixInfoBean.Builder.class);
      }

      // Construct using com.sh.game.protos.MixProtos.MixInfoBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        index_ = 0;

        id_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MixProtos.internal_static_mix_MixInfoBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.MixInfoBean getDefaultInstanceForType() {
        return com.sh.game.protos.MixProtos.MixInfoBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.MixInfoBean build() {
        com.sh.game.protos.MixProtos.MixInfoBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.MixInfoBean buildPartial() {
        com.sh.game.protos.MixProtos.MixInfoBean result = new com.sh.game.protos.MixProtos.MixInfoBean(this);
        result.index_ = index_;
        result.id_ = id_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MixProtos.MixInfoBean) {
          return mergeFrom((com.sh.game.protos.MixProtos.MixInfoBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MixProtos.MixInfoBean other) {
        if (other == com.sh.game.protos.MixProtos.MixInfoBean.getDefaultInstance()) return this;
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MixProtos.MixInfoBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MixProtos.MixInfoBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private int id_ ;
      /**
       * <pre>
       *道具id
       * </pre>
       *
       * <code>int32 id = 2;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <pre>
       *道具id
       * </pre>
       *
       * <code>int32 id = 2;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *道具id
       * </pre>
       *
       * <code>int32 id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:mix.MixInfoBean)
    }

    // @@protoc_insertion_point(class_scope:mix.MixInfoBean)
    private static final com.sh.game.protos.MixProtos.MixInfoBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MixProtos.MixInfoBean();
    }

    public static com.sh.game.protos.MixProtos.MixInfoBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MixInfoBean>
        PARSER = new com.google.protobuf.AbstractParser<MixInfoBean>() {
      @java.lang.Override
      public MixInfoBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MixInfoBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MixInfoBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MixInfoBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MixProtos.MixInfoBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMixMergeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:mix.ReqMixMerge)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    int getIndex();

    /**
     * <pre>
     *材料装备id
     * </pre>
     *
     * <code>int64 itemId = 2;</code>
     * @return The itemId.
     */
    long getItemId();
  }
  /**
   * <pre>
   ** class='ReqMixMerge' id='1' desc='请求融合装备' 
   * </pre>
   *
   * Protobuf type {@code mix.ReqMixMerge}
   */
  public static final class ReqMixMerge extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:mix.ReqMixMerge)
      ReqMixMergeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMixMerge.newBuilder() to construct.
    private ReqMixMerge(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMixMerge() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMixMerge();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMixMerge(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              index_ = input.readInt32();
              break;
            }
            case 16: {

              itemId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixMerge_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixMerge_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MixProtos.ReqMixMerge.class, com.sh.game.protos.MixProtos.ReqMixMerge.Builder.class);
    }

    public static final int INDEX_FIELD_NUMBER = 1;
    private int index_;
    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    public static final int ITEMID_FIELD_NUMBER = 2;
    private long itemId_;
    /**
     * <pre>
     *材料装备id
     * </pre>
     *
     * <code>int64 itemId = 2;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public long getItemId() {
      return itemId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (index_ != 0) {
        output.writeInt32(1, index_);
      }
      if (itemId_ != 0L) {
        output.writeInt64(2, itemId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, index_);
      }
      if (itemId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, itemId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MixProtos.ReqMixMerge)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MixProtos.ReqMixMerge other = (com.sh.game.protos.MixProtos.ReqMixMerge) obj;

      if (getIndex()
          != other.getIndex()) return false;
      if (getItemId()
          != other.getItemId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (37 * hash) + ITEMID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getItemId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ReqMixMerge parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MixProtos.ReqMixMerge prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMixMerge' id='1' desc='请求融合装备' 
     * </pre>
     *
     * Protobuf type {@code mix.ReqMixMerge}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:mix.ReqMixMerge)
        com.sh.game.protos.MixProtos.ReqMixMergeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixMerge_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixMerge_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MixProtos.ReqMixMerge.class, com.sh.game.protos.MixProtos.ReqMixMerge.Builder.class);
      }

      // Construct using com.sh.game.protos.MixProtos.ReqMixMerge.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        index_ = 0;

        itemId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixMerge_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ReqMixMerge getDefaultInstanceForType() {
        return com.sh.game.protos.MixProtos.ReqMixMerge.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ReqMixMerge build() {
        com.sh.game.protos.MixProtos.ReqMixMerge result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ReqMixMerge buildPartial() {
        com.sh.game.protos.MixProtos.ReqMixMerge result = new com.sh.game.protos.MixProtos.ReqMixMerge(this);
        result.index_ = index_;
        result.itemId_ = itemId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MixProtos.ReqMixMerge) {
          return mergeFrom((com.sh.game.protos.MixProtos.ReqMixMerge)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MixProtos.ReqMixMerge other) {
        if (other == com.sh.game.protos.MixProtos.ReqMixMerge.getDefaultInstance()) return this;
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        if (other.getItemId() != 0L) {
          setItemId(other.getItemId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MixProtos.ReqMixMerge parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MixProtos.ReqMixMerge) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }

      private long itemId_ ;
      /**
       * <pre>
       *材料装备id
       * </pre>
       *
       * <code>int64 itemId = 2;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public long getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       *材料装备id
       * </pre>
       *
       * <code>int64 itemId = 2;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(long value) {
        
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *材料装备id
       * </pre>
       *
       * <code>int64 itemId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        
        itemId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:mix.ReqMixMerge)
    }

    // @@protoc_insertion_point(class_scope:mix.ReqMixMerge)
    private static final com.sh.game.protos.MixProtos.ReqMixMerge DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MixProtos.ReqMixMerge();
    }

    public static com.sh.game.protos.MixProtos.ReqMixMerge getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMixMerge>
        PARSER = new com.google.protobuf.AbstractParser<ReqMixMerge>() {
      @java.lang.Override
      public ReqMixMerge parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMixMerge(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMixMerge> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqMixMerge> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MixProtos.ReqMixMerge getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMixBreakOrBuilder extends
      // @@protoc_insertion_point(interface_extends:mix.ReqMixBreak)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *装备id
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    int getIndex();
  }
  /**
   * <pre>
   ** class='ReqMixBreak' id='2' desc='请求分解装备' 
   * </pre>
   *
   * Protobuf type {@code mix.ReqMixBreak}
   */
  public static final class ReqMixBreak extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:mix.ReqMixBreak)
      ReqMixBreakOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMixBreak.newBuilder() to construct.
    private ReqMixBreak(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMixBreak() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMixBreak();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMixBreak(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              index_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixBreak_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixBreak_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MixProtos.ReqMixBreak.class, com.sh.game.protos.MixProtos.ReqMixBreak.Builder.class);
    }

    public static final int INDEX_FIELD_NUMBER = 1;
    private int index_;
    /**
     * <pre>
     *装备id
     * </pre>
     *
     * <code>int32 index = 1;</code>
     * @return The index.
     */
    @java.lang.Override
    public int getIndex() {
      return index_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (index_ != 0) {
        output.writeInt32(1, index_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (index_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, index_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MixProtos.ReqMixBreak)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MixProtos.ReqMixBreak other = (com.sh.game.protos.MixProtos.ReqMixBreak) obj;

      if (getIndex()
          != other.getIndex()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + INDEX_FIELD_NUMBER;
      hash = (53 * hash) + getIndex();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ReqMixBreak parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MixProtos.ReqMixBreak prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMixBreak' id='2' desc='请求分解装备' 
     * </pre>
     *
     * Protobuf type {@code mix.ReqMixBreak}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:mix.ReqMixBreak)
        com.sh.game.protos.MixProtos.ReqMixBreakOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixBreak_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixBreak_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MixProtos.ReqMixBreak.class, com.sh.game.protos.MixProtos.ReqMixBreak.Builder.class);
      }

      // Construct using com.sh.game.protos.MixProtos.ReqMixBreak.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        index_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixBreak_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ReqMixBreak getDefaultInstanceForType() {
        return com.sh.game.protos.MixProtos.ReqMixBreak.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ReqMixBreak build() {
        com.sh.game.protos.MixProtos.ReqMixBreak result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ReqMixBreak buildPartial() {
        com.sh.game.protos.MixProtos.ReqMixBreak result = new com.sh.game.protos.MixProtos.ReqMixBreak(this);
        result.index_ = index_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MixProtos.ReqMixBreak) {
          return mergeFrom((com.sh.game.protos.MixProtos.ReqMixBreak)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MixProtos.ReqMixBreak other) {
        if (other == com.sh.game.protos.MixProtos.ReqMixBreak.getDefaultInstance()) return this;
        if (other.getIndex() != 0) {
          setIndex(other.getIndex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MixProtos.ReqMixBreak parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MixProtos.ReqMixBreak) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int index_ ;
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return The index.
       */
      @java.lang.Override
      public int getIndex() {
        return index_;
      }
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @param value The index to set.
       * @return This builder for chaining.
       */
      public Builder setIndex(int value) {
        
        index_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int32 index = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIndex() {
        
        index_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:mix.ReqMixBreak)
    }

    // @@protoc_insertion_point(class_scope:mix.ReqMixBreak)
    private static final com.sh.game.protos.MixProtos.ReqMixBreak DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MixProtos.ReqMixBreak();
    }

    public static com.sh.game.protos.MixProtos.ReqMixBreak getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMixBreak>
        PARSER = new com.google.protobuf.AbstractParser<ReqMixBreak>() {
      @java.lang.Override
      public ReqMixBreak parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMixBreak(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMixBreak> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqMixBreak> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MixProtos.ReqMixBreak getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMixInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:mix.ReqMixInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqMixInfo' id='3' desc='请求部位融合信息' 
   * </pre>
   *
   * Protobuf type {@code mix.ReqMixInfo}
   */
  public static final class ReqMixInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:mix.ReqMixInfo)
      ReqMixInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMixInfo.newBuilder() to construct.
    private ReqMixInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMixInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMixInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMixInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MixProtos.ReqMixInfo.class, com.sh.game.protos.MixProtos.ReqMixInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MixProtos.ReqMixInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MixProtos.ReqMixInfo other = (com.sh.game.protos.MixProtos.ReqMixInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ReqMixInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MixProtos.ReqMixInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMixInfo' id='3' desc='请求部位融合信息' 
     * </pre>
     *
     * Protobuf type {@code mix.ReqMixInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:mix.ReqMixInfo)
        com.sh.game.protos.MixProtos.ReqMixInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MixProtos.ReqMixInfo.class, com.sh.game.protos.MixProtos.ReqMixInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.MixProtos.ReqMixInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ReqMixInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ReqMixInfo getDefaultInstanceForType() {
        return com.sh.game.protos.MixProtos.ReqMixInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ReqMixInfo build() {
        com.sh.game.protos.MixProtos.ReqMixInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ReqMixInfo buildPartial() {
        com.sh.game.protos.MixProtos.ReqMixInfo result = new com.sh.game.protos.MixProtos.ReqMixInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MixProtos.ReqMixInfo) {
          return mergeFrom((com.sh.game.protos.MixProtos.ReqMixInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MixProtos.ReqMixInfo other) {
        if (other == com.sh.game.protos.MixProtos.ReqMixInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MixProtos.ReqMixInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MixProtos.ReqMixInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:mix.ReqMixInfo)
    }

    // @@protoc_insertion_point(class_scope:mix.ReqMixInfo)
    private static final com.sh.game.protos.MixProtos.ReqMixInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MixProtos.ReqMixInfo();
    }

    public static com.sh.game.protos.MixProtos.ReqMixInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMixInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqMixInfo>() {
      @java.lang.Override
      public ReqMixInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMixInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMixInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqMixInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MixProtos.ReqMixInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMixResultOrBuilder extends
      // @@protoc_insertion_point(interface_extends:mix.ResMixResult)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *变化
     * </pre>
     *
     * <code>.mix.MixInfoBean mix = 1;</code>
     * @return Whether the mix field is set.
     */
    boolean hasMix();
    /**
     * <pre>
     *变化
     * </pre>
     *
     * <code>.mix.MixInfoBean mix = 1;</code>
     * @return The mix.
     */
    com.sh.game.protos.MixProtos.MixInfoBean getMix();
    /**
     * <pre>
     *变化
     * </pre>
     *
     * <code>.mix.MixInfoBean mix = 1;</code>
     */
    com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder getMixOrBuilder();
  }
  /**
   * <pre>
   ** class='ResMixResult' id='11' desc='融合/分解成功返回' 
   * </pre>
   *
   * Protobuf type {@code mix.ResMixResult}
   */
  public static final class ResMixResult extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:mix.ResMixResult)
      ResMixResultOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMixResult.newBuilder() to construct.
    private ResMixResult(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMixResult() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMixResult();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMixResult(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.sh.game.protos.MixProtos.MixInfoBean.Builder subBuilder = null;
              if (mix_ != null) {
                subBuilder = mix_.toBuilder();
              }
              mix_ = input.readMessage(com.sh.game.protos.MixProtos.MixInfoBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(mix_);
                mix_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ResMixResult_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ResMixResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MixProtos.ResMixResult.class, com.sh.game.protos.MixProtos.ResMixResult.Builder.class);
    }

    public static final int MIX_FIELD_NUMBER = 1;
    private com.sh.game.protos.MixProtos.MixInfoBean mix_;
    /**
     * <pre>
     *变化
     * </pre>
     *
     * <code>.mix.MixInfoBean mix = 1;</code>
     * @return Whether the mix field is set.
     */
    @java.lang.Override
    public boolean hasMix() {
      return mix_ != null;
    }
    /**
     * <pre>
     *变化
     * </pre>
     *
     * <code>.mix.MixInfoBean mix = 1;</code>
     * @return The mix.
     */
    @java.lang.Override
    public com.sh.game.protos.MixProtos.MixInfoBean getMix() {
      return mix_ == null ? com.sh.game.protos.MixProtos.MixInfoBean.getDefaultInstance() : mix_;
    }
    /**
     * <pre>
     *变化
     * </pre>
     *
     * <code>.mix.MixInfoBean mix = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder getMixOrBuilder() {
      return getMix();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (mix_ != null) {
        output.writeMessage(1, getMix());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (mix_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getMix());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MixProtos.ResMixResult)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MixProtos.ResMixResult other = (com.sh.game.protos.MixProtos.ResMixResult) obj;

      if (hasMix() != other.hasMix()) return false;
      if (hasMix()) {
        if (!getMix()
            .equals(other.getMix())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMix()) {
        hash = (37 * hash) + MIX_FIELD_NUMBER;
        hash = (53 * hash) + getMix().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ResMixResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MixProtos.ResMixResult prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMixResult' id='11' desc='融合/分解成功返回' 
     * </pre>
     *
     * Protobuf type {@code mix.ResMixResult}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:mix.ResMixResult)
        com.sh.game.protos.MixProtos.ResMixResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ResMixResult_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ResMixResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MixProtos.ResMixResult.class, com.sh.game.protos.MixProtos.ResMixResult.Builder.class);
      }

      // Construct using com.sh.game.protos.MixProtos.ResMixResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (mixBuilder_ == null) {
          mix_ = null;
        } else {
          mix_ = null;
          mixBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ResMixResult_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ResMixResult getDefaultInstanceForType() {
        return com.sh.game.protos.MixProtos.ResMixResult.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ResMixResult build() {
        com.sh.game.protos.MixProtos.ResMixResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ResMixResult buildPartial() {
        com.sh.game.protos.MixProtos.ResMixResult result = new com.sh.game.protos.MixProtos.ResMixResult(this);
        if (mixBuilder_ == null) {
          result.mix_ = mix_;
        } else {
          result.mix_ = mixBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MixProtos.ResMixResult) {
          return mergeFrom((com.sh.game.protos.MixProtos.ResMixResult)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MixProtos.ResMixResult other) {
        if (other == com.sh.game.protos.MixProtos.ResMixResult.getDefaultInstance()) return this;
        if (other.hasMix()) {
          mergeMix(other.getMix());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MixProtos.ResMixResult parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MixProtos.ResMixResult) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.sh.game.protos.MixProtos.MixInfoBean mix_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.MixProtos.MixInfoBean, com.sh.game.protos.MixProtos.MixInfoBean.Builder, com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder> mixBuilder_;
      /**
       * <pre>
       *变化
       * </pre>
       *
       * <code>.mix.MixInfoBean mix = 1;</code>
       * @return Whether the mix field is set.
       */
      public boolean hasMix() {
        return mixBuilder_ != null || mix_ != null;
      }
      /**
       * <pre>
       *变化
       * </pre>
       *
       * <code>.mix.MixInfoBean mix = 1;</code>
       * @return The mix.
       */
      public com.sh.game.protos.MixProtos.MixInfoBean getMix() {
        if (mixBuilder_ == null) {
          return mix_ == null ? com.sh.game.protos.MixProtos.MixInfoBean.getDefaultInstance() : mix_;
        } else {
          return mixBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *变化
       * </pre>
       *
       * <code>.mix.MixInfoBean mix = 1;</code>
       */
      public Builder setMix(com.sh.game.protos.MixProtos.MixInfoBean value) {
        if (mixBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mix_ = value;
          onChanged();
        } else {
          mixBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *变化
       * </pre>
       *
       * <code>.mix.MixInfoBean mix = 1;</code>
       */
      public Builder setMix(
          com.sh.game.protos.MixProtos.MixInfoBean.Builder builderForValue) {
        if (mixBuilder_ == null) {
          mix_ = builderForValue.build();
          onChanged();
        } else {
          mixBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *变化
       * </pre>
       *
       * <code>.mix.MixInfoBean mix = 1;</code>
       */
      public Builder mergeMix(com.sh.game.protos.MixProtos.MixInfoBean value) {
        if (mixBuilder_ == null) {
          if (mix_ != null) {
            mix_ =
              com.sh.game.protos.MixProtos.MixInfoBean.newBuilder(mix_).mergeFrom(value).buildPartial();
          } else {
            mix_ = value;
          }
          onChanged();
        } else {
          mixBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *变化
       * </pre>
       *
       * <code>.mix.MixInfoBean mix = 1;</code>
       */
      public Builder clearMix() {
        if (mixBuilder_ == null) {
          mix_ = null;
          onChanged();
        } else {
          mix_ = null;
          mixBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *变化
       * </pre>
       *
       * <code>.mix.MixInfoBean mix = 1;</code>
       */
      public com.sh.game.protos.MixProtos.MixInfoBean.Builder getMixBuilder() {
        
        onChanged();
        return getMixFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *变化
       * </pre>
       *
       * <code>.mix.MixInfoBean mix = 1;</code>
       */
      public com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder getMixOrBuilder() {
        if (mixBuilder_ != null) {
          return mixBuilder_.getMessageOrBuilder();
        } else {
          return mix_ == null ?
              com.sh.game.protos.MixProtos.MixInfoBean.getDefaultInstance() : mix_;
        }
      }
      /**
       * <pre>
       *变化
       * </pre>
       *
       * <code>.mix.MixInfoBean mix = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.MixProtos.MixInfoBean, com.sh.game.protos.MixProtos.MixInfoBean.Builder, com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder> 
          getMixFieldBuilder() {
        if (mixBuilder_ == null) {
          mixBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.MixProtos.MixInfoBean, com.sh.game.protos.MixProtos.MixInfoBean.Builder, com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder>(
                  getMix(),
                  getParentForChildren(),
                  isClean());
          mix_ = null;
        }
        return mixBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:mix.ResMixResult)
    }

    // @@protoc_insertion_point(class_scope:mix.ResMixResult)
    private static final com.sh.game.protos.MixProtos.ResMixResult DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MixProtos.ResMixResult();
    }

    public static com.sh.game.protos.MixProtos.ResMixResult getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMixResult>
        PARSER = new com.google.protobuf.AbstractParser<ResMixResult>() {
      @java.lang.Override
      public ResMixResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMixResult(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMixResult> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResMixResult> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MixProtos.ResMixResult getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMixInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:mix.ResMixInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    java.util.List<com.sh.game.protos.MixProtos.MixInfoBean> 
        getMixList();
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    com.sh.game.protos.MixProtos.MixInfoBean getMix(int index);
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    int getMixCount();
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    java.util.List<? extends com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder> 
        getMixOrBuilderList();
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder getMixOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResMixInfo' id='12' desc='返回融合信息' 
   * </pre>
   *
   * Protobuf type {@code mix.ResMixInfo}
   */
  public static final class ResMixInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:mix.ResMixInfo)
      ResMixInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMixInfo.newBuilder() to construct.
    private ResMixInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMixInfo() {
      mix_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMixInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMixInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                mix_ = new java.util.ArrayList<com.sh.game.protos.MixProtos.MixInfoBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              mix_.add(
                  input.readMessage(com.sh.game.protos.MixProtos.MixInfoBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          mix_ = java.util.Collections.unmodifiableList(mix_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ResMixInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MixProtos.internal_static_mix_ResMixInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MixProtos.ResMixInfo.class, com.sh.game.protos.MixProtos.ResMixInfo.Builder.class);
    }

    public static final int MIX_FIELD_NUMBER = 1;
    private java.util.List<com.sh.game.protos.MixProtos.MixInfoBean> mix_;
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.MixProtos.MixInfoBean> getMixList() {
      return mix_;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder> 
        getMixOrBuilderList() {
      return mix_;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    @java.lang.Override
    public int getMixCount() {
      return mix_.size();
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.MixProtos.MixInfoBean getMix(int index) {
      return mix_.get(index);
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>repeated .mix.MixInfoBean mix = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder getMixOrBuilder(
        int index) {
      return mix_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < mix_.size(); i++) {
        output.writeMessage(1, mix_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < mix_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, mix_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MixProtos.ResMixInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MixProtos.ResMixInfo other = (com.sh.game.protos.MixProtos.ResMixInfo) obj;

      if (!getMixList()
          .equals(other.getMixList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getMixCount() > 0) {
        hash = (37 * hash) + MIX_FIELD_NUMBER;
        hash = (53 * hash) + getMixList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MixProtos.ResMixInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MixProtos.ResMixInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMixInfo' id='12' desc='返回融合信息' 
     * </pre>
     *
     * Protobuf type {@code mix.ResMixInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:mix.ResMixInfo)
        com.sh.game.protos.MixProtos.ResMixInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ResMixInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ResMixInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MixProtos.ResMixInfo.class, com.sh.game.protos.MixProtos.ResMixInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.MixProtos.ResMixInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMixFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (mixBuilder_ == null) {
          mix_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          mixBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MixProtos.internal_static_mix_ResMixInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ResMixInfo getDefaultInstanceForType() {
        return com.sh.game.protos.MixProtos.ResMixInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ResMixInfo build() {
        com.sh.game.protos.MixProtos.ResMixInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MixProtos.ResMixInfo buildPartial() {
        com.sh.game.protos.MixProtos.ResMixInfo result = new com.sh.game.protos.MixProtos.ResMixInfo(this);
        int from_bitField0_ = bitField0_;
        if (mixBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            mix_ = java.util.Collections.unmodifiableList(mix_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.mix_ = mix_;
        } else {
          result.mix_ = mixBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MixProtos.ResMixInfo) {
          return mergeFrom((com.sh.game.protos.MixProtos.ResMixInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MixProtos.ResMixInfo other) {
        if (other == com.sh.game.protos.MixProtos.ResMixInfo.getDefaultInstance()) return this;
        if (mixBuilder_ == null) {
          if (!other.mix_.isEmpty()) {
            if (mix_.isEmpty()) {
              mix_ = other.mix_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureMixIsMutable();
              mix_.addAll(other.mix_);
            }
            onChanged();
          }
        } else {
          if (!other.mix_.isEmpty()) {
            if (mixBuilder_.isEmpty()) {
              mixBuilder_.dispose();
              mixBuilder_ = null;
              mix_ = other.mix_;
              bitField0_ = (bitField0_ & ~0x00000001);
              mixBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getMixFieldBuilder() : null;
            } else {
              mixBuilder_.addAllMessages(other.mix_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MixProtos.ResMixInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MixProtos.ResMixInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.sh.game.protos.MixProtos.MixInfoBean> mix_ =
        java.util.Collections.emptyList();
      private void ensureMixIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          mix_ = new java.util.ArrayList<com.sh.game.protos.MixProtos.MixInfoBean>(mix_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.MixProtos.MixInfoBean, com.sh.game.protos.MixProtos.MixInfoBean.Builder, com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder> mixBuilder_;

      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public java.util.List<com.sh.game.protos.MixProtos.MixInfoBean> getMixList() {
        if (mixBuilder_ == null) {
          return java.util.Collections.unmodifiableList(mix_);
        } else {
          return mixBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public int getMixCount() {
        if (mixBuilder_ == null) {
          return mix_.size();
        } else {
          return mixBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public com.sh.game.protos.MixProtos.MixInfoBean getMix(int index) {
        if (mixBuilder_ == null) {
          return mix_.get(index);
        } else {
          return mixBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public Builder setMix(
          int index, com.sh.game.protos.MixProtos.MixInfoBean value) {
        if (mixBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMixIsMutable();
          mix_.set(index, value);
          onChanged();
        } else {
          mixBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public Builder setMix(
          int index, com.sh.game.protos.MixProtos.MixInfoBean.Builder builderForValue) {
        if (mixBuilder_ == null) {
          ensureMixIsMutable();
          mix_.set(index, builderForValue.build());
          onChanged();
        } else {
          mixBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public Builder addMix(com.sh.game.protos.MixProtos.MixInfoBean value) {
        if (mixBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMixIsMutable();
          mix_.add(value);
          onChanged();
        } else {
          mixBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public Builder addMix(
          int index, com.sh.game.protos.MixProtos.MixInfoBean value) {
        if (mixBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMixIsMutable();
          mix_.add(index, value);
          onChanged();
        } else {
          mixBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public Builder addMix(
          com.sh.game.protos.MixProtos.MixInfoBean.Builder builderForValue) {
        if (mixBuilder_ == null) {
          ensureMixIsMutable();
          mix_.add(builderForValue.build());
          onChanged();
        } else {
          mixBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public Builder addMix(
          int index, com.sh.game.protos.MixProtos.MixInfoBean.Builder builderForValue) {
        if (mixBuilder_ == null) {
          ensureMixIsMutable();
          mix_.add(index, builderForValue.build());
          onChanged();
        } else {
          mixBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public Builder addAllMix(
          java.lang.Iterable<? extends com.sh.game.protos.MixProtos.MixInfoBean> values) {
        if (mixBuilder_ == null) {
          ensureMixIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, mix_);
          onChanged();
        } else {
          mixBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public Builder clearMix() {
        if (mixBuilder_ == null) {
          mix_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          mixBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public Builder removeMix(int index) {
        if (mixBuilder_ == null) {
          ensureMixIsMutable();
          mix_.remove(index);
          onChanged();
        } else {
          mixBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public com.sh.game.protos.MixProtos.MixInfoBean.Builder getMixBuilder(
          int index) {
        return getMixFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder getMixOrBuilder(
          int index) {
        if (mixBuilder_ == null) {
          return mix_.get(index);  } else {
          return mixBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public java.util.List<? extends com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder> 
           getMixOrBuilderList() {
        if (mixBuilder_ != null) {
          return mixBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(mix_);
        }
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public com.sh.game.protos.MixProtos.MixInfoBean.Builder addMixBuilder() {
        return getMixFieldBuilder().addBuilder(
            com.sh.game.protos.MixProtos.MixInfoBean.getDefaultInstance());
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public com.sh.game.protos.MixProtos.MixInfoBean.Builder addMixBuilder(
          int index) {
        return getMixFieldBuilder().addBuilder(
            index, com.sh.game.protos.MixProtos.MixInfoBean.getDefaultInstance());
      }
      /**
       * <pre>
       * </pre>
       *
       * <code>repeated .mix.MixInfoBean mix = 1;</code>
       */
      public java.util.List<com.sh.game.protos.MixProtos.MixInfoBean.Builder> 
           getMixBuilderList() {
        return getMixFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.MixProtos.MixInfoBean, com.sh.game.protos.MixProtos.MixInfoBean.Builder, com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder> 
          getMixFieldBuilder() {
        if (mixBuilder_ == null) {
          mixBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.MixProtos.MixInfoBean, com.sh.game.protos.MixProtos.MixInfoBean.Builder, com.sh.game.protos.MixProtos.MixInfoBeanOrBuilder>(
                  mix_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          mix_ = null;
        }
        return mixBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:mix.ResMixInfo)
    }

    // @@protoc_insertion_point(class_scope:mix.ResMixInfo)
    private static final com.sh.game.protos.MixProtos.ResMixInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MixProtos.ResMixInfo();
    }

    public static com.sh.game.protos.MixProtos.ResMixInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMixInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResMixInfo>() {
      @java.lang.Override
      public ResMixInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMixInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMixInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResMixInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MixProtos.ResMixInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_mix_MixInfoBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_mix_MixInfoBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_mix_ReqMixMerge_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_mix_ReqMixMerge_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_mix_ReqMixBreak_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_mix_ReqMixBreak_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_mix_ReqMixInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_mix_ReqMixInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_mix_ResMixResult_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_mix_ResMixResult_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_mix_ResMixInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_mix_ResMixInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tmix.proto\022\003mix\"(\n\013MixInfoBean\022\r\n\005index" +
      "\030\001 \001(\005\022\n\n\002id\030\002 \001(\005\",\n\013ReqMixMerge\022\r\n\005ind" +
      "ex\030\001 \001(\005\022\016\n\006itemId\030\002 \001(\003\"\034\n\013ReqMixBreak\022" +
      "\r\n\005index\030\001 \001(\005\"\014\n\nReqMixInfo\"-\n\014ResMixRe" +
      "sult\022\035\n\003mix\030\001 \001(\0132\020.mix.MixInfoBean\"+\n\nR" +
      "esMixInfo\022\035\n\003mix\030\001 \003(\0132\020.mix.MixInfoBean" +
      "B\037\n\022com.sh.game.protosB\tMixProtosb\006proto" +
      "3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_mix_MixInfoBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_mix_MixInfoBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_mix_MixInfoBean_descriptor,
        new java.lang.String[] { "Index", "Id", });
    internal_static_mix_ReqMixMerge_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_mix_ReqMixMerge_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_mix_ReqMixMerge_descriptor,
        new java.lang.String[] { "Index", "ItemId", });
    internal_static_mix_ReqMixBreak_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_mix_ReqMixBreak_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_mix_ReqMixBreak_descriptor,
        new java.lang.String[] { "Index", });
    internal_static_mix_ReqMixInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_mix_ReqMixInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_mix_ReqMixInfo_descriptor,
        new java.lang.String[] { });
    internal_static_mix_ResMixResult_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_mix_ResMixResult_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_mix_ResMixResult_descriptor,
        new java.lang.String[] { "Mix", });
    internal_static_mix_ResMixInfo_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_mix_ResMixInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_mix_ResMixInfo_descriptor,
        new java.lang.String[] { "Mix", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
