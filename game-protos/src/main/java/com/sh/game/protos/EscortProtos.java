// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: escort.proto

package com.sh.game.protos;

public final class EscortProtos {
  private EscortProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqDailyEscortInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:escort.ReqDailyEscortInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqDailyEscortInfo' id='1' desc='请求押镖信息' 
   * </pre>
   *
   * Protobuf type {@code escort.ReqDailyEscortInfo}
   */
  public static final class ReqDailyEscortInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:escort.ReqDailyEscortInfo)
      ReqDailyEscortInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqDailyEscortInfo.newBuilder() to construct.
    private ReqDailyEscortInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqDailyEscortInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqDailyEscortInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqDailyEscortInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.EscortProtos.ReqDailyEscortInfo.class, com.sh.game.protos.EscortProtos.ReqDailyEscortInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.EscortProtos.ReqDailyEscortInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.EscortProtos.ReqDailyEscortInfo other = (com.sh.game.protos.EscortProtos.ReqDailyEscortInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.EscortProtos.ReqDailyEscortInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqDailyEscortInfo' id='1' desc='请求押镖信息' 
     * </pre>
     *
     * Protobuf type {@code escort.ReqDailyEscortInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:escort.ReqDailyEscortInfo)
        com.sh.game.protos.EscortProtos.ReqDailyEscortInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.EscortProtos.ReqDailyEscortInfo.class, com.sh.game.protos.EscortProtos.ReqDailyEscortInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.EscortProtos.ReqDailyEscortInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortInfo getDefaultInstanceForType() {
        return com.sh.game.protos.EscortProtos.ReqDailyEscortInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortInfo build() {
        com.sh.game.protos.EscortProtos.ReqDailyEscortInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortInfo buildPartial() {
        com.sh.game.protos.EscortProtos.ReqDailyEscortInfo result = new com.sh.game.protos.EscortProtos.ReqDailyEscortInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.EscortProtos.ReqDailyEscortInfo) {
          return mergeFrom((com.sh.game.protos.EscortProtos.ReqDailyEscortInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.EscortProtos.ReqDailyEscortInfo other) {
        if (other == com.sh.game.protos.EscortProtos.ReqDailyEscortInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.EscortProtos.ReqDailyEscortInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.EscortProtos.ReqDailyEscortInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:escort.ReqDailyEscortInfo)
    }

    // @@protoc_insertion_point(class_scope:escort.ReqDailyEscortInfo)
    private static final com.sh.game.protos.EscortProtos.ReqDailyEscortInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.EscortProtos.ReqDailyEscortInfo();
    }

    public static com.sh.game.protos.EscortProtos.ReqDailyEscortInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqDailyEscortInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqDailyEscortInfo>() {
      @java.lang.Override
      public ReqDailyEscortInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqDailyEscortInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqDailyEscortInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqDailyEscortInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.EscortProtos.ReqDailyEscortInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqDailyEscortStartOrBuilder extends
      // @@protoc_insertion_point(interface_extends:escort.ReqDailyEscortStart)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *请求押镖id
     * </pre>
     *
     * <code>int32 escortId = 1;</code>
     * @return The escortId.
     */
    int getEscortId();
  }
  /**
   * <pre>
   ** class='ReqDailyEscortStart' id='2' desc='请求押镖' 
   * </pre>
   *
   * Protobuf type {@code escort.ReqDailyEscortStart}
   */
  public static final class ReqDailyEscortStart extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:escort.ReqDailyEscortStart)
      ReqDailyEscortStartOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqDailyEscortStart.newBuilder() to construct.
    private ReqDailyEscortStart(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqDailyEscortStart() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqDailyEscortStart();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqDailyEscortStart(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              escortId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortStart_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortStart_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.EscortProtos.ReqDailyEscortStart.class, com.sh.game.protos.EscortProtos.ReqDailyEscortStart.Builder.class);
    }

    public static final int ESCORTID_FIELD_NUMBER = 1;
    private int escortId_;
    /**
     * <pre>
     *请求押镖id
     * </pre>
     *
     * <code>int32 escortId = 1;</code>
     * @return The escortId.
     */
    @java.lang.Override
    public int getEscortId() {
      return escortId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (escortId_ != 0) {
        output.writeInt32(1, escortId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (escortId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, escortId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.EscortProtos.ReqDailyEscortStart)) {
        return super.equals(obj);
      }
      com.sh.game.protos.EscortProtos.ReqDailyEscortStart other = (com.sh.game.protos.EscortProtos.ReqDailyEscortStart) obj;

      if (getEscortId()
          != other.getEscortId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ESCORTID_FIELD_NUMBER;
      hash = (53 * hash) + getEscortId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.EscortProtos.ReqDailyEscortStart prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqDailyEscortStart' id='2' desc='请求押镖' 
     * </pre>
     *
     * Protobuf type {@code escort.ReqDailyEscortStart}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:escort.ReqDailyEscortStart)
        com.sh.game.protos.EscortProtos.ReqDailyEscortStartOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortStart_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortStart_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.EscortProtos.ReqDailyEscortStart.class, com.sh.game.protos.EscortProtos.ReqDailyEscortStart.Builder.class);
      }

      // Construct using com.sh.game.protos.EscortProtos.ReqDailyEscortStart.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        escortId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortStart_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortStart getDefaultInstanceForType() {
        return com.sh.game.protos.EscortProtos.ReqDailyEscortStart.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortStart build() {
        com.sh.game.protos.EscortProtos.ReqDailyEscortStart result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortStart buildPartial() {
        com.sh.game.protos.EscortProtos.ReqDailyEscortStart result = new com.sh.game.protos.EscortProtos.ReqDailyEscortStart(this);
        result.escortId_ = escortId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.EscortProtos.ReqDailyEscortStart) {
          return mergeFrom((com.sh.game.protos.EscortProtos.ReqDailyEscortStart)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.EscortProtos.ReqDailyEscortStart other) {
        if (other == com.sh.game.protos.EscortProtos.ReqDailyEscortStart.getDefaultInstance()) return this;
        if (other.getEscortId() != 0) {
          setEscortId(other.getEscortId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.EscortProtos.ReqDailyEscortStart parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.EscortProtos.ReqDailyEscortStart) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int escortId_ ;
      /**
       * <pre>
       *请求押镖id
       * </pre>
       *
       * <code>int32 escortId = 1;</code>
       * @return The escortId.
       */
      @java.lang.Override
      public int getEscortId() {
        return escortId_;
      }
      /**
       * <pre>
       *请求押镖id
       * </pre>
       *
       * <code>int32 escortId = 1;</code>
       * @param value The escortId to set.
       * @return This builder for chaining.
       */
      public Builder setEscortId(int value) {
        
        escortId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *请求押镖id
       * </pre>
       *
       * <code>int32 escortId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEscortId() {
        
        escortId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:escort.ReqDailyEscortStart)
    }

    // @@protoc_insertion_point(class_scope:escort.ReqDailyEscortStart)
    private static final com.sh.game.protos.EscortProtos.ReqDailyEscortStart DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.EscortProtos.ReqDailyEscortStart();
    }

    public static com.sh.game.protos.EscortProtos.ReqDailyEscortStart getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqDailyEscortStart>
        PARSER = new com.google.protobuf.AbstractParser<ReqDailyEscortStart>() {
      @java.lang.Override
      public ReqDailyEscortStart parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqDailyEscortStart(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqDailyEscortStart> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqDailyEscortStart> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.EscortProtos.ReqDailyEscortStart getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqDailyEscortAbortOrBuilder extends
      // @@protoc_insertion_point(interface_extends:escort.ReqDailyEscortAbort)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqDailyEscortAbort' id='3' desc='请求放弃押镖' 
   * </pre>
   *
   * Protobuf type {@code escort.ReqDailyEscortAbort}
   */
  public static final class ReqDailyEscortAbort extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:escort.ReqDailyEscortAbort)
      ReqDailyEscortAbortOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqDailyEscortAbort.newBuilder() to construct.
    private ReqDailyEscortAbort(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqDailyEscortAbort() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqDailyEscortAbort();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqDailyEscortAbort(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAbort_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAbort_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.EscortProtos.ReqDailyEscortAbort.class, com.sh.game.protos.EscortProtos.ReqDailyEscortAbort.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.EscortProtos.ReqDailyEscortAbort)) {
        return super.equals(obj);
      }
      com.sh.game.protos.EscortProtos.ReqDailyEscortAbort other = (com.sh.game.protos.EscortProtos.ReqDailyEscortAbort) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.EscortProtos.ReqDailyEscortAbort prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqDailyEscortAbort' id='3' desc='请求放弃押镖' 
     * </pre>
     *
     * Protobuf type {@code escort.ReqDailyEscortAbort}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:escort.ReqDailyEscortAbort)
        com.sh.game.protos.EscortProtos.ReqDailyEscortAbortOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAbort_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAbort_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.EscortProtos.ReqDailyEscortAbort.class, com.sh.game.protos.EscortProtos.ReqDailyEscortAbort.Builder.class);
      }

      // Construct using com.sh.game.protos.EscortProtos.ReqDailyEscortAbort.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAbort_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortAbort getDefaultInstanceForType() {
        return com.sh.game.protos.EscortProtos.ReqDailyEscortAbort.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortAbort build() {
        com.sh.game.protos.EscortProtos.ReqDailyEscortAbort result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortAbort buildPartial() {
        com.sh.game.protos.EscortProtos.ReqDailyEscortAbort result = new com.sh.game.protos.EscortProtos.ReqDailyEscortAbort(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.EscortProtos.ReqDailyEscortAbort) {
          return mergeFrom((com.sh.game.protos.EscortProtos.ReqDailyEscortAbort)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.EscortProtos.ReqDailyEscortAbort other) {
        if (other == com.sh.game.protos.EscortProtos.ReqDailyEscortAbort.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.EscortProtos.ReqDailyEscortAbort parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.EscortProtos.ReqDailyEscortAbort) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:escort.ReqDailyEscortAbort)
    }

    // @@protoc_insertion_point(class_scope:escort.ReqDailyEscortAbort)
    private static final com.sh.game.protos.EscortProtos.ReqDailyEscortAbort DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.EscortProtos.ReqDailyEscortAbort();
    }

    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAbort getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqDailyEscortAbort>
        PARSER = new com.google.protobuf.AbstractParser<ReqDailyEscortAbort>() {
      @java.lang.Override
      public ReqDailyEscortAbort parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqDailyEscortAbort(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqDailyEscortAbort> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqDailyEscortAbort> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.EscortProtos.ReqDailyEscortAbort getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqDailyEscortAcquireOrBuilder extends
      // @@protoc_insertion_point(interface_extends:escort.ReqDailyEscortAcquire)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *extra
     * </pre>
     *
     * <code>int32 extra = 1;</code>
     * @return The extra.
     */
    int getExtra();
  }
  /**
   * <pre>
   ** class='ReqDailyEscortAcquire' id='4' desc='请求领奖' 
   * </pre>
   *
   * Protobuf type {@code escort.ReqDailyEscortAcquire}
   */
  public static final class ReqDailyEscortAcquire extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:escort.ReqDailyEscortAcquire)
      ReqDailyEscortAcquireOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqDailyEscortAcquire.newBuilder() to construct.
    private ReqDailyEscortAcquire(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqDailyEscortAcquire() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqDailyEscortAcquire();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqDailyEscortAcquire(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              extra_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAcquire_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAcquire_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire.class, com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire.Builder.class);
    }

    public static final int EXTRA_FIELD_NUMBER = 1;
    private int extra_;
    /**
     * <pre>
     *extra
     * </pre>
     *
     * <code>int32 extra = 1;</code>
     * @return The extra.
     */
    @java.lang.Override
    public int getExtra() {
      return extra_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (extra_ != 0) {
        output.writeInt32(1, extra_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (extra_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, extra_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire)) {
        return super.equals(obj);
      }
      com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire other = (com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire) obj;

      if (getExtra()
          != other.getExtra()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + EXTRA_FIELD_NUMBER;
      hash = (53 * hash) + getExtra();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqDailyEscortAcquire' id='4' desc='请求领奖' 
     * </pre>
     *
     * Protobuf type {@code escort.ReqDailyEscortAcquire}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:escort.ReqDailyEscortAcquire)
        com.sh.game.protos.EscortProtos.ReqDailyEscortAcquireOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAcquire_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAcquire_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire.class, com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire.Builder.class);
      }

      // Construct using com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        extra_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ReqDailyEscortAcquire_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire getDefaultInstanceForType() {
        return com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire build() {
        com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire buildPartial() {
        com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire result = new com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire(this);
        result.extra_ = extra_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire) {
          return mergeFrom((com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire other) {
        if (other == com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire.getDefaultInstance()) return this;
        if (other.getExtra() != 0) {
          setExtra(other.getExtra());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int extra_ ;
      /**
       * <pre>
       *extra
       * </pre>
       *
       * <code>int32 extra = 1;</code>
       * @return The extra.
       */
      @java.lang.Override
      public int getExtra() {
        return extra_;
      }
      /**
       * <pre>
       *extra
       * </pre>
       *
       * <code>int32 extra = 1;</code>
       * @param value The extra to set.
       * @return This builder for chaining.
       */
      public Builder setExtra(int value) {
        
        extra_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *extra
       * </pre>
       *
       * <code>int32 extra = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtra() {
        
        extra_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:escort.ReqDailyEscortAcquire)
    }

    // @@protoc_insertion_point(class_scope:escort.ReqDailyEscortAcquire)
    private static final com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire();
    }

    public static com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqDailyEscortAcquire>
        PARSER = new com.google.protobuf.AbstractParser<ReqDailyEscortAcquire>() {
      @java.lang.Override
      public ReqDailyEscortAcquire parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqDailyEscortAcquire(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqDailyEscortAcquire> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqDailyEscortAcquire> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.EscortProtos.ReqDailyEscortAcquire getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResDailyEscortInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:escort.ResDailyEscortInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *轮次
     * </pre>
     *
     * <code>int32 estRound = 1;</code>
     * @return The estRound.
     */
    int getEstRound();

    /**
     * <pre>
     *劫镖次数
     * </pre>
     *
     * <code>int32 robTimes = 2;</code>
     * @return The robTimes.
     */
    int getRobTimes();
  }
  /**
   * <pre>
   ** class='ResDailyEscortInfo' id='11' desc='返回押镖信息' 
   * </pre>
   *
   * Protobuf type {@code escort.ResDailyEscortInfo}
   */
  public static final class ResDailyEscortInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:escort.ResDailyEscortInfo)
      ResDailyEscortInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResDailyEscortInfo.newBuilder() to construct.
    private ResDailyEscortInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResDailyEscortInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResDailyEscortInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResDailyEscortInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              estRound_ = input.readInt32();
              break;
            }
            case 16: {

              robTimes_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.EscortProtos.ResDailyEscortInfo.class, com.sh.game.protos.EscortProtos.ResDailyEscortInfo.Builder.class);
    }

    public static final int ESTROUND_FIELD_NUMBER = 1;
    private int estRound_;
    /**
     * <pre>
     *轮次
     * </pre>
     *
     * <code>int32 estRound = 1;</code>
     * @return The estRound.
     */
    @java.lang.Override
    public int getEstRound() {
      return estRound_;
    }

    public static final int ROBTIMES_FIELD_NUMBER = 2;
    private int robTimes_;
    /**
     * <pre>
     *劫镖次数
     * </pre>
     *
     * <code>int32 robTimes = 2;</code>
     * @return The robTimes.
     */
    @java.lang.Override
    public int getRobTimes() {
      return robTimes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (estRound_ != 0) {
        output.writeInt32(1, estRound_);
      }
      if (robTimes_ != 0) {
        output.writeInt32(2, robTimes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (estRound_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, estRound_);
      }
      if (robTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, robTimes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.EscortProtos.ResDailyEscortInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.EscortProtos.ResDailyEscortInfo other = (com.sh.game.protos.EscortProtos.ResDailyEscortInfo) obj;

      if (getEstRound()
          != other.getEstRound()) return false;
      if (getRobTimes()
          != other.getRobTimes()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ESTROUND_FIELD_NUMBER;
      hash = (53 * hash) + getEstRound();
      hash = (37 * hash) + ROBTIMES_FIELD_NUMBER;
      hash = (53 * hash) + getRobTimes();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.EscortProtos.ResDailyEscortInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResDailyEscortInfo' id='11' desc='返回押镖信息' 
     * </pre>
     *
     * Protobuf type {@code escort.ResDailyEscortInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:escort.ResDailyEscortInfo)
        com.sh.game.protos.EscortProtos.ResDailyEscortInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.EscortProtos.ResDailyEscortInfo.class, com.sh.game.protos.EscortProtos.ResDailyEscortInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.EscortProtos.ResDailyEscortInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        estRound_ = 0;

        robTimes_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyEscortInfo getDefaultInstanceForType() {
        return com.sh.game.protos.EscortProtos.ResDailyEscortInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyEscortInfo build() {
        com.sh.game.protos.EscortProtos.ResDailyEscortInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyEscortInfo buildPartial() {
        com.sh.game.protos.EscortProtos.ResDailyEscortInfo result = new com.sh.game.protos.EscortProtos.ResDailyEscortInfo(this);
        result.estRound_ = estRound_;
        result.robTimes_ = robTimes_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.EscortProtos.ResDailyEscortInfo) {
          return mergeFrom((com.sh.game.protos.EscortProtos.ResDailyEscortInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.EscortProtos.ResDailyEscortInfo other) {
        if (other == com.sh.game.protos.EscortProtos.ResDailyEscortInfo.getDefaultInstance()) return this;
        if (other.getEstRound() != 0) {
          setEstRound(other.getEstRound());
        }
        if (other.getRobTimes() != 0) {
          setRobTimes(other.getRobTimes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.EscortProtos.ResDailyEscortInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.EscortProtos.ResDailyEscortInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int estRound_ ;
      /**
       * <pre>
       *轮次
       * </pre>
       *
       * <code>int32 estRound = 1;</code>
       * @return The estRound.
       */
      @java.lang.Override
      public int getEstRound() {
        return estRound_;
      }
      /**
       * <pre>
       *轮次
       * </pre>
       *
       * <code>int32 estRound = 1;</code>
       * @param value The estRound to set.
       * @return This builder for chaining.
       */
      public Builder setEstRound(int value) {
        
        estRound_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *轮次
       * </pre>
       *
       * <code>int32 estRound = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEstRound() {
        
        estRound_ = 0;
        onChanged();
        return this;
      }

      private int robTimes_ ;
      /**
       * <pre>
       *劫镖次数
       * </pre>
       *
       * <code>int32 robTimes = 2;</code>
       * @return The robTimes.
       */
      @java.lang.Override
      public int getRobTimes() {
        return robTimes_;
      }
      /**
       * <pre>
       *劫镖次数
       * </pre>
       *
       * <code>int32 robTimes = 2;</code>
       * @param value The robTimes to set.
       * @return This builder for chaining.
       */
      public Builder setRobTimes(int value) {
        
        robTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *劫镖次数
       * </pre>
       *
       * <code>int32 robTimes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRobTimes() {
        
        robTimes_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:escort.ResDailyEscortInfo)
    }

    // @@protoc_insertion_point(class_scope:escort.ResDailyEscortInfo)
    private static final com.sh.game.protos.EscortProtos.ResDailyEscortInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.EscortProtos.ResDailyEscortInfo();
    }

    public static com.sh.game.protos.EscortProtos.ResDailyEscortInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResDailyEscortInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResDailyEscortInfo>() {
      @java.lang.Override
      public ResDailyEscortInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResDailyEscortInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResDailyEscortInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResDailyEscortInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.EscortProtos.ResDailyEscortInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResDailyEscortActorOrBuilder extends
      // @@protoc_insertion_point(interface_extends:escort.ResDailyEscortActor)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *正在押送的镖车配置id
     * </pre>
     *
     * <code>int32 escortingCid = 1;</code>
     * @return The escortingCid.
     */
    int getEscortingCid();

    /**
     * <pre>
     *正在押送的镖车唯一id
     * </pre>
     *
     * <code>int64 escortingUid = 2;</code>
     * @return The escortingUid.
     */
    long getEscortingUid();

    /**
     * <pre>
     *状态 1成功/2失败
     * </pre>
     *
     * <code>int32 status = 3;</code>
     * @return The status.
     */
    int getStatus();

    /**
     * <pre>
     *结束时间
     * </pre>
     *
     * <code>int32 endTime = 4;</code>
     * @return The endTime.
     */
    int getEndTime();

    /**
     * <pre>
     *血量
     * </pre>
     *
     * <code>sint64 hp = 5;</code>
     * @return The hp.
     */
    long getHp();

    /**
     * <pre>
     *目标地图
     * </pre>
     *
     * <code>int32 dest = 6;</code>
     * @return The dest.
     */
    int getDest();

    /**
     * <pre>
     *地图
     * </pre>
     *
     * <code>int32 map = 7;</code>
     * @return The map.
     */
    int getMap();

    /**
     * <pre>
     *坐标x
     * </pre>
     *
     * <code>int32 x = 8;</code>
     * @return The x.
     */
    int getX();

    /**
     * <pre>
     *坐标y
     * </pre>
     *
     * <code>int32 y = 9;</code>
     * @return The y.
     */
    int getY();
  }
  /**
   * <pre>
   ** class='ResDailyEscortActor' id='12' desc='同步镖车信息' 
   * </pre>
   *
   * Protobuf type {@code escort.ResDailyEscortActor}
   */
  public static final class ResDailyEscortActor extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:escort.ResDailyEscortActor)
      ResDailyEscortActorOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResDailyEscortActor.newBuilder() to construct.
    private ResDailyEscortActor(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResDailyEscortActor() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResDailyEscortActor();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResDailyEscortActor(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              escortingCid_ = input.readInt32();
              break;
            }
            case 16: {

              escortingUid_ = input.readInt64();
              break;
            }
            case 24: {

              status_ = input.readInt32();
              break;
            }
            case 32: {

              endTime_ = input.readInt32();
              break;
            }
            case 40: {

              hp_ = input.readSInt64();
              break;
            }
            case 48: {

              dest_ = input.readInt32();
              break;
            }
            case 56: {

              map_ = input.readInt32();
              break;
            }
            case 64: {

              x_ = input.readInt32();
              break;
            }
            case 72: {

              y_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortActor_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortActor_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.EscortProtos.ResDailyEscortActor.class, com.sh.game.protos.EscortProtos.ResDailyEscortActor.Builder.class);
    }

    public static final int ESCORTINGCID_FIELD_NUMBER = 1;
    private int escortingCid_;
    /**
     * <pre>
     *正在押送的镖车配置id
     * </pre>
     *
     * <code>int32 escortingCid = 1;</code>
     * @return The escortingCid.
     */
    @java.lang.Override
    public int getEscortingCid() {
      return escortingCid_;
    }

    public static final int ESCORTINGUID_FIELD_NUMBER = 2;
    private long escortingUid_;
    /**
     * <pre>
     *正在押送的镖车唯一id
     * </pre>
     *
     * <code>int64 escortingUid = 2;</code>
     * @return The escortingUid.
     */
    @java.lang.Override
    public long getEscortingUid() {
      return escortingUid_;
    }

    public static final int STATUS_FIELD_NUMBER = 3;
    private int status_;
    /**
     * <pre>
     *状态 1成功/2失败
     * </pre>
     *
     * <code>int32 status = 3;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    public static final int ENDTIME_FIELD_NUMBER = 4;
    private int endTime_;
    /**
     * <pre>
     *结束时间
     * </pre>
     *
     * <code>int32 endTime = 4;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public int getEndTime() {
      return endTime_;
    }

    public static final int HP_FIELD_NUMBER = 5;
    private long hp_;
    /**
     * <pre>
     *血量
     * </pre>
     *
     * <code>sint64 hp = 5;</code>
     * @return The hp.
     */
    @java.lang.Override
    public long getHp() {
      return hp_;
    }

    public static final int DEST_FIELD_NUMBER = 6;
    private int dest_;
    /**
     * <pre>
     *目标地图
     * </pre>
     *
     * <code>int32 dest = 6;</code>
     * @return The dest.
     */
    @java.lang.Override
    public int getDest() {
      return dest_;
    }

    public static final int MAP_FIELD_NUMBER = 7;
    private int map_;
    /**
     * <pre>
     *地图
     * </pre>
     *
     * <code>int32 map = 7;</code>
     * @return The map.
     */
    @java.lang.Override
    public int getMap() {
      return map_;
    }

    public static final int X_FIELD_NUMBER = 8;
    private int x_;
    /**
     * <pre>
     *坐标x
     * </pre>
     *
     * <code>int32 x = 8;</code>
     * @return The x.
     */
    @java.lang.Override
    public int getX() {
      return x_;
    }

    public static final int Y_FIELD_NUMBER = 9;
    private int y_;
    /**
     * <pre>
     *坐标y
     * </pre>
     *
     * <code>int32 y = 9;</code>
     * @return The y.
     */
    @java.lang.Override
    public int getY() {
      return y_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (escortingCid_ != 0) {
        output.writeInt32(1, escortingCid_);
      }
      if (escortingUid_ != 0L) {
        output.writeInt64(2, escortingUid_);
      }
      if (status_ != 0) {
        output.writeInt32(3, status_);
      }
      if (endTime_ != 0) {
        output.writeInt32(4, endTime_);
      }
      if (hp_ != 0L) {
        output.writeSInt64(5, hp_);
      }
      if (dest_ != 0) {
        output.writeInt32(6, dest_);
      }
      if (map_ != 0) {
        output.writeInt32(7, map_);
      }
      if (x_ != 0) {
        output.writeInt32(8, x_);
      }
      if (y_ != 0) {
        output.writeInt32(9, y_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (escortingCid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, escortingCid_);
      }
      if (escortingUid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, escortingUid_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, status_);
      }
      if (endTime_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, endTime_);
      }
      if (hp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt64Size(5, hp_);
      }
      if (dest_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, dest_);
      }
      if (map_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, map_);
      }
      if (x_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, x_);
      }
      if (y_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, y_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.EscortProtos.ResDailyEscortActor)) {
        return super.equals(obj);
      }
      com.sh.game.protos.EscortProtos.ResDailyEscortActor other = (com.sh.game.protos.EscortProtos.ResDailyEscortActor) obj;

      if (getEscortingCid()
          != other.getEscortingCid()) return false;
      if (getEscortingUid()
          != other.getEscortingUid()) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (getEndTime()
          != other.getEndTime()) return false;
      if (getHp()
          != other.getHp()) return false;
      if (getDest()
          != other.getDest()) return false;
      if (getMap()
          != other.getMap()) return false;
      if (getX()
          != other.getX()) return false;
      if (getY()
          != other.getY()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ESCORTINGCID_FIELD_NUMBER;
      hash = (53 * hash) + getEscortingCid();
      hash = (37 * hash) + ESCORTINGUID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEscortingUid());
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
      hash = (53 * hash) + getEndTime();
      hash = (37 * hash) + HP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getHp());
      hash = (37 * hash) + DEST_FIELD_NUMBER;
      hash = (53 * hash) + getDest();
      hash = (37 * hash) + MAP_FIELD_NUMBER;
      hash = (53 * hash) + getMap();
      hash = (37 * hash) + X_FIELD_NUMBER;
      hash = (53 * hash) + getX();
      hash = (37 * hash) + Y_FIELD_NUMBER;
      hash = (53 * hash) + getY();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.EscortProtos.ResDailyEscortActor prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResDailyEscortActor' id='12' desc='同步镖车信息' 
     * </pre>
     *
     * Protobuf type {@code escort.ResDailyEscortActor}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:escort.ResDailyEscortActor)
        com.sh.game.protos.EscortProtos.ResDailyEscortActorOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortActor_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortActor_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.EscortProtos.ResDailyEscortActor.class, com.sh.game.protos.EscortProtos.ResDailyEscortActor.Builder.class);
      }

      // Construct using com.sh.game.protos.EscortProtos.ResDailyEscortActor.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        escortingCid_ = 0;

        escortingUid_ = 0L;

        status_ = 0;

        endTime_ = 0;

        hp_ = 0L;

        dest_ = 0;

        map_ = 0;

        x_ = 0;

        y_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortActor_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyEscortActor getDefaultInstanceForType() {
        return com.sh.game.protos.EscortProtos.ResDailyEscortActor.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyEscortActor build() {
        com.sh.game.protos.EscortProtos.ResDailyEscortActor result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyEscortActor buildPartial() {
        com.sh.game.protos.EscortProtos.ResDailyEscortActor result = new com.sh.game.protos.EscortProtos.ResDailyEscortActor(this);
        result.escortingCid_ = escortingCid_;
        result.escortingUid_ = escortingUid_;
        result.status_ = status_;
        result.endTime_ = endTime_;
        result.hp_ = hp_;
        result.dest_ = dest_;
        result.map_ = map_;
        result.x_ = x_;
        result.y_ = y_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.EscortProtos.ResDailyEscortActor) {
          return mergeFrom((com.sh.game.protos.EscortProtos.ResDailyEscortActor)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.EscortProtos.ResDailyEscortActor other) {
        if (other == com.sh.game.protos.EscortProtos.ResDailyEscortActor.getDefaultInstance()) return this;
        if (other.getEscortingCid() != 0) {
          setEscortingCid(other.getEscortingCid());
        }
        if (other.getEscortingUid() != 0L) {
          setEscortingUid(other.getEscortingUid());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        if (other.getEndTime() != 0) {
          setEndTime(other.getEndTime());
        }
        if (other.getHp() != 0L) {
          setHp(other.getHp());
        }
        if (other.getDest() != 0) {
          setDest(other.getDest());
        }
        if (other.getMap() != 0) {
          setMap(other.getMap());
        }
        if (other.getX() != 0) {
          setX(other.getX());
        }
        if (other.getY() != 0) {
          setY(other.getY());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.EscortProtos.ResDailyEscortActor parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.EscortProtos.ResDailyEscortActor) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int escortingCid_ ;
      /**
       * <pre>
       *正在押送的镖车配置id
       * </pre>
       *
       * <code>int32 escortingCid = 1;</code>
       * @return The escortingCid.
       */
      @java.lang.Override
      public int getEscortingCid() {
        return escortingCid_;
      }
      /**
       * <pre>
       *正在押送的镖车配置id
       * </pre>
       *
       * <code>int32 escortingCid = 1;</code>
       * @param value The escortingCid to set.
       * @return This builder for chaining.
       */
      public Builder setEscortingCid(int value) {
        
        escortingCid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *正在押送的镖车配置id
       * </pre>
       *
       * <code>int32 escortingCid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEscortingCid() {
        
        escortingCid_ = 0;
        onChanged();
        return this;
      }

      private long escortingUid_ ;
      /**
       * <pre>
       *正在押送的镖车唯一id
       * </pre>
       *
       * <code>int64 escortingUid = 2;</code>
       * @return The escortingUid.
       */
      @java.lang.Override
      public long getEscortingUid() {
        return escortingUid_;
      }
      /**
       * <pre>
       *正在押送的镖车唯一id
       * </pre>
       *
       * <code>int64 escortingUid = 2;</code>
       * @param value The escortingUid to set.
       * @return This builder for chaining.
       */
      public Builder setEscortingUid(long value) {
        
        escortingUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *正在押送的镖车唯一id
       * </pre>
       *
       * <code>int64 escortingUid = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEscortingUid() {
        
        escortingUid_ = 0L;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <pre>
       *状态 1成功/2失败
       * </pre>
       *
       * <code>int32 status = 3;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       *状态 1成功/2失败
       * </pre>
       *
       * <code>int32 status = 3;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *状态 1成功/2失败
       * </pre>
       *
       * <code>int32 status = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }

      private int endTime_ ;
      /**
       * <pre>
       *结束时间
       * </pre>
       *
       * <code>int32 endTime = 4;</code>
       * @return The endTime.
       */
      @java.lang.Override
      public int getEndTime() {
        return endTime_;
      }
      /**
       * <pre>
       *结束时间
       * </pre>
       *
       * <code>int32 endTime = 4;</code>
       * @param value The endTime to set.
       * @return This builder for chaining.
       */
      public Builder setEndTime(int value) {
        
        endTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *结束时间
       * </pre>
       *
       * <code>int32 endTime = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTime() {
        
        endTime_ = 0;
        onChanged();
        return this;
      }

      private long hp_ ;
      /**
       * <pre>
       *血量
       * </pre>
       *
       * <code>sint64 hp = 5;</code>
       * @return The hp.
       */
      @java.lang.Override
      public long getHp() {
        return hp_;
      }
      /**
       * <pre>
       *血量
       * </pre>
       *
       * <code>sint64 hp = 5;</code>
       * @param value The hp to set.
       * @return This builder for chaining.
       */
      public Builder setHp(long value) {
        
        hp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *血量
       * </pre>
       *
       * <code>sint64 hp = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearHp() {
        
        hp_ = 0L;
        onChanged();
        return this;
      }

      private int dest_ ;
      /**
       * <pre>
       *目标地图
       * </pre>
       *
       * <code>int32 dest = 6;</code>
       * @return The dest.
       */
      @java.lang.Override
      public int getDest() {
        return dest_;
      }
      /**
       * <pre>
       *目标地图
       * </pre>
       *
       * <code>int32 dest = 6;</code>
       * @param value The dest to set.
       * @return This builder for chaining.
       */
      public Builder setDest(int value) {
        
        dest_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *目标地图
       * </pre>
       *
       * <code>int32 dest = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearDest() {
        
        dest_ = 0;
        onChanged();
        return this;
      }

      private int map_ ;
      /**
       * <pre>
       *地图
       * </pre>
       *
       * <code>int32 map = 7;</code>
       * @return The map.
       */
      @java.lang.Override
      public int getMap() {
        return map_;
      }
      /**
       * <pre>
       *地图
       * </pre>
       *
       * <code>int32 map = 7;</code>
       * @param value The map to set.
       * @return This builder for chaining.
       */
      public Builder setMap(int value) {
        
        map_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *地图
       * </pre>
       *
       * <code>int32 map = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearMap() {
        
        map_ = 0;
        onChanged();
        return this;
      }

      private int x_ ;
      /**
       * <pre>
       *坐标x
       * </pre>
       *
       * <code>int32 x = 8;</code>
       * @return The x.
       */
      @java.lang.Override
      public int getX() {
        return x_;
      }
      /**
       * <pre>
       *坐标x
       * </pre>
       *
       * <code>int32 x = 8;</code>
       * @param value The x to set.
       * @return This builder for chaining.
       */
      public Builder setX(int value) {
        
        x_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *坐标x
       * </pre>
       *
       * <code>int32 x = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearX() {
        
        x_ = 0;
        onChanged();
        return this;
      }

      private int y_ ;
      /**
       * <pre>
       *坐标y
       * </pre>
       *
       * <code>int32 y = 9;</code>
       * @return The y.
       */
      @java.lang.Override
      public int getY() {
        return y_;
      }
      /**
       * <pre>
       *坐标y
       * </pre>
       *
       * <code>int32 y = 9;</code>
       * @param value The y to set.
       * @return This builder for chaining.
       */
      public Builder setY(int value) {
        
        y_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *坐标y
       * </pre>
       *
       * <code>int32 y = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearY() {
        
        y_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:escort.ResDailyEscortActor)
    }

    // @@protoc_insertion_point(class_scope:escort.ResDailyEscortActor)
    private static final com.sh.game.protos.EscortProtos.ResDailyEscortActor DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.EscortProtos.ResDailyEscortActor();
    }

    public static com.sh.game.protos.EscortProtos.ResDailyEscortActor getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResDailyEscortActor>
        PARSER = new com.google.protobuf.AbstractParser<ResDailyEscortActor>() {
      @java.lang.Override
      public ResDailyEscortActor parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResDailyEscortActor(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResDailyEscortActor> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResDailyEscortActor> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.EscortProtos.ResDailyEscortActor getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResDailyEscortAcquireOrBuilder extends
      // @@protoc_insertion_point(interface_extends:escort.ResDailyEscortAcquire)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *押镖结果 1成功 2失败
     * </pre>
     *
     * <code>int32 status = 1;</code>
     * @return The status.
     */
    int getStatus();
  }
  /**
   * <pre>
   ** class='ResDailyEscortAcquire' id='13' desc='返回押镖领取成功' 
   * </pre>
   *
   * Protobuf type {@code escort.ResDailyEscortAcquire}
   */
  public static final class ResDailyEscortAcquire extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:escort.ResDailyEscortAcquire)
      ResDailyEscortAcquireOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResDailyEscortAcquire.newBuilder() to construct.
    private ResDailyEscortAcquire(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResDailyEscortAcquire() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResDailyEscortAcquire();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResDailyEscortAcquire(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              status_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortAcquire_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortAcquire_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.EscortProtos.ResDailyEscortAcquire.class, com.sh.game.protos.EscortProtos.ResDailyEscortAcquire.Builder.class);
    }

    public static final int STATUS_FIELD_NUMBER = 1;
    private int status_;
    /**
     * <pre>
     *押镖结果 1成功 2失败
     * </pre>
     *
     * <code>int32 status = 1;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (status_ != 0) {
        output.writeInt32(1, status_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, status_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.EscortProtos.ResDailyEscortAcquire)) {
        return super.equals(obj);
      }
      com.sh.game.protos.EscortProtos.ResDailyEscortAcquire other = (com.sh.game.protos.EscortProtos.ResDailyEscortAcquire) obj;

      if (getStatus()
          != other.getStatus()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.EscortProtos.ResDailyEscortAcquire prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResDailyEscortAcquire' id='13' desc='返回押镖领取成功' 
     * </pre>
     *
     * Protobuf type {@code escort.ResDailyEscortAcquire}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:escort.ResDailyEscortAcquire)
        com.sh.game.protos.EscortProtos.ResDailyEscortAcquireOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortAcquire_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortAcquire_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.EscortProtos.ResDailyEscortAcquire.class, com.sh.game.protos.EscortProtos.ResDailyEscortAcquire.Builder.class);
      }

      // Construct using com.sh.game.protos.EscortProtos.ResDailyEscortAcquire.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        status_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyEscortAcquire_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyEscortAcquire getDefaultInstanceForType() {
        return com.sh.game.protos.EscortProtos.ResDailyEscortAcquire.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyEscortAcquire build() {
        com.sh.game.protos.EscortProtos.ResDailyEscortAcquire result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyEscortAcquire buildPartial() {
        com.sh.game.protos.EscortProtos.ResDailyEscortAcquire result = new com.sh.game.protos.EscortProtos.ResDailyEscortAcquire(this);
        result.status_ = status_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.EscortProtos.ResDailyEscortAcquire) {
          return mergeFrom((com.sh.game.protos.EscortProtos.ResDailyEscortAcquire)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.EscortProtos.ResDailyEscortAcquire other) {
        if (other == com.sh.game.protos.EscortProtos.ResDailyEscortAcquire.getDefaultInstance()) return this;
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.EscortProtos.ResDailyEscortAcquire parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.EscortProtos.ResDailyEscortAcquire) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int status_ ;
      /**
       * <pre>
       *押镖结果 1成功 2失败
       * </pre>
       *
       * <code>int32 status = 1;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <pre>
       *押镖结果 1成功 2失败
       * </pre>
       *
       * <code>int32 status = 1;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {
        
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *押镖结果 1成功 2失败
       * </pre>
       *
       * <code>int32 status = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        
        status_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:escort.ResDailyEscortAcquire)
    }

    // @@protoc_insertion_point(class_scope:escort.ResDailyEscortAcquire)
    private static final com.sh.game.protos.EscortProtos.ResDailyEscortAcquire DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.EscortProtos.ResDailyEscortAcquire();
    }

    public static com.sh.game.protos.EscortProtos.ResDailyEscortAcquire getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResDailyEscortAcquire>
        PARSER = new com.google.protobuf.AbstractParser<ResDailyEscortAcquire>() {
      @java.lang.Override
      public ResDailyEscortAcquire parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResDailyEscortAcquire(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResDailyEscortAcquire> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResDailyEscortAcquire> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.EscortProtos.ResDailyEscortAcquire getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResDailyRobEscortAcquireOrBuilder extends
      // @@protoc_insertion_point(interface_extends:escort.ResDailyRobEscortAcquire)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *镖车配置表id
     * </pre>
     *
     * <code>int32 escortCfgId = 1;</code>
     * @return The escortCfgId.
     */
    int getEscortCfgId();
  }
  /**
   * <pre>
   ** class='ResDailyRobEscortAcquire' id='14' desc='返回劫镖领取' 
   * </pre>
   *
   * Protobuf type {@code escort.ResDailyRobEscortAcquire}
   */
  public static final class ResDailyRobEscortAcquire extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:escort.ResDailyRobEscortAcquire)
      ResDailyRobEscortAcquireOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResDailyRobEscortAcquire.newBuilder() to construct.
    private ResDailyRobEscortAcquire(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResDailyRobEscortAcquire() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResDailyRobEscortAcquire();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResDailyRobEscortAcquire(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              escortCfgId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyRobEscortAcquire_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyRobEscortAcquire_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire.class, com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire.Builder.class);
    }

    public static final int ESCORTCFGID_FIELD_NUMBER = 1;
    private int escortCfgId_;
    /**
     * <pre>
     *镖车配置表id
     * </pre>
     *
     * <code>int32 escortCfgId = 1;</code>
     * @return The escortCfgId.
     */
    @java.lang.Override
    public int getEscortCfgId() {
      return escortCfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (escortCfgId_ != 0) {
        output.writeInt32(1, escortCfgId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (escortCfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, escortCfgId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire)) {
        return super.equals(obj);
      }
      com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire other = (com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire) obj;

      if (getEscortCfgId()
          != other.getEscortCfgId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ESCORTCFGID_FIELD_NUMBER;
      hash = (53 * hash) + getEscortCfgId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResDailyRobEscortAcquire' id='14' desc='返回劫镖领取' 
     * </pre>
     *
     * Protobuf type {@code escort.ResDailyRobEscortAcquire}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:escort.ResDailyRobEscortAcquire)
        com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquireOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyRobEscortAcquire_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyRobEscortAcquire_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire.class, com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire.Builder.class);
      }

      // Construct using com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        escortCfgId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.EscortProtos.internal_static_escort_ResDailyRobEscortAcquire_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire getDefaultInstanceForType() {
        return com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire build() {
        com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire buildPartial() {
        com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire result = new com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire(this);
        result.escortCfgId_ = escortCfgId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire) {
          return mergeFrom((com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire other) {
        if (other == com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire.getDefaultInstance()) return this;
        if (other.getEscortCfgId() != 0) {
          setEscortCfgId(other.getEscortCfgId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int escortCfgId_ ;
      /**
       * <pre>
       *镖车配置表id
       * </pre>
       *
       * <code>int32 escortCfgId = 1;</code>
       * @return The escortCfgId.
       */
      @java.lang.Override
      public int getEscortCfgId() {
        return escortCfgId_;
      }
      /**
       * <pre>
       *镖车配置表id
       * </pre>
       *
       * <code>int32 escortCfgId = 1;</code>
       * @param value The escortCfgId to set.
       * @return This builder for chaining.
       */
      public Builder setEscortCfgId(int value) {
        
        escortCfgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *镖车配置表id
       * </pre>
       *
       * <code>int32 escortCfgId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEscortCfgId() {
        
        escortCfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:escort.ResDailyRobEscortAcquire)
    }

    // @@protoc_insertion_point(class_scope:escort.ResDailyRobEscortAcquire)
    private static final com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire();
    }

    public static com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResDailyRobEscortAcquire>
        PARSER = new com.google.protobuf.AbstractParser<ResDailyRobEscortAcquire>() {
      @java.lang.Override
      public ResDailyRobEscortAcquire parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResDailyRobEscortAcquire(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResDailyRobEscortAcquire> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResDailyRobEscortAcquire> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.EscortProtos.ResDailyRobEscortAcquire getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_escort_ReqDailyEscortInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_escort_ReqDailyEscortInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_escort_ReqDailyEscortStart_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_escort_ReqDailyEscortStart_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_escort_ReqDailyEscortAbort_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_escort_ReqDailyEscortAbort_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_escort_ReqDailyEscortAcquire_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_escort_ReqDailyEscortAcquire_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_escort_ResDailyEscortInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_escort_ResDailyEscortInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_escort_ResDailyEscortActor_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_escort_ResDailyEscortActor_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_escort_ResDailyEscortAcquire_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_escort_ResDailyEscortAcquire_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_escort_ResDailyRobEscortAcquire_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_escort_ResDailyRobEscortAcquire_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014escort.proto\022\006escort\"\024\n\022ReqDailyEscort" +
      "Info\"\'\n\023ReqDailyEscortStart\022\020\n\010escortId\030" +
      "\001 \001(\005\"\025\n\023ReqDailyEscortAbort\"&\n\025ReqDaily" +
      "EscortAcquire\022\r\n\005extra\030\001 \001(\005\"8\n\022ResDaily" +
      "EscortInfo\022\020\n\010estRound\030\001 \001(\005\022\020\n\010robTimes" +
      "\030\002 \001(\005\"\237\001\n\023ResDailyEscortActor\022\024\n\014escort" +
      "ingCid\030\001 \001(\005\022\024\n\014escortingUid\030\002 \001(\003\022\016\n\006st" +
      "atus\030\003 \001(\005\022\017\n\007endTime\030\004 \001(\005\022\n\n\002hp\030\005 \001(\022\022" +
      "\014\n\004dest\030\006 \001(\005\022\013\n\003map\030\007 \001(\005\022\t\n\001x\030\010 \001(\005\022\t\n" +
      "\001y\030\t \001(\005\"\'\n\025ResDailyEscortAcquire\022\016\n\006sta" +
      "tus\030\001 \001(\005\"/\n\030ResDailyRobEscortAcquire\022\023\n" +
      "\013escortCfgId\030\001 \001(\005B\"\n\022com.sh.game.protos" +
      "B\014EscortProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_escort_ReqDailyEscortInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_escort_ReqDailyEscortInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_escort_ReqDailyEscortInfo_descriptor,
        new java.lang.String[] { });
    internal_static_escort_ReqDailyEscortStart_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_escort_ReqDailyEscortStart_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_escort_ReqDailyEscortStart_descriptor,
        new java.lang.String[] { "EscortId", });
    internal_static_escort_ReqDailyEscortAbort_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_escort_ReqDailyEscortAbort_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_escort_ReqDailyEscortAbort_descriptor,
        new java.lang.String[] { });
    internal_static_escort_ReqDailyEscortAcquire_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_escort_ReqDailyEscortAcquire_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_escort_ReqDailyEscortAcquire_descriptor,
        new java.lang.String[] { "Extra", });
    internal_static_escort_ResDailyEscortInfo_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_escort_ResDailyEscortInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_escort_ResDailyEscortInfo_descriptor,
        new java.lang.String[] { "EstRound", "RobTimes", });
    internal_static_escort_ResDailyEscortActor_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_escort_ResDailyEscortActor_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_escort_ResDailyEscortActor_descriptor,
        new java.lang.String[] { "EscortingCid", "EscortingUid", "Status", "EndTime", "Hp", "Dest", "Map", "X", "Y", });
    internal_static_escort_ResDailyEscortAcquire_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_escort_ResDailyEscortAcquire_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_escort_ResDailyEscortAcquire_descriptor,
        new java.lang.String[] { "Status", });
    internal_static_escort_ResDailyRobEscortAcquire_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_escort_ResDailyRobEscortAcquire_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_escort_ResDailyRobEscortAcquire_descriptor,
        new java.lang.String[] { "EscortCfgId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
