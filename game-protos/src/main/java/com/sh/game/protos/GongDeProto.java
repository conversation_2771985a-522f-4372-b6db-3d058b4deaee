// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: gongde.proto

package com.sh.game.protos;

public final class GongDeProto {
  private GongDeProto() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqGongDeInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gongde.ReqGongDeInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqGongDeInfo' id='1' desc='请求功德信息' 
   * </pre>
   *
   * Protobuf type {@code gongde.ReqGongDeInfo}
   */
  public static final class ReqGongDeInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:gongde.ReqGongDeInfo)
      ReqGongDeInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqGongDeInfo.newBuilder() to construct.
    private ReqGongDeInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqGongDeInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqGongDeInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqGongDeInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.GongDeProto.ReqGongDeInfo.class, com.sh.game.protos.GongDeProto.ReqGongDeInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.GongDeProto.ReqGongDeInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.GongDeProto.ReqGongDeInfo other = (com.sh.game.protos.GongDeProto.ReqGongDeInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.GongDeProto.ReqGongDeInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqGongDeInfo' id='1' desc='请求功德信息' 
     * </pre>
     *
     * Protobuf type {@code gongde.ReqGongDeInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gongde.ReqGongDeInfo)
        com.sh.game.protos.GongDeProto.ReqGongDeInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.GongDeProto.ReqGongDeInfo.class, com.sh.game.protos.GongDeProto.ReqGongDeInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.GongDeProto.ReqGongDeInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.GongDeProto.ReqGongDeInfo getDefaultInstanceForType() {
        return com.sh.game.protos.GongDeProto.ReqGongDeInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.GongDeProto.ReqGongDeInfo build() {
        com.sh.game.protos.GongDeProto.ReqGongDeInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.GongDeProto.ReqGongDeInfo buildPartial() {
        com.sh.game.protos.GongDeProto.ReqGongDeInfo result = new com.sh.game.protos.GongDeProto.ReqGongDeInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.GongDeProto.ReqGongDeInfo) {
          return mergeFrom((com.sh.game.protos.GongDeProto.ReqGongDeInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.GongDeProto.ReqGongDeInfo other) {
        if (other == com.sh.game.protos.GongDeProto.ReqGongDeInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.GongDeProto.ReqGongDeInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.GongDeProto.ReqGongDeInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:gongde.ReqGongDeInfo)
    }

    // @@protoc_insertion_point(class_scope:gongde.ReqGongDeInfo)
    private static final com.sh.game.protos.GongDeProto.ReqGongDeInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.GongDeProto.ReqGongDeInfo();
    }

    public static com.sh.game.protos.GongDeProto.ReqGongDeInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqGongDeInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqGongDeInfo>() {
      @java.lang.Override
      public ReqGongDeInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqGongDeInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqGongDeInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqGongDeInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.GongDeProto.ReqGongDeInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqGongDeRewardOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gongde.ReqGongDeReward)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 gongDeCid = 1;</code>
     * @return The gongDeCid.
     */
    int getGongDeCid();
  }
  /**
   * <pre>
   ** class='ReqGongDeReward' id='2' desc='请求功德每日奖励' 
   * </pre>
   *
   * Protobuf type {@code gongde.ReqGongDeReward}
   */
  public static final class ReqGongDeReward extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:gongde.ReqGongDeReward)
      ReqGongDeRewardOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqGongDeReward.newBuilder() to construct.
    private ReqGongDeReward(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqGongDeReward() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqGongDeReward();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqGongDeReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              gongDeCid_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeReward_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.GongDeProto.ReqGongDeReward.class, com.sh.game.protos.GongDeProto.ReqGongDeReward.Builder.class);
    }

    public static final int GONGDECID_FIELD_NUMBER = 1;
    private int gongDeCid_;
    /**
     * <code>int32 gongDeCid = 1;</code>
     * @return The gongDeCid.
     */
    @java.lang.Override
    public int getGongDeCid() {
      return gongDeCid_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (gongDeCid_ != 0) {
        output.writeInt32(1, gongDeCid_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (gongDeCid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, gongDeCid_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.GongDeProto.ReqGongDeReward)) {
        return super.equals(obj);
      }
      com.sh.game.protos.GongDeProto.ReqGongDeReward other = (com.sh.game.protos.GongDeProto.ReqGongDeReward) obj;

      if (getGongDeCid()
          != other.getGongDeCid()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + GONGDECID_FIELD_NUMBER;
      hash = (53 * hash) + getGongDeCid();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.GongDeProto.ReqGongDeReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.GongDeProto.ReqGongDeReward prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqGongDeReward' id='2' desc='请求功德每日奖励' 
     * </pre>
     *
     * Protobuf type {@code gongde.ReqGongDeReward}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gongde.ReqGongDeReward)
        com.sh.game.protos.GongDeProto.ReqGongDeRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeReward_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.GongDeProto.ReqGongDeReward.class, com.sh.game.protos.GongDeProto.ReqGongDeReward.Builder.class);
      }

      // Construct using com.sh.game.protos.GongDeProto.ReqGongDeReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        gongDeCid_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.GongDeProto.internal_static_gongde_ReqGongDeReward_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.GongDeProto.ReqGongDeReward getDefaultInstanceForType() {
        return com.sh.game.protos.GongDeProto.ReqGongDeReward.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.GongDeProto.ReqGongDeReward build() {
        com.sh.game.protos.GongDeProto.ReqGongDeReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.GongDeProto.ReqGongDeReward buildPartial() {
        com.sh.game.protos.GongDeProto.ReqGongDeReward result = new com.sh.game.protos.GongDeProto.ReqGongDeReward(this);
        result.gongDeCid_ = gongDeCid_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.GongDeProto.ReqGongDeReward) {
          return mergeFrom((com.sh.game.protos.GongDeProto.ReqGongDeReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.GongDeProto.ReqGongDeReward other) {
        if (other == com.sh.game.protos.GongDeProto.ReqGongDeReward.getDefaultInstance()) return this;
        if (other.getGongDeCid() != 0) {
          setGongDeCid(other.getGongDeCid());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.GongDeProto.ReqGongDeReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.GongDeProto.ReqGongDeReward) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int gongDeCid_ ;
      /**
       * <code>int32 gongDeCid = 1;</code>
       * @return The gongDeCid.
       */
      @java.lang.Override
      public int getGongDeCid() {
        return gongDeCid_;
      }
      /**
       * <code>int32 gongDeCid = 1;</code>
       * @param value The gongDeCid to set.
       * @return This builder for chaining.
       */
      public Builder setGongDeCid(int value) {
        
        gongDeCid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 gongDeCid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGongDeCid() {
        
        gongDeCid_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:gongde.ReqGongDeReward)
    }

    // @@protoc_insertion_point(class_scope:gongde.ReqGongDeReward)
    private static final com.sh.game.protos.GongDeProto.ReqGongDeReward DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.GongDeProto.ReqGongDeReward();
    }

    public static com.sh.game.protos.GongDeProto.ReqGongDeReward getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqGongDeReward>
        PARSER = new com.google.protobuf.AbstractParser<ReqGongDeReward>() {
      @java.lang.Override
      public ReqGongDeReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqGongDeReward(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqGongDeReward> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqGongDeReward> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.GongDeProto.ReqGongDeReward getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResGongDeInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:gongde.ResGongDeInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *玩家当前功德配置id
     * </pre>
     *
     * <code>int32 gongDeCid = 1;</code>
     * @return The gongDeCid.
     */
    int getGongDeCid();

    /**
     * <pre>
     *玩家当前功德经验
     * </pre>
     *
     * <code>int32 gongDeExp = 2;</code>
     * @return The gongDeExp.
     */
    int getGongDeExp();

    /**
     * <pre>
     *玩家是否领取功德每日奖励，1是
     * </pre>
     *
     * <code>int32 dayRewardGet = 3;</code>
     * @return The dayRewardGet.
     */
    int getDayRewardGet();

    /**
     * <pre>
     *历史领取
     * </pre>
     *
     * <code>repeated int32 hisRewardGets = 4;</code>
     * @return A list containing the hisRewardGets.
     */
    java.util.List<java.lang.Integer> getHisRewardGetsList();
    /**
     * <pre>
     *历史领取
     * </pre>
     *
     * <code>repeated int32 hisRewardGets = 4;</code>
     * @return The count of hisRewardGets.
     */
    int getHisRewardGetsCount();
    /**
     * <pre>
     *历史领取
     * </pre>
     *
     * <code>repeated int32 hisRewardGets = 4;</code>
     * @param index The index of the element to return.
     * @return The hisRewardGets at the given index.
     */
    int getHisRewardGets(int index);
  }
  /**
   * <pre>
   ** class='ResGongDeInfo' id='3' desc='返回功德信息' 
   * </pre>
   *
   * Protobuf type {@code gongde.ResGongDeInfo}
   */
  public static final class ResGongDeInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:gongde.ResGongDeInfo)
      ResGongDeInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResGongDeInfo.newBuilder() to construct.
    private ResGongDeInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResGongDeInfo() {
      hisRewardGets_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResGongDeInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResGongDeInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              gongDeCid_ = input.readInt32();
              break;
            }
            case 16: {

              gongDeExp_ = input.readInt32();
              break;
            }
            case 24: {

              dayRewardGet_ = input.readInt32();
              break;
            }
            case 32: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                hisRewardGets_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              hisRewardGets_.addInt(input.readInt32());
              break;
            }
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                hisRewardGets_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                hisRewardGets_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          hisRewardGets_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.GongDeProto.internal_static_gongde_ResGongDeInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.GongDeProto.internal_static_gongde_ResGongDeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.GongDeProto.ResGongDeInfo.class, com.sh.game.protos.GongDeProto.ResGongDeInfo.Builder.class);
    }

    public static final int GONGDECID_FIELD_NUMBER = 1;
    private int gongDeCid_;
    /**
     * <pre>
     *玩家当前功德配置id
     * </pre>
     *
     * <code>int32 gongDeCid = 1;</code>
     * @return The gongDeCid.
     */
    @java.lang.Override
    public int getGongDeCid() {
      return gongDeCid_;
    }

    public static final int GONGDEEXP_FIELD_NUMBER = 2;
    private int gongDeExp_;
    /**
     * <pre>
     *玩家当前功德经验
     * </pre>
     *
     * <code>int32 gongDeExp = 2;</code>
     * @return The gongDeExp.
     */
    @java.lang.Override
    public int getGongDeExp() {
      return gongDeExp_;
    }

    public static final int DAYREWARDGET_FIELD_NUMBER = 3;
    private int dayRewardGet_;
    /**
     * <pre>
     *玩家是否领取功德每日奖励，1是
     * </pre>
     *
     * <code>int32 dayRewardGet = 3;</code>
     * @return The dayRewardGet.
     */
    @java.lang.Override
    public int getDayRewardGet() {
      return dayRewardGet_;
    }

    public static final int HISREWARDGETS_FIELD_NUMBER = 4;
    private com.google.protobuf.Internal.IntList hisRewardGets_;
    /**
     * <pre>
     *历史领取
     * </pre>
     *
     * <code>repeated int32 hisRewardGets = 4;</code>
     * @return A list containing the hisRewardGets.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getHisRewardGetsList() {
      return hisRewardGets_;
    }
    /**
     * <pre>
     *历史领取
     * </pre>
     *
     * <code>repeated int32 hisRewardGets = 4;</code>
     * @return The count of hisRewardGets.
     */
    public int getHisRewardGetsCount() {
      return hisRewardGets_.size();
    }
    /**
     * <pre>
     *历史领取
     * </pre>
     *
     * <code>repeated int32 hisRewardGets = 4;</code>
     * @param index The index of the element to return.
     * @return The hisRewardGets at the given index.
     */
    public int getHisRewardGets(int index) {
      return hisRewardGets_.getInt(index);
    }
    private int hisRewardGetsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (gongDeCid_ != 0) {
        output.writeInt32(1, gongDeCid_);
      }
      if (gongDeExp_ != 0) {
        output.writeInt32(2, gongDeExp_);
      }
      if (dayRewardGet_ != 0) {
        output.writeInt32(3, dayRewardGet_);
      }
      if (getHisRewardGetsList().size() > 0) {
        output.writeUInt32NoTag(34);
        output.writeUInt32NoTag(hisRewardGetsMemoizedSerializedSize);
      }
      for (int i = 0; i < hisRewardGets_.size(); i++) {
        output.writeInt32NoTag(hisRewardGets_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (gongDeCid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, gongDeCid_);
      }
      if (gongDeExp_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, gongDeExp_);
      }
      if (dayRewardGet_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, dayRewardGet_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < hisRewardGets_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(hisRewardGets_.getInt(i));
        }
        size += dataSize;
        if (!getHisRewardGetsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        hisRewardGetsMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.GongDeProto.ResGongDeInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.GongDeProto.ResGongDeInfo other = (com.sh.game.protos.GongDeProto.ResGongDeInfo) obj;

      if (getGongDeCid()
          != other.getGongDeCid()) return false;
      if (getGongDeExp()
          != other.getGongDeExp()) return false;
      if (getDayRewardGet()
          != other.getDayRewardGet()) return false;
      if (!getHisRewardGetsList()
          .equals(other.getHisRewardGetsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + GONGDECID_FIELD_NUMBER;
      hash = (53 * hash) + getGongDeCid();
      hash = (37 * hash) + GONGDEEXP_FIELD_NUMBER;
      hash = (53 * hash) + getGongDeExp();
      hash = (37 * hash) + DAYREWARDGET_FIELD_NUMBER;
      hash = (53 * hash) + getDayRewardGet();
      if (getHisRewardGetsCount() > 0) {
        hash = (37 * hash) + HISREWARDGETS_FIELD_NUMBER;
        hash = (53 * hash) + getHisRewardGetsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.GongDeProto.ResGongDeInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.GongDeProto.ResGongDeInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResGongDeInfo' id='3' desc='返回功德信息' 
     * </pre>
     *
     * Protobuf type {@code gongde.ResGongDeInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:gongde.ResGongDeInfo)
        com.sh.game.protos.GongDeProto.ResGongDeInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.GongDeProto.internal_static_gongde_ResGongDeInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.GongDeProto.internal_static_gongde_ResGongDeInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.GongDeProto.ResGongDeInfo.class, com.sh.game.protos.GongDeProto.ResGongDeInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.GongDeProto.ResGongDeInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        gongDeCid_ = 0;

        gongDeExp_ = 0;

        dayRewardGet_ = 0;

        hisRewardGets_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.GongDeProto.internal_static_gongde_ResGongDeInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.GongDeProto.ResGongDeInfo getDefaultInstanceForType() {
        return com.sh.game.protos.GongDeProto.ResGongDeInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.GongDeProto.ResGongDeInfo build() {
        com.sh.game.protos.GongDeProto.ResGongDeInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.GongDeProto.ResGongDeInfo buildPartial() {
        com.sh.game.protos.GongDeProto.ResGongDeInfo result = new com.sh.game.protos.GongDeProto.ResGongDeInfo(this);
        int from_bitField0_ = bitField0_;
        result.gongDeCid_ = gongDeCid_;
        result.gongDeExp_ = gongDeExp_;
        result.dayRewardGet_ = dayRewardGet_;
        if (((bitField0_ & 0x00000001) != 0)) {
          hisRewardGets_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.hisRewardGets_ = hisRewardGets_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.GongDeProto.ResGongDeInfo) {
          return mergeFrom((com.sh.game.protos.GongDeProto.ResGongDeInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.GongDeProto.ResGongDeInfo other) {
        if (other == com.sh.game.protos.GongDeProto.ResGongDeInfo.getDefaultInstance()) return this;
        if (other.getGongDeCid() != 0) {
          setGongDeCid(other.getGongDeCid());
        }
        if (other.getGongDeExp() != 0) {
          setGongDeExp(other.getGongDeExp());
        }
        if (other.getDayRewardGet() != 0) {
          setDayRewardGet(other.getDayRewardGet());
        }
        if (!other.hisRewardGets_.isEmpty()) {
          if (hisRewardGets_.isEmpty()) {
            hisRewardGets_ = other.hisRewardGets_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureHisRewardGetsIsMutable();
            hisRewardGets_.addAll(other.hisRewardGets_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.GongDeProto.ResGongDeInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.GongDeProto.ResGongDeInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int gongDeCid_ ;
      /**
       * <pre>
       *玩家当前功德配置id
       * </pre>
       *
       * <code>int32 gongDeCid = 1;</code>
       * @return The gongDeCid.
       */
      @java.lang.Override
      public int getGongDeCid() {
        return gongDeCid_;
      }
      /**
       * <pre>
       *玩家当前功德配置id
       * </pre>
       *
       * <code>int32 gongDeCid = 1;</code>
       * @param value The gongDeCid to set.
       * @return This builder for chaining.
       */
      public Builder setGongDeCid(int value) {
        
        gongDeCid_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家当前功德配置id
       * </pre>
       *
       * <code>int32 gongDeCid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGongDeCid() {
        
        gongDeCid_ = 0;
        onChanged();
        return this;
      }

      private int gongDeExp_ ;
      /**
       * <pre>
       *玩家当前功德经验
       * </pre>
       *
       * <code>int32 gongDeExp = 2;</code>
       * @return The gongDeExp.
       */
      @java.lang.Override
      public int getGongDeExp() {
        return gongDeExp_;
      }
      /**
       * <pre>
       *玩家当前功德经验
       * </pre>
       *
       * <code>int32 gongDeExp = 2;</code>
       * @param value The gongDeExp to set.
       * @return This builder for chaining.
       */
      public Builder setGongDeExp(int value) {
        
        gongDeExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家当前功德经验
       * </pre>
       *
       * <code>int32 gongDeExp = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGongDeExp() {
        
        gongDeExp_ = 0;
        onChanged();
        return this;
      }

      private int dayRewardGet_ ;
      /**
       * <pre>
       *玩家是否领取功德每日奖励，1是
       * </pre>
       *
       * <code>int32 dayRewardGet = 3;</code>
       * @return The dayRewardGet.
       */
      @java.lang.Override
      public int getDayRewardGet() {
        return dayRewardGet_;
      }
      /**
       * <pre>
       *玩家是否领取功德每日奖励，1是
       * </pre>
       *
       * <code>int32 dayRewardGet = 3;</code>
       * @param value The dayRewardGet to set.
       * @return This builder for chaining.
       */
      public Builder setDayRewardGet(int value) {
        
        dayRewardGet_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *玩家是否领取功德每日奖励，1是
       * </pre>
       *
       * <code>int32 dayRewardGet = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDayRewardGet() {
        
        dayRewardGet_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList hisRewardGets_ = emptyIntList();
      private void ensureHisRewardGetsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          hisRewardGets_ = mutableCopy(hisRewardGets_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       *历史领取
       * </pre>
       *
       * <code>repeated int32 hisRewardGets = 4;</code>
       * @return A list containing the hisRewardGets.
       */
      public java.util.List<java.lang.Integer>
          getHisRewardGetsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(hisRewardGets_) : hisRewardGets_;
      }
      /**
       * <pre>
       *历史领取
       * </pre>
       *
       * <code>repeated int32 hisRewardGets = 4;</code>
       * @return The count of hisRewardGets.
       */
      public int getHisRewardGetsCount() {
        return hisRewardGets_.size();
      }
      /**
       * <pre>
       *历史领取
       * </pre>
       *
       * <code>repeated int32 hisRewardGets = 4;</code>
       * @param index The index of the element to return.
       * @return The hisRewardGets at the given index.
       */
      public int getHisRewardGets(int index) {
        return hisRewardGets_.getInt(index);
      }
      /**
       * <pre>
       *历史领取
       * </pre>
       *
       * <code>repeated int32 hisRewardGets = 4;</code>
       * @param index The index to set the value at.
       * @param value The hisRewardGets to set.
       * @return This builder for chaining.
       */
      public Builder setHisRewardGets(
          int index, int value) {
        ensureHisRewardGetsIsMutable();
        hisRewardGets_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *历史领取
       * </pre>
       *
       * <code>repeated int32 hisRewardGets = 4;</code>
       * @param value The hisRewardGets to add.
       * @return This builder for chaining.
       */
      public Builder addHisRewardGets(int value) {
        ensureHisRewardGetsIsMutable();
        hisRewardGets_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *历史领取
       * </pre>
       *
       * <code>repeated int32 hisRewardGets = 4;</code>
       * @param values The hisRewardGets to add.
       * @return This builder for chaining.
       */
      public Builder addAllHisRewardGets(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureHisRewardGetsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, hisRewardGets_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *历史领取
       * </pre>
       *
       * <code>repeated int32 hisRewardGets = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHisRewardGets() {
        hisRewardGets_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:gongde.ResGongDeInfo)
    }

    // @@protoc_insertion_point(class_scope:gongde.ResGongDeInfo)
    private static final com.sh.game.protos.GongDeProto.ResGongDeInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.GongDeProto.ResGongDeInfo();
    }

    public static com.sh.game.protos.GongDeProto.ResGongDeInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResGongDeInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResGongDeInfo>() {
      @java.lang.Override
      public ResGongDeInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResGongDeInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResGongDeInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResGongDeInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.GongDeProto.ResGongDeInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gongde_ReqGongDeInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_gongde_ReqGongDeInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gongde_ReqGongDeReward_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_gongde_ReqGongDeReward_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_gongde_ResGongDeInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_gongde_ResGongDeInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014gongde.proto\022\006gongde\"\017\n\rReqGongDeInfo\"" +
      "$\n\017ReqGongDeReward\022\021\n\tgongDeCid\030\001 \001(\005\"b\n" +
      "\rResGongDeInfo\022\021\n\tgongDeCid\030\001 \001(\005\022\021\n\tgon" +
      "gDeExp\030\002 \001(\005\022\024\n\014dayRewardGet\030\003 \001(\005\022\025\n\rhi" +
      "sRewardGets\030\004 \003(\005B!\n\022com.sh.game.protosB" +
      "\013GongDeProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_gongde_ReqGongDeInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_gongde_ReqGongDeInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_gongde_ReqGongDeInfo_descriptor,
        new java.lang.String[] { });
    internal_static_gongde_ReqGongDeReward_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_gongde_ReqGongDeReward_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_gongde_ReqGongDeReward_descriptor,
        new java.lang.String[] { "GongDeCid", });
    internal_static_gongde_ResGongDeInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_gongde_ResGongDeInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_gongde_ResGongDeInfo_descriptor,
        new java.lang.String[] { "GongDeCid", "GongDeExp", "DayRewardGet", "HisRewardGets", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
