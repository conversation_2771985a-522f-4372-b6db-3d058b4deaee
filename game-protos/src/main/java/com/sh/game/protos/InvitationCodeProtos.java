// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: invitationCode.proto

package com.sh.game.protos;

public final class InvitationCodeProtos {
  private InvitationCodeProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqInvitationCodeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:invitationCode.ReqInvitationCode)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqInvitationCode' id='1' desc='请求玩家已生成的邀请码' 
   * </pre>
   *
   * Protobuf type {@code invitationCode.ReqInvitationCode}
   */
  public static final class ReqInvitationCode extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:invitationCode.ReqInvitationCode)
      ReqInvitationCodeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqInvitationCode.newBuilder() to construct.
    private ReqInvitationCode(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqInvitationCode() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqInvitationCode();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqInvitationCode(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ReqInvitationCode_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ReqInvitationCode_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode.class, com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode)) {
        return super.equals(obj);
      }
      com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode other = (com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqInvitationCode' id='1' desc='请求玩家已生成的邀请码' 
     * </pre>
     *
     * Protobuf type {@code invitationCode.ReqInvitationCode}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:invitationCode.ReqInvitationCode)
        com.sh.game.protos.InvitationCodeProtos.ReqInvitationCodeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ReqInvitationCode_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ReqInvitationCode_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode.class, com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode.Builder.class);
      }

      // Construct using com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ReqInvitationCode_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode getDefaultInstanceForType() {
        return com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode build() {
        com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode buildPartial() {
        com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode result = new com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode) {
          return mergeFrom((com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode other) {
        if (other == com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:invitationCode.ReqInvitationCode)
    }

    // @@protoc_insertion_point(class_scope:invitationCode.ReqInvitationCode)
    private static final com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode();
    }

    public static com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqInvitationCode>
        PARSER = new com.google.protobuf.AbstractParser<ReqInvitationCode>() {
      @java.lang.Override
      public ReqInvitationCode parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqInvitationCode(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqInvitationCode> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqInvitationCode> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.InvitationCodeProtos.ReqInvitationCode getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResInvitationCodeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:invitationCode.ResInvitationCode)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *是否显示邀请码
     * </pre>
     *
     * <code>bool show = 1;</code>
     * @return The show.
     */
    boolean getShow();

    /**
     * <pre>
     *邀请码
     * </pre>
     *
     * <code>string code = 2;</code>
     * @return The code.
     */
    java.lang.String getCode();
    /**
     * <pre>
     *邀请码
     * </pre>
     *
     * <code>string code = 2;</code>
     * @return The bytes for code.
     */
    com.google.protobuf.ByteString
        getCodeBytes();
  }
  /**
   * <pre>
   ** class='ResInvitationCode' id='2' desc='返回玩家已生成的邀请码' 
   * </pre>
   *
   * Protobuf type {@code invitationCode.ResInvitationCode}
   */
  public static final class ResInvitationCode extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:invitationCode.ResInvitationCode)
      ResInvitationCodeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResInvitationCode.newBuilder() to construct.
    private ResInvitationCode(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResInvitationCode() {
      code_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResInvitationCode();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResInvitationCode(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              show_ = input.readBool();
              break;
            }
            case 18: {
              java.lang.String s = input.readStringRequireUtf8();

              code_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ResInvitationCode_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ResInvitationCode_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.class, com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.Builder.class);
    }

    public static final int SHOW_FIELD_NUMBER = 1;
    private boolean show_;
    /**
     * <pre>
     *是否显示邀请码
     * </pre>
     *
     * <code>bool show = 1;</code>
     * @return The show.
     */
    @java.lang.Override
    public boolean getShow() {
      return show_;
    }

    public static final int CODE_FIELD_NUMBER = 2;
    private volatile java.lang.Object code_;
    /**
     * <pre>
     *邀请码
     * </pre>
     *
     * <code>string code = 2;</code>
     * @return The code.
     */
    @java.lang.Override
    public java.lang.String getCode() {
      java.lang.Object ref = code_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        code_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *邀请码
     * </pre>
     *
     * <code>string code = 2;</code>
     * @return The bytes for code.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCodeBytes() {
      java.lang.Object ref = code_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        code_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (show_ != false) {
        output.writeBool(1, show_);
      }
      if (!getCodeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, code_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (show_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, show_);
      }
      if (!getCodeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, code_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.InvitationCodeProtos.ResInvitationCode)) {
        return super.equals(obj);
      }
      com.sh.game.protos.InvitationCodeProtos.ResInvitationCode other = (com.sh.game.protos.InvitationCodeProtos.ResInvitationCode) obj;

      if (getShow()
          != other.getShow()) return false;
      if (!getCode()
          .equals(other.getCode())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SHOW_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getShow());
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.InvitationCodeProtos.ResInvitationCode prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResInvitationCode' id='2' desc='返回玩家已生成的邀请码' 
     * </pre>
     *
     * Protobuf type {@code invitationCode.ResInvitationCode}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:invitationCode.ResInvitationCode)
        com.sh.game.protos.InvitationCodeProtos.ResInvitationCodeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ResInvitationCode_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ResInvitationCode_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.class, com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.Builder.class);
      }

      // Construct using com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        show_ = false;

        code_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.InvitationCodeProtos.internal_static_invitationCode_ResInvitationCode_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.InvitationCodeProtos.ResInvitationCode getDefaultInstanceForType() {
        return com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.InvitationCodeProtos.ResInvitationCode build() {
        com.sh.game.protos.InvitationCodeProtos.ResInvitationCode result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.InvitationCodeProtos.ResInvitationCode buildPartial() {
        com.sh.game.protos.InvitationCodeProtos.ResInvitationCode result = new com.sh.game.protos.InvitationCodeProtos.ResInvitationCode(this);
        result.show_ = show_;
        result.code_ = code_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.InvitationCodeProtos.ResInvitationCode) {
          return mergeFrom((com.sh.game.protos.InvitationCodeProtos.ResInvitationCode)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.InvitationCodeProtos.ResInvitationCode other) {
        if (other == com.sh.game.protos.InvitationCodeProtos.ResInvitationCode.getDefaultInstance()) return this;
        if (other.getShow() != false) {
          setShow(other.getShow());
        }
        if (!other.getCode().isEmpty()) {
          code_ = other.code_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.InvitationCodeProtos.ResInvitationCode parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.InvitationCodeProtos.ResInvitationCode) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private boolean show_ ;
      /**
       * <pre>
       *是否显示邀请码
       * </pre>
       *
       * <code>bool show = 1;</code>
       * @return The show.
       */
      @java.lang.Override
      public boolean getShow() {
        return show_;
      }
      /**
       * <pre>
       *是否显示邀请码
       * </pre>
       *
       * <code>bool show = 1;</code>
       * @param value The show to set.
       * @return This builder for chaining.
       */
      public Builder setShow(boolean value) {
        
        show_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否显示邀请码
       * </pre>
       *
       * <code>bool show = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearShow() {
        
        show_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object code_ = "";
      /**
       * <pre>
       *邀请码
       * </pre>
       *
       * <code>string code = 2;</code>
       * @return The code.
       */
      public java.lang.String getCode() {
        java.lang.Object ref = code_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          code_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *邀请码
       * </pre>
       *
       * <code>string code = 2;</code>
       * @return The bytes for code.
       */
      public com.google.protobuf.ByteString
          getCodeBytes() {
        java.lang.Object ref = code_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          code_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *邀请码
       * </pre>
       *
       * <code>string code = 2;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        code_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邀请码
       * </pre>
       *
       * <code>string code = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        
        code_ = getDefaultInstance().getCode();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *邀请码
       * </pre>
       *
       * <code>string code = 2;</code>
       * @param value The bytes for code to set.
       * @return This builder for chaining.
       */
      public Builder setCodeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        code_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:invitationCode.ResInvitationCode)
    }

    // @@protoc_insertion_point(class_scope:invitationCode.ResInvitationCode)
    private static final com.sh.game.protos.InvitationCodeProtos.ResInvitationCode DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.InvitationCodeProtos.ResInvitationCode();
    }

    public static com.sh.game.protos.InvitationCodeProtos.ResInvitationCode getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResInvitationCode>
        PARSER = new com.google.protobuf.AbstractParser<ResInvitationCode>() {
      @java.lang.Override
      public ResInvitationCode parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResInvitationCode(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResInvitationCode> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResInvitationCode> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.InvitationCodeProtos.ResInvitationCode getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_invitationCode_ReqInvitationCode_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_invitationCode_ReqInvitationCode_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_invitationCode_ResInvitationCode_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_invitationCode_ResInvitationCode_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024invitationCode.proto\022\016invitationCode\"\023" +
      "\n\021ReqInvitationCode\"/\n\021ResInvitationCode" +
      "\022\014\n\004show\030\001 \001(\010\022\014\n\004code\030\002 \001(\tB*\n\022com.sh.g" +
      "ame.protosB\024InvitationCodeProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_invitationCode_ReqInvitationCode_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_invitationCode_ReqInvitationCode_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_invitationCode_ReqInvitationCode_descriptor,
        new java.lang.String[] { });
    internal_static_invitationCode_ResInvitationCode_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_invitationCode_ResInvitationCode_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_invitationCode_ResInvitationCode_descriptor,
        new java.lang.String[] { "Show", "Code", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
