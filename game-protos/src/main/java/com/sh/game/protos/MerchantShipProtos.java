// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: merchantShip.proto

package com.sh.game.protos;

public final class MerchantShipProtos {
  private MerchantShipProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqMerchantShipBuyMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:merchantship.ReqMerchantShipBuyMessage)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqMerchantShipBuyMessage' id='1' desc='请求商船游戏道具购买' 
   * </pre>
   *
   * Protobuf type {@code merchantship.ReqMerchantShipBuyMessage}
   */
  public static final class ReqMerchantShipBuyMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:merchantship.ReqMerchantShipBuyMessage)
      ReqMerchantShipBuyMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMerchantShipBuyMessage.newBuilder() to construct.
    private ReqMerchantShipBuyMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMerchantShipBuyMessage() {
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMerchantShipBuyMessage();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMerchantShipBuyMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipBuyMessage_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipBuyMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ReqMerchantShipBuyMessage.class, Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ReqMerchantShipBuyMessage)) {
        return super.equals(obj);
      }
      ReqMerchantShipBuyMessage other = (ReqMerchantShipBuyMessage) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ReqMerchantShipBuyMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipBuyMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipBuyMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipBuyMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipBuyMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipBuyMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipBuyMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ReqMerchantShipBuyMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ReqMerchantShipBuyMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ReqMerchantShipBuyMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ReqMerchantShipBuyMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ReqMerchantShipBuyMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ReqMerchantShipBuyMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMerchantShipBuyMessage' id='1' desc='请求商船游戏道具购买' 
     * </pre>
     *
     * Protobuf type {@code merchantship.ReqMerchantShipBuyMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:merchantship.ReqMerchantShipBuyMessage)
        ReqMerchantShipBuyMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipBuyMessage_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipBuyMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ReqMerchantShipBuyMessage.class, Builder.class);
      }

      // Construct using com.sh.game.protos.MerchantShipProtos.ReqMerchantShipBuyMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipBuyMessage_descriptor;
      }

      @Override
      public ReqMerchantShipBuyMessage getDefaultInstanceForType() {
        return ReqMerchantShipBuyMessage.getDefaultInstance();
      }

      @Override
      public ReqMerchantShipBuyMessage build() {
        ReqMerchantShipBuyMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public ReqMerchantShipBuyMessage buildPartial() {
        ReqMerchantShipBuyMessage result = new ReqMerchantShipBuyMessage(this);
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ReqMerchantShipBuyMessage) {
          return mergeFrom((ReqMerchantShipBuyMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ReqMerchantShipBuyMessage other) {
        if (other == ReqMerchantShipBuyMessage.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ReqMerchantShipBuyMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ReqMerchantShipBuyMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:merchantship.ReqMerchantShipBuyMessage)
    }

    // @@protoc_insertion_point(class_scope:merchantship.ReqMerchantShipBuyMessage)
    private static final ReqMerchantShipBuyMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ReqMerchantShipBuyMessage();
    }

    public static ReqMerchantShipBuyMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMerchantShipBuyMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqMerchantShipBuyMessage>() {
      @Override
      public ReqMerchantShipBuyMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMerchantShipBuyMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMerchantShipBuyMessage> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<ReqMerchantShipBuyMessage> getParserForType() {
      return PARSER;
    }

    @Override
    public ReqMerchantShipBuyMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMerchantShipAdvertiseBuyMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:merchantship.ReqMerchantShipAdvertiseBuyMessage)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqMerchantShipAdvertiseBuyMessage' id='2' desc='请求商船游戏广告道具购买' 
   * </pre>
   *
   * Protobuf type {@code merchantship.ReqMerchantShipAdvertiseBuyMessage}
   */
  public static final class ReqMerchantShipAdvertiseBuyMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:merchantship.ReqMerchantShipAdvertiseBuyMessage)
      ReqMerchantShipAdvertiseBuyMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMerchantShipAdvertiseBuyMessage.newBuilder() to construct.
    private ReqMerchantShipAdvertiseBuyMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMerchantShipAdvertiseBuyMessage() {
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMerchantShipAdvertiseBuyMessage();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMerchantShipAdvertiseBuyMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ReqMerchantShipAdvertiseBuyMessage.class, Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ReqMerchantShipAdvertiseBuyMessage)) {
        return super.equals(obj);
      }
      ReqMerchantShipAdvertiseBuyMessage other = (ReqMerchantShipAdvertiseBuyMessage) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ReqMerchantShipAdvertiseBuyMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ReqMerchantShipAdvertiseBuyMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMerchantShipAdvertiseBuyMessage' id='2' desc='请求商船游戏广告道具购买' 
     * </pre>
     *
     * Protobuf type {@code merchantship.ReqMerchantShipAdvertiseBuyMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:merchantship.ReqMerchantShipAdvertiseBuyMessage)
        ReqMerchantShipAdvertiseBuyMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ReqMerchantShipAdvertiseBuyMessage.class, Builder.class);
      }

      // Construct using com.sh.game.protos.MerchantShipProtos.ReqMerchantShipAdvertiseBuyMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_descriptor;
      }

      @Override
      public ReqMerchantShipAdvertiseBuyMessage getDefaultInstanceForType() {
        return ReqMerchantShipAdvertiseBuyMessage.getDefaultInstance();
      }

      @Override
      public ReqMerchantShipAdvertiseBuyMessage build() {
        ReqMerchantShipAdvertiseBuyMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public ReqMerchantShipAdvertiseBuyMessage buildPartial() {
        ReqMerchantShipAdvertiseBuyMessage result = new ReqMerchantShipAdvertiseBuyMessage(this);
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ReqMerchantShipAdvertiseBuyMessage) {
          return mergeFrom((ReqMerchantShipAdvertiseBuyMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ReqMerchantShipAdvertiseBuyMessage other) {
        if (other == ReqMerchantShipAdvertiseBuyMessage.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ReqMerchantShipAdvertiseBuyMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ReqMerchantShipAdvertiseBuyMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:merchantship.ReqMerchantShipAdvertiseBuyMessage)
    }

    // @@protoc_insertion_point(class_scope:merchantship.ReqMerchantShipAdvertiseBuyMessage)
    private static final ReqMerchantShipAdvertiseBuyMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ReqMerchantShipAdvertiseBuyMessage();
    }

    public static ReqMerchantShipAdvertiseBuyMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMerchantShipAdvertiseBuyMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqMerchantShipAdvertiseBuyMessage>() {
      @Override
      public ReqMerchantShipAdvertiseBuyMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMerchantShipAdvertiseBuyMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMerchantShipAdvertiseBuyMessage> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<ReqMerchantShipAdvertiseBuyMessage> getParserForType() {
      return PARSER;
    }

    @Override
    public ReqMerchantShipAdvertiseBuyMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMerchantShipRewardMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:merchantship.ReqMerchantShipRewardMessage)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqMerchantShipRewardMessage' id='3' desc='请求商船游戏通关奖励' 
   * </pre>
   *
   * Protobuf type {@code merchantship.ReqMerchantShipRewardMessage}
   */
  public static final class ReqMerchantShipRewardMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:merchantship.ReqMerchantShipRewardMessage)
      ReqMerchantShipRewardMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMerchantShipRewardMessage.newBuilder() to construct.
    private ReqMerchantShipRewardMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMerchantShipRewardMessage() {
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMerchantShipRewardMessage();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMerchantShipRewardMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipRewardMessage_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipRewardMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ReqMerchantShipRewardMessage.class, Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ReqMerchantShipRewardMessage)) {
        return super.equals(obj);
      }
      ReqMerchantShipRewardMessage other = (ReqMerchantShipRewardMessage) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ReqMerchantShipRewardMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipRewardMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipRewardMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipRewardMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipRewardMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipRewardMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipRewardMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ReqMerchantShipRewardMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ReqMerchantShipRewardMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ReqMerchantShipRewardMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ReqMerchantShipRewardMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ReqMerchantShipRewardMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ReqMerchantShipRewardMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMerchantShipRewardMessage' id='3' desc='请求商船游戏通关奖励' 
     * </pre>
     *
     * Protobuf type {@code merchantship.ReqMerchantShipRewardMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:merchantship.ReqMerchantShipRewardMessage)
        ReqMerchantShipRewardMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipRewardMessage_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipRewardMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ReqMerchantShipRewardMessage.class, Builder.class);
      }

      // Construct using com.sh.game.protos.MerchantShipProtos.ReqMerchantShipRewardMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipRewardMessage_descriptor;
      }

      @Override
      public ReqMerchantShipRewardMessage getDefaultInstanceForType() {
        return ReqMerchantShipRewardMessage.getDefaultInstance();
      }

      @Override
      public ReqMerchantShipRewardMessage build() {
        ReqMerchantShipRewardMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public ReqMerchantShipRewardMessage buildPartial() {
        ReqMerchantShipRewardMessage result = new ReqMerchantShipRewardMessage(this);
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ReqMerchantShipRewardMessage) {
          return mergeFrom((ReqMerchantShipRewardMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ReqMerchantShipRewardMessage other) {
        if (other == ReqMerchantShipRewardMessage.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ReqMerchantShipRewardMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ReqMerchantShipRewardMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:merchantship.ReqMerchantShipRewardMessage)
    }

    // @@protoc_insertion_point(class_scope:merchantship.ReqMerchantShipRewardMessage)
    private static final ReqMerchantShipRewardMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ReqMerchantShipRewardMessage();
    }

    public static ReqMerchantShipRewardMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMerchantShipRewardMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqMerchantShipRewardMessage>() {
      @Override
      public ReqMerchantShipRewardMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMerchantShipRewardMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMerchantShipRewardMessage> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<ReqMerchantShipRewardMessage> getParserForType() {
      return PARSER;
    }

    @Override
    public ReqMerchantShipRewardMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMerchantShipItemInfoMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:merchantship.ResMerchantShipItemInfoMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 itemCfgId = 1;</code>
     * @return The itemCfgId.
     */
    int getItemCfgId();
  }
  /**
   * <pre>
   ** class='ResMerchantShipItemInfoMessage' id='4' desc='推送商船游戏道具' 
   * </pre>
   *
   * Protobuf type {@code merchantship.ResMerchantShipItemInfoMessage}
   */
  public static final class ResMerchantShipItemInfoMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:merchantship.ResMerchantShipItemInfoMessage)
      ResMerchantShipItemInfoMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMerchantShipItemInfoMessage.newBuilder() to construct.
    private ResMerchantShipItemInfoMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMerchantShipItemInfoMessage() {
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMerchantShipItemInfoMessage();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMerchantShipItemInfoMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              itemCfgId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MerchantShipProtos.internal_static_merchantship_ResMerchantShipItemInfoMessage_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MerchantShipProtos.internal_static_merchantship_ResMerchantShipItemInfoMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ResMerchantShipItemInfoMessage.class, Builder.class);
    }

    public static final int ITEMCFGID_FIELD_NUMBER = 1;
    private int itemCfgId_;
    /**
     * <code>int32 itemCfgId = 1;</code>
     * @return The itemCfgId.
     */
    @Override
    public int getItemCfgId() {
      return itemCfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (itemCfgId_ != 0) {
        output.writeInt32(1, itemCfgId_);
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (itemCfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, itemCfgId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ResMerchantShipItemInfoMessage)) {
        return super.equals(obj);
      }
      ResMerchantShipItemInfoMessage other = (ResMerchantShipItemInfoMessage) obj;

      if (getItemCfgId()
          != other.getItemCfgId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ITEMCFGID_FIELD_NUMBER;
      hash = (53 * hash) + getItemCfgId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ResMerchantShipItemInfoMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ResMerchantShipItemInfoMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ResMerchantShipItemInfoMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ResMerchantShipItemInfoMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ResMerchantShipItemInfoMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ResMerchantShipItemInfoMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ResMerchantShipItemInfoMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ResMerchantShipItemInfoMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ResMerchantShipItemInfoMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ResMerchantShipItemInfoMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ResMerchantShipItemInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ResMerchantShipItemInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ResMerchantShipItemInfoMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMerchantShipItemInfoMessage' id='4' desc='推送商船游戏道具' 
     * </pre>
     *
     * Protobuf type {@code merchantship.ResMerchantShipItemInfoMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:merchantship.ResMerchantShipItemInfoMessage)
        ResMerchantShipItemInfoMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MerchantShipProtos.internal_static_merchantship_ResMerchantShipItemInfoMessage_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MerchantShipProtos.internal_static_merchantship_ResMerchantShipItemInfoMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ResMerchantShipItemInfoMessage.class, Builder.class);
      }

      // Construct using com.sh.game.protos.MerchantShipProtos.ResMerchantShipItemInfoMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        itemCfgId_ = 0;

        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MerchantShipProtos.internal_static_merchantship_ResMerchantShipItemInfoMessage_descriptor;
      }

      @Override
      public ResMerchantShipItemInfoMessage getDefaultInstanceForType() {
        return ResMerchantShipItemInfoMessage.getDefaultInstance();
      }

      @Override
      public ResMerchantShipItemInfoMessage build() {
        ResMerchantShipItemInfoMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public ResMerchantShipItemInfoMessage buildPartial() {
        ResMerchantShipItemInfoMessage result = new ResMerchantShipItemInfoMessage(this);
        result.itemCfgId_ = itemCfgId_;
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ResMerchantShipItemInfoMessage) {
          return mergeFrom((ResMerchantShipItemInfoMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ResMerchantShipItemInfoMessage other) {
        if (other == ResMerchantShipItemInfoMessage.getDefaultInstance()) return this;
        if (other.getItemCfgId() != 0) {
          setItemCfgId(other.getItemCfgId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ResMerchantShipItemInfoMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ResMerchantShipItemInfoMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int itemCfgId_ ;
      /**
       * <code>int32 itemCfgId = 1;</code>
       * @return The itemCfgId.
       */
      @Override
      public int getItemCfgId() {
        return itemCfgId_;
      }
      /**
       * <code>int32 itemCfgId = 1;</code>
       * @param value The itemCfgId to set.
       * @return This builder for chaining.
       */
      public Builder setItemCfgId(int value) {
        
        itemCfgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 itemCfgId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemCfgId() {
        
        itemCfgId_ = 0;
        onChanged();
        return this;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:merchantship.ResMerchantShipItemInfoMessage)
    }

    // @@protoc_insertion_point(class_scope:merchantship.ResMerchantShipItemInfoMessage)
    private static final ResMerchantShipItemInfoMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ResMerchantShipItemInfoMessage();
    }

    public static ResMerchantShipItemInfoMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMerchantShipItemInfoMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResMerchantShipItemInfoMessage>() {
      @Override
      public ResMerchantShipItemInfoMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMerchantShipItemInfoMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMerchantShipItemInfoMessage> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<ResMerchantShipItemInfoMessage> getParserForType() {
      return PARSER;
    }

    @Override
    public ResMerchantShipItemInfoMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMerchantShipRewardMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:merchantship.ResMerchantShipRewardMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    java.util.List<AbcProtos.CommonItemBean>
        getRewardsList();
    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    AbcProtos.CommonItemBean getRewards(int index);
    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    int getRewardsCount();
    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    java.util.List<? extends AbcProtos.CommonItemBeanOrBuilder>
        getRewardsOrBuilderList();
    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    AbcProtos.CommonItemBeanOrBuilder getRewardsOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResMerchantShipRewardMessage' id='5' desc='推送商船游戏通关奖励' 
   * </pre>
   *
   * Protobuf type {@code merchantship.ResMerchantShipRewardMessage}
   */
  public static final class ResMerchantShipRewardMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:merchantship.ResMerchantShipRewardMessage)
      ResMerchantShipRewardMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMerchantShipRewardMessage.newBuilder() to construct.
    private ResMerchantShipRewardMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMerchantShipRewardMessage() {
      rewards_ = java.util.Collections.emptyList();
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMerchantShipRewardMessage();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMerchantShipRewardMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                rewards_ = new java.util.ArrayList<AbcProtos.CommonItemBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              rewards_.add(
                  input.readMessage(AbcProtos.CommonItemBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          rewards_ = java.util.Collections.unmodifiableList(rewards_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MerchantShipProtos.internal_static_merchantship_ResMerchantShipRewardMessage_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MerchantShipProtos.internal_static_merchantship_ResMerchantShipRewardMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ResMerchantShipRewardMessage.class, Builder.class);
    }

    public static final int REWARDS_FIELD_NUMBER = 1;
    private java.util.List<AbcProtos.CommonItemBean> rewards_;
    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    @Override
    public java.util.List<AbcProtos.CommonItemBean> getRewardsList() {
      return rewards_;
    }
    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    @Override
    public java.util.List<? extends AbcProtos.CommonItemBeanOrBuilder>
        getRewardsOrBuilderList() {
      return rewards_;
    }
    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    @Override
    public int getRewardsCount() {
      return rewards_.size();
    }
    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    @Override
    public AbcProtos.CommonItemBean getRewards(int index) {
      return rewards_.get(index);
    }
    /**
     * <code>repeated .abc.CommonItemBean rewards = 1;</code>
     */
    @Override
    public AbcProtos.CommonItemBeanOrBuilder getRewardsOrBuilder(
        int index) {
      return rewards_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < rewards_.size(); i++) {
        output.writeMessage(1, rewards_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < rewards_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, rewards_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ResMerchantShipRewardMessage)) {
        return super.equals(obj);
      }
      ResMerchantShipRewardMessage other = (ResMerchantShipRewardMessage) obj;

      if (!getRewardsList()
          .equals(other.getRewardsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRewardsCount() > 0) {
        hash = (37 * hash) + REWARDS_FIELD_NUMBER;
        hash = (53 * hash) + getRewardsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ResMerchantShipRewardMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ResMerchantShipRewardMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ResMerchantShipRewardMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ResMerchantShipRewardMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ResMerchantShipRewardMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ResMerchantShipRewardMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ResMerchantShipRewardMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ResMerchantShipRewardMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ResMerchantShipRewardMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ResMerchantShipRewardMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ResMerchantShipRewardMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ResMerchantShipRewardMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ResMerchantShipRewardMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMerchantShipRewardMessage' id='5' desc='推送商船游戏通关奖励' 
     * </pre>
     *
     * Protobuf type {@code merchantship.ResMerchantShipRewardMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:merchantship.ResMerchantShipRewardMessage)
        ResMerchantShipRewardMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MerchantShipProtos.internal_static_merchantship_ResMerchantShipRewardMessage_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MerchantShipProtos.internal_static_merchantship_ResMerchantShipRewardMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ResMerchantShipRewardMessage.class, Builder.class);
      }

      // Construct using com.sh.game.protos.MerchantShipProtos.ResMerchantShipRewardMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getRewardsFieldBuilder();
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        if (rewardsBuilder_ == null) {
          rewards_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rewardsBuilder_.clear();
        }
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MerchantShipProtos.internal_static_merchantship_ResMerchantShipRewardMessage_descriptor;
      }

      @Override
      public ResMerchantShipRewardMessage getDefaultInstanceForType() {
        return ResMerchantShipRewardMessage.getDefaultInstance();
      }

      @Override
      public ResMerchantShipRewardMessage build() {
        ResMerchantShipRewardMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public ResMerchantShipRewardMessage buildPartial() {
        ResMerchantShipRewardMessage result = new ResMerchantShipRewardMessage(this);
        int from_bitField0_ = bitField0_;
        if (rewardsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rewards_ = java.util.Collections.unmodifiableList(rewards_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rewards_ = rewards_;
        } else {
          result.rewards_ = rewardsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ResMerchantShipRewardMessage) {
          return mergeFrom((ResMerchantShipRewardMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ResMerchantShipRewardMessage other) {
        if (other == ResMerchantShipRewardMessage.getDefaultInstance()) return this;
        if (rewardsBuilder_ == null) {
          if (!other.rewards_.isEmpty()) {
            if (rewards_.isEmpty()) {
              rewards_ = other.rewards_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardsIsMutable();
              rewards_.addAll(other.rewards_);
            }
            onChanged();
          }
        } else {
          if (!other.rewards_.isEmpty()) {
            if (rewardsBuilder_.isEmpty()) {
              rewardsBuilder_.dispose();
              rewardsBuilder_ = null;
              rewards_ = other.rewards_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardsFieldBuilder() : null;
            } else {
              rewardsBuilder_.addAllMessages(other.rewards_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ResMerchantShipRewardMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ResMerchantShipRewardMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<AbcProtos.CommonItemBean> rewards_ =
        java.util.Collections.emptyList();
      private void ensureRewardsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rewards_ = new java.util.ArrayList<AbcProtos.CommonItemBean>(rewards_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          AbcProtos.CommonItemBean, AbcProtos.CommonItemBean.Builder, AbcProtos.CommonItemBeanOrBuilder> rewardsBuilder_;

      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public java.util.List<AbcProtos.CommonItemBean> getRewardsList() {
        if (rewardsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewards_);
        } else {
          return rewardsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public int getRewardsCount() {
        if (rewardsBuilder_ == null) {
          return rewards_.size();
        } else {
          return rewardsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public AbcProtos.CommonItemBean getRewards(int index) {
        if (rewardsBuilder_ == null) {
          return rewards_.get(index);
        } else {
          return rewardsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public Builder setRewards(
          int index, AbcProtos.CommonItemBean value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardsIsMutable();
          rewards_.set(index, value);
          onChanged();
        } else {
          rewardsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public Builder setRewards(
          int index, AbcProtos.CommonItemBean.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public Builder addRewards(AbcProtos.CommonItemBean value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardsIsMutable();
          rewards_.add(value);
          onChanged();
        } else {
          rewardsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public Builder addRewards(
          int index, AbcProtos.CommonItemBean value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardsIsMutable();
          rewards_.add(index, value);
          onChanged();
        } else {
          rewardsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public Builder addRewards(
          AbcProtos.CommonItemBean.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.add(builderForValue.build());
          onChanged();
        } else {
          rewardsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public Builder addRewards(
          int index, AbcProtos.CommonItemBean.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public Builder addAllRewards(
          Iterable<? extends AbcProtos.CommonItemBean> values) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewards_);
          onChanged();
        } else {
          rewardsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public Builder clearRewards() {
        if (rewardsBuilder_ == null) {
          rewards_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public Builder removeRewards(int index) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.remove(index);
          onChanged();
        } else {
          rewardsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public AbcProtos.CommonItemBean.Builder getRewardsBuilder(
          int index) {
        return getRewardsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public AbcProtos.CommonItemBeanOrBuilder getRewardsOrBuilder(
          int index) {
        if (rewardsBuilder_ == null) {
          return rewards_.get(index);  } else {
          return rewardsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public java.util.List<? extends AbcProtos.CommonItemBeanOrBuilder>
           getRewardsOrBuilderList() {
        if (rewardsBuilder_ != null) {
          return rewardsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewards_);
        }
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public AbcProtos.CommonItemBean.Builder addRewardsBuilder() {
        return getRewardsFieldBuilder().addBuilder(
            AbcProtos.CommonItemBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public AbcProtos.CommonItemBean.Builder addRewardsBuilder(
          int index) {
        return getRewardsFieldBuilder().addBuilder(
            index, AbcProtos.CommonItemBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonItemBean rewards = 1;</code>
       */
      public java.util.List<AbcProtos.CommonItemBean.Builder>
           getRewardsBuilderList() {
        return getRewardsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          AbcProtos.CommonItemBean, AbcProtos.CommonItemBean.Builder, AbcProtos.CommonItemBeanOrBuilder>
          getRewardsFieldBuilder() {
        if (rewardsBuilder_ == null) {
          rewardsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              AbcProtos.CommonItemBean, AbcProtos.CommonItemBean.Builder, AbcProtos.CommonItemBeanOrBuilder>(
                  rewards_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rewards_ = null;
        }
        return rewardsBuilder_;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:merchantship.ResMerchantShipRewardMessage)
    }

    // @@protoc_insertion_point(class_scope:merchantship.ResMerchantShipRewardMessage)
    private static final ResMerchantShipRewardMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ResMerchantShipRewardMessage();
    }

    public static ResMerchantShipRewardMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMerchantShipRewardMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResMerchantShipRewardMessage>() {
      @Override
      public ResMerchantShipRewardMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMerchantShipRewardMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMerchantShipRewardMessage> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<ResMerchantShipRewardMessage> getParserForType() {
      return PARSER;
    }

    @Override
    public ResMerchantShipRewardMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMerchantShipItemSellMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:merchantship.ReqMerchantShipItemSellMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    java.util.List<AbcProtos.CommonKeyValueBean>
        getItemsList();
    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    AbcProtos.CommonKeyValueBean getItems(int index);
    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    int getItemsCount();
    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    java.util.List<? extends AbcProtos.CommonKeyValueBeanOrBuilder>
        getItemsOrBuilderList();
    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    AbcProtos.CommonKeyValueBeanOrBuilder getItemsOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ReqMerchantShipItemSellMessage' id='6' desc='请求商船游戏道具出售' 
   * </pre>
   *
   * Protobuf type {@code merchantship.ReqMerchantShipItemSellMessage}
   */
  public static final class ReqMerchantShipItemSellMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:merchantship.ReqMerchantShipItemSellMessage)
      ReqMerchantShipItemSellMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMerchantShipItemSellMessage.newBuilder() to construct.
    private ReqMerchantShipItemSellMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMerchantShipItemSellMessage() {
      items_ = java.util.Collections.emptyList();
    }

    @Override
    @SuppressWarnings({"unused"})
    protected Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMerchantShipItemSellMessage();
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMerchantShipItemSellMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                items_ = new java.util.ArrayList<AbcProtos.CommonKeyValueBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              items_.add(
                  input.readMessage(AbcProtos.CommonKeyValueBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          items_ = java.util.Collections.unmodifiableList(items_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipItemSellMessage_descriptor;
    }

    @Override
    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipItemSellMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ReqMerchantShipItemSellMessage.class, Builder.class);
    }

    public static final int ITEMS_FIELD_NUMBER = 1;
    private java.util.List<AbcProtos.CommonKeyValueBean> items_;
    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    @Override
    public java.util.List<AbcProtos.CommonKeyValueBean> getItemsList() {
      return items_;
    }
    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    @Override
    public java.util.List<? extends AbcProtos.CommonKeyValueBeanOrBuilder>
        getItemsOrBuilderList() {
      return items_;
    }
    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    @Override
    public int getItemsCount() {
      return items_.size();
    }
    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    @Override
    public AbcProtos.CommonKeyValueBean getItems(int index) {
      return items_.get(index);
    }
    /**
     * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
     */
    @Override
    public AbcProtos.CommonKeyValueBeanOrBuilder getItemsOrBuilder(
        int index) {
      return items_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < items_.size(); i++) {
        output.writeMessage(1, items_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < items_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, items_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @Override
    public boolean equals(final Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof ReqMerchantShipItemSellMessage)) {
        return super.equals(obj);
      }
      ReqMerchantShipItemSellMessage other = (ReqMerchantShipItemSellMessage) obj;

      if (!getItemsList()
          .equals(other.getItemsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getItemsCount() > 0) {
        hash = (37 * hash) + ITEMS_FIELD_NUMBER;
        hash = (53 * hash) + getItemsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static ReqMerchantShipItemSellMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipItemSellMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipItemSellMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipItemSellMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipItemSellMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReqMerchantShipItemSellMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReqMerchantShipItemSellMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ReqMerchantShipItemSellMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static ReqMerchantShipItemSellMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static ReqMerchantShipItemSellMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static ReqMerchantShipItemSellMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static ReqMerchantShipItemSellMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(ReqMerchantShipItemSellMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMerchantShipItemSellMessage' id='6' desc='请求商船游戏道具出售' 
     * </pre>
     *
     * Protobuf type {@code merchantship.ReqMerchantShipItemSellMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:merchantship.ReqMerchantShipItemSellMessage)
        ReqMerchantShipItemSellMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipItemSellMessage_descriptor;
      }

      @Override
      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipItemSellMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ReqMerchantShipItemSellMessage.class, Builder.class);
      }

      // Construct using com.sh.game.protos.MerchantShipProtos.ReqMerchantShipItemSellMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getItemsFieldBuilder();
        }
      }
      @Override
      public Builder clear() {
        super.clear();
        if (itemsBuilder_ == null) {
          items_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          itemsBuilder_.clear();
        }
        return this;
      }

      @Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MerchantShipProtos.internal_static_merchantship_ReqMerchantShipItemSellMessage_descriptor;
      }

      @Override
      public ReqMerchantShipItemSellMessage getDefaultInstanceForType() {
        return ReqMerchantShipItemSellMessage.getDefaultInstance();
      }

      @Override
      public ReqMerchantShipItemSellMessage build() {
        ReqMerchantShipItemSellMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @Override
      public ReqMerchantShipItemSellMessage buildPartial() {
        ReqMerchantShipItemSellMessage result = new ReqMerchantShipItemSellMessage(this);
        int from_bitField0_ = bitField0_;
        if (itemsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            items_ = java.util.Collections.unmodifiableList(items_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.items_ = items_;
        } else {
          result.items_ = itemsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @Override
      public Builder clone() {
        return super.clone();
      }
      @Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.setField(field, value);
      }
      @Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          Object value) {
        return super.addRepeatedField(field, value);
      }
      @Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ReqMerchantShipItemSellMessage) {
          return mergeFrom((ReqMerchantShipItemSellMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ReqMerchantShipItemSellMessage other) {
        if (other == ReqMerchantShipItemSellMessage.getDefaultInstance()) return this;
        if (itemsBuilder_ == null) {
          if (!other.items_.isEmpty()) {
            if (items_.isEmpty()) {
              items_ = other.items_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureItemsIsMutable();
              items_.addAll(other.items_);
            }
            onChanged();
          }
        } else {
          if (!other.items_.isEmpty()) {
            if (itemsBuilder_.isEmpty()) {
              itemsBuilder_.dispose();
              itemsBuilder_ = null;
              items_ = other.items_;
              bitField0_ = (bitField0_ & ~0x00000001);
              itemsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getItemsFieldBuilder() : null;
            } else {
              itemsBuilder_.addAllMessages(other.items_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @Override
      public final boolean isInitialized() {
        return true;
      }

      @Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ReqMerchantShipItemSellMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ReqMerchantShipItemSellMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<AbcProtos.CommonKeyValueBean> items_ =
        java.util.Collections.emptyList();
      private void ensureItemsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          items_ = new java.util.ArrayList<AbcProtos.CommonKeyValueBean>(items_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          AbcProtos.CommonKeyValueBean, AbcProtos.CommonKeyValueBean.Builder, AbcProtos.CommonKeyValueBeanOrBuilder> itemsBuilder_;

      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public java.util.List<AbcProtos.CommonKeyValueBean> getItemsList() {
        if (itemsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(items_);
        } else {
          return itemsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public int getItemsCount() {
        if (itemsBuilder_ == null) {
          return items_.size();
        } else {
          return itemsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public AbcProtos.CommonKeyValueBean getItems(int index) {
        if (itemsBuilder_ == null) {
          return items_.get(index);
        } else {
          return itemsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public Builder setItems(
          int index, AbcProtos.CommonKeyValueBean value) {
        if (itemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemsIsMutable();
          items_.set(index, value);
          onChanged();
        } else {
          itemsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public Builder setItems(
          int index, AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          items_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public Builder addItems(AbcProtos.CommonKeyValueBean value) {
        if (itemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemsIsMutable();
          items_.add(value);
          onChanged();
        } else {
          itemsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public Builder addItems(
          int index, AbcProtos.CommonKeyValueBean value) {
        if (itemsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemsIsMutable();
          items_.add(index, value);
          onChanged();
        } else {
          itemsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public Builder addItems(
          AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          items_.add(builderForValue.build());
          onChanged();
        } else {
          itemsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public Builder addItems(
          int index, AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          items_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public Builder addAllItems(
          Iterable<? extends AbcProtos.CommonKeyValueBean> values) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, items_);
          onChanged();
        } else {
          itemsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public Builder clearItems() {
        if (itemsBuilder_ == null) {
          items_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          itemsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public Builder removeItems(int index) {
        if (itemsBuilder_ == null) {
          ensureItemsIsMutable();
          items_.remove(index);
          onChanged();
        } else {
          itemsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public AbcProtos.CommonKeyValueBean.Builder getItemsBuilder(
          int index) {
        return getItemsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public AbcProtos.CommonKeyValueBeanOrBuilder getItemsOrBuilder(
          int index) {
        if (itemsBuilder_ == null) {
          return items_.get(index);  } else {
          return itemsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public java.util.List<? extends AbcProtos.CommonKeyValueBeanOrBuilder>
           getItemsOrBuilderList() {
        if (itemsBuilder_ != null) {
          return itemsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(items_);
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public AbcProtos.CommonKeyValueBean.Builder addItemsBuilder() {
        return getItemsFieldBuilder().addBuilder(
            AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public AbcProtos.CommonKeyValueBean.Builder addItemsBuilder(
          int index) {
        return getItemsFieldBuilder().addBuilder(
            index, AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonKeyValueBean items = 1;</code>
       */
      public java.util.List<AbcProtos.CommonKeyValueBean.Builder>
           getItemsBuilderList() {
        return getItemsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          AbcProtos.CommonKeyValueBean, AbcProtos.CommonKeyValueBean.Builder, AbcProtos.CommonKeyValueBeanOrBuilder>
          getItemsFieldBuilder() {
        if (itemsBuilder_ == null) {
          itemsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              AbcProtos.CommonKeyValueBean, AbcProtos.CommonKeyValueBean.Builder, AbcProtos.CommonKeyValueBeanOrBuilder>(
                  items_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          items_ = null;
        }
        return itemsBuilder_;
      }
      @Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:merchantship.ReqMerchantShipItemSellMessage)
    }

    // @@protoc_insertion_point(class_scope:merchantship.ReqMerchantShipItemSellMessage)
    private static final ReqMerchantShipItemSellMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new ReqMerchantShipItemSellMessage();
    }

    public static ReqMerchantShipItemSellMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMerchantShipItemSellMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqMerchantShipItemSellMessage>() {
      @Override
      public ReqMerchantShipItemSellMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMerchantShipItemSellMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMerchantShipItemSellMessage> parser() {
      return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<ReqMerchantShipItemSellMessage> getParserForType() {
      return PARSER;
    }

    @Override
    public ReqMerchantShipItemSellMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_merchantship_ReqMerchantShipBuyMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_merchantship_ReqMerchantShipBuyMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_merchantship_ReqMerchantShipRewardMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_merchantship_ReqMerchantShipRewardMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_merchantship_ResMerchantShipItemInfoMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_merchantship_ResMerchantShipItemInfoMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_merchantship_ResMerchantShipRewardMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_merchantship_ResMerchantShipRewardMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_merchantship_ReqMerchantShipItemSellMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_merchantship_ReqMerchantShipItemSellMessage_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\022merchantShip.proto\022\014merchantship\032\tabc." +
      "proto\"\033\n\031ReqMerchantShipBuyMessage\"$\n\"Re" +
      "qMerchantShipAdvertiseBuyMessage\"\036\n\034ReqM" +
      "erchantShipRewardMessage\"3\n\036ResMerchantS" +
      "hipItemInfoMessage\022\021\n\titemCfgId\030\001 \001(\005\"D\n" +
      "\034ResMerchantShipRewardMessage\022$\n\007rewards" +
      "\030\001 \003(\0132\023.abc.CommonItemBean\"H\n\036ReqMercha" +
      "ntShipItemSellMessage\022&\n\005items\030\001 \003(\0132\027.a" +
      "bc.CommonKeyValueBeanB(\n\022com.sh.game.pro" +
      "tosB\022MerchantShipProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          AbcProtos.getDescriptor(),
        });
    internal_static_merchantship_ReqMerchantShipBuyMessage_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_merchantship_ReqMerchantShipBuyMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_merchantship_ReqMerchantShipBuyMessage_descriptor,
        new String[] { });
    internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_merchantship_ReqMerchantShipAdvertiseBuyMessage_descriptor,
        new String[] { });
    internal_static_merchantship_ReqMerchantShipRewardMessage_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_merchantship_ReqMerchantShipRewardMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_merchantship_ReqMerchantShipRewardMessage_descriptor,
        new String[] { });
    internal_static_merchantship_ResMerchantShipItemInfoMessage_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_merchantship_ResMerchantShipItemInfoMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_merchantship_ResMerchantShipItemInfoMessage_descriptor,
        new String[] { "ItemCfgId", });
    internal_static_merchantship_ResMerchantShipRewardMessage_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_merchantship_ResMerchantShipRewardMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_merchantship_ResMerchantShipRewardMessage_descriptor,
        new String[] { "Rewards", });
    internal_static_merchantship_ReqMerchantShipItemSellMessage_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_merchantship_ReqMerchantShipItemSellMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_merchantship_ReqMerchantShipItemSellMessage_descriptor,
        new String[] { "Items", });
    AbcProtos.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
