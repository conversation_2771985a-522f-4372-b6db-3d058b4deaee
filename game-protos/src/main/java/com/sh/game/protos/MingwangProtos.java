// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: mingwang.proto

package com.sh.game.protos;

public final class MingwangProtos {
  private MingwangProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqUpLevelMingWangOrBuilder extends
      // @@protoc_insertion_point(interface_extends:mingwang.ReqUpLevelMingWang)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqUpLevelMingWang' id='1' desc='请求升级江湖名望' 
   * </pre>
   *
   * Protobuf type {@code mingwang.ReqUpLevelMingWang}
   */
  public static final class ReqUpLevelMingWang extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:mingwang.ReqUpLevelMingWang)
      ReqUpLevelMingWangOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqUpLevelMingWang.newBuilder() to construct.
    private ReqUpLevelMingWang(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqUpLevelMingWang() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqUpLevelMingWang();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqUpLevelMingWang(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ReqUpLevelMingWang_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ReqUpLevelMingWang_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.class, com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang other = (com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqUpLevelMingWang' id='1' desc='请求升级江湖名望' 
     * </pre>
     *
     * Protobuf type {@code mingwang.ReqUpLevelMingWang}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:mingwang.ReqUpLevelMingWang)
        com.sh.game.protos.MingwangProtos.ReqUpLevelMingWangOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ReqUpLevelMingWang_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ReqUpLevelMingWang_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.class, com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.Builder.class);
      }

      // Construct using com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ReqUpLevelMingWang_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang getDefaultInstanceForType() {
        return com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang build() {
        com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang buildPartial() {
        com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang result = new com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang) {
          return mergeFrom((com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang other) {
        if (other == com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:mingwang.ReqUpLevelMingWang)
    }

    // @@protoc_insertion_point(class_scope:mingwang.ReqUpLevelMingWang)
    private static final com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang();
    }

    public static com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqUpLevelMingWang>
        PARSER = new com.google.protobuf.AbstractParser<ReqUpLevelMingWang>() {
      @java.lang.Override
      public ReqUpLevelMingWang parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqUpLevelMingWang(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqUpLevelMingWang> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqUpLevelMingWang> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMingWangOrBuilder extends
      // @@protoc_insertion_point(interface_extends:mingwang.ResMingWang)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *江湖名望,值未cfg_ih_prestige的id
     * </pre>
     *
     * <code>int32 mingWang = 1;</code>
     * @return The mingWang.
     */
    int getMingWang();
  }
  /**
   * <pre>
   ** class='ResMingWang' id='2' desc='返回江湖名望信息' 
   * </pre>
   *
   * Protobuf type {@code mingwang.ResMingWang}
   */
  public static final class ResMingWang extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:mingwang.ResMingWang)
      ResMingWangOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMingWang.newBuilder() to construct.
    private ResMingWang(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMingWang() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMingWang();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMingWang(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              mingWang_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ResMingWang_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ResMingWang_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MingwangProtos.ResMingWang.class, com.sh.game.protos.MingwangProtos.ResMingWang.Builder.class);
    }

    public static final int MINGWANG_FIELD_NUMBER = 1;
    private int mingWang_;
    /**
     * <pre>
     *江湖名望,值未cfg_ih_prestige的id
     * </pre>
     *
     * <code>int32 mingWang = 1;</code>
     * @return The mingWang.
     */
    @java.lang.Override
    public int getMingWang() {
      return mingWang_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (mingWang_ != 0) {
        output.writeInt32(1, mingWang_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (mingWang_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, mingWang_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MingwangProtos.ResMingWang)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MingwangProtos.ResMingWang other = (com.sh.game.protos.MingwangProtos.ResMingWang) obj;

      if (getMingWang()
          != other.getMingWang()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + MINGWANG_FIELD_NUMBER;
      hash = (53 * hash) + getMingWang();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MingwangProtos.ResMingWang parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MingwangProtos.ResMingWang prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMingWang' id='2' desc='返回江湖名望信息' 
     * </pre>
     *
     * Protobuf type {@code mingwang.ResMingWang}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:mingwang.ResMingWang)
        com.sh.game.protos.MingwangProtos.ResMingWangOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ResMingWang_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ResMingWang_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MingwangProtos.ResMingWang.class, com.sh.game.protos.MingwangProtos.ResMingWang.Builder.class);
      }

      // Construct using com.sh.game.protos.MingwangProtos.ResMingWang.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        mingWang_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MingwangProtos.internal_static_mingwang_ResMingWang_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MingwangProtos.ResMingWang getDefaultInstanceForType() {
        return com.sh.game.protos.MingwangProtos.ResMingWang.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MingwangProtos.ResMingWang build() {
        com.sh.game.protos.MingwangProtos.ResMingWang result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MingwangProtos.ResMingWang buildPartial() {
        com.sh.game.protos.MingwangProtos.ResMingWang result = new com.sh.game.protos.MingwangProtos.ResMingWang(this);
        result.mingWang_ = mingWang_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MingwangProtos.ResMingWang) {
          return mergeFrom((com.sh.game.protos.MingwangProtos.ResMingWang)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MingwangProtos.ResMingWang other) {
        if (other == com.sh.game.protos.MingwangProtos.ResMingWang.getDefaultInstance()) return this;
        if (other.getMingWang() != 0) {
          setMingWang(other.getMingWang());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MingwangProtos.ResMingWang parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MingwangProtos.ResMingWang) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int mingWang_ ;
      /**
       * <pre>
       *江湖名望,值未cfg_ih_prestige的id
       * </pre>
       *
       * <code>int32 mingWang = 1;</code>
       * @return The mingWang.
       */
      @java.lang.Override
      public int getMingWang() {
        return mingWang_;
      }
      /**
       * <pre>
       *江湖名望,值未cfg_ih_prestige的id
       * </pre>
       *
       * <code>int32 mingWang = 1;</code>
       * @param value The mingWang to set.
       * @return This builder for chaining.
       */
      public Builder setMingWang(int value) {
        
        mingWang_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *江湖名望,值未cfg_ih_prestige的id
       * </pre>
       *
       * <code>int32 mingWang = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMingWang() {
        
        mingWang_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:mingwang.ResMingWang)
    }

    // @@protoc_insertion_point(class_scope:mingwang.ResMingWang)
    private static final com.sh.game.protos.MingwangProtos.ResMingWang DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MingwangProtos.ResMingWang();
    }

    public static com.sh.game.protos.MingwangProtos.ResMingWang getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMingWang>
        PARSER = new com.google.protobuf.AbstractParser<ResMingWang>() {
      @java.lang.Override
      public ResMingWang parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMingWang(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMingWang> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResMingWang> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MingwangProtos.ResMingWang getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_mingwang_ReqUpLevelMingWang_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_mingwang_ReqUpLevelMingWang_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_mingwang_ResMingWang_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_mingwang_ResMingWang_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016mingwang.proto\022\010mingwang\"\024\n\022ReqUpLevel" +
      "MingWang\"\037\n\013ResMingWang\022\020\n\010mingWang\030\001 \001(" +
      "\005B$\n\022com.sh.game.protosB\016MingwangProtosb" +
      "\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_mingwang_ReqUpLevelMingWang_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_mingwang_ReqUpLevelMingWang_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_mingwang_ReqUpLevelMingWang_descriptor,
        new java.lang.String[] { });
    internal_static_mingwang_ResMingWang_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_mingwang_ResMingWang_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_mingwang_ResMingWang_descriptor,
        new java.lang.String[] { "MingWang", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
