// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: fate.proto

package com.sh.game.protos;

public final class FateProtos {
  private FateProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface FateBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.FateBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *天命类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     *天命等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    int getLevel();
  }
  /**
   * Protobuf type {@code fate.FateBean}
   */
  public static final class <PERSON><PERSON>ean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.FateBean)
      FateBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FateBean.newBuilder() to construct.
    private FateBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FateBean() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FateBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FateBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            case 16: {

              level_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_FateBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_FateBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.FateBean.class, com.sh.game.protos.FateProtos.FateBean.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *天命类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     *天命等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.FateBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.FateBean other = (com.sh.game.protos.FateProtos.FateBean) obj;

      if (getType()
          != other.getType()) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.FateBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.FateBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.FateBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code fate.FateBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.FateBean)
        com.sh.game.protos.FateProtos.FateBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_FateBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_FateBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.FateBean.class, com.sh.game.protos.FateProtos.FateBean.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.FateBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        level_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_FateBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.FateBean getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.FateBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.FateBean build() {
        com.sh.game.protos.FateProtos.FateBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.FateBean buildPartial() {
        com.sh.game.protos.FateProtos.FateBean result = new com.sh.game.protos.FateProtos.FateBean(this);
        result.type_ = type_;
        result.level_ = level_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.FateBean) {
          return mergeFrom((com.sh.game.protos.FateProtos.FateBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.FateBean other) {
        if (other == com.sh.game.protos.FateProtos.FateBean.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.FateBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.FateBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *天命类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *天命类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *天命类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       *天命等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       *天命等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *天命等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.FateBean)
    }

    // @@protoc_insertion_point(class_scope:fate.FateBean)
    private static final com.sh.game.protos.FateProtos.FateBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.FateBean();
    }

    public static com.sh.game.protos.FateProtos.FateBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<FateBean>
        PARSER = new com.google.protobuf.AbstractParser<FateBean>() {
      @java.lang.Override
      public FateBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FateBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FateBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FateBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.FateBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFateInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.ReqFateInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqFateInfo' id='1' desc='请求天命信息' 
   * </pre>
   *
   * Protobuf type {@code fate.ReqFateInfo}
   */
  public static final class ReqFateInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.ReqFateInfo)
      ReqFateInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFateInfo.newBuilder() to construct.
    private ReqFateInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFateInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFateInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFateInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.ReqFateInfo.class, com.sh.game.protos.FateProtos.ReqFateInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.ReqFateInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.ReqFateInfo other = (com.sh.game.protos.FateProtos.ReqFateInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.ReqFateInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFateInfo' id='1' desc='请求天命信息' 
     * </pre>
     *
     * Protobuf type {@code fate.ReqFateInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.ReqFateInfo)
        com.sh.game.protos.FateProtos.ReqFateInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.ReqFateInfo.class, com.sh.game.protos.FateProtos.ReqFateInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.ReqFateInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateInfo getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.ReqFateInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateInfo build() {
        com.sh.game.protos.FateProtos.ReqFateInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateInfo buildPartial() {
        com.sh.game.protos.FateProtos.ReqFateInfo result = new com.sh.game.protos.FateProtos.ReqFateInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.ReqFateInfo) {
          return mergeFrom((com.sh.game.protos.FateProtos.ReqFateInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.ReqFateInfo other) {
        if (other == com.sh.game.protos.FateProtos.ReqFateInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.ReqFateInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.ReqFateInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.ReqFateInfo)
    }

    // @@protoc_insertion_point(class_scope:fate.ReqFateInfo)
    private static final com.sh.game.protos.FateProtos.ReqFateInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.ReqFateInfo();
    }

    public static com.sh.game.protos.FateProtos.ReqFateInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFateInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqFateInfo>() {
      @java.lang.Override
      public ReqFateInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFateInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFateInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFateInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.ReqFateInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResFateInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.ResFateInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *当前命格总等级套装配置id
     * </pre>
     *
     * <code>int32 suitConfigId = 1;</code>
     * @return The suitConfigId.
     */
    int getSuitConfigId();

    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    java.util.List<com.sh.game.protos.FateProtos.FateBean> 
        getFateBeanListList();
    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    com.sh.game.protos.FateProtos.FateBean getFateBeanList(int index);
    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    int getFateBeanListCount();
    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    java.util.List<? extends com.sh.game.protos.FateProtos.FateBeanOrBuilder> 
        getFateBeanListOrBuilderList();
    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    com.sh.game.protos.FateProtos.FateBeanOrBuilder getFateBeanListOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResFateInfo' id='2' desc='返回天命信息' 
   * </pre>
   *
   * Protobuf type {@code fate.ResFateInfo}
   */
  public static final class ResFateInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.ResFateInfo)
      ResFateInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResFateInfo.newBuilder() to construct.
    private ResFateInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResFateInfo() {
      fateBeanList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResFateInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResFateInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              suitConfigId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                fateBeanList_ = new java.util.ArrayList<com.sh.game.protos.FateProtos.FateBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              fateBeanList_.add(
                  input.readMessage(com.sh.game.protos.FateProtos.FateBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          fateBeanList_ = java.util.Collections.unmodifiableList(fateBeanList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ResFateInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ResFateInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.ResFateInfo.class, com.sh.game.protos.FateProtos.ResFateInfo.Builder.class);
    }

    public static final int SUITCONFIGID_FIELD_NUMBER = 1;
    private int suitConfigId_;
    /**
     * <pre>
     *当前命格总等级套装配置id
     * </pre>
     *
     * <code>int32 suitConfigId = 1;</code>
     * @return The suitConfigId.
     */
    @java.lang.Override
    public int getSuitConfigId() {
      return suitConfigId_;
    }

    public static final int FATEBEANLIST_FIELD_NUMBER = 2;
    private java.util.List<com.sh.game.protos.FateProtos.FateBean> fateBeanList_;
    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.FateProtos.FateBean> getFateBeanListList() {
      return fateBeanList_;
    }
    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.FateProtos.FateBeanOrBuilder> 
        getFateBeanListOrBuilderList() {
      return fateBeanList_;
    }
    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    @java.lang.Override
    public int getFateBeanListCount() {
      return fateBeanList_.size();
    }
    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.FateProtos.FateBean getFateBeanList(int index) {
      return fateBeanList_.get(index);
    }
    /**
     * <pre>
     *天命信息
     * </pre>
     *
     * <code>repeated .fate.FateBean fateBeanList = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.FateProtos.FateBeanOrBuilder getFateBeanListOrBuilder(
        int index) {
      return fateBeanList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (suitConfigId_ != 0) {
        output.writeInt32(1, suitConfigId_);
      }
      for (int i = 0; i < fateBeanList_.size(); i++) {
        output.writeMessage(2, fateBeanList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (suitConfigId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, suitConfigId_);
      }
      for (int i = 0; i < fateBeanList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, fateBeanList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.ResFateInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.ResFateInfo other = (com.sh.game.protos.FateProtos.ResFateInfo) obj;

      if (getSuitConfigId()
          != other.getSuitConfigId()) return false;
      if (!getFateBeanListList()
          .equals(other.getFateBeanListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SUITCONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getSuitConfigId();
      if (getFateBeanListCount() > 0) {
        hash = (37 * hash) + FATEBEANLIST_FIELD_NUMBER;
        hash = (53 * hash) + getFateBeanListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ResFateInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.ResFateInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResFateInfo' id='2' desc='返回天命信息' 
     * </pre>
     *
     * Protobuf type {@code fate.ResFateInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.ResFateInfo)
        com.sh.game.protos.FateProtos.ResFateInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ResFateInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ResFateInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.ResFateInfo.class, com.sh.game.protos.FateProtos.ResFateInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.ResFateInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getFateBeanListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        suitConfigId_ = 0;

        if (fateBeanListBuilder_ == null) {
          fateBeanList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          fateBeanListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ResFateInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ResFateInfo getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.ResFateInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ResFateInfo build() {
        com.sh.game.protos.FateProtos.ResFateInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ResFateInfo buildPartial() {
        com.sh.game.protos.FateProtos.ResFateInfo result = new com.sh.game.protos.FateProtos.ResFateInfo(this);
        int from_bitField0_ = bitField0_;
        result.suitConfigId_ = suitConfigId_;
        if (fateBeanListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            fateBeanList_ = java.util.Collections.unmodifiableList(fateBeanList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.fateBeanList_ = fateBeanList_;
        } else {
          result.fateBeanList_ = fateBeanListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.ResFateInfo) {
          return mergeFrom((com.sh.game.protos.FateProtos.ResFateInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.ResFateInfo other) {
        if (other == com.sh.game.protos.FateProtos.ResFateInfo.getDefaultInstance()) return this;
        if (other.getSuitConfigId() != 0) {
          setSuitConfigId(other.getSuitConfigId());
        }
        if (fateBeanListBuilder_ == null) {
          if (!other.fateBeanList_.isEmpty()) {
            if (fateBeanList_.isEmpty()) {
              fateBeanList_ = other.fateBeanList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureFateBeanListIsMutable();
              fateBeanList_.addAll(other.fateBeanList_);
            }
            onChanged();
          }
        } else {
          if (!other.fateBeanList_.isEmpty()) {
            if (fateBeanListBuilder_.isEmpty()) {
              fateBeanListBuilder_.dispose();
              fateBeanListBuilder_ = null;
              fateBeanList_ = other.fateBeanList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              fateBeanListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getFateBeanListFieldBuilder() : null;
            } else {
              fateBeanListBuilder_.addAllMessages(other.fateBeanList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.ResFateInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.ResFateInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int suitConfigId_ ;
      /**
       * <pre>
       *当前命格总等级套装配置id
       * </pre>
       *
       * <code>int32 suitConfigId = 1;</code>
       * @return The suitConfigId.
       */
      @java.lang.Override
      public int getSuitConfigId() {
        return suitConfigId_;
      }
      /**
       * <pre>
       *当前命格总等级套装配置id
       * </pre>
       *
       * <code>int32 suitConfigId = 1;</code>
       * @param value The suitConfigId to set.
       * @return This builder for chaining.
       */
      public Builder setSuitConfigId(int value) {
        
        suitConfigId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前命格总等级套装配置id
       * </pre>
       *
       * <code>int32 suitConfigId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuitConfigId() {
        
        suitConfigId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.FateProtos.FateBean> fateBeanList_ =
        java.util.Collections.emptyList();
      private void ensureFateBeanListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          fateBeanList_ = new java.util.ArrayList<com.sh.game.protos.FateProtos.FateBean>(fateBeanList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.FateProtos.FateBean, com.sh.game.protos.FateProtos.FateBean.Builder, com.sh.game.protos.FateProtos.FateBeanOrBuilder> fateBeanListBuilder_;

      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public java.util.List<com.sh.game.protos.FateProtos.FateBean> getFateBeanListList() {
        if (fateBeanListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(fateBeanList_);
        } else {
          return fateBeanListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public int getFateBeanListCount() {
        if (fateBeanListBuilder_ == null) {
          return fateBeanList_.size();
        } else {
          return fateBeanListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public com.sh.game.protos.FateProtos.FateBean getFateBeanList(int index) {
        if (fateBeanListBuilder_ == null) {
          return fateBeanList_.get(index);
        } else {
          return fateBeanListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public Builder setFateBeanList(
          int index, com.sh.game.protos.FateProtos.FateBean value) {
        if (fateBeanListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFateBeanListIsMutable();
          fateBeanList_.set(index, value);
          onChanged();
        } else {
          fateBeanListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public Builder setFateBeanList(
          int index, com.sh.game.protos.FateProtos.FateBean.Builder builderForValue) {
        if (fateBeanListBuilder_ == null) {
          ensureFateBeanListIsMutable();
          fateBeanList_.set(index, builderForValue.build());
          onChanged();
        } else {
          fateBeanListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public Builder addFateBeanList(com.sh.game.protos.FateProtos.FateBean value) {
        if (fateBeanListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFateBeanListIsMutable();
          fateBeanList_.add(value);
          onChanged();
        } else {
          fateBeanListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public Builder addFateBeanList(
          int index, com.sh.game.protos.FateProtos.FateBean value) {
        if (fateBeanListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFateBeanListIsMutable();
          fateBeanList_.add(index, value);
          onChanged();
        } else {
          fateBeanListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public Builder addFateBeanList(
          com.sh.game.protos.FateProtos.FateBean.Builder builderForValue) {
        if (fateBeanListBuilder_ == null) {
          ensureFateBeanListIsMutable();
          fateBeanList_.add(builderForValue.build());
          onChanged();
        } else {
          fateBeanListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public Builder addFateBeanList(
          int index, com.sh.game.protos.FateProtos.FateBean.Builder builderForValue) {
        if (fateBeanListBuilder_ == null) {
          ensureFateBeanListIsMutable();
          fateBeanList_.add(index, builderForValue.build());
          onChanged();
        } else {
          fateBeanListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public Builder addAllFateBeanList(
          java.lang.Iterable<? extends com.sh.game.protos.FateProtos.FateBean> values) {
        if (fateBeanListBuilder_ == null) {
          ensureFateBeanListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, fateBeanList_);
          onChanged();
        } else {
          fateBeanListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public Builder clearFateBeanList() {
        if (fateBeanListBuilder_ == null) {
          fateBeanList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          fateBeanListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public Builder removeFateBeanList(int index) {
        if (fateBeanListBuilder_ == null) {
          ensureFateBeanListIsMutable();
          fateBeanList_.remove(index);
          onChanged();
        } else {
          fateBeanListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public com.sh.game.protos.FateProtos.FateBean.Builder getFateBeanListBuilder(
          int index) {
        return getFateBeanListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public com.sh.game.protos.FateProtos.FateBeanOrBuilder getFateBeanListOrBuilder(
          int index) {
        if (fateBeanListBuilder_ == null) {
          return fateBeanList_.get(index);  } else {
          return fateBeanListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public java.util.List<? extends com.sh.game.protos.FateProtos.FateBeanOrBuilder> 
           getFateBeanListOrBuilderList() {
        if (fateBeanListBuilder_ != null) {
          return fateBeanListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(fateBeanList_);
        }
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public com.sh.game.protos.FateProtos.FateBean.Builder addFateBeanListBuilder() {
        return getFateBeanListFieldBuilder().addBuilder(
            com.sh.game.protos.FateProtos.FateBean.getDefaultInstance());
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public com.sh.game.protos.FateProtos.FateBean.Builder addFateBeanListBuilder(
          int index) {
        return getFateBeanListFieldBuilder().addBuilder(
            index, com.sh.game.protos.FateProtos.FateBean.getDefaultInstance());
      }
      /**
       * <pre>
       *天命信息
       * </pre>
       *
       * <code>repeated .fate.FateBean fateBeanList = 2;</code>
       */
      public java.util.List<com.sh.game.protos.FateProtos.FateBean.Builder> 
           getFateBeanListBuilderList() {
        return getFateBeanListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.FateProtos.FateBean, com.sh.game.protos.FateProtos.FateBean.Builder, com.sh.game.protos.FateProtos.FateBeanOrBuilder> 
          getFateBeanListFieldBuilder() {
        if (fateBeanListBuilder_ == null) {
          fateBeanListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.FateProtos.FateBean, com.sh.game.protos.FateProtos.FateBean.Builder, com.sh.game.protos.FateProtos.FateBeanOrBuilder>(
                  fateBeanList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          fateBeanList_ = null;
        }
        return fateBeanListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.ResFateInfo)
    }

    // @@protoc_insertion_point(class_scope:fate.ResFateInfo)
    private static final com.sh.game.protos.FateProtos.ResFateInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.ResFateInfo();
    }

    public static com.sh.game.protos.FateProtos.ResFateInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResFateInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResFateInfo>() {
      @java.lang.Override
      public ResFateInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResFateInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResFateInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResFateInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.ResFateInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFateUpgradeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.ReqFateUpgrade)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *天命锁定类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ReqFateUpgrade' id='3' desc='请求天命升级' 
   * </pre>
   *
   * Protobuf type {@code fate.ReqFateUpgrade}
   */
  public static final class ReqFateUpgrade extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.ReqFateUpgrade)
      ReqFateUpgradeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFateUpgrade.newBuilder() to construct.
    private ReqFateUpgrade(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFateUpgrade() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFateUpgrade();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFateUpgrade(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateUpgrade_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateUpgrade_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.ReqFateUpgrade.class, com.sh.game.protos.FateProtos.ReqFateUpgrade.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     *天命锁定类型
     * </pre>
     *
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.ReqFateUpgrade)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.ReqFateUpgrade other = (com.sh.game.protos.FateProtos.ReqFateUpgrade) obj;

      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.ReqFateUpgrade prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFateUpgrade' id='3' desc='请求天命升级' 
     * </pre>
     *
     * Protobuf type {@code fate.ReqFateUpgrade}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.ReqFateUpgrade)
        com.sh.game.protos.FateProtos.ReqFateUpgradeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateUpgrade_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateUpgrade_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.ReqFateUpgrade.class, com.sh.game.protos.FateProtos.ReqFateUpgrade.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.ReqFateUpgrade.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateUpgrade_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateUpgrade getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.ReqFateUpgrade.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateUpgrade build() {
        com.sh.game.protos.FateProtos.ReqFateUpgrade result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateUpgrade buildPartial() {
        com.sh.game.protos.FateProtos.ReqFateUpgrade result = new com.sh.game.protos.FateProtos.ReqFateUpgrade(this);
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.ReqFateUpgrade) {
          return mergeFrom((com.sh.game.protos.FateProtos.ReqFateUpgrade)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.ReqFateUpgrade other) {
        if (other == com.sh.game.protos.FateProtos.ReqFateUpgrade.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.ReqFateUpgrade parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.ReqFateUpgrade) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       *天命锁定类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       *天命锁定类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *天命锁定类型
       * </pre>
       *
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.ReqFateUpgrade)
    }

    // @@protoc_insertion_point(class_scope:fate.ReqFateUpgrade)
    private static final com.sh.game.protos.FateProtos.ReqFateUpgrade DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.ReqFateUpgrade();
    }

    public static com.sh.game.protos.FateProtos.ReqFateUpgrade getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFateUpgrade>
        PARSER = new com.google.protobuf.AbstractParser<ReqFateUpgrade>() {
      @java.lang.Override
      public ReqFateUpgrade parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFateUpgrade(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFateUpgrade> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFateUpgrade> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.ReqFateUpgrade getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFateDrawInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.ReqFateDrawInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqFateDrawInfo' id='4' desc='请求每日运势信息' 
   * </pre>
   *
   * Protobuf type {@code fate.ReqFateDrawInfo}
   */
  public static final class ReqFateDrawInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.ReqFateDrawInfo)
      ReqFateDrawInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFateDrawInfo.newBuilder() to construct.
    private ReqFateDrawInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFateDrawInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFateDrawInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFateDrawInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.ReqFateDrawInfo.class, com.sh.game.protos.FateProtos.ReqFateDrawInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.ReqFateDrawInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.ReqFateDrawInfo other = (com.sh.game.protos.FateProtos.ReqFateDrawInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.ReqFateDrawInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFateDrawInfo' id='4' desc='请求每日运势信息' 
     * </pre>
     *
     * Protobuf type {@code fate.ReqFateDrawInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.ReqFateDrawInfo)
        com.sh.game.protos.FateProtos.ReqFateDrawInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.ReqFateDrawInfo.class, com.sh.game.protos.FateProtos.ReqFateDrawInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.ReqFateDrawInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawInfo getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.ReqFateDrawInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawInfo build() {
        com.sh.game.protos.FateProtos.ReqFateDrawInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawInfo buildPartial() {
        com.sh.game.protos.FateProtos.ReqFateDrawInfo result = new com.sh.game.protos.FateProtos.ReqFateDrawInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.ReqFateDrawInfo) {
          return mergeFrom((com.sh.game.protos.FateProtos.ReqFateDrawInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.ReqFateDrawInfo other) {
        if (other == com.sh.game.protos.FateProtos.ReqFateDrawInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.ReqFateDrawInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.ReqFateDrawInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.ReqFateDrawInfo)
    }

    // @@protoc_insertion_point(class_scope:fate.ReqFateDrawInfo)
    private static final com.sh.game.protos.FateProtos.ReqFateDrawInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.ReqFateDrawInfo();
    }

    public static com.sh.game.protos.FateProtos.ReqFateDrawInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFateDrawInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqFateDrawInfo>() {
      @java.lang.Override
      public ReqFateDrawInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFateDrawInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFateDrawInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFateDrawInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.ReqFateDrawInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResFateDrawInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.ResFateDrawInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *当前未领取的运势配置id
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     * @return The configId.
     */
    int getConfigId();
  }
  /**
   * <pre>
   ** class='ResFateDrawInfo' id='5' desc='返回每日运势信息' 
   * </pre>
   *
   * Protobuf type {@code fate.ResFateDrawInfo}
   */
  public static final class ResFateDrawInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.ResFateDrawInfo)
      ResFateDrawInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResFateDrawInfo.newBuilder() to construct.
    private ResFateDrawInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResFateDrawInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResFateDrawInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResFateDrawInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              configId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ResFateDrawInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ResFateDrawInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.ResFateDrawInfo.class, com.sh.game.protos.FateProtos.ResFateDrawInfo.Builder.class);
    }

    public static final int CONFIGID_FIELD_NUMBER = 1;
    private int configId_;
    /**
     * <pre>
     *当前未领取的运势配置id
     * </pre>
     *
     * <code>int32 configId = 1;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (configId_ != 0) {
        output.writeInt32(1, configId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, configId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.ResFateDrawInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.ResFateDrawInfo other = (com.sh.game.protos.FateProtos.ResFateDrawInfo) obj;

      if (getConfigId()
          != other.getConfigId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ResFateDrawInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.ResFateDrawInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResFateDrawInfo' id='5' desc='返回每日运势信息' 
     * </pre>
     *
     * Protobuf type {@code fate.ResFateDrawInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.ResFateDrawInfo)
        com.sh.game.protos.FateProtos.ResFateDrawInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ResFateDrawInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ResFateDrawInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.ResFateDrawInfo.class, com.sh.game.protos.FateProtos.ResFateDrawInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.ResFateDrawInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        configId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ResFateDrawInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ResFateDrawInfo getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.ResFateDrawInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ResFateDrawInfo build() {
        com.sh.game.protos.FateProtos.ResFateDrawInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ResFateDrawInfo buildPartial() {
        com.sh.game.protos.FateProtos.ResFateDrawInfo result = new com.sh.game.protos.FateProtos.ResFateDrawInfo(this);
        result.configId_ = configId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.ResFateDrawInfo) {
          return mergeFrom((com.sh.game.protos.FateProtos.ResFateDrawInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.ResFateDrawInfo other) {
        if (other == com.sh.game.protos.FateProtos.ResFateDrawInfo.getDefaultInstance()) return this;
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.ResFateDrawInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.ResFateDrawInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int configId_ ;
      /**
       * <pre>
       *当前未领取的运势配置id
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <pre>
       *当前未领取的运势配置id
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {
        
        configId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前未领取的运势配置id
       * </pre>
       *
       * <code>int32 configId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        
        configId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.ResFateDrawInfo)
    }

    // @@protoc_insertion_point(class_scope:fate.ResFateDrawInfo)
    private static final com.sh.game.protos.FateProtos.ResFateDrawInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.ResFateDrawInfo();
    }

    public static com.sh.game.protos.FateProtos.ResFateDrawInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResFateDrawInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResFateDrawInfo>() {
      @java.lang.Override
      public ResFateDrawInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResFateDrawInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResFateDrawInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResFateDrawInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.ResFateDrawInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFateDrawOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.ReqFateDraw)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqFateDraw' id='6' desc='请求运势抽签' 
   * </pre>
   *
   * Protobuf type {@code fate.ReqFateDraw}
   */
  public static final class ReqFateDraw extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.ReqFateDraw)
      ReqFateDrawOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFateDraw.newBuilder() to construct.
    private ReqFateDraw(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFateDraw() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFateDraw();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFateDraw(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDraw_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDraw_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.ReqFateDraw.class, com.sh.game.protos.FateProtos.ReqFateDraw.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.ReqFateDraw)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.ReqFateDraw other = (com.sh.game.protos.FateProtos.ReqFateDraw) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDraw parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.ReqFateDraw prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFateDraw' id='6' desc='请求运势抽签' 
     * </pre>
     *
     * Protobuf type {@code fate.ReqFateDraw}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.ReqFateDraw)
        com.sh.game.protos.FateProtos.ReqFateDrawOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDraw_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDraw_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.ReqFateDraw.class, com.sh.game.protos.FateProtos.ReqFateDraw.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.ReqFateDraw.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDraw_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDraw getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.ReqFateDraw.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDraw build() {
        com.sh.game.protos.FateProtos.ReqFateDraw result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDraw buildPartial() {
        com.sh.game.protos.FateProtos.ReqFateDraw result = new com.sh.game.protos.FateProtos.ReqFateDraw(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.ReqFateDraw) {
          return mergeFrom((com.sh.game.protos.FateProtos.ReqFateDraw)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.ReqFateDraw other) {
        if (other == com.sh.game.protos.FateProtos.ReqFateDraw.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.ReqFateDraw parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.ReqFateDraw) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.ReqFateDraw)
    }

    // @@protoc_insertion_point(class_scope:fate.ReqFateDraw)
    private static final com.sh.game.protos.FateProtos.ReqFateDraw DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.ReqFateDraw();
    }

    public static com.sh.game.protos.FateProtos.ReqFateDraw getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFateDraw>
        PARSER = new com.google.protobuf.AbstractParser<ReqFateDraw>() {
      @java.lang.Override
      public ReqFateDraw parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFateDraw(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFateDraw> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFateDraw> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.ReqFateDraw getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFateDrawLuckyOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.ReqFateDrawLucky)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqFateDrawLucky' id='7' desc='请求运势转运' 
   * </pre>
   *
   * Protobuf type {@code fate.ReqFateDrawLucky}
   */
  public static final class ReqFateDrawLucky extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.ReqFateDrawLucky)
      ReqFateDrawLuckyOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFateDrawLucky.newBuilder() to construct.
    private ReqFateDrawLucky(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFateDrawLucky() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFateDrawLucky();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFateDrawLucky(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawLucky_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawLucky_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.ReqFateDrawLucky.class, com.sh.game.protos.FateProtos.ReqFateDrawLucky.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.ReqFateDrawLucky)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.ReqFateDrawLucky other = (com.sh.game.protos.FateProtos.ReqFateDrawLucky) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.ReqFateDrawLucky prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFateDrawLucky' id='7' desc='请求运势转运' 
     * </pre>
     *
     * Protobuf type {@code fate.ReqFateDrawLucky}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.ReqFateDrawLucky)
        com.sh.game.protos.FateProtos.ReqFateDrawLuckyOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawLucky_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawLucky_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.ReqFateDrawLucky.class, com.sh.game.protos.FateProtos.ReqFateDrawLucky.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.ReqFateDrawLucky.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawLucky_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawLucky getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.ReqFateDrawLucky.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawLucky build() {
        com.sh.game.protos.FateProtos.ReqFateDrawLucky result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawLucky buildPartial() {
        com.sh.game.protos.FateProtos.ReqFateDrawLucky result = new com.sh.game.protos.FateProtos.ReqFateDrawLucky(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.ReqFateDrawLucky) {
          return mergeFrom((com.sh.game.protos.FateProtos.ReqFateDrawLucky)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.ReqFateDrawLucky other) {
        if (other == com.sh.game.protos.FateProtos.ReqFateDrawLucky.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.ReqFateDrawLucky parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.ReqFateDrawLucky) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.ReqFateDrawLucky)
    }

    // @@protoc_insertion_point(class_scope:fate.ReqFateDrawLucky)
    private static final com.sh.game.protos.FateProtos.ReqFateDrawLucky DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.ReqFateDrawLucky();
    }

    public static com.sh.game.protos.FateProtos.ReqFateDrawLucky getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFateDrawLucky>
        PARSER = new com.google.protobuf.AbstractParser<ReqFateDrawLucky>() {
      @java.lang.Override
      public ReqFateDrawLucky parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFateDrawLucky(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFateDrawLucky> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFateDrawLucky> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.ReqFateDrawLucky getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFateDrawRewardOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.ReqFateDrawReward)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqFateDrawReward' id='8' desc='请求领取运势奖励' 
   * </pre>
   *
   * Protobuf type {@code fate.ReqFateDrawReward}
   */
  public static final class ReqFateDrawReward extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.ReqFateDrawReward)
      ReqFateDrawRewardOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFateDrawReward.newBuilder() to construct.
    private ReqFateDrawReward(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFateDrawReward() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFateDrawReward();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFateDrawReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawReward_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.ReqFateDrawReward.class, com.sh.game.protos.FateProtos.ReqFateDrawReward.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.ReqFateDrawReward)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.ReqFateDrawReward other = (com.sh.game.protos.FateProtos.ReqFateDrawReward) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.ReqFateDrawReward prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFateDrawReward' id='8' desc='请求领取运势奖励' 
     * </pre>
     *
     * Protobuf type {@code fate.ReqFateDrawReward}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.ReqFateDrawReward)
        com.sh.game.protos.FateProtos.ReqFateDrawRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawReward_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.ReqFateDrawReward.class, com.sh.game.protos.FateProtos.ReqFateDrawReward.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.ReqFateDrawReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawReward_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawReward getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.ReqFateDrawReward.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawReward build() {
        com.sh.game.protos.FateProtos.ReqFateDrawReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawReward buildPartial() {
        com.sh.game.protos.FateProtos.ReqFateDrawReward result = new com.sh.game.protos.FateProtos.ReqFateDrawReward(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.ReqFateDrawReward) {
          return mergeFrom((com.sh.game.protos.FateProtos.ReqFateDrawReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.ReqFateDrawReward other) {
        if (other == com.sh.game.protos.FateProtos.ReqFateDrawReward.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.ReqFateDrawReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.ReqFateDrawReward) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.ReqFateDrawReward)
    }

    // @@protoc_insertion_point(class_scope:fate.ReqFateDrawReward)
    private static final com.sh.game.protos.FateProtos.ReqFateDrawReward DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.ReqFateDrawReward();
    }

    public static com.sh.game.protos.FateProtos.ReqFateDrawReward getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFateDrawReward>
        PARSER = new com.google.protobuf.AbstractParser<ReqFateDrawReward>() {
      @java.lang.Override
      public ReqFateDrawReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFateDrawReward(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFateDrawReward> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFateDrawReward> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.ReqFateDrawReward getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFateDrawBuyOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fate.ReqFateDrawBuy)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqFateDrawBuy' id='9' desc='请求购买运势抽签次数' 
   * </pre>
   *
   * Protobuf type {@code fate.ReqFateDrawBuy}
   */
  public static final class ReqFateDrawBuy extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fate.ReqFateDrawBuy)
      ReqFateDrawBuyOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFateDrawBuy.newBuilder() to construct.
    private ReqFateDrawBuy(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFateDrawBuy() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFateDrawBuy();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFateDrawBuy(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawBuy_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawBuy_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FateProtos.ReqFateDrawBuy.class, com.sh.game.protos.FateProtos.ReqFateDrawBuy.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FateProtos.ReqFateDrawBuy)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FateProtos.ReqFateDrawBuy other = (com.sh.game.protos.FateProtos.ReqFateDrawBuy) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FateProtos.ReqFateDrawBuy prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFateDrawBuy' id='9' desc='请求购买运势抽签次数' 
     * </pre>
     *
     * Protobuf type {@code fate.ReqFateDrawBuy}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fate.ReqFateDrawBuy)
        com.sh.game.protos.FateProtos.ReqFateDrawBuyOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawBuy_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawBuy_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FateProtos.ReqFateDrawBuy.class, com.sh.game.protos.FateProtos.ReqFateDrawBuy.Builder.class);
      }

      // Construct using com.sh.game.protos.FateProtos.ReqFateDrawBuy.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FateProtos.internal_static_fate_ReqFateDrawBuy_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawBuy getDefaultInstanceForType() {
        return com.sh.game.protos.FateProtos.ReqFateDrawBuy.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawBuy build() {
        com.sh.game.protos.FateProtos.ReqFateDrawBuy result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FateProtos.ReqFateDrawBuy buildPartial() {
        com.sh.game.protos.FateProtos.ReqFateDrawBuy result = new com.sh.game.protos.FateProtos.ReqFateDrawBuy(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FateProtos.ReqFateDrawBuy) {
          return mergeFrom((com.sh.game.protos.FateProtos.ReqFateDrawBuy)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FateProtos.ReqFateDrawBuy other) {
        if (other == com.sh.game.protos.FateProtos.ReqFateDrawBuy.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FateProtos.ReqFateDrawBuy parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FateProtos.ReqFateDrawBuy) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fate.ReqFateDrawBuy)
    }

    // @@protoc_insertion_point(class_scope:fate.ReqFateDrawBuy)
    private static final com.sh.game.protos.FateProtos.ReqFateDrawBuy DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FateProtos.ReqFateDrawBuy();
    }

    public static com.sh.game.protos.FateProtos.ReqFateDrawBuy getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFateDrawBuy>
        PARSER = new com.google.protobuf.AbstractParser<ReqFateDrawBuy>() {
      @java.lang.Override
      public ReqFateDrawBuy parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFateDrawBuy(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFateDrawBuy> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFateDrawBuy> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FateProtos.ReqFateDrawBuy getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_FateBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_FateBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_ReqFateInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_ReqFateInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_ResFateInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_ResFateInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_ReqFateUpgrade_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_ReqFateUpgrade_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_ReqFateDrawInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_ReqFateDrawInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_ResFateDrawInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_ResFateDrawInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_ReqFateDraw_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_ReqFateDraw_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_ReqFateDrawLucky_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_ReqFateDrawLucky_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_ReqFateDrawReward_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_ReqFateDrawReward_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fate_ReqFateDrawBuy_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fate_ReqFateDrawBuy_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nfate.proto\022\004fate\"\'\n\010FateBean\022\014\n\004type\030\001" +
      " \001(\005\022\r\n\005level\030\002 \001(\005\"\r\n\013ReqFateInfo\"I\n\013Re" +
      "sFateInfo\022\024\n\014suitConfigId\030\001 \001(\005\022$\n\014fateB" +
      "eanList\030\002 \003(\0132\016.fate.FateBean\"\036\n\016ReqFate" +
      "Upgrade\022\014\n\004type\030\001 \001(\005\"\021\n\017ReqFateDrawInfo" +
      "\"#\n\017ResFateDrawInfo\022\020\n\010configId\030\001 \001(\005\"\r\n" +
      "\013ReqFateDraw\"\022\n\020ReqFateDrawLucky\"\023\n\021ReqF" +
      "ateDrawReward\"\020\n\016ReqFateDrawBuyB \n\022com.s" +
      "h.game.protosB\nFateProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_fate_FateBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_fate_FateBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_FateBean_descriptor,
        new java.lang.String[] { "Type", "Level", });
    internal_static_fate_ReqFateInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_fate_ReqFateInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_ReqFateInfo_descriptor,
        new java.lang.String[] { });
    internal_static_fate_ResFateInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_fate_ResFateInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_ResFateInfo_descriptor,
        new java.lang.String[] { "SuitConfigId", "FateBeanList", });
    internal_static_fate_ReqFateUpgrade_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_fate_ReqFateUpgrade_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_ReqFateUpgrade_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_fate_ReqFateDrawInfo_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_fate_ReqFateDrawInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_ReqFateDrawInfo_descriptor,
        new java.lang.String[] { });
    internal_static_fate_ResFateDrawInfo_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_fate_ResFateDrawInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_ResFateDrawInfo_descriptor,
        new java.lang.String[] { "ConfigId", });
    internal_static_fate_ReqFateDraw_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_fate_ReqFateDraw_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_ReqFateDraw_descriptor,
        new java.lang.String[] { });
    internal_static_fate_ReqFateDrawLucky_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_fate_ReqFateDrawLucky_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_ReqFateDrawLucky_descriptor,
        new java.lang.String[] { });
    internal_static_fate_ReqFateDrawReward_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_fate_ReqFateDrawReward_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_ReqFateDrawReward_descriptor,
        new java.lang.String[] { });
    internal_static_fate_ReqFateDrawBuy_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_fate_ReqFateDrawBuy_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fate_ReqFateDrawBuy_descriptor,
        new java.lang.String[] { });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
