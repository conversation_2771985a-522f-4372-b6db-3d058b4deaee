// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: transformSuperMan.proto

package com.sh.game.protos;

public final class TransformSuperManProtos {
  private TransformSuperManProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqRoleToSuperManInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:transformSuperMan.ReqRoleToSuperManInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqRoleToSuperManInfo' id='1' desc='请求狂暴之力信息' 
   * </pre>
   *
   * Protobuf type {@code transformSuperMan.ReqRoleToSuperManInfo}
   */
  public static final class ReqRoleToSuperManInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:transformSuperMan.ReqRoleToSuperManInfo)
      ReqRoleToSuperManInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqRoleToSuperManInfo.newBuilder() to construct.
    private ReqRoleToSuperManInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqRoleToSuperManInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqRoleToSuperManInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqRoleToSuperManInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqRoleToSuperManInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqRoleToSuperManInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo.class, com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo other = (com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqRoleToSuperManInfo' id='1' desc='请求狂暴之力信息' 
     * </pre>
     *
     * Protobuf type {@code transformSuperMan.ReqRoleToSuperManInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:transformSuperMan.ReqRoleToSuperManInfo)
        com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqRoleToSuperManInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqRoleToSuperManInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo.class, com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqRoleToSuperManInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo getDefaultInstanceForType() {
        return com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo build() {
        com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo buildPartial() {
        com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo result = new com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo) {
          return mergeFrom((com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo other) {
        if (other == com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:transformSuperMan.ReqRoleToSuperManInfo)
    }

    // @@protoc_insertion_point(class_scope:transformSuperMan.ReqRoleToSuperManInfo)
    private static final com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo();
    }

    public static com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqRoleToSuperManInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqRoleToSuperManInfo>() {
      @java.lang.Override
      public ReqRoleToSuperManInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqRoleToSuperManInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqRoleToSuperManInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqRoleToSuperManInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.TransformSuperManProtos.ReqRoleToSuperManInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqBuyRoleToSuperManOrBuilder extends
      // @@protoc_insertion_point(interface_extends:transformSuperMan.ReqBuyRoleToSuperMan)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqBuyRoleToSuperMan' id='2' desc='请求购买狂暴之力' 
   * </pre>
   *
   * Protobuf type {@code transformSuperMan.ReqBuyRoleToSuperMan}
   */
  public static final class ReqBuyRoleToSuperMan extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:transformSuperMan.ReqBuyRoleToSuperMan)
      ReqBuyRoleToSuperManOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqBuyRoleToSuperMan.newBuilder() to construct.
    private ReqBuyRoleToSuperMan(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqBuyRoleToSuperMan() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqBuyRoleToSuperMan();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqBuyRoleToSuperMan(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqBuyRoleToSuperMan_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqBuyRoleToSuperMan_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan.class, com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan)) {
        return super.equals(obj);
      }
      com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan other = (com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqBuyRoleToSuperMan' id='2' desc='请求购买狂暴之力' 
     * </pre>
     *
     * Protobuf type {@code transformSuperMan.ReqBuyRoleToSuperMan}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:transformSuperMan.ReqBuyRoleToSuperMan)
        com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperManOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqBuyRoleToSuperMan_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqBuyRoleToSuperMan_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan.class, com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan.Builder.class);
      }

      // Construct using com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqBuyRoleToSuperMan_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan getDefaultInstanceForType() {
        return com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan build() {
        com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan buildPartial() {
        com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan result = new com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan) {
          return mergeFrom((com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan other) {
        if (other == com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:transformSuperMan.ReqBuyRoleToSuperMan)
    }

    // @@protoc_insertion_point(class_scope:transformSuperMan.ReqBuyRoleToSuperMan)
    private static final com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan();
    }

    public static com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqBuyRoleToSuperMan>
        PARSER = new com.google.protobuf.AbstractParser<ReqBuyRoleToSuperMan>() {
      @java.lang.Override
      public ReqBuyRoleToSuperMan parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqBuyRoleToSuperMan(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqBuyRoleToSuperMan> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqBuyRoleToSuperMan> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.TransformSuperManProtos.ReqBuyRoleToSuperMan getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqSuperManProtectOrBuilder extends
      // @@protoc_insertion_point(interface_extends:transformSuperMan.ReqSuperManProtect)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *是否开启狂暴保护，0: 关 1: 回城 2：随机
     * </pre>
     *
     * <code>int32 isOpen = 1;</code>
     * @return The isOpen.
     */
    int getIsOpen();
  }
  /**
   * <pre>
   ** class='ReqSuperManProtect' id='3' desc='请求狂暴保护状态变更' 
   * </pre>
   *
   * Protobuf type {@code transformSuperMan.ReqSuperManProtect}
   */
  public static final class ReqSuperManProtect extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:transformSuperMan.ReqSuperManProtect)
      ReqSuperManProtectOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqSuperManProtect.newBuilder() to construct.
    private ReqSuperManProtect(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqSuperManProtect() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqSuperManProtect();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqSuperManProtect(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              isOpen_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqSuperManProtect_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqSuperManProtect_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.class, com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.Builder.class);
    }

    public static final int ISOPEN_FIELD_NUMBER = 1;
    private int isOpen_;
    /**
     * <pre>
     *是否开启狂暴保护，0: 关 1: 回城 2：随机
     * </pre>
     *
     * <code>int32 isOpen = 1;</code>
     * @return The isOpen.
     */
    @java.lang.Override
    public int getIsOpen() {
      return isOpen_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (isOpen_ != 0) {
        output.writeInt32(1, isOpen_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (isOpen_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, isOpen_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect)) {
        return super.equals(obj);
      }
      com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect other = (com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect) obj;

      if (getIsOpen()
          != other.getIsOpen()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ISOPEN_FIELD_NUMBER;
      hash = (53 * hash) + getIsOpen();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqSuperManProtect' id='3' desc='请求狂暴保护状态变更' 
     * </pre>
     *
     * Protobuf type {@code transformSuperMan.ReqSuperManProtect}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:transformSuperMan.ReqSuperManProtect)
        com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtectOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqSuperManProtect_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqSuperManProtect_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.class, com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.Builder.class);
      }

      // Construct using com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isOpen_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ReqSuperManProtect_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect getDefaultInstanceForType() {
        return com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect build() {
        com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect buildPartial() {
        com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect result = new com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect(this);
        result.isOpen_ = isOpen_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect) {
          return mergeFrom((com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect other) {
        if (other == com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect.getDefaultInstance()) return this;
        if (other.getIsOpen() != 0) {
          setIsOpen(other.getIsOpen());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int isOpen_ ;
      /**
       * <pre>
       *是否开启狂暴保护，0: 关 1: 回城 2：随机
       * </pre>
       *
       * <code>int32 isOpen = 1;</code>
       * @return The isOpen.
       */
      @java.lang.Override
      public int getIsOpen() {
        return isOpen_;
      }
      /**
       * <pre>
       *是否开启狂暴保护，0: 关 1: 回城 2：随机
       * </pre>
       *
       * <code>int32 isOpen = 1;</code>
       * @param value The isOpen to set.
       * @return This builder for chaining.
       */
      public Builder setIsOpen(int value) {
        
        isOpen_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否开启狂暴保护，0: 关 1: 回城 2：随机
       * </pre>
       *
       * <code>int32 isOpen = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsOpen() {
        
        isOpen_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:transformSuperMan.ReqSuperManProtect)
    }

    // @@protoc_insertion_point(class_scope:transformSuperMan.ReqSuperManProtect)
    private static final com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect();
    }

    public static com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqSuperManProtect>
        PARSER = new com.google.protobuf.AbstractParser<ReqSuperManProtect>() {
      @java.lang.Override
      public ReqSuperManProtect parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqSuperManProtect(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqSuperManProtect> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqSuperManProtect> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.TransformSuperManProtos.ReqSuperManProtect getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResRoleToSuperManInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:transformSuperMan.ResRoleToSuperManInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *是否开启狂暴之力
     * </pre>
     *
     * <code>bool isSuccess = 1;</code>
     * @return The isSuccess.
     */
    boolean getIsSuccess();

    /**
     * <pre>
     *是否开启狂暴保护，0: 关 1: 回城 2：随机
     * </pre>
     *
     * <code>int32 protect = 2;</code>
     * @return The protect.
     */
    int getProtect();
  }
  /**
   * <pre>
   ** class='ResRoleToSuperManInfo' id='11' desc='返回是否拥有狂暴之力' 
   * </pre>
   *
   * Protobuf type {@code transformSuperMan.ResRoleToSuperManInfo}
   */
  public static final class ResRoleToSuperManInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:transformSuperMan.ResRoleToSuperManInfo)
      ResRoleToSuperManInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResRoleToSuperManInfo.newBuilder() to construct.
    private ResRoleToSuperManInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResRoleToSuperManInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResRoleToSuperManInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResRoleToSuperManInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              isSuccess_ = input.readBool();
              break;
            }
            case 16: {

              protect_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ResRoleToSuperManInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ResRoleToSuperManInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo.class, com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo.Builder.class);
    }

    public static final int ISSUCCESS_FIELD_NUMBER = 1;
    private boolean isSuccess_;
    /**
     * <pre>
     *是否开启狂暴之力
     * </pre>
     *
     * <code>bool isSuccess = 1;</code>
     * @return The isSuccess.
     */
    @java.lang.Override
    public boolean getIsSuccess() {
      return isSuccess_;
    }

    public static final int PROTECT_FIELD_NUMBER = 2;
    private int protect_;
    /**
     * <pre>
     *是否开启狂暴保护，0: 关 1: 回城 2：随机
     * </pre>
     *
     * <code>int32 protect = 2;</code>
     * @return The protect.
     */
    @java.lang.Override
    public int getProtect() {
      return protect_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (isSuccess_ != false) {
        output.writeBool(1, isSuccess_);
      }
      if (protect_ != 0) {
        output.writeInt32(2, protect_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (isSuccess_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isSuccess_);
      }
      if (protect_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, protect_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo other = (com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo) obj;

      if (getIsSuccess()
          != other.getIsSuccess()) return false;
      if (getProtect()
          != other.getProtect()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ISSUCCESS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsSuccess());
      hash = (37 * hash) + PROTECT_FIELD_NUMBER;
      hash = (53 * hash) + getProtect();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResRoleToSuperManInfo' id='11' desc='返回是否拥有狂暴之力' 
     * </pre>
     *
     * Protobuf type {@code transformSuperMan.ResRoleToSuperManInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:transformSuperMan.ResRoleToSuperManInfo)
        com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ResRoleToSuperManInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ResRoleToSuperManInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo.class, com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isSuccess_ = false;

        protect_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.TransformSuperManProtos.internal_static_transformSuperMan_ResRoleToSuperManInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo getDefaultInstanceForType() {
        return com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo build() {
        com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo buildPartial() {
        com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo result = new com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo(this);
        result.isSuccess_ = isSuccess_;
        result.protect_ = protect_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo) {
          return mergeFrom((com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo other) {
        if (other == com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo.getDefaultInstance()) return this;
        if (other.getIsSuccess() != false) {
          setIsSuccess(other.getIsSuccess());
        }
        if (other.getProtect() != 0) {
          setProtect(other.getProtect());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private boolean isSuccess_ ;
      /**
       * <pre>
       *是否开启狂暴之力
       * </pre>
       *
       * <code>bool isSuccess = 1;</code>
       * @return The isSuccess.
       */
      @java.lang.Override
      public boolean getIsSuccess() {
        return isSuccess_;
      }
      /**
       * <pre>
       *是否开启狂暴之力
       * </pre>
       *
       * <code>bool isSuccess = 1;</code>
       * @param value The isSuccess to set.
       * @return This builder for chaining.
       */
      public Builder setIsSuccess(boolean value) {
        
        isSuccess_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否开启狂暴之力
       * </pre>
       *
       * <code>bool isSuccess = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsSuccess() {
        
        isSuccess_ = false;
        onChanged();
        return this;
      }

      private int protect_ ;
      /**
       * <pre>
       *是否开启狂暴保护，0: 关 1: 回城 2：随机
       * </pre>
       *
       * <code>int32 protect = 2;</code>
       * @return The protect.
       */
      @java.lang.Override
      public int getProtect() {
        return protect_;
      }
      /**
       * <pre>
       *是否开启狂暴保护，0: 关 1: 回城 2：随机
       * </pre>
       *
       * <code>int32 protect = 2;</code>
       * @param value The protect to set.
       * @return This builder for chaining.
       */
      public Builder setProtect(int value) {
        
        protect_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *是否开启狂暴保护，0: 关 1: 回城 2：随机
       * </pre>
       *
       * <code>int32 protect = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearProtect() {
        
        protect_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:transformSuperMan.ResRoleToSuperManInfo)
    }

    // @@protoc_insertion_point(class_scope:transformSuperMan.ResRoleToSuperManInfo)
    private static final com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo();
    }

    public static com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResRoleToSuperManInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResRoleToSuperManInfo>() {
      @java.lang.Override
      public ResRoleToSuperManInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResRoleToSuperManInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResRoleToSuperManInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResRoleToSuperManInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.TransformSuperManProtos.ResRoleToSuperManInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_transformSuperMan_ReqRoleToSuperManInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_transformSuperMan_ReqRoleToSuperManInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_transformSuperMan_ReqBuyRoleToSuperMan_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_transformSuperMan_ReqBuyRoleToSuperMan_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_transformSuperMan_ReqSuperManProtect_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_transformSuperMan_ReqSuperManProtect_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_transformSuperMan_ResRoleToSuperManInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_transformSuperMan_ResRoleToSuperManInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027transformSuperMan.proto\022\021transformSupe" +
      "rMan\"\027\n\025ReqRoleToSuperManInfo\"\026\n\024ReqBuyR" +
      "oleToSuperMan\"$\n\022ReqSuperManProtect\022\016\n\006i" +
      "sOpen\030\001 \001(\005\";\n\025ResRoleToSuperManInfo\022\021\n\t" +
      "isSuccess\030\001 \001(\010\022\017\n\007protect\030\002 \001(\005B-\n\022com." +
      "sh.game.protosB\027TransformSuperManProtosb" +
      "\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_transformSuperMan_ReqRoleToSuperManInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_transformSuperMan_ReqRoleToSuperManInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_transformSuperMan_ReqRoleToSuperManInfo_descriptor,
        new java.lang.String[] { });
    internal_static_transformSuperMan_ReqBuyRoleToSuperMan_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_transformSuperMan_ReqBuyRoleToSuperMan_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_transformSuperMan_ReqBuyRoleToSuperMan_descriptor,
        new java.lang.String[] { });
    internal_static_transformSuperMan_ReqSuperManProtect_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_transformSuperMan_ReqSuperManProtect_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_transformSuperMan_ReqSuperManProtect_descriptor,
        new java.lang.String[] { "IsOpen", });
    internal_static_transformSuperMan_ResRoleToSuperManInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_transformSuperMan_ResRoleToSuperManInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_transformSuperMan_ResRoleToSuperManInfo_descriptor,
        new java.lang.String[] { "IsSuccess", "Protect", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
