// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: chongWuEquip.proto

package com.sh.game.protos;

public final class ChongWuEquipProtos {
  private ChongWuEquipProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqChongWuEquipQiangHuaOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chongWuEquip.ReqChongWuEquipQiangHua)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 pos = 1;</code>
     * @return The pos.
     */
    int getPos();
  }
  /**
   * <pre>
   ** class='ReqChongWuEquipQiangHua' id='1' desc='请求宠物装备强化' 
   * </pre>
   *
   * Protobuf type {@code chongWuEquip.ReqChongWuEquipQiangHua}
   */
  public static final class ReqChongWuEquipQiangHua extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chongWuEquip.ReqChongWuEquipQiangHua)
      ReqChongWuEquipQiangHuaOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqChongWuEquipQiangHua.newBuilder() to construct.
    private ReqChongWuEquipQiangHua(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqChongWuEquipQiangHua() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqChongWuEquipQiangHua();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqChongWuEquipQiangHua(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              pos_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipQiangHua_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipQiangHua_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua.Builder.class);
    }

    public static final int POS_FIELD_NUMBER = 1;
    private int pos_;
    /**
     * <pre>
     *部位
     * </pre>
     *
     * <code>int32 pos = 1;</code>
     * @return The pos.
     */
    @java.lang.Override
    public int getPos() {
      return pos_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (pos_ != 0) {
        output.writeInt32(1, pos_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (pos_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, pos_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua other = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua) obj;

      if (getPos()
          != other.getPos()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + POS_FIELD_NUMBER;
      hash = (53 * hash) + getPos();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqChongWuEquipQiangHua' id='1' desc='请求宠物装备强化' 
     * </pre>
     *
     * Protobuf type {@code chongWuEquip.ReqChongWuEquipQiangHua}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chongWuEquip.ReqChongWuEquipQiangHua)
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHuaOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipQiangHua_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipQiangHua_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua.Builder.class);
      }

      // Construct using com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        pos_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipQiangHua_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua getDefaultInstanceForType() {
        return com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua build() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua buildPartial() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua result = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua(this);
        result.pos_ = pos_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua) {
          return mergeFrom((com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua other) {
        if (other == com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua.getDefaultInstance()) return this;
        if (other.getPos() != 0) {
          setPos(other.getPos());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int pos_ ;
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @return The pos.
       */
      @java.lang.Override
      public int getPos() {
        return pos_;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @param value The pos to set.
       * @return This builder for chaining.
       */
      public Builder setPos(int value) {
        
        pos_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *部位
       * </pre>
       *
       * <code>int32 pos = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPos() {
        
        pos_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chongWuEquip.ReqChongWuEquipQiangHua)
    }

    // @@protoc_insertion_point(class_scope:chongWuEquip.ReqChongWuEquipQiangHua)
    private static final com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua();
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqChongWuEquipQiangHua>
        PARSER = new com.google.protobuf.AbstractParser<ReqChongWuEquipQiangHua>() {
      @java.lang.Override
      public ReqChongWuEquipQiangHua parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqChongWuEquipQiangHua(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqChongWuEquipQiangHua> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqChongWuEquipQiangHua> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipQiangHua getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResChongWuUpSeccussOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chongWuEquip.ResChongWuUpSeccuss)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *结果
     * </pre>
     *
     * <code>bool success = 1;</code>
     * @return The success.
     */
    boolean getSuccess();

    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    java.util.List<com.sh.game.protos.AbcProtos.RolePetQiangHuaBean> 
        getPosList();
    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    com.sh.game.protos.AbcProtos.RolePetQiangHuaBean getPos(int index);
    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    int getPosCount();
    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    java.util.List<? extends com.sh.game.protos.AbcProtos.RolePetQiangHuaBeanOrBuilder> 
        getPosOrBuilderList();
    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    com.sh.game.protos.AbcProtos.RolePetQiangHuaBeanOrBuilder getPosOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResChongWuUpSeccuss' id='2' desc='返回强化信息' 
   * </pre>
   *
   * Protobuf type {@code chongWuEquip.ResChongWuUpSeccuss}
   */
  public static final class ResChongWuUpSeccuss extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chongWuEquip.ResChongWuUpSeccuss)
      ResChongWuUpSeccussOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResChongWuUpSeccuss.newBuilder() to construct.
    private ResChongWuUpSeccuss(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResChongWuUpSeccuss() {
      pos_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResChongWuUpSeccuss();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResChongWuUpSeccuss(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              success_ = input.readBool();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                pos_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.RolePetQiangHuaBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              pos_.add(
                  input.readMessage(com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          pos_ = java.util.Collections.unmodifiableList(pos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuUpSeccuss_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuUpSeccuss_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.class, com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.Builder.class);
    }

    public static final int SUCCESS_FIELD_NUMBER = 1;
    private boolean success_;
    /**
     * <pre>
     *结果
     * </pre>
     *
     * <code>bool success = 1;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }

    public static final int POS_FIELD_NUMBER = 2;
    private java.util.List<com.sh.game.protos.AbcProtos.RolePetQiangHuaBean> pos_;
    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.AbcProtos.RolePetQiangHuaBean> getPosList() {
      return pos_;
    }
    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.AbcProtos.RolePetQiangHuaBeanOrBuilder> 
        getPosOrBuilderList() {
      return pos_;
    }
    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    @java.lang.Override
    public int getPosCount() {
      return pos_.size();
    }
    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.RolePetQiangHuaBean getPos(int index) {
      return pos_.get(index);
    }
    /**
     * <pre>
     *宠物装备强化信息
     * </pre>
     *
     * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.RolePetQiangHuaBeanOrBuilder getPosOrBuilder(
        int index) {
      return pos_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (success_ != false) {
        output.writeBool(1, success_);
      }
      for (int i = 0; i < pos_.size(); i++) {
        output.writeMessage(2, pos_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (success_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, success_);
      }
      for (int i = 0; i < pos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, pos_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss other = (com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss) obj;

      if (getSuccess()
          != other.getSuccess()) return false;
      if (!getPosList()
          .equals(other.getPosList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getSuccess());
      if (getPosCount() > 0) {
        hash = (37 * hash) + POS_FIELD_NUMBER;
        hash = (53 * hash) + getPosList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResChongWuUpSeccuss' id='2' desc='返回强化信息' 
     * </pre>
     *
     * Protobuf type {@code chongWuEquip.ResChongWuUpSeccuss}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chongWuEquip.ResChongWuUpSeccuss)
        com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccussOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuUpSeccuss_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuUpSeccuss_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.class, com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.Builder.class);
      }

      // Construct using com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPosFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        success_ = false;

        if (posBuilder_ == null) {
          pos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          posBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuUpSeccuss_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss getDefaultInstanceForType() {
        return com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss build() {
        com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss buildPartial() {
        com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss result = new com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss(this);
        int from_bitField0_ = bitField0_;
        result.success_ = success_;
        if (posBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            pos_ = java.util.Collections.unmodifiableList(pos_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.pos_ = pos_;
        } else {
          result.pos_ = posBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss) {
          return mergeFrom((com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss other) {
        if (other == com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss.getDefaultInstance()) return this;
        if (other.getSuccess() != false) {
          setSuccess(other.getSuccess());
        }
        if (posBuilder_ == null) {
          if (!other.pos_.isEmpty()) {
            if (pos_.isEmpty()) {
              pos_ = other.pos_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensurePosIsMutable();
              pos_.addAll(other.pos_);
            }
            onChanged();
          }
        } else {
          if (!other.pos_.isEmpty()) {
            if (posBuilder_.isEmpty()) {
              posBuilder_.dispose();
              posBuilder_ = null;
              pos_ = other.pos_;
              bitField0_ = (bitField0_ & ~0x00000001);
              posBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getPosFieldBuilder() : null;
            } else {
              posBuilder_.addAllMessages(other.pos_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean success_ ;
      /**
       * <pre>
       *结果
       * </pre>
       *
       * <code>bool success = 1;</code>
       * @return The success.
       */
      @java.lang.Override
      public boolean getSuccess() {
        return success_;
      }
      /**
       * <pre>
       *结果
       * </pre>
       *
       * <code>bool success = 1;</code>
       * @param value The success to set.
       * @return This builder for chaining.
       */
      public Builder setSuccess(boolean value) {
        
        success_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *结果
       * </pre>
       *
       * <code>bool success = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuccess() {
        
        success_ = false;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.AbcProtos.RolePetQiangHuaBean> pos_ =
        java.util.Collections.emptyList();
      private void ensurePosIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          pos_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.RolePetQiangHuaBean>(pos_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.RolePetQiangHuaBean, com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder, com.sh.game.protos.AbcProtos.RolePetQiangHuaBeanOrBuilder> posBuilder_;

      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.RolePetQiangHuaBean> getPosList() {
        if (posBuilder_ == null) {
          return java.util.Collections.unmodifiableList(pos_);
        } else {
          return posBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public int getPosCount() {
        if (posBuilder_ == null) {
          return pos_.size();
        } else {
          return posBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.RolePetQiangHuaBean getPos(int index) {
        if (posBuilder_ == null) {
          return pos_.get(index);
        } else {
          return posBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public Builder setPos(
          int index, com.sh.game.protos.AbcProtos.RolePetQiangHuaBean value) {
        if (posBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosIsMutable();
          pos_.set(index, value);
          onChanged();
        } else {
          posBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public Builder setPos(
          int index, com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder builderForValue) {
        if (posBuilder_ == null) {
          ensurePosIsMutable();
          pos_.set(index, builderForValue.build());
          onChanged();
        } else {
          posBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public Builder addPos(com.sh.game.protos.AbcProtos.RolePetQiangHuaBean value) {
        if (posBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosIsMutable();
          pos_.add(value);
          onChanged();
        } else {
          posBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public Builder addPos(
          int index, com.sh.game.protos.AbcProtos.RolePetQiangHuaBean value) {
        if (posBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePosIsMutable();
          pos_.add(index, value);
          onChanged();
        } else {
          posBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public Builder addPos(
          com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder builderForValue) {
        if (posBuilder_ == null) {
          ensurePosIsMutable();
          pos_.add(builderForValue.build());
          onChanged();
        } else {
          posBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public Builder addPos(
          int index, com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder builderForValue) {
        if (posBuilder_ == null) {
          ensurePosIsMutable();
          pos_.add(index, builderForValue.build());
          onChanged();
        } else {
          posBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public Builder addAllPos(
          java.lang.Iterable<? extends com.sh.game.protos.AbcProtos.RolePetQiangHuaBean> values) {
        if (posBuilder_ == null) {
          ensurePosIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, pos_);
          onChanged();
        } else {
          posBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public Builder clearPos() {
        if (posBuilder_ == null) {
          pos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          posBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public Builder removePos(int index) {
        if (posBuilder_ == null) {
          ensurePosIsMutable();
          pos_.remove(index);
          onChanged();
        } else {
          posBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder getPosBuilder(
          int index) {
        return getPosFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.RolePetQiangHuaBeanOrBuilder getPosOrBuilder(
          int index) {
        if (posBuilder_ == null) {
          return pos_.get(index);  } else {
          return posBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public java.util.List<? extends com.sh.game.protos.AbcProtos.RolePetQiangHuaBeanOrBuilder> 
           getPosOrBuilderList() {
        if (posBuilder_ != null) {
          return posBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(pos_);
        }
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder addPosBuilder() {
        return getPosFieldBuilder().addBuilder(
            com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.getDefaultInstance());
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder addPosBuilder(
          int index) {
        return getPosFieldBuilder().addBuilder(
            index, com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.getDefaultInstance());
      }
      /**
       * <pre>
       *宠物装备强化信息
       * </pre>
       *
       * <code>repeated .abc.RolePetQiangHuaBean pos = 2;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder> 
           getPosBuilderList() {
        return getPosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.RolePetQiangHuaBean, com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder, com.sh.game.protos.AbcProtos.RolePetQiangHuaBeanOrBuilder> 
          getPosFieldBuilder() {
        if (posBuilder_ == null) {
          posBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.AbcProtos.RolePetQiangHuaBean, com.sh.game.protos.AbcProtos.RolePetQiangHuaBean.Builder, com.sh.game.protos.AbcProtos.RolePetQiangHuaBeanOrBuilder>(
                  pos_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          pos_ = null;
        }
        return posBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chongWuEquip.ResChongWuUpSeccuss)
    }

    // @@protoc_insertion_point(class_scope:chongWuEquip.ResChongWuUpSeccuss)
    private static final com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss();
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResChongWuUpSeccuss>
        PARSER = new com.google.protobuf.AbstractParser<ResChongWuUpSeccuss>() {
      @java.lang.Override
      public ResChongWuUpSeccuss parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResChongWuUpSeccuss(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResChongWuUpSeccuss> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResChongWuUpSeccuss> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChongWuEquipProtos.ResChongWuUpSeccuss getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqChongWuEquipJianDingOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chongWuEquip.ReqChongWuEquipJianDing)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *装备id
     * </pre>
     *
     * <code>int64 equipId = 1;</code>
     * @return The equipId.
     */
    long getEquipId();
  }
  /**
   * <pre>
   ** class='ReqChongWuEquipJianDing' id='3' desc='请求宠物装备鉴定' 
   * </pre>
   *
   * Protobuf type {@code chongWuEquip.ReqChongWuEquipJianDing}
   */
  public static final class ReqChongWuEquipJianDing extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chongWuEquip.ReqChongWuEquipJianDing)
      ReqChongWuEquipJianDingOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqChongWuEquipJianDing.newBuilder() to construct.
    private ReqChongWuEquipJianDing(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqChongWuEquipJianDing() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqChongWuEquipJianDing();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqChongWuEquipJianDing(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              equipId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipJianDing_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipJianDing_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing.Builder.class);
    }

    public static final int EQUIPID_FIELD_NUMBER = 1;
    private long equipId_;
    /**
     * <pre>
     *装备id
     * </pre>
     *
     * <code>int64 equipId = 1;</code>
     * @return The equipId.
     */
    @java.lang.Override
    public long getEquipId() {
      return equipId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (equipId_ != 0L) {
        output.writeInt64(1, equipId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (equipId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, equipId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing other = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing) obj;

      if (getEquipId()
          != other.getEquipId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + EQUIPID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEquipId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqChongWuEquipJianDing' id='3' desc='请求宠物装备鉴定' 
     * </pre>
     *
     * Protobuf type {@code chongWuEquip.ReqChongWuEquipJianDing}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chongWuEquip.ReqChongWuEquipJianDing)
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDingOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipJianDing_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipJianDing_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing.Builder.class);
      }

      // Construct using com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        equipId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipJianDing_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing getDefaultInstanceForType() {
        return com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing build() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing buildPartial() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing result = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing(this);
        result.equipId_ = equipId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing) {
          return mergeFrom((com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing other) {
        if (other == com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing.getDefaultInstance()) return this;
        if (other.getEquipId() != 0L) {
          setEquipId(other.getEquipId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long equipId_ ;
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int64 equipId = 1;</code>
       * @return The equipId.
       */
      @java.lang.Override
      public long getEquipId() {
        return equipId_;
      }
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int64 equipId = 1;</code>
       * @param value The equipId to set.
       * @return This builder for chaining.
       */
      public Builder setEquipId(long value) {
        
        equipId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int64 equipId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEquipId() {
        
        equipId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chongWuEquip.ReqChongWuEquipJianDing)
    }

    // @@protoc_insertion_point(class_scope:chongWuEquip.ReqChongWuEquipJianDing)
    private static final com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing();
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqChongWuEquipJianDing>
        PARSER = new com.google.protobuf.AbstractParser<ReqChongWuEquipJianDing>() {
      @java.lang.Override
      public ReqChongWuEquipJianDing parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqChongWuEquipJianDing(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqChongWuEquipJianDing> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqChongWuEquipJianDing> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipJianDing getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqChongWuEquipXiLianOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chongWuEquip.ReqChongWuEquipXiLian)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *装备id
     * </pre>
     *
     * <code>int64 equipId = 1;</code>
     * @return The equipId.
     */
    long getEquipId();
  }
  /**
   * <pre>
   ** class='ReqChongWuEquipXiLian' id='4' desc='请求宠物装备洗练' 
   * </pre>
   *
   * Protobuf type {@code chongWuEquip.ReqChongWuEquipXiLian}
   */
  public static final class ReqChongWuEquipXiLian extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chongWuEquip.ReqChongWuEquipXiLian)
      ReqChongWuEquipXiLianOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqChongWuEquipXiLian.newBuilder() to construct.
    private ReqChongWuEquipXiLian(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqChongWuEquipXiLian() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqChongWuEquipXiLian();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqChongWuEquipXiLian(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              equipId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipXiLian_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipXiLian_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian.Builder.class);
    }

    public static final int EQUIPID_FIELD_NUMBER = 1;
    private long equipId_;
    /**
     * <pre>
     *装备id
     * </pre>
     *
     * <code>int64 equipId = 1;</code>
     * @return The equipId.
     */
    @java.lang.Override
    public long getEquipId() {
      return equipId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (equipId_ != 0L) {
        output.writeInt64(1, equipId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (equipId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, equipId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian other = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian) obj;

      if (getEquipId()
          != other.getEquipId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + EQUIPID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEquipId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqChongWuEquipXiLian' id='4' desc='请求宠物装备洗练' 
     * </pre>
     *
     * Protobuf type {@code chongWuEquip.ReqChongWuEquipXiLian}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chongWuEquip.ReqChongWuEquipXiLian)
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLianOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipXiLian_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipXiLian_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian.Builder.class);
      }

      // Construct using com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        equipId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuEquipXiLian_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian getDefaultInstanceForType() {
        return com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian build() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian buildPartial() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian result = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian(this);
        result.equipId_ = equipId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian) {
          return mergeFrom((com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian other) {
        if (other == com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian.getDefaultInstance()) return this;
        if (other.getEquipId() != 0L) {
          setEquipId(other.getEquipId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long equipId_ ;
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int64 equipId = 1;</code>
       * @return The equipId.
       */
      @java.lang.Override
      public long getEquipId() {
        return equipId_;
      }
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int64 equipId = 1;</code>
       * @param value The equipId to set.
       * @return This builder for chaining.
       */
      public Builder setEquipId(long value) {
        
        equipId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int64 equipId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEquipId() {
        
        equipId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chongWuEquip.ReqChongWuEquipXiLian)
    }

    // @@protoc_insertion_point(class_scope:chongWuEquip.ReqChongWuEquipXiLian)
    private static final com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian();
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqChongWuEquipXiLian>
        PARSER = new com.google.protobuf.AbstractParser<ReqChongWuEquipXiLian>() {
      @java.lang.Override
      public ReqChongWuEquipXiLian parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqChongWuEquipXiLian(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqChongWuEquipXiLian> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqChongWuEquipXiLian> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuEquipXiLian getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResChongWuEquipJianDingOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chongWuEquip.ResChongWuEquipJianDing)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *宠物装备鉴定信息
     * </pre>
     *
     * <code>.abc.CommonItemBean itemBean = 1;</code>
     * @return Whether the itemBean field is set.
     */
    boolean hasItemBean();
    /**
     * <pre>
     *宠物装备鉴定信息
     * </pre>
     *
     * <code>.abc.CommonItemBean itemBean = 1;</code>
     * @return The itemBean.
     */
    com.sh.game.protos.AbcProtos.CommonItemBean getItemBean();
    /**
     * <pre>
     *宠物装备鉴定信息
     * </pre>
     *
     * <code>.abc.CommonItemBean itemBean = 1;</code>
     */
    com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder getItemBeanOrBuilder();
  }
  /**
   * <pre>
   ** class='ResChongWuEquipJianDing' id='5' desc='返回鉴定信息' 
   * </pre>
   *
   * Protobuf type {@code chongWuEquip.ResChongWuEquipJianDing}
   */
  public static final class ResChongWuEquipJianDing extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chongWuEquip.ResChongWuEquipJianDing)
      ResChongWuEquipJianDingOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResChongWuEquipJianDing.newBuilder() to construct.
    private ResChongWuEquipJianDing(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResChongWuEquipJianDing() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResChongWuEquipJianDing();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResChongWuEquipJianDing(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.sh.game.protos.AbcProtos.CommonItemBean.Builder subBuilder = null;
              if (itemBean_ != null) {
                subBuilder = itemBean_.toBuilder();
              }
              itemBean_ = input.readMessage(com.sh.game.protos.AbcProtos.CommonItemBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(itemBean_);
                itemBean_ = subBuilder.buildPartial();
              }

              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuEquipJianDing_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuEquipJianDing_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing.class, com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing.Builder.class);
    }

    public static final int ITEMBEAN_FIELD_NUMBER = 1;
    private com.sh.game.protos.AbcProtos.CommonItemBean itemBean_;
    /**
     * <pre>
     *宠物装备鉴定信息
     * </pre>
     *
     * <code>.abc.CommonItemBean itemBean = 1;</code>
     * @return Whether the itemBean field is set.
     */
    @java.lang.Override
    public boolean hasItemBean() {
      return itemBean_ != null;
    }
    /**
     * <pre>
     *宠物装备鉴定信息
     * </pre>
     *
     * <code>.abc.CommonItemBean itemBean = 1;</code>
     * @return The itemBean.
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonItemBean getItemBean() {
      return itemBean_ == null ? com.sh.game.protos.AbcProtos.CommonItemBean.getDefaultInstance() : itemBean_;
    }
    /**
     * <pre>
     *宠物装备鉴定信息
     * </pre>
     *
     * <code>.abc.CommonItemBean itemBean = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder getItemBeanOrBuilder() {
      return getItemBean();
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (itemBean_ != null) {
        output.writeMessage(1, getItemBean());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (itemBean_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getItemBean());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing other = (com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing) obj;

      if (hasItemBean() != other.hasItemBean()) return false;
      if (hasItemBean()) {
        if (!getItemBean()
            .equals(other.getItemBean())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasItemBean()) {
        hash = (37 * hash) + ITEMBEAN_FIELD_NUMBER;
        hash = (53 * hash) + getItemBean().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResChongWuEquipJianDing' id='5' desc='返回鉴定信息' 
     * </pre>
     *
     * Protobuf type {@code chongWuEquip.ResChongWuEquipJianDing}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chongWuEquip.ResChongWuEquipJianDing)
        com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDingOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuEquipJianDing_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuEquipJianDing_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing.class, com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing.Builder.class);
      }

      // Construct using com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (itemBeanBuilder_ == null) {
          itemBean_ = null;
        } else {
          itemBean_ = null;
          itemBeanBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ResChongWuEquipJianDing_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing getDefaultInstanceForType() {
        return com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing build() {
        com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing buildPartial() {
        com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing result = new com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing(this);
        if (itemBeanBuilder_ == null) {
          result.itemBean_ = itemBean_;
        } else {
          result.itemBean_ = itemBeanBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing) {
          return mergeFrom((com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing other) {
        if (other == com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing.getDefaultInstance()) return this;
        if (other.hasItemBean()) {
          mergeItemBean(other.getItemBean());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.sh.game.protos.AbcProtos.CommonItemBean itemBean_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonItemBean, com.sh.game.protos.AbcProtos.CommonItemBean.Builder, com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder> itemBeanBuilder_;
      /**
       * <pre>
       *宠物装备鉴定信息
       * </pre>
       *
       * <code>.abc.CommonItemBean itemBean = 1;</code>
       * @return Whether the itemBean field is set.
       */
      public boolean hasItemBean() {
        return itemBeanBuilder_ != null || itemBean_ != null;
      }
      /**
       * <pre>
       *宠物装备鉴定信息
       * </pre>
       *
       * <code>.abc.CommonItemBean itemBean = 1;</code>
       * @return The itemBean.
       */
      public com.sh.game.protos.AbcProtos.CommonItemBean getItemBean() {
        if (itemBeanBuilder_ == null) {
          return itemBean_ == null ? com.sh.game.protos.AbcProtos.CommonItemBean.getDefaultInstance() : itemBean_;
        } else {
          return itemBeanBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       *宠物装备鉴定信息
       * </pre>
       *
       * <code>.abc.CommonItemBean itemBean = 1;</code>
       */
      public Builder setItemBean(com.sh.game.protos.AbcProtos.CommonItemBean value) {
        if (itemBeanBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          itemBean_ = value;
          onChanged();
        } else {
          itemBeanBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <pre>
       *宠物装备鉴定信息
       * </pre>
       *
       * <code>.abc.CommonItemBean itemBean = 1;</code>
       */
      public Builder setItemBean(
          com.sh.game.protos.AbcProtos.CommonItemBean.Builder builderForValue) {
        if (itemBeanBuilder_ == null) {
          itemBean_ = builderForValue.build();
          onChanged();
        } else {
          itemBeanBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <pre>
       *宠物装备鉴定信息
       * </pre>
       *
       * <code>.abc.CommonItemBean itemBean = 1;</code>
       */
      public Builder mergeItemBean(com.sh.game.protos.AbcProtos.CommonItemBean value) {
        if (itemBeanBuilder_ == null) {
          if (itemBean_ != null) {
            itemBean_ =
              com.sh.game.protos.AbcProtos.CommonItemBean.newBuilder(itemBean_).mergeFrom(value).buildPartial();
          } else {
            itemBean_ = value;
          }
          onChanged();
        } else {
          itemBeanBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <pre>
       *宠物装备鉴定信息
       * </pre>
       *
       * <code>.abc.CommonItemBean itemBean = 1;</code>
       */
      public Builder clearItemBean() {
        if (itemBeanBuilder_ == null) {
          itemBean_ = null;
          onChanged();
        } else {
          itemBean_ = null;
          itemBeanBuilder_ = null;
        }

        return this;
      }
      /**
       * <pre>
       *宠物装备鉴定信息
       * </pre>
       *
       * <code>.abc.CommonItemBean itemBean = 1;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonItemBean.Builder getItemBeanBuilder() {
        
        onChanged();
        return getItemBeanFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       *宠物装备鉴定信息
       * </pre>
       *
       * <code>.abc.CommonItemBean itemBean = 1;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder getItemBeanOrBuilder() {
        if (itemBeanBuilder_ != null) {
          return itemBeanBuilder_.getMessageOrBuilder();
        } else {
          return itemBean_ == null ?
              com.sh.game.protos.AbcProtos.CommonItemBean.getDefaultInstance() : itemBean_;
        }
      }
      /**
       * <pre>
       *宠物装备鉴定信息
       * </pre>
       *
       * <code>.abc.CommonItemBean itemBean = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonItemBean, com.sh.game.protos.AbcProtos.CommonItemBean.Builder, com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder> 
          getItemBeanFieldBuilder() {
        if (itemBeanBuilder_ == null) {
          itemBeanBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonItemBean, com.sh.game.protos.AbcProtos.CommonItemBean.Builder, com.sh.game.protos.AbcProtos.CommonItemBeanOrBuilder>(
                  getItemBean(),
                  getParentForChildren(),
                  isClean());
          itemBean_ = null;
        }
        return itemBeanBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chongWuEquip.ResChongWuEquipJianDing)
    }

    // @@protoc_insertion_point(class_scope:chongWuEquip.ResChongWuEquipJianDing)
    private static final com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing();
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResChongWuEquipJianDing>
        PARSER = new com.google.protobuf.AbstractParser<ResChongWuEquipJianDing>() {
      @java.lang.Override
      public ResChongWuEquipJianDing parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResChongWuEquipJianDing(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResChongWuEquipJianDing> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResChongWuEquipJianDing> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChongWuEquipProtos.ResChongWuEquipJianDing getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqChongWuUpInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chongWuEquip.ReqChongWuUpInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqChongWuUpInfo' id='6' desc='请求宠物装备强化信息' 
   * </pre>
   *
   * Protobuf type {@code chongWuEquip.ReqChongWuUpInfo}
   */
  public static final class ReqChongWuUpInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chongWuEquip.ReqChongWuUpInfo)
      ReqChongWuUpInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqChongWuUpInfo.newBuilder() to construct.
    private ReqChongWuUpInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqChongWuUpInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqChongWuUpInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqChongWuUpInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo other = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqChongWuUpInfo' id='6' desc='请求宠物装备强化信息' 
     * </pre>
     *
     * Protobuf type {@code chongWuEquip.ReqChongWuUpInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chongWuEquip.ReqChongWuUpInfo)
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo getDefaultInstanceForType() {
        return com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo build() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo buildPartial() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo result = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo) {
          return mergeFrom((com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo other) {
        if (other == com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chongWuEquip.ReqChongWuUpInfo)
    }

    // @@protoc_insertion_point(class_scope:chongWuEquip.ReqChongWuUpInfo)
    private static final com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo();
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqChongWuUpInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqChongWuUpInfo>() {
      @java.lang.Override
      public ReqChongWuUpInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqChongWuUpInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqChongWuUpInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqChongWuUpInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqChongWuUpDianHuaOrBuilder extends
      // @@protoc_insertion_point(interface_extends:chongWuEquip.ReqChongWuUpDianHua)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *装备id
     * </pre>
     *
     * <code>int64 equipId = 1;</code>
     * @return The equipId.
     */
    long getEquipId();
  }
  /**
   * <pre>
   ** class='ReqChongWuUpDianHua' id='7' desc='请求宠物装备点化' 
   * </pre>
   *
   * Protobuf type {@code chongWuEquip.ReqChongWuUpDianHua}
   */
  public static final class ReqChongWuUpDianHua extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:chongWuEquip.ReqChongWuUpDianHua)
      ReqChongWuUpDianHuaOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqChongWuUpDianHua.newBuilder() to construct.
    private ReqChongWuUpDianHua(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqChongWuUpDianHua() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqChongWuUpDianHua();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqChongWuUpDianHua(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              equipId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpDianHua_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpDianHua_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua.Builder.class);
    }

    public static final int EQUIPID_FIELD_NUMBER = 1;
    private long equipId_;
    /**
     * <pre>
     *装备id
     * </pre>
     *
     * <code>int64 equipId = 1;</code>
     * @return The equipId.
     */
    @java.lang.Override
    public long getEquipId() {
      return equipId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (equipId_ != 0L) {
        output.writeInt64(1, equipId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (equipId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, equipId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua other = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua) obj;

      if (getEquipId()
          != other.getEquipId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + EQUIPID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEquipId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqChongWuUpDianHua' id='7' desc='请求宠物装备点化' 
     * </pre>
     *
     * Protobuf type {@code chongWuEquip.ReqChongWuUpDianHua}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:chongWuEquip.ReqChongWuUpDianHua)
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHuaOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpDianHua_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpDianHua_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua.class, com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua.Builder.class);
      }

      // Construct using com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        equipId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ChongWuEquipProtos.internal_static_chongWuEquip_ReqChongWuUpDianHua_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua getDefaultInstanceForType() {
        return com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua build() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua buildPartial() {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua result = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua(this);
        result.equipId_ = equipId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua) {
          return mergeFrom((com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua other) {
        if (other == com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua.getDefaultInstance()) return this;
        if (other.getEquipId() != 0L) {
          setEquipId(other.getEquipId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long equipId_ ;
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int64 equipId = 1;</code>
       * @return The equipId.
       */
      @java.lang.Override
      public long getEquipId() {
        return equipId_;
      }
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int64 equipId = 1;</code>
       * @param value The equipId to set.
       * @return This builder for chaining.
       */
      public Builder setEquipId(long value) {
        
        equipId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *装备id
       * </pre>
       *
       * <code>int64 equipId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEquipId() {
        
        equipId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:chongWuEquip.ReqChongWuUpDianHua)
    }

    // @@protoc_insertion_point(class_scope:chongWuEquip.ReqChongWuUpDianHua)
    private static final com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua();
    }

    public static com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqChongWuUpDianHua>
        PARSER = new com.google.protobuf.AbstractParser<ReqChongWuUpDianHua>() {
      @java.lang.Override
      public ReqChongWuUpDianHua parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqChongWuUpDianHua(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqChongWuUpDianHua> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqChongWuUpDianHua> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ChongWuEquipProtos.ReqChongWuUpDianHua getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chongWuEquip_ReqChongWuEquipQiangHua_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chongWuEquip_ReqChongWuEquipQiangHua_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chongWuEquip_ResChongWuUpSeccuss_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chongWuEquip_ResChongWuUpSeccuss_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chongWuEquip_ReqChongWuEquipJianDing_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chongWuEquip_ReqChongWuEquipJianDing_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chongWuEquip_ReqChongWuEquipXiLian_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chongWuEquip_ReqChongWuEquipXiLian_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chongWuEquip_ResChongWuEquipJianDing_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chongWuEquip_ResChongWuEquipJianDing_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chongWuEquip_ReqChongWuUpInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chongWuEquip_ReqChongWuUpInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_chongWuEquip_ReqChongWuUpDianHua_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_chongWuEquip_ReqChongWuUpDianHua_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022chongWuEquip.proto\022\014chongWuEquip\032\tabc." +
      "proto\"&\n\027ReqChongWuEquipQiangHua\022\013\n\003pos\030" +
      "\001 \001(\005\"M\n\023ResChongWuUpSeccuss\022\017\n\007success\030" +
      "\001 \001(\010\022%\n\003pos\030\002 \003(\0132\030.abc.RolePetQiangHua" +
      "Bean\"*\n\027ReqChongWuEquipJianDing\022\017\n\007equip" +
      "Id\030\001 \001(\003\"(\n\025ReqChongWuEquipXiLian\022\017\n\007equ" +
      "ipId\030\001 \001(\003\"@\n\027ResChongWuEquipJianDing\022%\n" +
      "\010itemBean\030\001 \001(\0132\023.abc.CommonItemBean\"\022\n\020" +
      "ReqChongWuUpInfo\"&\n\023ReqChongWuUpDianHua\022" +
      "\017\n\007equipId\030\001 \001(\003B(\n\022com.sh.game.protosB\022" +
      "ChongWuEquipProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.sh.game.protos.AbcProtos.getDescriptor(),
        });
    internal_static_chongWuEquip_ReqChongWuEquipQiangHua_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_chongWuEquip_ReqChongWuEquipQiangHua_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chongWuEquip_ReqChongWuEquipQiangHua_descriptor,
        new java.lang.String[] { "Pos", });
    internal_static_chongWuEquip_ResChongWuUpSeccuss_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_chongWuEquip_ResChongWuUpSeccuss_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chongWuEquip_ResChongWuUpSeccuss_descriptor,
        new java.lang.String[] { "Success", "Pos", });
    internal_static_chongWuEquip_ReqChongWuEquipJianDing_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_chongWuEquip_ReqChongWuEquipJianDing_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chongWuEquip_ReqChongWuEquipJianDing_descriptor,
        new java.lang.String[] { "EquipId", });
    internal_static_chongWuEquip_ReqChongWuEquipXiLian_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_chongWuEquip_ReqChongWuEquipXiLian_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chongWuEquip_ReqChongWuEquipXiLian_descriptor,
        new java.lang.String[] { "EquipId", });
    internal_static_chongWuEquip_ResChongWuEquipJianDing_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_chongWuEquip_ResChongWuEquipJianDing_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chongWuEquip_ResChongWuEquipJianDing_descriptor,
        new java.lang.String[] { "ItemBean", });
    internal_static_chongWuEquip_ReqChongWuUpInfo_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_chongWuEquip_ReqChongWuUpInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chongWuEquip_ReqChongWuUpInfo_descriptor,
        new java.lang.String[] { });
    internal_static_chongWuEquip_ReqChongWuUpDianHua_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_chongWuEquip_ReqChongWuUpDianHua_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_chongWuEquip_ReqChongWuUpDianHua_descriptor,
        new java.lang.String[] { "EquipId", });
    com.sh.game.protos.AbcProtos.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
