// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: makewith.proto

package com.sh.game.protos;

public final class MakeWithProtos {
  private MakeWithProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:makewith.Data)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 奖励组
     * </pre>
     *
     * <code>int32 rewardGroup = 1;</code>
     * @return The rewardGroup.
     */
    int getRewardGroup();

    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> 
        getInfoList();
    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBean getInfo(int index);
    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    int getInfoCount();
    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getInfoOrBuilderList();
    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code makewith.Data}
   */
  public static final class Data extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:makewith.Data)
      DataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Data.newBuilder() to construct.
    private Data(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Data() {
      info_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Data();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Data(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              rewardGroup_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                info_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              info_.add(
                  input.readMessage(com.sh.game.protos.AbcProtos.CommonKeyValueBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          info_ = java.util.Collections.unmodifiableList(info_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_Data_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_Data_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MakeWithProtos.Data.class, com.sh.game.protos.MakeWithProtos.Data.Builder.class);
    }

    public static final int REWARDGROUP_FIELD_NUMBER = 1;
    private int rewardGroup_;
    /**
     * <pre>
     * 奖励组
     * </pre>
     *
     * <code>int32 rewardGroup = 1;</code>
     * @return The rewardGroup.
     */
    @java.lang.Override
    public int getRewardGroup() {
      return rewardGroup_;
    }

    public static final int INFO_FIELD_NUMBER = 2;
    private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> info_;
    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getInfoList() {
      return info_;
    }
    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
        getInfoOrBuilderList() {
      return info_;
    }
    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    @java.lang.Override
    public int getInfoCount() {
      return info_.size();
    }
    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBean getInfo(int index) {
      return info_.get(index);
    }
    /**
     * <pre>
     *key=奖励ID，value=是否领过
     * </pre>
     *
     * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getInfoOrBuilder(
        int index) {
      return info_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (rewardGroup_ != 0) {
        output.writeInt32(1, rewardGroup_);
      }
      for (int i = 0; i < info_.size(); i++) {
        output.writeMessage(2, info_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rewardGroup_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rewardGroup_);
      }
      for (int i = 0; i < info_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, info_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MakeWithProtos.Data)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MakeWithProtos.Data other = (com.sh.game.protos.MakeWithProtos.Data) obj;

      if (getRewardGroup()
          != other.getRewardGroup()) return false;
      if (!getInfoList()
          .equals(other.getInfoList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + REWARDGROUP_FIELD_NUMBER;
      hash = (53 * hash) + getRewardGroup();
      if (getInfoCount() > 0) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfoList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.Data parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MakeWithProtos.Data prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code makewith.Data}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:makewith.Data)
        com.sh.game.protos.MakeWithProtos.DataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_Data_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_Data_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MakeWithProtos.Data.class, com.sh.game.protos.MakeWithProtos.Data.Builder.class);
      }

      // Construct using com.sh.game.protos.MakeWithProtos.Data.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        rewardGroup_ = 0;

        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infoBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_Data_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.Data getDefaultInstanceForType() {
        return com.sh.game.protos.MakeWithProtos.Data.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.Data build() {
        com.sh.game.protos.MakeWithProtos.Data result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.Data buildPartial() {
        com.sh.game.protos.MakeWithProtos.Data result = new com.sh.game.protos.MakeWithProtos.Data(this);
        int from_bitField0_ = bitField0_;
        result.rewardGroup_ = rewardGroup_;
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            info_ = java.util.Collections.unmodifiableList(info_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.info_ = info_;
        } else {
          result.info_ = infoBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MakeWithProtos.Data) {
          return mergeFrom((com.sh.game.protos.MakeWithProtos.Data)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MakeWithProtos.Data other) {
        if (other == com.sh.game.protos.MakeWithProtos.Data.getDefaultInstance()) return this;
        if (other.getRewardGroup() != 0) {
          setRewardGroup(other.getRewardGroup());
        }
        if (infoBuilder_ == null) {
          if (!other.info_.isEmpty()) {
            if (info_.isEmpty()) {
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfoIsMutable();
              info_.addAll(other.info_);
            }
            onChanged();
          }
        } else {
          if (!other.info_.isEmpty()) {
            if (infoBuilder_.isEmpty()) {
              infoBuilder_.dispose();
              infoBuilder_ = null;
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfoFieldBuilder() : null;
            } else {
              infoBuilder_.addAllMessages(other.info_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MakeWithProtos.Data parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MakeWithProtos.Data) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int rewardGroup_ ;
      /**
       * <pre>
       * 奖励组
       * </pre>
       *
       * <code>int32 rewardGroup = 1;</code>
       * @return The rewardGroup.
       */
      @java.lang.Override
      public int getRewardGroup() {
        return rewardGroup_;
      }
      /**
       * <pre>
       * 奖励组
       * </pre>
       *
       * <code>int32 rewardGroup = 1;</code>
       * @param value The rewardGroup to set.
       * @return This builder for chaining.
       */
      public Builder setRewardGroup(int value) {
        
        rewardGroup_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 奖励组
       * </pre>
       *
       * <code>int32 rewardGroup = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRewardGroup() {
        
        rewardGroup_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> info_ =
        java.util.Collections.emptyList();
      private void ensureInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          info_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueBean>(info_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> infoBuilder_;

      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean> getInfoList() {
        if (infoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(info_);
        } else {
          return infoBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public int getInfoCount() {
        if (infoBuilder_ == null) {
          return info_.size();
        } else {
          return infoBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean getInfo(int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);
        } else {
          return infoBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public Builder setInfo(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.set(index, value);
          onChanged();
        } else {
          infoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public Builder setInfo(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.set(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public Builder addInfo(com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(value);
          onChanged();
        } else {
          infoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public Builder addInfo(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(index, value);
          onChanged();
        } else {
          infoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public Builder addInfo(
          com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public Builder addInfo(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public Builder addAllInfo(
          java.lang.Iterable<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBean> values) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, info_);
          onChanged();
        } else {
          infoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public Builder removeInfo(int index) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.remove(index);
          onChanged();
        } else {
          infoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder getInfoBuilder(
          int index) {
        return getInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder getInfoOrBuilder(
          int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);  } else {
          return infoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
           getInfoOrBuilderList() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(info_);
        }
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addInfoBuilder() {
        return getInfoFieldBuilder().addBuilder(
            com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder addInfoBuilder(
          int index) {
        return getInfoFieldBuilder().addBuilder(
            index, com.sh.game.protos.AbcProtos.CommonKeyValueBean.getDefaultInstance());
      }
      /**
       * <pre>
       *key=奖励ID，value=是否领过
       * </pre>
       *
       * <code>repeated .abc.CommonKeyValueBean info = 2;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder> 
           getInfoBuilderList() {
        return getInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonKeyValueBean, com.sh.game.protos.AbcProtos.CommonKeyValueBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueBeanOrBuilder>(
                  info_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:makewith.Data)
    }

    // @@protoc_insertion_point(class_scope:makewith.Data)
    private static final com.sh.game.protos.MakeWithProtos.Data DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MakeWithProtos.Data();
    }

    public static com.sh.game.protos.MakeWithProtos.Data getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<Data>
        PARSER = new com.google.protobuf.AbstractParser<Data>() {
      @java.lang.Override
      public Data parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Data(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Data> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Data> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.Data getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MakeWithDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:makewith.MakeWithData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 配置id
     * </pre>
     *
     * <code>int32 lotteryId = 1;</code>
     * @return The lotteryId.
     */
    int getLotteryId();

    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    java.util.List<com.sh.game.protos.MakeWithProtos.Data> 
        getDataList();
    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    com.sh.game.protos.MakeWithProtos.Data getData(int index);
    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    int getDataCount();
    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    java.util.List<? extends com.sh.game.protos.MakeWithProtos.DataOrBuilder> 
        getDataOrBuilderList();
    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    com.sh.game.protos.MakeWithProtos.DataOrBuilder getDataOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code makewith.MakeWithData}
   */
  public static final class MakeWithData extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:makewith.MakeWithData)
      MakeWithDataOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MakeWithData.newBuilder() to construct.
    private MakeWithData(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MakeWithData() {
      data_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MakeWithData();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MakeWithData(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              lotteryId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                data_ = new java.util.ArrayList<com.sh.game.protos.MakeWithProtos.Data>();
                mutable_bitField0_ |= 0x00000001;
              }
              data_.add(
                  input.readMessage(com.sh.game.protos.MakeWithProtos.Data.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          data_ = java.util.Collections.unmodifiableList(data_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_MakeWithData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_MakeWithData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MakeWithProtos.MakeWithData.class, com.sh.game.protos.MakeWithProtos.MakeWithData.Builder.class);
    }

    public static final int LOTTERYID_FIELD_NUMBER = 1;
    private int lotteryId_;
    /**
     * <pre>
     * 配置id
     * </pre>
     *
     * <code>int32 lotteryId = 1;</code>
     * @return The lotteryId.
     */
    @java.lang.Override
    public int getLotteryId() {
      return lotteryId_;
    }

    public static final int DATA_FIELD_NUMBER = 2;
    private java.util.List<com.sh.game.protos.MakeWithProtos.Data> data_;
    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.MakeWithProtos.Data> getDataList() {
      return data_;
    }
    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.MakeWithProtos.DataOrBuilder> 
        getDataOrBuilderList() {
      return data_;
    }
    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    @java.lang.Override
    public int getDataCount() {
      return data_.size();
    }
    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.Data getData(int index) {
      return data_.get(index);
    }
    /**
     * <code>repeated .makewith.Data data = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.DataOrBuilder getDataOrBuilder(
        int index) {
      return data_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (lotteryId_ != 0) {
        output.writeInt32(1, lotteryId_);
      }
      for (int i = 0; i < data_.size(); i++) {
        output.writeMessage(2, data_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (lotteryId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, lotteryId_);
      }
      for (int i = 0; i < data_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, data_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MakeWithProtos.MakeWithData)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MakeWithProtos.MakeWithData other = (com.sh.game.protos.MakeWithProtos.MakeWithData) obj;

      if (getLotteryId()
          != other.getLotteryId()) return false;
      if (!getDataList()
          .equals(other.getDataList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + LOTTERYID_FIELD_NUMBER;
      hash = (53 * hash) + getLotteryId();
      if (getDataCount() > 0) {
        hash = (37 * hash) + DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDataList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.MakeWithData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MakeWithProtos.MakeWithData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code makewith.MakeWithData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:makewith.MakeWithData)
        com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_MakeWithData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_MakeWithData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MakeWithProtos.MakeWithData.class, com.sh.game.protos.MakeWithProtos.MakeWithData.Builder.class);
      }

      // Construct using com.sh.game.protos.MakeWithProtos.MakeWithData.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        lotteryId_ = 0;

        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          dataBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_MakeWithData_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.MakeWithData getDefaultInstanceForType() {
        return com.sh.game.protos.MakeWithProtos.MakeWithData.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.MakeWithData build() {
        com.sh.game.protos.MakeWithProtos.MakeWithData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.MakeWithData buildPartial() {
        com.sh.game.protos.MakeWithProtos.MakeWithData result = new com.sh.game.protos.MakeWithProtos.MakeWithData(this);
        int from_bitField0_ = bitField0_;
        result.lotteryId_ = lotteryId_;
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            data_ = java.util.Collections.unmodifiableList(data_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.data_ = data_;
        } else {
          result.data_ = dataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MakeWithProtos.MakeWithData) {
          return mergeFrom((com.sh.game.protos.MakeWithProtos.MakeWithData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MakeWithProtos.MakeWithData other) {
        if (other == com.sh.game.protos.MakeWithProtos.MakeWithData.getDefaultInstance()) return this;
        if (other.getLotteryId() != 0) {
          setLotteryId(other.getLotteryId());
        }
        if (dataBuilder_ == null) {
          if (!other.data_.isEmpty()) {
            if (data_.isEmpty()) {
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureDataIsMutable();
              data_.addAll(other.data_);
            }
            onChanged();
          }
        } else {
          if (!other.data_.isEmpty()) {
            if (dataBuilder_.isEmpty()) {
              dataBuilder_.dispose();
              dataBuilder_ = null;
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000001);
              dataBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getDataFieldBuilder() : null;
            } else {
              dataBuilder_.addAllMessages(other.data_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MakeWithProtos.MakeWithData parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MakeWithProtos.MakeWithData) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int lotteryId_ ;
      /**
       * <pre>
       * 配置id
       * </pre>
       *
       * <code>int32 lotteryId = 1;</code>
       * @return The lotteryId.
       */
      @java.lang.Override
      public int getLotteryId() {
        return lotteryId_;
      }
      /**
       * <pre>
       * 配置id
       * </pre>
       *
       * <code>int32 lotteryId = 1;</code>
       * @param value The lotteryId to set.
       * @return This builder for chaining.
       */
      public Builder setLotteryId(int value) {
        
        lotteryId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 配置id
       * </pre>
       *
       * <code>int32 lotteryId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLotteryId() {
        
        lotteryId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.MakeWithProtos.Data> data_ =
        java.util.Collections.emptyList();
      private void ensureDataIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          data_ = new java.util.ArrayList<com.sh.game.protos.MakeWithProtos.Data>(data_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.MakeWithProtos.Data, com.sh.game.protos.MakeWithProtos.Data.Builder, com.sh.game.protos.MakeWithProtos.DataOrBuilder> dataBuilder_;

      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public java.util.List<com.sh.game.protos.MakeWithProtos.Data> getDataList() {
        if (dataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(data_);
        } else {
          return dataBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public int getDataCount() {
        if (dataBuilder_ == null) {
          return data_.size();
        } else {
          return dataBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public com.sh.game.protos.MakeWithProtos.Data getData(int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);
        } else {
          return dataBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public Builder setData(
          int index, com.sh.game.protos.MakeWithProtos.Data value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.set(index, value);
          onChanged();
        } else {
          dataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public Builder setData(
          int index, com.sh.game.protos.MakeWithProtos.Data.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.set(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public Builder addData(com.sh.game.protos.MakeWithProtos.Data value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(value);
          onChanged();
        } else {
          dataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public Builder addData(
          int index, com.sh.game.protos.MakeWithProtos.Data value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(index, value);
          onChanged();
        } else {
          dataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public Builder addData(
          com.sh.game.protos.MakeWithProtos.Data.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public Builder addData(
          int index, com.sh.game.protos.MakeWithProtos.Data.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public Builder addAllData(
          java.lang.Iterable<? extends com.sh.game.protos.MakeWithProtos.Data> values) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, data_);
          onChanged();
        } else {
          dataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public Builder removeData(int index) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.remove(index);
          onChanged();
        } else {
          dataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public com.sh.game.protos.MakeWithProtos.Data.Builder getDataBuilder(
          int index) {
        return getDataFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public com.sh.game.protos.MakeWithProtos.DataOrBuilder getDataOrBuilder(
          int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);  } else {
          return dataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public java.util.List<? extends com.sh.game.protos.MakeWithProtos.DataOrBuilder> 
           getDataOrBuilderList() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(data_);
        }
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public com.sh.game.protos.MakeWithProtos.Data.Builder addDataBuilder() {
        return getDataFieldBuilder().addBuilder(
            com.sh.game.protos.MakeWithProtos.Data.getDefaultInstance());
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public com.sh.game.protos.MakeWithProtos.Data.Builder addDataBuilder(
          int index) {
        return getDataFieldBuilder().addBuilder(
            index, com.sh.game.protos.MakeWithProtos.Data.getDefaultInstance());
      }
      /**
       * <code>repeated .makewith.Data data = 2;</code>
       */
      public java.util.List<com.sh.game.protos.MakeWithProtos.Data.Builder> 
           getDataBuilderList() {
        return getDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.MakeWithProtos.Data, com.sh.game.protos.MakeWithProtos.Data.Builder, com.sh.game.protos.MakeWithProtos.DataOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.MakeWithProtos.Data, com.sh.game.protos.MakeWithProtos.Data.Builder, com.sh.game.protos.MakeWithProtos.DataOrBuilder>(
                  data_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:makewith.MakeWithData)
    }

    // @@protoc_insertion_point(class_scope:makewith.MakeWithData)
    private static final com.sh.game.protos.MakeWithProtos.MakeWithData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MakeWithProtos.MakeWithData();
    }

    public static com.sh.game.protos.MakeWithProtos.MakeWithData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MakeWithData>
        PARSER = new com.google.protobuf.AbstractParser<MakeWithData>() {
      @java.lang.Override
      public MakeWithData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MakeWithData(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MakeWithData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MakeWithData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.MakeWithData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMakeWithInfoMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:makewith.ReqMakeWithInfoMessage)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqMakeWithInfoMessage' id='1' desc='请求许愿池信息' 
   * </pre>
   *
   * Protobuf type {@code makewith.ReqMakeWithInfoMessage}
   */
  public static final class ReqMakeWithInfoMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:makewith.ReqMakeWithInfoMessage)
      ReqMakeWithInfoMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMakeWithInfoMessage.newBuilder() to construct.
    private ReqMakeWithInfoMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMakeWithInfoMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMakeWithInfoMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMakeWithInfoMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithInfoMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithInfoMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage.class, com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage other = (com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMakeWithInfoMessage' id='1' desc='请求许愿池信息' 
     * </pre>
     *
     * Protobuf type {@code makewith.ReqMakeWithInfoMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:makewith.ReqMakeWithInfoMessage)
        com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithInfoMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithInfoMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage.class, com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithInfoMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage getDefaultInstanceForType() {
        return com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage build() {
        com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage buildPartial() {
        com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage result = new com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage) {
          return mergeFrom((com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage other) {
        if (other == com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:makewith.ReqMakeWithInfoMessage)
    }

    // @@protoc_insertion_point(class_scope:makewith.ReqMakeWithInfoMessage)
    private static final com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage();
    }

    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMakeWithInfoMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqMakeWithInfoMessage>() {
      @java.lang.Override
      public ReqMakeWithInfoMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMakeWithInfoMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMakeWithInfoMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqMakeWithInfoMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.ReqMakeWithInfoMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResMakeWithInfoMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:makewith.ResMakeWithInfoMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    java.util.List<com.sh.game.protos.MakeWithProtos.MakeWithData> 
        getInfosList();
    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    com.sh.game.protos.MakeWithProtos.MakeWithData getInfos(int index);
    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    int getInfosCount();
    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    java.util.List<? extends com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder> 
        getInfosOrBuilderList();
    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder getInfosOrBuilder(
        int index);

    /**
     * <pre>
     * 抽取次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    int getCount();

    /**
     * <pre>
     * 每日抽取次数
     * </pre>
     *
     * <code>int32 dayCount = 3;</code>
     * @return The dayCount.
     */
    int getDayCount();
  }
  /**
   * <pre>
   ** class='ResMakeWithInfoMessage' id='2' desc='返回许愿池信息' 
   * </pre>
   *
   * Protobuf type {@code makewith.ResMakeWithInfoMessage}
   */
  public static final class ResMakeWithInfoMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:makewith.ResMakeWithInfoMessage)
      ResMakeWithInfoMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResMakeWithInfoMessage.newBuilder() to construct.
    private ResMakeWithInfoMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResMakeWithInfoMessage() {
      infos_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResMakeWithInfoMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResMakeWithInfoMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                infos_ = new java.util.ArrayList<com.sh.game.protos.MakeWithProtos.MakeWithData>();
                mutable_bitField0_ |= 0x00000001;
              }
              infos_.add(
                  input.readMessage(com.sh.game.protos.MakeWithProtos.MakeWithData.parser(), extensionRegistry));
              break;
            }
            case 16: {

              count_ = input.readInt32();
              break;
            }
            case 24: {

              dayCount_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          infos_ = java.util.Collections.unmodifiableList(infos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ResMakeWithInfoMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ResMakeWithInfoMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage.class, com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage.Builder.class);
    }

    public static final int INFOS_FIELD_NUMBER = 1;
    private java.util.List<com.sh.game.protos.MakeWithProtos.MakeWithData> infos_;
    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.MakeWithProtos.MakeWithData> getInfosList() {
      return infos_;
    }
    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder> 
        getInfosOrBuilderList() {
      return infos_;
    }
    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    @java.lang.Override
    public int getInfosCount() {
      return infos_.size();
    }
    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.MakeWithData getInfos(int index) {
      return infos_.get(index);
    }
    /**
     * <code>repeated .makewith.MakeWithData infos = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder getInfosOrBuilder(
        int index) {
      return infos_.get(index);
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private int count_;
    /**
     * <pre>
     * 抽取次数
     * </pre>
     *
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    public static final int DAYCOUNT_FIELD_NUMBER = 3;
    private int dayCount_;
    /**
     * <pre>
     * 每日抽取次数
     * </pre>
     *
     * <code>int32 dayCount = 3;</code>
     * @return The dayCount.
     */
    @java.lang.Override
    public int getDayCount() {
      return dayCount_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < infos_.size(); i++) {
        output.writeMessage(1, infos_.get(i));
      }
      if (count_ != 0) {
        output.writeInt32(2, count_);
      }
      if (dayCount_ != 0) {
        output.writeInt32(3, dayCount_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < infos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, infos_.get(i));
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, count_);
      }
      if (dayCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, dayCount_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage other = (com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage) obj;

      if (!getInfosList()
          .equals(other.getInfosList())) return false;
      if (getCount()
          != other.getCount()) return false;
      if (getDayCount()
          != other.getDayCount()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfosCount() > 0) {
        hash = (37 * hash) + INFOS_FIELD_NUMBER;
        hash = (53 * hash) + getInfosList().hashCode();
      }
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (37 * hash) + DAYCOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getDayCount();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResMakeWithInfoMessage' id='2' desc='返回许愿池信息' 
     * </pre>
     *
     * Protobuf type {@code makewith.ResMakeWithInfoMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:makewith.ResMakeWithInfoMessage)
        com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ResMakeWithInfoMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ResMakeWithInfoMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage.class, com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfosFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infosBuilder_ == null) {
          infos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infosBuilder_.clear();
        }
        count_ = 0;

        dayCount_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ResMakeWithInfoMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage getDefaultInstanceForType() {
        return com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage build() {
        com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage buildPartial() {
        com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage result = new com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage(this);
        int from_bitField0_ = bitField0_;
        if (infosBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            infos_ = java.util.Collections.unmodifiableList(infos_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.infos_ = infos_;
        } else {
          result.infos_ = infosBuilder_.build();
        }
        result.count_ = count_;
        result.dayCount_ = dayCount_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage) {
          return mergeFrom((com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage other) {
        if (other == com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage.getDefaultInstance()) return this;
        if (infosBuilder_ == null) {
          if (!other.infos_.isEmpty()) {
            if (infos_.isEmpty()) {
              infos_ = other.infos_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfosIsMutable();
              infos_.addAll(other.infos_);
            }
            onChanged();
          }
        } else {
          if (!other.infos_.isEmpty()) {
            if (infosBuilder_.isEmpty()) {
              infosBuilder_.dispose();
              infosBuilder_ = null;
              infos_ = other.infos_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infosBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfosFieldBuilder() : null;
            } else {
              infosBuilder_.addAllMessages(other.infos_);
            }
          }
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        if (other.getDayCount() != 0) {
          setDayCount(other.getDayCount());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.sh.game.protos.MakeWithProtos.MakeWithData> infos_ =
        java.util.Collections.emptyList();
      private void ensureInfosIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          infos_ = new java.util.ArrayList<com.sh.game.protos.MakeWithProtos.MakeWithData>(infos_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.MakeWithProtos.MakeWithData, com.sh.game.protos.MakeWithProtos.MakeWithData.Builder, com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder> infosBuilder_;

      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public java.util.List<com.sh.game.protos.MakeWithProtos.MakeWithData> getInfosList() {
        if (infosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(infos_);
        } else {
          return infosBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public int getInfosCount() {
        if (infosBuilder_ == null) {
          return infos_.size();
        } else {
          return infosBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public com.sh.game.protos.MakeWithProtos.MakeWithData getInfos(int index) {
        if (infosBuilder_ == null) {
          return infos_.get(index);
        } else {
          return infosBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public Builder setInfos(
          int index, com.sh.game.protos.MakeWithProtos.MakeWithData value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.set(index, value);
          onChanged();
        } else {
          infosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public Builder setInfos(
          int index, com.sh.game.protos.MakeWithProtos.MakeWithData.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.set(index, builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public Builder addInfos(com.sh.game.protos.MakeWithProtos.MakeWithData value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.add(value);
          onChanged();
        } else {
          infosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public Builder addInfos(
          int index, com.sh.game.protos.MakeWithProtos.MakeWithData value) {
        if (infosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfosIsMutable();
          infos_.add(index, value);
          onChanged();
        } else {
          infosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public Builder addInfos(
          com.sh.game.protos.MakeWithProtos.MakeWithData.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.add(builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public Builder addInfos(
          int index, com.sh.game.protos.MakeWithProtos.MakeWithData.Builder builderForValue) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.add(index, builderForValue.build());
          onChanged();
        } else {
          infosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public Builder addAllInfos(
          java.lang.Iterable<? extends com.sh.game.protos.MakeWithProtos.MakeWithData> values) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, infos_);
          onChanged();
        } else {
          infosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public Builder clearInfos() {
        if (infosBuilder_ == null) {
          infos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infosBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public Builder removeInfos(int index) {
        if (infosBuilder_ == null) {
          ensureInfosIsMutable();
          infos_.remove(index);
          onChanged();
        } else {
          infosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public com.sh.game.protos.MakeWithProtos.MakeWithData.Builder getInfosBuilder(
          int index) {
        return getInfosFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder getInfosOrBuilder(
          int index) {
        if (infosBuilder_ == null) {
          return infos_.get(index);  } else {
          return infosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public java.util.List<? extends com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder> 
           getInfosOrBuilderList() {
        if (infosBuilder_ != null) {
          return infosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(infos_);
        }
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public com.sh.game.protos.MakeWithProtos.MakeWithData.Builder addInfosBuilder() {
        return getInfosFieldBuilder().addBuilder(
            com.sh.game.protos.MakeWithProtos.MakeWithData.getDefaultInstance());
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public com.sh.game.protos.MakeWithProtos.MakeWithData.Builder addInfosBuilder(
          int index) {
        return getInfosFieldBuilder().addBuilder(
            index, com.sh.game.protos.MakeWithProtos.MakeWithData.getDefaultInstance());
      }
      /**
       * <code>repeated .makewith.MakeWithData infos = 1;</code>
       */
      public java.util.List<com.sh.game.protos.MakeWithProtos.MakeWithData.Builder> 
           getInfosBuilderList() {
        return getInfosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.MakeWithProtos.MakeWithData, com.sh.game.protos.MakeWithProtos.MakeWithData.Builder, com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder> 
          getInfosFieldBuilder() {
        if (infosBuilder_ == null) {
          infosBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.MakeWithProtos.MakeWithData, com.sh.game.protos.MakeWithProtos.MakeWithData.Builder, com.sh.game.protos.MakeWithProtos.MakeWithDataOrBuilder>(
                  infos_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          infos_ = null;
        }
        return infosBuilder_;
      }

      private int count_ ;
      /**
       * <pre>
       * 抽取次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       * 抽取次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 抽取次数
       * </pre>
       *
       * <code>int32 count = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }

      private int dayCount_ ;
      /**
       * <pre>
       * 每日抽取次数
       * </pre>
       *
       * <code>int32 dayCount = 3;</code>
       * @return The dayCount.
       */
      @java.lang.Override
      public int getDayCount() {
        return dayCount_;
      }
      /**
       * <pre>
       * 每日抽取次数
       * </pre>
       *
       * <code>int32 dayCount = 3;</code>
       * @param value The dayCount to set.
       * @return This builder for chaining.
       */
      public Builder setDayCount(int value) {
        
        dayCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 每日抽取次数
       * </pre>
       *
       * <code>int32 dayCount = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDayCount() {
        
        dayCount_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:makewith.ResMakeWithInfoMessage)
    }

    // @@protoc_insertion_point(class_scope:makewith.ResMakeWithInfoMessage)
    private static final com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage();
    }

    public static com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResMakeWithInfoMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResMakeWithInfoMessage>() {
      @java.lang.Override
      public ResMakeWithInfoMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResMakeWithInfoMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResMakeWithInfoMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResMakeWithInfoMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.ResMakeWithInfoMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqMakeWithMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:makewith.ReqMakeWithMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 id = 1;</code>
     * @return The id.
     */
    int getId();

    /**
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    int getCount();
  }
  /**
   * <pre>
   ** class='ReqMakeWithMessage' id='3' desc='请求许愿' 
   * </pre>
   *
   * Protobuf type {@code makewith.ReqMakeWithMessage}
   */
  public static final class ReqMakeWithMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:makewith.ReqMakeWithMessage)
      ReqMakeWithMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqMakeWithMessage.newBuilder() to construct.
    private ReqMakeWithMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqMakeWithMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqMakeWithMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqMakeWithMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              id_ = input.readInt32();
              break;
            }
            case 16: {

              count_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage.class, com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <code>int32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    public static final int COUNT_FIELD_NUMBER = 2;
    private int count_;
    /**
     * <code>int32 count = 2;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeInt32(1, id_);
      }
      if (count_ != 0) {
        output.writeInt32(2, count_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, count_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage other = (com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage) obj;

      if (getId()
          != other.getId()) return false;
      if (getCount()
          != other.getCount()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqMakeWithMessage' id='3' desc='请求许愿' 
     * </pre>
     *
     * Protobuf type {@code makewith.ReqMakeWithMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:makewith.ReqMakeWithMessage)
        com.sh.game.protos.MakeWithProtos.ReqMakeWithMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage.class, com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        id_ = 0;

        count_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqMakeWithMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage getDefaultInstanceForType() {
        return com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage build() {
        com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage buildPartial() {
        com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage result = new com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage(this);
        result.id_ = id_;
        result.count_ = count_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage) {
          return mergeFrom((com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage other) {
        if (other == com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int id_ ;
      /**
       * <code>int32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>int32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {
        
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        
        id_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <code>int32 count = 2;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <code>int32 count = 2;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 count = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:makewith.ReqMakeWithMessage)
    }

    // @@protoc_insertion_point(class_scope:makewith.ReqMakeWithMessage)
    private static final com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage();
    }

    public static com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqMakeWithMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqMakeWithMessage>() {
      @java.lang.Override
      public ReqMakeWithMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqMakeWithMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqMakeWithMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqMakeWithMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.ReqMakeWithMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFirstMakeWithMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:makewith.ReqFirstMakeWithMessage)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqFirstMakeWithMessage' id='4' desc='请求第一次许愿必出' 
   * </pre>
   *
   * Protobuf type {@code makewith.ReqFirstMakeWithMessage}
   */
  public static final class ReqFirstMakeWithMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:makewith.ReqFirstMakeWithMessage)
      ReqFirstMakeWithMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFirstMakeWithMessage.newBuilder() to construct.
    private ReqFirstMakeWithMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFirstMakeWithMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFirstMakeWithMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFirstMakeWithMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqFirstMakeWithMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqFirstMakeWithMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage.class, com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage other = (com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFirstMakeWithMessage' id='4' desc='请求第一次许愿必出' 
     * </pre>
     *
     * Protobuf type {@code makewith.ReqFirstMakeWithMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:makewith.ReqFirstMakeWithMessage)
        com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqFirstMakeWithMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqFirstMakeWithMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage.class, com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.MakeWithProtos.internal_static_makewith_ReqFirstMakeWithMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage getDefaultInstanceForType() {
        return com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage build() {
        com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage buildPartial() {
        com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage result = new com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage) {
          return mergeFrom((com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage other) {
        if (other == com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:makewith.ReqFirstMakeWithMessage)
    }

    // @@protoc_insertion_point(class_scope:makewith.ReqFirstMakeWithMessage)
    private static final com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage();
    }

    public static com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFirstMakeWithMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqFirstMakeWithMessage>() {
      @java.lang.Override
      public ReqFirstMakeWithMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFirstMakeWithMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFirstMakeWithMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFirstMakeWithMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.MakeWithProtos.ReqFirstMakeWithMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_makewith_Data_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_makewith_Data_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_makewith_MakeWithData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_makewith_MakeWithData_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_makewith_ReqMakeWithInfoMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_makewith_ReqMakeWithInfoMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_makewith_ResMakeWithInfoMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_makewith_ResMakeWithInfoMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_makewith_ReqMakeWithMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_makewith_ReqMakeWithMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_makewith_ReqFirstMakeWithMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_makewith_ReqFirstMakeWithMessage_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016makewith.proto\022\010makewith\032\tabc.proto\"B\n" +
      "\004Data\022\023\n\013rewardGroup\030\001 \001(\005\022%\n\004info\030\002 \003(\013" +
      "2\027.abc.CommonKeyValueBean\"?\n\014MakeWithDat" +
      "a\022\021\n\tlotteryId\030\001 \001(\005\022\034\n\004data\030\002 \003(\0132\016.mak" +
      "ewith.Data\"\030\n\026ReqMakeWithInfoMessage\"`\n\026" +
      "ResMakeWithInfoMessage\022%\n\005infos\030\001 \003(\0132\026." +
      "makewith.MakeWithData\022\r\n\005count\030\002 \001(\005\022\020\n\010" +
      "dayCount\030\003 \001(\005\"/\n\022ReqMakeWithMessage\022\n\n\002" +
      "id\030\001 \001(\005\022\r\n\005count\030\002 \001(\005\"\031\n\027ReqFirstMakeW" +
      "ithMessageB$\n\022com.sh.game.protosB\016MakeWi" +
      "thProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.sh.game.protos.AbcProtos.getDescriptor(),
        });
    internal_static_makewith_Data_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_makewith_Data_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_makewith_Data_descriptor,
        new java.lang.String[] { "RewardGroup", "Info", });
    internal_static_makewith_MakeWithData_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_makewith_MakeWithData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_makewith_MakeWithData_descriptor,
        new java.lang.String[] { "LotteryId", "Data", });
    internal_static_makewith_ReqMakeWithInfoMessage_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_makewith_ReqMakeWithInfoMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_makewith_ReqMakeWithInfoMessage_descriptor,
        new java.lang.String[] { });
    internal_static_makewith_ResMakeWithInfoMessage_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_makewith_ResMakeWithInfoMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_makewith_ResMakeWithInfoMessage_descriptor,
        new java.lang.String[] { "Infos", "Count", "DayCount", });
    internal_static_makewith_ReqMakeWithMessage_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_makewith_ReqMakeWithMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_makewith_ReqMakeWithMessage_descriptor,
        new java.lang.String[] { "Id", "Count", });
    internal_static_makewith_ReqFirstMakeWithMessage_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_makewith_ReqFirstMakeWithMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_makewith_ReqFirstMakeWithMessage_descriptor,
        new java.lang.String[] { });
    com.sh.game.protos.AbcProtos.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
