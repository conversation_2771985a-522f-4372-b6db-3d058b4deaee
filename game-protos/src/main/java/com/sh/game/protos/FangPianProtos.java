// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: fangPian.proto

package com.sh.game.protos;

public final class FangPianProtos {
  private FangPianProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqFangPianInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fangPian.ReqFangPianInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqFangPianInfo' id='1' desc='请求玩家防骗奖励信息' 
   * </pre>
   *
   * Protobuf type {@code fangPian.ReqFangPianInfo}
   */
  public static final class ReqFangPianInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fangPian.ReqFangPianInfo)
      ReqFangPianInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFangPianInfo.newBuilder() to construct.
    private ReqFangPianInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFangPianInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFangPianInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFangPianInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FangPianProtos.ReqFangPianInfo.class, com.sh.game.protos.FangPianProtos.ReqFangPianInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FangPianProtos.ReqFangPianInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FangPianProtos.ReqFangPianInfo other = (com.sh.game.protos.FangPianProtos.ReqFangPianInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FangPianProtos.ReqFangPianInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFangPianInfo' id='1' desc='请求玩家防骗奖励信息' 
     * </pre>
     *
     * Protobuf type {@code fangPian.ReqFangPianInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fangPian.ReqFangPianInfo)
        com.sh.game.protos.FangPianProtos.ReqFangPianInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FangPianProtos.ReqFangPianInfo.class, com.sh.game.protos.FangPianProtos.ReqFangPianInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.FangPianProtos.ReqFangPianInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FangPianProtos.ReqFangPianInfo getDefaultInstanceForType() {
        return com.sh.game.protos.FangPianProtos.ReqFangPianInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FangPianProtos.ReqFangPianInfo build() {
        com.sh.game.protos.FangPianProtos.ReqFangPianInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FangPianProtos.ReqFangPianInfo buildPartial() {
        com.sh.game.protos.FangPianProtos.ReqFangPianInfo result = new com.sh.game.protos.FangPianProtos.ReqFangPianInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FangPianProtos.ReqFangPianInfo) {
          return mergeFrom((com.sh.game.protos.FangPianProtos.ReqFangPianInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FangPianProtos.ReqFangPianInfo other) {
        if (other == com.sh.game.protos.FangPianProtos.ReqFangPianInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FangPianProtos.ReqFangPianInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FangPianProtos.ReqFangPianInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fangPian.ReqFangPianInfo)
    }

    // @@protoc_insertion_point(class_scope:fangPian.ReqFangPianInfo)
    private static final com.sh.game.protos.FangPianProtos.ReqFangPianInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FangPianProtos.ReqFangPianInfo();
    }

    public static com.sh.game.protos.FangPianProtos.ReqFangPianInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFangPianInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqFangPianInfo>() {
      @java.lang.Override
      public ReqFangPianInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFangPianInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFangPianInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFangPianInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FangPianProtos.ReqFangPianInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResFangPianInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fangPian.ResFangPianInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *上次重置时间
     * </pre>
     *
     * <code>sint64 lastTime = 1;</code>
     * @return The lastTime.
     */
    long getLastTime();

    /**
     * <pre>
     *已领取防骗奖励id
     * </pre>
     *
     * <code>repeated int32 cfgId = 2;</code>
     * @return A list containing the cfgId.
     */
    java.util.List<java.lang.Integer> getCfgIdList();
    /**
     * <pre>
     *已领取防骗奖励id
     * </pre>
     *
     * <code>repeated int32 cfgId = 2;</code>
     * @return The count of cfgId.
     */
    int getCfgIdCount();
    /**
     * <pre>
     *已领取防骗奖励id
     * </pre>
     *
     * <code>repeated int32 cfgId = 2;</code>
     * @param index The index of the element to return.
     * @return The cfgId at the given index.
     */
    int getCfgId(int index);
  }
  /**
   * <pre>
   ** class='ResFangPianInfo' id='2' desc='返回玩家防骗奖励信息' 
   * </pre>
   *
   * Protobuf type {@code fangPian.ResFangPianInfo}
   */
  public static final class ResFangPianInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fangPian.ResFangPianInfo)
      ResFangPianInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResFangPianInfo.newBuilder() to construct.
    private ResFangPianInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResFangPianInfo() {
      cfgId_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResFangPianInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResFangPianInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              lastTime_ = input.readSInt64();
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                cfgId_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              cfgId_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                cfgId_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                cfgId_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          cfgId_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ResFangPianInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ResFangPianInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FangPianProtos.ResFangPianInfo.class, com.sh.game.protos.FangPianProtos.ResFangPianInfo.Builder.class);
    }

    public static final int LASTTIME_FIELD_NUMBER = 1;
    private long lastTime_;
    /**
     * <pre>
     *上次重置时间
     * </pre>
     *
     * <code>sint64 lastTime = 1;</code>
     * @return The lastTime.
     */
    @java.lang.Override
    public long getLastTime() {
      return lastTime_;
    }

    public static final int CFGID_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList cfgId_;
    /**
     * <pre>
     *已领取防骗奖励id
     * </pre>
     *
     * <code>repeated int32 cfgId = 2;</code>
     * @return A list containing the cfgId.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getCfgIdList() {
      return cfgId_;
    }
    /**
     * <pre>
     *已领取防骗奖励id
     * </pre>
     *
     * <code>repeated int32 cfgId = 2;</code>
     * @return The count of cfgId.
     */
    public int getCfgIdCount() {
      return cfgId_.size();
    }
    /**
     * <pre>
     *已领取防骗奖励id
     * </pre>
     *
     * <code>repeated int32 cfgId = 2;</code>
     * @param index The index of the element to return.
     * @return The cfgId at the given index.
     */
    public int getCfgId(int index) {
      return cfgId_.getInt(index);
    }
    private int cfgIdMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (lastTime_ != 0L) {
        output.writeSInt64(1, lastTime_);
      }
      if (getCfgIdList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(cfgIdMemoizedSerializedSize);
      }
      for (int i = 0; i < cfgId_.size(); i++) {
        output.writeInt32NoTag(cfgId_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (lastTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeSInt64Size(1, lastTime_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < cfgId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(cfgId_.getInt(i));
        }
        size += dataSize;
        if (!getCfgIdList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        cfgIdMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FangPianProtos.ResFangPianInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FangPianProtos.ResFangPianInfo other = (com.sh.game.protos.FangPianProtos.ResFangPianInfo) obj;

      if (getLastTime()
          != other.getLastTime()) return false;
      if (!getCfgIdList()
          .equals(other.getCfgIdList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + LASTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastTime());
      if (getCfgIdCount() > 0) {
        hash = (37 * hash) + CFGID_FIELD_NUMBER;
        hash = (53 * hash) + getCfgIdList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FangPianProtos.ResFangPianInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResFangPianInfo' id='2' desc='返回玩家防骗奖励信息' 
     * </pre>
     *
     * Protobuf type {@code fangPian.ResFangPianInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fangPian.ResFangPianInfo)
        com.sh.game.protos.FangPianProtos.ResFangPianInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ResFangPianInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ResFangPianInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FangPianProtos.ResFangPianInfo.class, com.sh.game.protos.FangPianProtos.ResFangPianInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.FangPianProtos.ResFangPianInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        lastTime_ = 0L;

        cfgId_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ResFangPianInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FangPianProtos.ResFangPianInfo getDefaultInstanceForType() {
        return com.sh.game.protos.FangPianProtos.ResFangPianInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FangPianProtos.ResFangPianInfo build() {
        com.sh.game.protos.FangPianProtos.ResFangPianInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FangPianProtos.ResFangPianInfo buildPartial() {
        com.sh.game.protos.FangPianProtos.ResFangPianInfo result = new com.sh.game.protos.FangPianProtos.ResFangPianInfo(this);
        int from_bitField0_ = bitField0_;
        result.lastTime_ = lastTime_;
        if (((bitField0_ & 0x00000001) != 0)) {
          cfgId_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.cfgId_ = cfgId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FangPianProtos.ResFangPianInfo) {
          return mergeFrom((com.sh.game.protos.FangPianProtos.ResFangPianInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FangPianProtos.ResFangPianInfo other) {
        if (other == com.sh.game.protos.FangPianProtos.ResFangPianInfo.getDefaultInstance()) return this;
        if (other.getLastTime() != 0L) {
          setLastTime(other.getLastTime());
        }
        if (!other.cfgId_.isEmpty()) {
          if (cfgId_.isEmpty()) {
            cfgId_ = other.cfgId_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureCfgIdIsMutable();
            cfgId_.addAll(other.cfgId_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FangPianProtos.ResFangPianInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FangPianProtos.ResFangPianInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long lastTime_ ;
      /**
       * <pre>
       *上次重置时间
       * </pre>
       *
       * <code>sint64 lastTime = 1;</code>
       * @return The lastTime.
       */
      @java.lang.Override
      public long getLastTime() {
        return lastTime_;
      }
      /**
       * <pre>
       *上次重置时间
       * </pre>
       *
       * <code>sint64 lastTime = 1;</code>
       * @param value The lastTime to set.
       * @return This builder for chaining.
       */
      public Builder setLastTime(long value) {
        
        lastTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *上次重置时间
       * </pre>
       *
       * <code>sint64 lastTime = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastTime() {
        
        lastTime_ = 0L;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList cfgId_ = emptyIntList();
      private void ensureCfgIdIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          cfgId_ = mutableCopy(cfgId_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       *已领取防骗奖励id
       * </pre>
       *
       * <code>repeated int32 cfgId = 2;</code>
       * @return A list containing the cfgId.
       */
      public java.util.List<java.lang.Integer>
          getCfgIdList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(cfgId_) : cfgId_;
      }
      /**
       * <pre>
       *已领取防骗奖励id
       * </pre>
       *
       * <code>repeated int32 cfgId = 2;</code>
       * @return The count of cfgId.
       */
      public int getCfgIdCount() {
        return cfgId_.size();
      }
      /**
       * <pre>
       *已领取防骗奖励id
       * </pre>
       *
       * <code>repeated int32 cfgId = 2;</code>
       * @param index The index of the element to return.
       * @return The cfgId at the given index.
       */
      public int getCfgId(int index) {
        return cfgId_.getInt(index);
      }
      /**
       * <pre>
       *已领取防骗奖励id
       * </pre>
       *
       * <code>repeated int32 cfgId = 2;</code>
       * @param index The index to set the value at.
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(
          int index, int value) {
        ensureCfgIdIsMutable();
        cfgId_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *已领取防骗奖励id
       * </pre>
       *
       * <code>repeated int32 cfgId = 2;</code>
       * @param value The cfgId to add.
       * @return This builder for chaining.
       */
      public Builder addCfgId(int value) {
        ensureCfgIdIsMutable();
        cfgId_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *已领取防骗奖励id
       * </pre>
       *
       * <code>repeated int32 cfgId = 2;</code>
       * @param values The cfgId to add.
       * @return This builder for chaining.
       */
      public Builder addAllCfgId(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureCfgIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, cfgId_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *已领取防骗奖励id
       * </pre>
       *
       * <code>repeated int32 cfgId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {
        cfgId_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fangPian.ResFangPianInfo)
    }

    // @@protoc_insertion_point(class_scope:fangPian.ResFangPianInfo)
    private static final com.sh.game.protos.FangPianProtos.ResFangPianInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FangPianProtos.ResFangPianInfo();
    }

    public static com.sh.game.protos.FangPianProtos.ResFangPianInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResFangPianInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResFangPianInfo>() {
      @java.lang.Override
      public ResFangPianInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResFangPianInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResFangPianInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResFangPianInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FangPianProtos.ResFangPianInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqFangPianRewardOrBuilder extends
      // @@protoc_insertion_point(interface_extends:fangPian.ReqFangPianReward)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqFangPianReward' id='3' desc='请求领取玩家防骗奖励' 
   * </pre>
   *
   * Protobuf type {@code fangPian.ReqFangPianReward}
   */
  public static final class ReqFangPianReward extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:fangPian.ReqFangPianReward)
      ReqFangPianRewardOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqFangPianReward.newBuilder() to construct.
    private ReqFangPianReward(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqFangPianReward() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqFangPianReward();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqFangPianReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianReward_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.FangPianProtos.ReqFangPianReward.class, com.sh.game.protos.FangPianProtos.ReqFangPianReward.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.FangPianProtos.ReqFangPianReward)) {
        return super.equals(obj);
      }
      com.sh.game.protos.FangPianProtos.ReqFangPianReward other = (com.sh.game.protos.FangPianProtos.ReqFangPianReward) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.FangPianProtos.ReqFangPianReward prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqFangPianReward' id='3' desc='请求领取玩家防骗奖励' 
     * </pre>
     *
     * Protobuf type {@code fangPian.ReqFangPianReward}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:fangPian.ReqFangPianReward)
        com.sh.game.protos.FangPianProtos.ReqFangPianRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianReward_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.FangPianProtos.ReqFangPianReward.class, com.sh.game.protos.FangPianProtos.ReqFangPianReward.Builder.class);
      }

      // Construct using com.sh.game.protos.FangPianProtos.ReqFangPianReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.FangPianProtos.internal_static_fangPian_ReqFangPianReward_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.FangPianProtos.ReqFangPianReward getDefaultInstanceForType() {
        return com.sh.game.protos.FangPianProtos.ReqFangPianReward.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.FangPianProtos.ReqFangPianReward build() {
        com.sh.game.protos.FangPianProtos.ReqFangPianReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.FangPianProtos.ReqFangPianReward buildPartial() {
        com.sh.game.protos.FangPianProtos.ReqFangPianReward result = new com.sh.game.protos.FangPianProtos.ReqFangPianReward(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.FangPianProtos.ReqFangPianReward) {
          return mergeFrom((com.sh.game.protos.FangPianProtos.ReqFangPianReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.FangPianProtos.ReqFangPianReward other) {
        if (other == com.sh.game.protos.FangPianProtos.ReqFangPianReward.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.FangPianProtos.ReqFangPianReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.FangPianProtos.ReqFangPianReward) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:fangPian.ReqFangPianReward)
    }

    // @@protoc_insertion_point(class_scope:fangPian.ReqFangPianReward)
    private static final com.sh.game.protos.FangPianProtos.ReqFangPianReward DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.FangPianProtos.ReqFangPianReward();
    }

    public static com.sh.game.protos.FangPianProtos.ReqFangPianReward getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqFangPianReward>
        PARSER = new com.google.protobuf.AbstractParser<ReqFangPianReward>() {
      @java.lang.Override
      public ReqFangPianReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqFangPianReward(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqFangPianReward> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqFangPianReward> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.FangPianProtos.ReqFangPianReward getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fangPian_ReqFangPianInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fangPian_ReqFangPianInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fangPian_ResFangPianInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fangPian_ResFangPianInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_fangPian_ReqFangPianReward_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_fangPian_ReqFangPianReward_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016fangPian.proto\022\010fangPian\"\021\n\017ReqFangPia" +
      "nInfo\"2\n\017ResFangPianInfo\022\020\n\010lastTime\030\001 \001" +
      "(\022\022\r\n\005cfgId\030\002 \003(\005\"\023\n\021ReqFangPianRewardB$" +
      "\n\022com.sh.game.protosB\016FangPianProtosb\006pr" +
      "oto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_fangPian_ReqFangPianInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_fangPian_ReqFangPianInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fangPian_ReqFangPianInfo_descriptor,
        new java.lang.String[] { });
    internal_static_fangPian_ResFangPianInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_fangPian_ResFangPianInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fangPian_ResFangPianInfo_descriptor,
        new java.lang.String[] { "LastTime", "CfgId", });
    internal_static_fangPian_ReqFangPianReward_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_fangPian_ReqFangPianReward_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_fangPian_ReqFangPianReward_descriptor,
        new java.lang.String[] { });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
