// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: qiaoqian.proto

package com.sh.game.protos;

public final class QiaoQianProtos {
  private QiaoQianProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QiaoQianFighterBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.QiaoQianFighterBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 roleId = 1;</code>
     * @return The roleId.
     */
    long getRoleId();

    /**
     * <code>int32 robotCfgId = 2;</code>
     * @return The robotCfgId.
     */
    int getRobotCfgId();

    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueStringBean> 
        getSecretaryList();
    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueStringBean getSecretary(int index);
    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    int getSecretaryCount();
    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueStringBeanOrBuilder> 
        getSecretaryOrBuilderList();
    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    com.sh.game.protos.AbcProtos.CommonKeyValueStringBeanOrBuilder getSecretaryOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code qiaoqian.QiaoQianFighterBean}
   */
  public static final class QiaoQianFighterBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.QiaoQianFighterBean)
      QiaoQianFighterBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QiaoQianFighterBean.newBuilder() to construct.
    private QiaoQianFighterBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QiaoQianFighterBean() {
      secretary_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QiaoQianFighterBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QiaoQianFighterBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              roleId_ = input.readInt64();
              break;
            }
            case 16: {

              robotCfgId_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                secretary_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueStringBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              secretary_.add(
                  input.readMessage(com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          secretary_ = java.util.Collections.unmodifiableList(secretary_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianFighterBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianFighterBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.class, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder.class);
    }

    public static final int ROLEID_FIELD_NUMBER = 1;
    private long roleId_;
    /**
     * <code>int64 roleId = 1;</code>
     * @return The roleId.
     */
    @java.lang.Override
    public long getRoleId() {
      return roleId_;
    }

    public static final int ROBOTCFGID_FIELD_NUMBER = 2;
    private int robotCfgId_;
    /**
     * <code>int32 robotCfgId = 2;</code>
     * @return The robotCfgId.
     */
    @java.lang.Override
    public int getRobotCfgId() {
      return robotCfgId_;
    }

    public static final int SECRETARY_FIELD_NUMBER = 3;
    private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueStringBean> secretary_;
    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueStringBean> getSecretaryList() {
      return secretary_;
    }
    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueStringBeanOrBuilder> 
        getSecretaryOrBuilderList() {
      return secretary_;
    }
    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    @java.lang.Override
    public int getSecretaryCount() {
      return secretary_.size();
    }
    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueStringBean getSecretary(int index) {
      return secretary_.get(index);
    }
    /**
     * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonKeyValueStringBeanOrBuilder getSecretaryOrBuilder(
        int index) {
      return secretary_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (roleId_ != 0L) {
        output.writeInt64(1, roleId_);
      }
      if (robotCfgId_ != 0) {
        output.writeInt32(2, robotCfgId_);
      }
      for (int i = 0; i < secretary_.size(); i++) {
        output.writeMessage(3, secretary_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (roleId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, roleId_);
      }
      if (robotCfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, robotCfgId_);
      }
      for (int i = 0; i < secretary_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, secretary_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean other = (com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean) obj;

      if (getRoleId()
          != other.getRoleId()) return false;
      if (getRobotCfgId()
          != other.getRobotCfgId()) return false;
      if (!getSecretaryList()
          .equals(other.getSecretaryList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ROLEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoleId());
      hash = (37 * hash) + ROBOTCFGID_FIELD_NUMBER;
      hash = (53 * hash) + getRobotCfgId();
      if (getSecretaryCount() > 0) {
        hash = (37 * hash) + SECRETARY_FIELD_NUMBER;
        hash = (53 * hash) + getSecretaryList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code qiaoqian.QiaoQianFighterBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.QiaoQianFighterBean)
        com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianFighterBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianFighterBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.class, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSecretaryFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        roleId_ = 0L;

        robotCfgId_ = 0;

        if (secretaryBuilder_ == null) {
          secretary_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          secretaryBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianFighterBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean build() {
        com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean buildPartial() {
        com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean result = new com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean(this);
        int from_bitField0_ = bitField0_;
        result.roleId_ = roleId_;
        result.robotCfgId_ = robotCfgId_;
        if (secretaryBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            secretary_ = java.util.Collections.unmodifiableList(secretary_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.secretary_ = secretary_;
        } else {
          result.secretary_ = secretaryBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean other) {
        if (other == com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.getDefaultInstance()) return this;
        if (other.getRoleId() != 0L) {
          setRoleId(other.getRoleId());
        }
        if (other.getRobotCfgId() != 0) {
          setRobotCfgId(other.getRobotCfgId());
        }
        if (secretaryBuilder_ == null) {
          if (!other.secretary_.isEmpty()) {
            if (secretary_.isEmpty()) {
              secretary_ = other.secretary_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSecretaryIsMutable();
              secretary_.addAll(other.secretary_);
            }
            onChanged();
          }
        } else {
          if (!other.secretary_.isEmpty()) {
            if (secretaryBuilder_.isEmpty()) {
              secretaryBuilder_.dispose();
              secretaryBuilder_ = null;
              secretary_ = other.secretary_;
              bitField0_ = (bitField0_ & ~0x00000001);
              secretaryBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSecretaryFieldBuilder() : null;
            } else {
              secretaryBuilder_.addAllMessages(other.secretary_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long roleId_ ;
      /**
       * <code>int64 roleId = 1;</code>
       * @return The roleId.
       */
      @java.lang.Override
      public long getRoleId() {
        return roleId_;
      }
      /**
       * <code>int64 roleId = 1;</code>
       * @param value The roleId to set.
       * @return This builder for chaining.
       */
      public Builder setRoleId(long value) {
        
        roleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 roleId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRoleId() {
        
        roleId_ = 0L;
        onChanged();
        return this;
      }

      private int robotCfgId_ ;
      /**
       * <code>int32 robotCfgId = 2;</code>
       * @return The robotCfgId.
       */
      @java.lang.Override
      public int getRobotCfgId() {
        return robotCfgId_;
      }
      /**
       * <code>int32 robotCfgId = 2;</code>
       * @param value The robotCfgId to set.
       * @return This builder for chaining.
       */
      public Builder setRobotCfgId(int value) {
        
        robotCfgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 robotCfgId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRobotCfgId() {
        
        robotCfgId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueStringBean> secretary_ =
        java.util.Collections.emptyList();
      private void ensureSecretaryIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          secretary_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonKeyValueStringBean>(secretary_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueStringBean, com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueStringBeanOrBuilder> secretaryBuilder_;

      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueStringBean> getSecretaryList() {
        if (secretaryBuilder_ == null) {
          return java.util.Collections.unmodifiableList(secretary_);
        } else {
          return secretaryBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public int getSecretaryCount() {
        if (secretaryBuilder_ == null) {
          return secretary_.size();
        } else {
          return secretaryBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueStringBean getSecretary(int index) {
        if (secretaryBuilder_ == null) {
          return secretary_.get(index);
        } else {
          return secretaryBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public Builder setSecretary(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueStringBean value) {
        if (secretaryBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSecretaryIsMutable();
          secretary_.set(index, value);
          onChanged();
        } else {
          secretaryBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public Builder setSecretary(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder builderForValue) {
        if (secretaryBuilder_ == null) {
          ensureSecretaryIsMutable();
          secretary_.set(index, builderForValue.build());
          onChanged();
        } else {
          secretaryBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public Builder addSecretary(com.sh.game.protos.AbcProtos.CommonKeyValueStringBean value) {
        if (secretaryBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSecretaryIsMutable();
          secretary_.add(value);
          onChanged();
        } else {
          secretaryBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public Builder addSecretary(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueStringBean value) {
        if (secretaryBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSecretaryIsMutable();
          secretary_.add(index, value);
          onChanged();
        } else {
          secretaryBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public Builder addSecretary(
          com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder builderForValue) {
        if (secretaryBuilder_ == null) {
          ensureSecretaryIsMutable();
          secretary_.add(builderForValue.build());
          onChanged();
        } else {
          secretaryBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public Builder addSecretary(
          int index, com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder builderForValue) {
        if (secretaryBuilder_ == null) {
          ensureSecretaryIsMutable();
          secretary_.add(index, builderForValue.build());
          onChanged();
        } else {
          secretaryBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public Builder addAllSecretary(
          java.lang.Iterable<? extends com.sh.game.protos.AbcProtos.CommonKeyValueStringBean> values) {
        if (secretaryBuilder_ == null) {
          ensureSecretaryIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, secretary_);
          onChanged();
        } else {
          secretaryBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public Builder clearSecretary() {
        if (secretaryBuilder_ == null) {
          secretary_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          secretaryBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public Builder removeSecretary(int index) {
        if (secretaryBuilder_ == null) {
          ensureSecretaryIsMutable();
          secretary_.remove(index);
          onChanged();
        } else {
          secretaryBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder getSecretaryBuilder(
          int index) {
        return getSecretaryFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueStringBeanOrBuilder getSecretaryOrBuilder(
          int index) {
        if (secretaryBuilder_ == null) {
          return secretary_.get(index);  } else {
          return secretaryBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonKeyValueStringBeanOrBuilder> 
           getSecretaryOrBuilderList() {
        if (secretaryBuilder_ != null) {
          return secretaryBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(secretary_);
        }
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder addSecretaryBuilder() {
        return getSecretaryFieldBuilder().addBuilder(
            com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder addSecretaryBuilder(
          int index) {
        return getSecretaryFieldBuilder().addBuilder(
            index, com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonKeyValueStringBean secretary = 3;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder> 
           getSecretaryBuilderList() {
        return getSecretaryFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonKeyValueStringBean, com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueStringBeanOrBuilder> 
          getSecretaryFieldBuilder() {
        if (secretaryBuilder_ == null) {
          secretaryBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonKeyValueStringBean, com.sh.game.protos.AbcProtos.CommonKeyValueStringBean.Builder, com.sh.game.protos.AbcProtos.CommonKeyValueStringBeanOrBuilder>(
                  secretary_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          secretary_ = null;
        }
        return secretaryBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.QiaoQianFighterBean)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.QiaoQianFighterBean)
    private static final com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean();
    }

    public static com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QiaoQianFighterBean>
        PARSER = new com.google.protobuf.AbstractParser<QiaoQianFighterBean>() {
      @java.lang.Override
      public QiaoQianFighterBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QiaoQianFighterBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QiaoQianFighterBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QiaoQianFighterBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QiaoQianMemberBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.QiaoQianMemberBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 roleId = 1;</code>
     * @return The roleId.
     */
    long getRoleId();

    /**
     * <code>int32 robotCfgId = 2;</code>
     * @return The robotCfgId.
     */
    int getRobotCfgId();

    /**
     * <code>int32 score = 3;</code>
     * @return The score.
     */
    int getScore();

    /**
     * <code>string income = 4;</code>
     * @return The income.
     */
    java.lang.String getIncome();
    /**
     * <code>string income = 4;</code>
     * @return The bytes for income.
     */
    com.google.protobuf.ByteString
        getIncomeBytes();

    /**
     * <code>string name = 5;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 5;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>int32 level = 6;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> 
        getFashionsList();
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    com.sh.game.protos.AbcProtos.CommonSlotBean getFashions(int index);
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    int getFashionsCount();
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    java.util.List<? extends com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> 
        getFashionsOrBuilderList();
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder getFashionsOrBuilder(
        int index);

    /**
     * <pre>
     *每秒收益(修为) 
     * </pre>
     *
     * <code>string secondIncome = 8;</code>
     * @return The secondIncome.
     */
    java.lang.String getSecondIncome();
    /**
     * <pre>
     *每秒收益(修为) 
     * </pre>
     *
     * <code>string secondIncome = 8;</code>
     * @return The bytes for secondIncome.
     */
    com.google.protobuf.ByteString
        getSecondIncomeBytes();
  }
  /**
   * Protobuf type {@code qiaoqian.QiaoQianMemberBean}
   */
  public static final class QiaoQianMemberBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.QiaoQianMemberBean)
      QiaoQianMemberBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QiaoQianMemberBean.newBuilder() to construct.
    private QiaoQianMemberBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QiaoQianMemberBean() {
      income_ = "";
      name_ = "";
      fashions_ = java.util.Collections.emptyList();
      secondIncome_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QiaoQianMemberBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QiaoQianMemberBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              roleId_ = input.readInt64();
              break;
            }
            case 16: {

              robotCfgId_ = input.readInt32();
              break;
            }
            case 24: {

              score_ = input.readInt32();
              break;
            }
            case 34: {
              java.lang.String s = input.readStringRequireUtf8();

              income_ = s;
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 48: {

              level_ = input.readInt32();
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                fashions_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonSlotBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              fashions_.add(
                  input.readMessage(com.sh.game.protos.AbcProtos.CommonSlotBean.parser(), extensionRegistry));
              break;
            }
            case 66: {
              java.lang.String s = input.readStringRequireUtf8();

              secondIncome_ = s;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          fashions_ = java.util.Collections.unmodifiableList(fashions_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianMemberBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianMemberBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.class, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder.class);
    }

    public static final int ROLEID_FIELD_NUMBER = 1;
    private long roleId_;
    /**
     * <code>int64 roleId = 1;</code>
     * @return The roleId.
     */
    @java.lang.Override
    public long getRoleId() {
      return roleId_;
    }

    public static final int ROBOTCFGID_FIELD_NUMBER = 2;
    private int robotCfgId_;
    /**
     * <code>int32 robotCfgId = 2;</code>
     * @return The robotCfgId.
     */
    @java.lang.Override
    public int getRobotCfgId() {
      return robotCfgId_;
    }

    public static final int SCORE_FIELD_NUMBER = 3;
    private int score_;
    /**
     * <code>int32 score = 3;</code>
     * @return The score.
     */
    @java.lang.Override
    public int getScore() {
      return score_;
    }

    public static final int INCOME_FIELD_NUMBER = 4;
    private volatile java.lang.Object income_;
    /**
     * <code>string income = 4;</code>
     * @return The income.
     */
    @java.lang.Override
    public java.lang.String getIncome() {
      java.lang.Object ref = income_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        income_ = s;
        return s;
      }
    }
    /**
     * <code>string income = 4;</code>
     * @return The bytes for income.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getIncomeBytes() {
      java.lang.Object ref = income_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        income_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 5;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 5;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LEVEL_FIELD_NUMBER = 6;
    private int level_;
    /**
     * <code>int32 level = 6;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int FASHIONS_FIELD_NUMBER = 7;
    private java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> fashions_;
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> getFashionsList() {
      return fashions_;
    }
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> 
        getFashionsOrBuilderList() {
      return fashions_;
    }
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public int getFashionsCount() {
      return fashions_.size();
    }
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonSlotBean getFashions(int index) {
      return fashions_.get(index);
    }
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder getFashionsOrBuilder(
        int index) {
      return fashions_.get(index);
    }

    public static final int SECONDINCOME_FIELD_NUMBER = 8;
    private volatile java.lang.Object secondIncome_;
    /**
     * <pre>
     *每秒收益(修为) 
     * </pre>
     *
     * <code>string secondIncome = 8;</code>
     * @return The secondIncome.
     */
    @java.lang.Override
    public java.lang.String getSecondIncome() {
      java.lang.Object ref = secondIncome_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        secondIncome_ = s;
        return s;
      }
    }
    /**
     * <pre>
     *每秒收益(修为) 
     * </pre>
     *
     * <code>string secondIncome = 8;</code>
     * @return The bytes for secondIncome.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSecondIncomeBytes() {
      java.lang.Object ref = secondIncome_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        secondIncome_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (roleId_ != 0L) {
        output.writeInt64(1, roleId_);
      }
      if (robotCfgId_ != 0) {
        output.writeInt32(2, robotCfgId_);
      }
      if (score_ != 0) {
        output.writeInt32(3, score_);
      }
      if (!getIncomeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, income_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, name_);
      }
      if (level_ != 0) {
        output.writeInt32(6, level_);
      }
      for (int i = 0; i < fashions_.size(); i++) {
        output.writeMessage(7, fashions_.get(i));
      }
      if (!getSecondIncomeBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, secondIncome_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (roleId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, roleId_);
      }
      if (robotCfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, robotCfgId_);
      }
      if (score_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, score_);
      }
      if (!getIncomeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, income_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, name_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, level_);
      }
      for (int i = 0; i < fashions_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, fashions_.get(i));
      }
      if (!getSecondIncomeBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, secondIncome_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean other = (com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean) obj;

      if (getRoleId()
          != other.getRoleId()) return false;
      if (getRobotCfgId()
          != other.getRobotCfgId()) return false;
      if (getScore()
          != other.getScore()) return false;
      if (!getIncome()
          .equals(other.getIncome())) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (!getFashionsList()
          .equals(other.getFashionsList())) return false;
      if (!getSecondIncome()
          .equals(other.getSecondIncome())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ROLEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoleId());
      hash = (37 * hash) + ROBOTCFGID_FIELD_NUMBER;
      hash = (53 * hash) + getRobotCfgId();
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore();
      hash = (37 * hash) + INCOME_FIELD_NUMBER;
      hash = (53 * hash) + getIncome().hashCode();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      if (getFashionsCount() > 0) {
        hash = (37 * hash) + FASHIONS_FIELD_NUMBER;
        hash = (53 * hash) + getFashionsList().hashCode();
      }
      hash = (37 * hash) + SECONDINCOME_FIELD_NUMBER;
      hash = (53 * hash) + getSecondIncome().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code qiaoqian.QiaoQianMemberBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.QiaoQianMemberBean)
        com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianMemberBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianMemberBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.class, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getFashionsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        roleId_ = 0L;

        robotCfgId_ = 0;

        score_ = 0;

        income_ = "";

        name_ = "";

        level_ = 0;

        if (fashionsBuilder_ == null) {
          fashions_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          fashionsBuilder_.clear();
        }
        secondIncome_ = "";

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianMemberBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean build() {
        com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean buildPartial() {
        com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean result = new com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean(this);
        int from_bitField0_ = bitField0_;
        result.roleId_ = roleId_;
        result.robotCfgId_ = robotCfgId_;
        result.score_ = score_;
        result.income_ = income_;
        result.name_ = name_;
        result.level_ = level_;
        if (fashionsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            fashions_ = java.util.Collections.unmodifiableList(fashions_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.fashions_ = fashions_;
        } else {
          result.fashions_ = fashionsBuilder_.build();
        }
        result.secondIncome_ = secondIncome_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean other) {
        if (other == com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.getDefaultInstance()) return this;
        if (other.getRoleId() != 0L) {
          setRoleId(other.getRoleId());
        }
        if (other.getRobotCfgId() != 0) {
          setRobotCfgId(other.getRobotCfgId());
        }
        if (other.getScore() != 0) {
          setScore(other.getScore());
        }
        if (!other.getIncome().isEmpty()) {
          income_ = other.income_;
          onChanged();
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (fashionsBuilder_ == null) {
          if (!other.fashions_.isEmpty()) {
            if (fashions_.isEmpty()) {
              fashions_ = other.fashions_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureFashionsIsMutable();
              fashions_.addAll(other.fashions_);
            }
            onChanged();
          }
        } else {
          if (!other.fashions_.isEmpty()) {
            if (fashionsBuilder_.isEmpty()) {
              fashionsBuilder_.dispose();
              fashionsBuilder_ = null;
              fashions_ = other.fashions_;
              bitField0_ = (bitField0_ & ~0x00000001);
              fashionsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getFashionsFieldBuilder() : null;
            } else {
              fashionsBuilder_.addAllMessages(other.fashions_);
            }
          }
        }
        if (!other.getSecondIncome().isEmpty()) {
          secondIncome_ = other.secondIncome_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long roleId_ ;
      /**
       * <code>int64 roleId = 1;</code>
       * @return The roleId.
       */
      @java.lang.Override
      public long getRoleId() {
        return roleId_;
      }
      /**
       * <code>int64 roleId = 1;</code>
       * @param value The roleId to set.
       * @return This builder for chaining.
       */
      public Builder setRoleId(long value) {
        
        roleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 roleId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRoleId() {
        
        roleId_ = 0L;
        onChanged();
        return this;
      }

      private int robotCfgId_ ;
      /**
       * <code>int32 robotCfgId = 2;</code>
       * @return The robotCfgId.
       */
      @java.lang.Override
      public int getRobotCfgId() {
        return robotCfgId_;
      }
      /**
       * <code>int32 robotCfgId = 2;</code>
       * @param value The robotCfgId to set.
       * @return This builder for chaining.
       */
      public Builder setRobotCfgId(int value) {
        
        robotCfgId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 robotCfgId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRobotCfgId() {
        
        robotCfgId_ = 0;
        onChanged();
        return this;
      }

      private int score_ ;
      /**
       * <code>int32 score = 3;</code>
       * @return The score.
       */
      @java.lang.Override
      public int getScore() {
        return score_;
      }
      /**
       * <code>int32 score = 3;</code>
       * @param value The score to set.
       * @return This builder for chaining.
       */
      public Builder setScore(int value) {
        
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 score = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearScore() {
        
        score_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object income_ = "";
      /**
       * <code>string income = 4;</code>
       * @return The income.
       */
      public java.lang.String getIncome() {
        java.lang.Object ref = income_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          income_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string income = 4;</code>
       * @return The bytes for income.
       */
      public com.google.protobuf.ByteString
          getIncomeBytes() {
        java.lang.Object ref = income_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          income_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string income = 4;</code>
       * @param value The income to set.
       * @return This builder for chaining.
       */
      public Builder setIncome(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        income_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string income = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIncome() {
        
        income_ = getDefaultInstance().getIncome();
        onChanged();
        return this;
      }
      /**
       * <code>string income = 4;</code>
       * @param value The bytes for income to set.
       * @return This builder for chaining.
       */
      public Builder setIncomeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        income_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 5;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 5;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 5;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 5;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <code>int32 level = 6;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <code>int32 level = 6;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 level = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> fashions_ =
        java.util.Collections.emptyList();
      private void ensureFashionsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          fashions_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonSlotBean>(fashions_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonSlotBean, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder, com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> fashionsBuilder_;

      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> getFashionsList() {
        if (fashionsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(fashions_);
        } else {
          return fashionsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public int getFashionsCount() {
        if (fashionsBuilder_ == null) {
          return fashions_.size();
        } else {
          return fashionsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBean getFashions(int index) {
        if (fashionsBuilder_ == null) {
          return fashions_.get(index);
        } else {
          return fashionsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder setFashions(
          int index, com.sh.game.protos.AbcProtos.CommonSlotBean value) {
        if (fashionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFashionsIsMutable();
          fashions_.set(index, value);
          onChanged();
        } else {
          fashionsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder setFashions(
          int index, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder builderForValue) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          fashions_.set(index, builderForValue.build());
          onChanged();
        } else {
          fashionsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addFashions(com.sh.game.protos.AbcProtos.CommonSlotBean value) {
        if (fashionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFashionsIsMutable();
          fashions_.add(value);
          onChanged();
        } else {
          fashionsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addFashions(
          int index, com.sh.game.protos.AbcProtos.CommonSlotBean value) {
        if (fashionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFashionsIsMutable();
          fashions_.add(index, value);
          onChanged();
        } else {
          fashionsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addFashions(
          com.sh.game.protos.AbcProtos.CommonSlotBean.Builder builderForValue) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          fashions_.add(builderForValue.build());
          onChanged();
        } else {
          fashionsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addFashions(
          int index, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder builderForValue) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          fashions_.add(index, builderForValue.build());
          onChanged();
        } else {
          fashionsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addAllFashions(
          java.lang.Iterable<? extends com.sh.game.protos.AbcProtos.CommonSlotBean> values) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, fashions_);
          onChanged();
        } else {
          fashionsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder clearFashions() {
        if (fashionsBuilder_ == null) {
          fashions_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          fashionsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder removeFashions(int index) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          fashions_.remove(index);
          onChanged();
        } else {
          fashionsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBean.Builder getFashionsBuilder(
          int index) {
        return getFashionsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder getFashionsOrBuilder(
          int index) {
        if (fashionsBuilder_ == null) {
          return fashions_.get(index);  } else {
          return fashionsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> 
           getFashionsOrBuilderList() {
        if (fashionsBuilder_ != null) {
          return fashionsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(fashions_);
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBean.Builder addFashionsBuilder() {
        return getFashionsFieldBuilder().addBuilder(
            com.sh.game.protos.AbcProtos.CommonSlotBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBean.Builder addFashionsBuilder(
          int index) {
        return getFashionsFieldBuilder().addBuilder(
            index, com.sh.game.protos.AbcProtos.CommonSlotBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean.Builder> 
           getFashionsBuilderList() {
        return getFashionsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonSlotBean, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder, com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> 
          getFashionsFieldBuilder() {
        if (fashionsBuilder_ == null) {
          fashionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonSlotBean, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder, com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder>(
                  fashions_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          fashions_ = null;
        }
        return fashionsBuilder_;
      }

      private java.lang.Object secondIncome_ = "";
      /**
       * <pre>
       *每秒收益(修为) 
       * </pre>
       *
       * <code>string secondIncome = 8;</code>
       * @return The secondIncome.
       */
      public java.lang.String getSecondIncome() {
        java.lang.Object ref = secondIncome_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          secondIncome_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       *每秒收益(修为) 
       * </pre>
       *
       * <code>string secondIncome = 8;</code>
       * @return The bytes for secondIncome.
       */
      public com.google.protobuf.ByteString
          getSecondIncomeBytes() {
        java.lang.Object ref = secondIncome_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          secondIncome_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       *每秒收益(修为) 
       * </pre>
       *
       * <code>string secondIncome = 8;</code>
       * @param value The secondIncome to set.
       * @return This builder for chaining.
       */
      public Builder setSecondIncome(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        secondIncome_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *每秒收益(修为) 
       * </pre>
       *
       * <code>string secondIncome = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearSecondIncome() {
        
        secondIncome_ = getDefaultInstance().getSecondIncome();
        onChanged();
        return this;
      }
      /**
       * <pre>
       *每秒收益(修为) 
       * </pre>
       *
       * <code>string secondIncome = 8;</code>
       * @param value The bytes for secondIncome to set.
       * @return This builder for chaining.
       */
      public Builder setSecondIncomeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        secondIncome_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.QiaoQianMemberBean)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.QiaoQianMemberBean)
    private static final com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean();
    }

    public static com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QiaoQianMemberBean>
        PARSER = new com.google.protobuf.AbstractParser<QiaoQianMemberBean>() {
      @java.lang.Override
      public QiaoQianMemberBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QiaoQianMemberBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QiaoQianMemberBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QiaoQianMemberBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QiaoQianLogBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.QiaoQianLogBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 roleId = 1;</code>
     * @return The roleId.
     */
    long getRoleId();

    /**
     * <code>int32 score = 2;</code>
     * @return The score.
     */
    int getScore();

    /**
     * <code>int32 newScore = 3;</code>
     * @return The newScore.
     */
    int getNewScore();

    /**
     * <code>int32 time = 4;</code>
     * @return The time.
     */
    int getTime();

    /**
     * <code>string name = 5;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>string name = 5;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>int32 level = 6;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> 
        getFashionsList();
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    com.sh.game.protos.AbcProtos.CommonSlotBean getFashions(int index);
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    int getFashionsCount();
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    java.util.List<? extends com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> 
        getFashionsOrBuilderList();
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder getFashionsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code qiaoqian.QiaoQianLogBean}
   */
  public static final class QiaoQianLogBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.QiaoQianLogBean)
      QiaoQianLogBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QiaoQianLogBean.newBuilder() to construct.
    private QiaoQianLogBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QiaoQianLogBean() {
      name_ = "";
      fashions_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QiaoQianLogBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QiaoQianLogBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              roleId_ = input.readInt64();
              break;
            }
            case 16: {

              score_ = input.readInt32();
              break;
            }
            case 24: {

              newScore_ = input.readInt32();
              break;
            }
            case 32: {

              time_ = input.readInt32();
              break;
            }
            case 42: {
              java.lang.String s = input.readStringRequireUtf8();

              name_ = s;
              break;
            }
            case 48: {

              level_ = input.readInt32();
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                fashions_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonSlotBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              fashions_.add(
                  input.readMessage(com.sh.game.protos.AbcProtos.CommonSlotBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          fashions_ = java.util.Collections.unmodifiableList(fashions_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianLogBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianLogBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.class, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder.class);
    }

    public static final int ROLEID_FIELD_NUMBER = 1;
    private long roleId_;
    /**
     * <code>int64 roleId = 1;</code>
     * @return The roleId.
     */
    @java.lang.Override
    public long getRoleId() {
      return roleId_;
    }

    public static final int SCORE_FIELD_NUMBER = 2;
    private int score_;
    /**
     * <code>int32 score = 2;</code>
     * @return The score.
     */
    @java.lang.Override
    public int getScore() {
      return score_;
    }

    public static final int NEWSCORE_FIELD_NUMBER = 3;
    private int newScore_;
    /**
     * <code>int32 newScore = 3;</code>
     * @return The newScore.
     */
    @java.lang.Override
    public int getNewScore() {
      return newScore_;
    }

    public static final int TIME_FIELD_NUMBER = 4;
    private int time_;
    /**
     * <code>int32 time = 4;</code>
     * @return The time.
     */
    @java.lang.Override
    public int getTime() {
      return time_;
    }

    public static final int NAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object name_;
    /**
     * <code>string name = 5;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        name_ = s;
        return s;
      }
    }
    /**
     * <code>string name = 5;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LEVEL_FIELD_NUMBER = 6;
    private int level_;
    /**
     * <code>int32 level = 6;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int FASHIONS_FIELD_NUMBER = 7;
    private java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> fashions_;
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> getFashionsList() {
      return fashions_;
    }
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> 
        getFashionsOrBuilderList() {
      return fashions_;
    }
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public int getFashionsCount() {
      return fashions_.size();
    }
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonSlotBean getFashions(int index) {
      return fashions_.get(index);
    }
    /**
     * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder getFashionsOrBuilder(
        int index) {
      return fashions_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (roleId_ != 0L) {
        output.writeInt64(1, roleId_);
      }
      if (score_ != 0) {
        output.writeInt32(2, score_);
      }
      if (newScore_ != 0) {
        output.writeInt32(3, newScore_);
      }
      if (time_ != 0) {
        output.writeInt32(4, time_);
      }
      if (!getNameBytes().isEmpty()) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, name_);
      }
      if (level_ != 0) {
        output.writeInt32(6, level_);
      }
      for (int i = 0; i < fashions_.size(); i++) {
        output.writeMessage(7, fashions_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (roleId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, roleId_);
      }
      if (score_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, score_);
      }
      if (newScore_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, newScore_);
      }
      if (time_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, time_);
      }
      if (!getNameBytes().isEmpty()) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, name_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, level_);
      }
      for (int i = 0; i < fashions_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, fashions_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean other = (com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean) obj;

      if (getRoleId()
          != other.getRoleId()) return false;
      if (getScore()
          != other.getScore()) return false;
      if (getNewScore()
          != other.getNewScore()) return false;
      if (getTime()
          != other.getTime()) return false;
      if (!getName()
          .equals(other.getName())) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (!getFashionsList()
          .equals(other.getFashionsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ROLEID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRoleId());
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore();
      hash = (37 * hash) + NEWSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getNewScore();
      hash = (37 * hash) + TIME_FIELD_NUMBER;
      hash = (53 * hash) + getTime();
      hash = (37 * hash) + NAME_FIELD_NUMBER;
      hash = (53 * hash) + getName().hashCode();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      if (getFashionsCount() > 0) {
        hash = (37 * hash) + FASHIONS_FIELD_NUMBER;
        hash = (53 * hash) + getFashionsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code qiaoqian.QiaoQianLogBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.QiaoQianLogBean)
        com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianLogBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianLogBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.class, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getFashionsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        roleId_ = 0L;

        score_ = 0;

        newScore_ = 0;

        time_ = 0;

        name_ = "";

        level_ = 0;

        if (fashionsBuilder_ == null) {
          fashions_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          fashionsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_QiaoQianLogBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean build() {
        com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean buildPartial() {
        com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean result = new com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean(this);
        int from_bitField0_ = bitField0_;
        result.roleId_ = roleId_;
        result.score_ = score_;
        result.newScore_ = newScore_;
        result.time_ = time_;
        result.name_ = name_;
        result.level_ = level_;
        if (fashionsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            fashions_ = java.util.Collections.unmodifiableList(fashions_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.fashions_ = fashions_;
        } else {
          result.fashions_ = fashionsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean other) {
        if (other == com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.getDefaultInstance()) return this;
        if (other.getRoleId() != 0L) {
          setRoleId(other.getRoleId());
        }
        if (other.getScore() != 0) {
          setScore(other.getScore());
        }
        if (other.getNewScore() != 0) {
          setNewScore(other.getNewScore());
        }
        if (other.getTime() != 0) {
          setTime(other.getTime());
        }
        if (!other.getName().isEmpty()) {
          name_ = other.name_;
          onChanged();
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (fashionsBuilder_ == null) {
          if (!other.fashions_.isEmpty()) {
            if (fashions_.isEmpty()) {
              fashions_ = other.fashions_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureFashionsIsMutable();
              fashions_.addAll(other.fashions_);
            }
            onChanged();
          }
        } else {
          if (!other.fashions_.isEmpty()) {
            if (fashionsBuilder_.isEmpty()) {
              fashionsBuilder_.dispose();
              fashionsBuilder_ = null;
              fashions_ = other.fashions_;
              bitField0_ = (bitField0_ & ~0x00000001);
              fashionsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getFashionsFieldBuilder() : null;
            } else {
              fashionsBuilder_.addAllMessages(other.fashions_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long roleId_ ;
      /**
       * <code>int64 roleId = 1;</code>
       * @return The roleId.
       */
      @java.lang.Override
      public long getRoleId() {
        return roleId_;
      }
      /**
       * <code>int64 roleId = 1;</code>
       * @param value The roleId to set.
       * @return This builder for chaining.
       */
      public Builder setRoleId(long value) {
        
        roleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 roleId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRoleId() {
        
        roleId_ = 0L;
        onChanged();
        return this;
      }

      private int score_ ;
      /**
       * <code>int32 score = 2;</code>
       * @return The score.
       */
      @java.lang.Override
      public int getScore() {
        return score_;
      }
      /**
       * <code>int32 score = 2;</code>
       * @param value The score to set.
       * @return This builder for chaining.
       */
      public Builder setScore(int value) {
        
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 score = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearScore() {
        
        score_ = 0;
        onChanged();
        return this;
      }

      private int newScore_ ;
      /**
       * <code>int32 newScore = 3;</code>
       * @return The newScore.
       */
      @java.lang.Override
      public int getNewScore() {
        return newScore_;
      }
      /**
       * <code>int32 newScore = 3;</code>
       * @param value The newScore to set.
       * @return This builder for chaining.
       */
      public Builder setNewScore(int value) {
        
        newScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 newScore = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewScore() {
        
        newScore_ = 0;
        onChanged();
        return this;
      }

      private int time_ ;
      /**
       * <code>int32 time = 4;</code>
       * @return The time.
       */
      @java.lang.Override
      public int getTime() {
        return time_;
      }
      /**
       * <code>int32 time = 4;</code>
       * @param value The time to set.
       * @return This builder for chaining.
       */
      public Builder setTime(int value) {
        
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 time = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTime() {
        
        time_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>string name = 5;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string name = 5;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string name = 5;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>string name = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>string name = 5;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
        
        name_ = value;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <code>int32 level = 6;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <code>int32 level = 6;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 level = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> fashions_ =
        java.util.Collections.emptyList();
      private void ensureFashionsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          fashions_ = new java.util.ArrayList<com.sh.game.protos.AbcProtos.CommonSlotBean>(fashions_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonSlotBean, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder, com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> fashionsBuilder_;

      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean> getFashionsList() {
        if (fashionsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(fashions_);
        } else {
          return fashionsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public int getFashionsCount() {
        if (fashionsBuilder_ == null) {
          return fashions_.size();
        } else {
          return fashionsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBean getFashions(int index) {
        if (fashionsBuilder_ == null) {
          return fashions_.get(index);
        } else {
          return fashionsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder setFashions(
          int index, com.sh.game.protos.AbcProtos.CommonSlotBean value) {
        if (fashionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFashionsIsMutable();
          fashions_.set(index, value);
          onChanged();
        } else {
          fashionsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder setFashions(
          int index, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder builderForValue) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          fashions_.set(index, builderForValue.build());
          onChanged();
        } else {
          fashionsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addFashions(com.sh.game.protos.AbcProtos.CommonSlotBean value) {
        if (fashionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFashionsIsMutable();
          fashions_.add(value);
          onChanged();
        } else {
          fashionsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addFashions(
          int index, com.sh.game.protos.AbcProtos.CommonSlotBean value) {
        if (fashionsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFashionsIsMutable();
          fashions_.add(index, value);
          onChanged();
        } else {
          fashionsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addFashions(
          com.sh.game.protos.AbcProtos.CommonSlotBean.Builder builderForValue) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          fashions_.add(builderForValue.build());
          onChanged();
        } else {
          fashionsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addFashions(
          int index, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder builderForValue) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          fashions_.add(index, builderForValue.build());
          onChanged();
        } else {
          fashionsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder addAllFashions(
          java.lang.Iterable<? extends com.sh.game.protos.AbcProtos.CommonSlotBean> values) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, fashions_);
          onChanged();
        } else {
          fashionsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder clearFashions() {
        if (fashionsBuilder_ == null) {
          fashions_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          fashionsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public Builder removeFashions(int index) {
        if (fashionsBuilder_ == null) {
          ensureFashionsIsMutable();
          fashions_.remove(index);
          onChanged();
        } else {
          fashionsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBean.Builder getFashionsBuilder(
          int index) {
        return getFashionsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder getFashionsOrBuilder(
          int index) {
        if (fashionsBuilder_ == null) {
          return fashions_.get(index);  } else {
          return fashionsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public java.util.List<? extends com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> 
           getFashionsOrBuilderList() {
        if (fashionsBuilder_ != null) {
          return fashionsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(fashions_);
        }
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBean.Builder addFashionsBuilder() {
        return getFashionsFieldBuilder().addBuilder(
            com.sh.game.protos.AbcProtos.CommonSlotBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public com.sh.game.protos.AbcProtos.CommonSlotBean.Builder addFashionsBuilder(
          int index) {
        return getFashionsFieldBuilder().addBuilder(
            index, com.sh.game.protos.AbcProtos.CommonSlotBean.getDefaultInstance());
      }
      /**
       * <code>repeated .abc.CommonSlotBean fashions = 7;</code>
       */
      public java.util.List<com.sh.game.protos.AbcProtos.CommonSlotBean.Builder> 
           getFashionsBuilderList() {
        return getFashionsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.AbcProtos.CommonSlotBean, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder, com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder> 
          getFashionsFieldBuilder() {
        if (fashionsBuilder_ == null) {
          fashionsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.AbcProtos.CommonSlotBean, com.sh.game.protos.AbcProtos.CommonSlotBean.Builder, com.sh.game.protos.AbcProtos.CommonSlotBeanOrBuilder>(
                  fashions_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          fashions_ = null;
        }
        return fashionsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.QiaoQianLogBean)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.QiaoQianLogBean)
    private static final com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean();
    }

    public static com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<QiaoQianLogBean>
        PARSER = new com.google.protobuf.AbstractParser<QiaoQianLogBean>() {
      @java.lang.Override
      public QiaoQianLogBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QiaoQianLogBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QiaoQianLogBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QiaoQianLogBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqQiaoQianInfoMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.ReqQiaoQianInfoMessage)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqQiaoQianInfoMessage' id='1' desc='请求乔迁信息' 
   * </pre>
   *
   * Protobuf type {@code qiaoqian.ReqQiaoQianInfoMessage}
   */
  public static final class ReqQiaoQianInfoMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.ReqQiaoQianInfoMessage)
      ReqQiaoQianInfoMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqQiaoQianInfoMessage.newBuilder() to construct.
    private ReqQiaoQianInfoMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqQiaoQianInfoMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqQiaoQianInfoMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqQiaoQianInfoMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianInfoMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianInfoMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage other = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqQiaoQianInfoMessage' id='1' desc='请求乔迁信息' 
     * </pre>
     *
     * Protobuf type {@code qiaoqian.ReqQiaoQianInfoMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.ReqQiaoQianInfoMessage)
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianInfoMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianInfoMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianInfoMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage build() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage buildPartial() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage result = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage other) {
        if (other == com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.ReqQiaoQianInfoMessage)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.ReqQiaoQianInfoMessage)
    private static final com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage();
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqQiaoQianInfoMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqQiaoQianInfoMessage>() {
      @java.lang.Override
      public ReqQiaoQianInfoMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqQiaoQianInfoMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqQiaoQianInfoMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqQiaoQianInfoMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianInfoMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResQiaoQianInfoMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.ResQiaoQianInfoMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 score = 1;</code>
     * @return The score.
     */
    int getScore();

    /**
     * <code>repeated int32 dianZans = 2;</code>
     * @return A list containing the dianZans.
     */
    java.util.List<java.lang.Integer> getDianZansList();
    /**
     * <code>repeated int32 dianZans = 2;</code>
     * @return The count of dianZans.
     */
    int getDianZansCount();
    /**
     * <code>repeated int32 dianZans = 2;</code>
     * @param index The index of the element to return.
     * @return The dianZans at the given index.
     */
    int getDianZans(int index);

    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> 
        getChallengesList();
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean getChallenges(int index);
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    int getChallengesCount();
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    java.util.List<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> 
        getChallengesOrBuilderList();
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder getChallengesOrBuilder(
        int index);

    /**
     * <code>int32 hisMaxShouGouIndex = 4;</code>
     * @return The hisMaxShouGouIndex.
     */
    int getHisMaxShouGouIndex();
  }
  /**
   * <pre>
   ** class='ResQiaoQianInfoMessage' id='2' desc='返回乔迁信息' 
   * </pre>
   *
   * Protobuf type {@code qiaoqian.ResQiaoQianInfoMessage}
   */
  public static final class ResQiaoQianInfoMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.ResQiaoQianInfoMessage)
      ResQiaoQianInfoMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResQiaoQianInfoMessage.newBuilder() to construct.
    private ResQiaoQianInfoMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResQiaoQianInfoMessage() {
      dianZans_ = emptyIntList();
      challenges_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResQiaoQianInfoMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResQiaoQianInfoMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              score_ = input.readInt32();
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                dianZans_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              dianZans_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                dianZans_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                dianZans_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                challenges_ = new java.util.ArrayList<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean>();
                mutable_bitField0_ |= 0x00000002;
              }
              challenges_.add(
                  input.readMessage(com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.parser(), extensionRegistry));
              break;
            }
            case 32: {

              hisMaxShouGouIndex_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          dianZans_.makeImmutable(); // C
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          challenges_ = java.util.Collections.unmodifiableList(challenges_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianInfoMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianInfoMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage.class, com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage.Builder.class);
    }

    public static final int SCORE_FIELD_NUMBER = 1;
    private int score_;
    /**
     * <code>int32 score = 1;</code>
     * @return The score.
     */
    @java.lang.Override
    public int getScore() {
      return score_;
    }

    public static final int DIANZANS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList dianZans_;
    /**
     * <code>repeated int32 dianZans = 2;</code>
     * @return A list containing the dianZans.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDianZansList() {
      return dianZans_;
    }
    /**
     * <code>repeated int32 dianZans = 2;</code>
     * @return The count of dianZans.
     */
    public int getDianZansCount() {
      return dianZans_.size();
    }
    /**
     * <code>repeated int32 dianZans = 2;</code>
     * @param index The index of the element to return.
     * @return The dianZans at the given index.
     */
    public int getDianZans(int index) {
      return dianZans_.getInt(index);
    }
    private int dianZansMemoizedSerializedSize = -1;

    public static final int CHALLENGES_FIELD_NUMBER = 3;
    private java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> challenges_;
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> getChallengesList() {
      return challenges_;
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> 
        getChallengesOrBuilderList() {
      return challenges_;
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    @java.lang.Override
    public int getChallengesCount() {
      return challenges_.size();
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean getChallenges(int index) {
      return challenges_.get(index);
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder getChallengesOrBuilder(
        int index) {
      return challenges_.get(index);
    }

    public static final int HISMAXSHOUGOUINDEX_FIELD_NUMBER = 4;
    private int hisMaxShouGouIndex_;
    /**
     * <code>int32 hisMaxShouGouIndex = 4;</code>
     * @return The hisMaxShouGouIndex.
     */
    @java.lang.Override
    public int getHisMaxShouGouIndex() {
      return hisMaxShouGouIndex_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (score_ != 0) {
        output.writeInt32(1, score_);
      }
      if (getDianZansList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(dianZansMemoizedSerializedSize);
      }
      for (int i = 0; i < dianZans_.size(); i++) {
        output.writeInt32NoTag(dianZans_.getInt(i));
      }
      for (int i = 0; i < challenges_.size(); i++) {
        output.writeMessage(3, challenges_.get(i));
      }
      if (hisMaxShouGouIndex_ != 0) {
        output.writeInt32(4, hisMaxShouGouIndex_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (score_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, score_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < dianZans_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dianZans_.getInt(i));
        }
        size += dataSize;
        if (!getDianZansList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        dianZansMemoizedSerializedSize = dataSize;
      }
      for (int i = 0; i < challenges_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, challenges_.get(i));
      }
      if (hisMaxShouGouIndex_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, hisMaxShouGouIndex_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage other = (com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage) obj;

      if (getScore()
          != other.getScore()) return false;
      if (!getDianZansList()
          .equals(other.getDianZansList())) return false;
      if (!getChallengesList()
          .equals(other.getChallengesList())) return false;
      if (getHisMaxShouGouIndex()
          != other.getHisMaxShouGouIndex()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore();
      if (getDianZansCount() > 0) {
        hash = (37 * hash) + DIANZANS_FIELD_NUMBER;
        hash = (53 * hash) + getDianZansList().hashCode();
      }
      if (getChallengesCount() > 0) {
        hash = (37 * hash) + CHALLENGES_FIELD_NUMBER;
        hash = (53 * hash) + getChallengesList().hashCode();
      }
      hash = (37 * hash) + HISMAXSHOUGOUINDEX_FIELD_NUMBER;
      hash = (53 * hash) + getHisMaxShouGouIndex();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResQiaoQianInfoMessage' id='2' desc='返回乔迁信息' 
     * </pre>
     *
     * Protobuf type {@code qiaoqian.ResQiaoQianInfoMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.ResQiaoQianInfoMessage)
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianInfoMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianInfoMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage.class, com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChallengesFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        score_ = 0;

        dianZans_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        if (challengesBuilder_ == null) {
          challenges_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          challengesBuilder_.clear();
        }
        hisMaxShouGouIndex_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianInfoMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage build() {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage buildPartial() {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage result = new com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage(this);
        int from_bitField0_ = bitField0_;
        result.score_ = score_;
        if (((bitField0_ & 0x00000001) != 0)) {
          dianZans_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.dianZans_ = dianZans_;
        if (challengesBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            challenges_ = java.util.Collections.unmodifiableList(challenges_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.challenges_ = challenges_;
        } else {
          result.challenges_ = challengesBuilder_.build();
        }
        result.hisMaxShouGouIndex_ = hisMaxShouGouIndex_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage other) {
        if (other == com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage.getDefaultInstance()) return this;
        if (other.getScore() != 0) {
          setScore(other.getScore());
        }
        if (!other.dianZans_.isEmpty()) {
          if (dianZans_.isEmpty()) {
            dianZans_ = other.dianZans_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDianZansIsMutable();
            dianZans_.addAll(other.dianZans_);
          }
          onChanged();
        }
        if (challengesBuilder_ == null) {
          if (!other.challenges_.isEmpty()) {
            if (challenges_.isEmpty()) {
              challenges_ = other.challenges_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureChallengesIsMutable();
              challenges_.addAll(other.challenges_);
            }
            onChanged();
          }
        } else {
          if (!other.challenges_.isEmpty()) {
            if (challengesBuilder_.isEmpty()) {
              challengesBuilder_.dispose();
              challengesBuilder_ = null;
              challenges_ = other.challenges_;
              bitField0_ = (bitField0_ & ~0x00000002);
              challengesBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChallengesFieldBuilder() : null;
            } else {
              challengesBuilder_.addAllMessages(other.challenges_);
            }
          }
        }
        if (other.getHisMaxShouGouIndex() != 0) {
          setHisMaxShouGouIndex(other.getHisMaxShouGouIndex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int score_ ;
      /**
       * <code>int32 score = 1;</code>
       * @return The score.
       */
      @java.lang.Override
      public int getScore() {
        return score_;
      }
      /**
       * <code>int32 score = 1;</code>
       * @param value The score to set.
       * @return This builder for chaining.
       */
      public Builder setScore(int value) {
        
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 score = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearScore() {
        
        score_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList dianZans_ = emptyIntList();
      private void ensureDianZansIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          dianZans_ = mutableCopy(dianZans_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int32 dianZans = 2;</code>
       * @return A list containing the dianZans.
       */
      public java.util.List<java.lang.Integer>
          getDianZansList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(dianZans_) : dianZans_;
      }
      /**
       * <code>repeated int32 dianZans = 2;</code>
       * @return The count of dianZans.
       */
      public int getDianZansCount() {
        return dianZans_.size();
      }
      /**
       * <code>repeated int32 dianZans = 2;</code>
       * @param index The index of the element to return.
       * @return The dianZans at the given index.
       */
      public int getDianZans(int index) {
        return dianZans_.getInt(index);
      }
      /**
       * <code>repeated int32 dianZans = 2;</code>
       * @param index The index to set the value at.
       * @param value The dianZans to set.
       * @return This builder for chaining.
       */
      public Builder setDianZans(
          int index, int value) {
        ensureDianZansIsMutable();
        dianZans_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 dianZans = 2;</code>
       * @param value The dianZans to add.
       * @return This builder for chaining.
       */
      public Builder addDianZans(int value) {
        ensureDianZansIsMutable();
        dianZans_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 dianZans = 2;</code>
       * @param values The dianZans to add.
       * @return This builder for chaining.
       */
      public Builder addAllDianZans(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDianZansIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, dianZans_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 dianZans = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDianZans() {
        dianZans_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> challenges_ =
        java.util.Collections.emptyList();
      private void ensureChallengesIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          challenges_ = new java.util.ArrayList<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean>(challenges_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> challengesBuilder_;

      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> getChallengesList() {
        if (challengesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(challenges_);
        } else {
          return challengesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public int getChallengesCount() {
        if (challengesBuilder_ == null) {
          return challenges_.size();
        } else {
          return challengesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean getChallenges(int index) {
        if (challengesBuilder_ == null) {
          return challenges_.get(index);
        } else {
          return challengesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public Builder setChallenges(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean value) {
        if (challengesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChallengesIsMutable();
          challenges_.set(index, value);
          onChanged();
        } else {
          challengesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public Builder setChallenges(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder builderForValue) {
        if (challengesBuilder_ == null) {
          ensureChallengesIsMutable();
          challenges_.set(index, builderForValue.build());
          onChanged();
        } else {
          challengesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public Builder addChallenges(com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean value) {
        if (challengesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChallengesIsMutable();
          challenges_.add(value);
          onChanged();
        } else {
          challengesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public Builder addChallenges(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean value) {
        if (challengesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChallengesIsMutable();
          challenges_.add(index, value);
          onChanged();
        } else {
          challengesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public Builder addChallenges(
          com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder builderForValue) {
        if (challengesBuilder_ == null) {
          ensureChallengesIsMutable();
          challenges_.add(builderForValue.build());
          onChanged();
        } else {
          challengesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public Builder addChallenges(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder builderForValue) {
        if (challengesBuilder_ == null) {
          ensureChallengesIsMutable();
          challenges_.add(index, builderForValue.build());
          onChanged();
        } else {
          challengesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public Builder addAllChallenges(
          java.lang.Iterable<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> values) {
        if (challengesBuilder_ == null) {
          ensureChallengesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, challenges_);
          onChanged();
        } else {
          challengesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public Builder clearChallenges() {
        if (challengesBuilder_ == null) {
          challenges_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          challengesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public Builder removeChallenges(int index) {
        if (challengesBuilder_ == null) {
          ensureChallengesIsMutable();
          challenges_.remove(index);
          onChanged();
        } else {
          challengesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder getChallengesBuilder(
          int index) {
        return getChallengesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder getChallengesOrBuilder(
          int index) {
        if (challengesBuilder_ == null) {
          return challenges_.get(index);  } else {
          return challengesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public java.util.List<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> 
           getChallengesOrBuilderList() {
        if (challengesBuilder_ != null) {
          return challengesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(challenges_);
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder addChallengesBuilder() {
        return getChallengesFieldBuilder().addBuilder(
            com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.getDefaultInstance());
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder addChallengesBuilder(
          int index) {
        return getChallengesFieldBuilder().addBuilder(
            index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.getDefaultInstance());
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean challenges = 3;</code>
       */
      public java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder> 
           getChallengesBuilderList() {
        return getChallengesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> 
          getChallengesFieldBuilder() {
        if (challengesBuilder_ == null) {
          challengesBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder>(
                  challenges_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          challenges_ = null;
        }
        return challengesBuilder_;
      }

      private int hisMaxShouGouIndex_ ;
      /**
       * <code>int32 hisMaxShouGouIndex = 4;</code>
       * @return The hisMaxShouGouIndex.
       */
      @java.lang.Override
      public int getHisMaxShouGouIndex() {
        return hisMaxShouGouIndex_;
      }
      /**
       * <code>int32 hisMaxShouGouIndex = 4;</code>
       * @param value The hisMaxShouGouIndex to set.
       * @return This builder for chaining.
       */
      public Builder setHisMaxShouGouIndex(int value) {
        
        hisMaxShouGouIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 hisMaxShouGouIndex = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHisMaxShouGouIndex() {
        
        hisMaxShouGouIndex_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.ResQiaoQianInfoMessage)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.ResQiaoQianInfoMessage)
    private static final com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage();
    }

    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResQiaoQianInfoMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResQiaoQianInfoMessage>() {
      @java.lang.Override
      public ResQiaoQianInfoMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResQiaoQianInfoMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResQiaoQianInfoMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResQiaoQianInfoMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.ResQiaoQianInfoMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqQiaoQianShouGouMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.ReqQiaoQianShouGouMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 targetId = 1;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * <pre>
   ** class='ReqQiaoQianShouGouMessage' id='3' desc='请求乔迁收购' 
   * </pre>
   *
   * Protobuf type {@code qiaoqian.ReqQiaoQianShouGouMessage}
   */
  public static final class ReqQiaoQianShouGouMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.ReqQiaoQianShouGouMessage)
      ReqQiaoQianShouGouMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqQiaoQianShouGouMessage.newBuilder() to construct.
    private ReqQiaoQianShouGouMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqQiaoQianShouGouMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqQiaoQianShouGouMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqQiaoQianShouGouMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              targetId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianShouGouMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianShouGouMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage.Builder.class);
    }

    public static final int TARGETID_FIELD_NUMBER = 1;
    private long targetId_;
    /**
     * <code>int64 targetId = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targetId_ != 0L) {
        output.writeInt64(1, targetId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, targetId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage other = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGETID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqQiaoQianShouGouMessage' id='3' desc='请求乔迁收购' 
     * </pre>
     *
     * Protobuf type {@code qiaoqian.ReqQiaoQianShouGouMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.ReqQiaoQianShouGouMessage)
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianShouGouMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianShouGouMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        targetId_ = 0L;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianShouGouMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage build() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage buildPartial() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage result = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage(this);
        result.targetId_ = targetId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage other) {
        if (other == com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private long targetId_ ;
      /**
       * <code>int64 targetId = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>int64 targetId = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {
        
        targetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int64 targetId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.ReqQiaoQianShouGouMessage)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.ReqQiaoQianShouGouMessage)
    private static final com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage();
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqQiaoQianShouGouMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqQiaoQianShouGouMessage>() {
      @java.lang.Override
      public ReqQiaoQianShouGouMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqQiaoQianShouGouMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqQiaoQianShouGouMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqQiaoQianShouGouMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianShouGouMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResQiaoQianShouGouMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.ResQiaoQianShouGouMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
     * @return Whether the att field is set.
     */
    boolean hasAtt();
    /**
     * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
     * @return The att.
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean getAtt();
    /**
     * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder getAttOrBuilder();

    /**
     * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
     * @return Whether the def field is set.
     */
    boolean hasDef();
    /**
     * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
     * @return The def.
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean getDef();
    /**
     * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder getDefOrBuilder();

    /**
     * <code>bool attWin = 3;</code>
     * @return The attWin.
     */
    boolean getAttWin();

    /**
     * <code>int32 newScore = 4;</code>
     * @return The newScore.
     */
    int getNewScore();
  }
  /**
   * <pre>
   ** class='ResQiaoQianShouGouMessage' id='4' desc='返回乔迁收购' 
   * </pre>
   *
   * Protobuf type {@code qiaoqian.ResQiaoQianShouGouMessage}
   */
  public static final class ResQiaoQianShouGouMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.ResQiaoQianShouGouMessage)
      ResQiaoQianShouGouMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResQiaoQianShouGouMessage.newBuilder() to construct.
    private ResQiaoQianShouGouMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResQiaoQianShouGouMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResQiaoQianShouGouMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResQiaoQianShouGouMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder subBuilder = null;
              if (att_ != null) {
                subBuilder = att_.toBuilder();
              }
              att_ = input.readMessage(com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(att_);
                att_ = subBuilder.buildPartial();
              }

              break;
            }
            case 18: {
              com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder subBuilder = null;
              if (def_ != null) {
                subBuilder = def_.toBuilder();
              }
              def_ = input.readMessage(com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.parser(), extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(def_);
                def_ = subBuilder.buildPartial();
              }

              break;
            }
            case 24: {

              attWin_ = input.readBool();
              break;
            }
            case 32: {

              newScore_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianShouGouMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianShouGouMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage.class, com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage.Builder.class);
    }

    public static final int ATT_FIELD_NUMBER = 1;
    private com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean att_;
    /**
     * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
     * @return Whether the att field is set.
     */
    @java.lang.Override
    public boolean hasAtt() {
      return att_ != null;
    }
    /**
     * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
     * @return The att.
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean getAtt() {
      return att_ == null ? com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.getDefaultInstance() : att_;
    }
    /**
     * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder getAttOrBuilder() {
      return getAtt();
    }

    public static final int DEF_FIELD_NUMBER = 2;
    private com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean def_;
    /**
     * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
     * @return Whether the def field is set.
     */
    @java.lang.Override
    public boolean hasDef() {
      return def_ != null;
    }
    /**
     * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
     * @return The def.
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean getDef() {
      return def_ == null ? com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.getDefaultInstance() : def_;
    }
    /**
     * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder getDefOrBuilder() {
      return getDef();
    }

    public static final int ATTWIN_FIELD_NUMBER = 3;
    private boolean attWin_;
    /**
     * <code>bool attWin = 3;</code>
     * @return The attWin.
     */
    @java.lang.Override
    public boolean getAttWin() {
      return attWin_;
    }

    public static final int NEWSCORE_FIELD_NUMBER = 4;
    private int newScore_;
    /**
     * <code>int32 newScore = 4;</code>
     * @return The newScore.
     */
    @java.lang.Override
    public int getNewScore() {
      return newScore_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (att_ != null) {
        output.writeMessage(1, getAtt());
      }
      if (def_ != null) {
        output.writeMessage(2, getDef());
      }
      if (attWin_ != false) {
        output.writeBool(3, attWin_);
      }
      if (newScore_ != 0) {
        output.writeInt32(4, newScore_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (att_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getAtt());
      }
      if (def_ != null) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getDef());
      }
      if (attWin_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, attWin_);
      }
      if (newScore_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, newScore_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage other = (com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage) obj;

      if (hasAtt() != other.hasAtt()) return false;
      if (hasAtt()) {
        if (!getAtt()
            .equals(other.getAtt())) return false;
      }
      if (hasDef() != other.hasDef()) return false;
      if (hasDef()) {
        if (!getDef()
            .equals(other.getDef())) return false;
      }
      if (getAttWin()
          != other.getAttWin()) return false;
      if (getNewScore()
          != other.getNewScore()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAtt()) {
        hash = (37 * hash) + ATT_FIELD_NUMBER;
        hash = (53 * hash) + getAtt().hashCode();
      }
      if (hasDef()) {
        hash = (37 * hash) + DEF_FIELD_NUMBER;
        hash = (53 * hash) + getDef().hashCode();
      }
      hash = (37 * hash) + ATTWIN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getAttWin());
      hash = (37 * hash) + NEWSCORE_FIELD_NUMBER;
      hash = (53 * hash) + getNewScore();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResQiaoQianShouGouMessage' id='4' desc='返回乔迁收购' 
     * </pre>
     *
     * Protobuf type {@code qiaoqian.ResQiaoQianShouGouMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.ResQiaoQianShouGouMessage)
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianShouGouMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianShouGouMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage.class, com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (attBuilder_ == null) {
          att_ = null;
        } else {
          att_ = null;
          attBuilder_ = null;
        }
        if (defBuilder_ == null) {
          def_ = null;
        } else {
          def_ = null;
          defBuilder_ = null;
        }
        attWin_ = false;

        newScore_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianShouGouMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage build() {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage buildPartial() {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage result = new com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage(this);
        if (attBuilder_ == null) {
          result.att_ = att_;
        } else {
          result.att_ = attBuilder_.build();
        }
        if (defBuilder_ == null) {
          result.def_ = def_;
        } else {
          result.def_ = defBuilder_.build();
        }
        result.attWin_ = attWin_;
        result.newScore_ = newScore_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage other) {
        if (other == com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage.getDefaultInstance()) return this;
        if (other.hasAtt()) {
          mergeAtt(other.getAtt());
        }
        if (other.hasDef()) {
          mergeDef(other.getDef());
        }
        if (other.getAttWin() != false) {
          setAttWin(other.getAttWin());
        }
        if (other.getNewScore() != 0) {
          setNewScore(other.getNewScore());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean att_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder> attBuilder_;
      /**
       * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
       * @return Whether the att field is set.
       */
      public boolean hasAtt() {
        return attBuilder_ != null || att_ != null;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
       * @return The att.
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean getAtt() {
        if (attBuilder_ == null) {
          return att_ == null ? com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.getDefaultInstance() : att_;
        } else {
          return attBuilder_.getMessage();
        }
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
       */
      public Builder setAtt(com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean value) {
        if (attBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          att_ = value;
          onChanged();
        } else {
          attBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
       */
      public Builder setAtt(
          com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder builderForValue) {
        if (attBuilder_ == null) {
          att_ = builderForValue.build();
          onChanged();
        } else {
          attBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
       */
      public Builder mergeAtt(com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean value) {
        if (attBuilder_ == null) {
          if (att_ != null) {
            att_ =
              com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.newBuilder(att_).mergeFrom(value).buildPartial();
          } else {
            att_ = value;
          }
          onChanged();
        } else {
          attBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
       */
      public Builder clearAtt() {
        if (attBuilder_ == null) {
          att_ = null;
          onChanged();
        } else {
          att_ = null;
          attBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder getAttBuilder() {
        
        onChanged();
        return getAttFieldBuilder().getBuilder();
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder getAttOrBuilder() {
        if (attBuilder_ != null) {
          return attBuilder_.getMessageOrBuilder();
        } else {
          return att_ == null ?
              com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.getDefaultInstance() : att_;
        }
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean att = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder> 
          getAttFieldBuilder() {
        if (attBuilder_ == null) {
          attBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder>(
                  getAtt(),
                  getParentForChildren(),
                  isClean());
          att_ = null;
        }
        return attBuilder_;
      }

      private com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean def_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder> defBuilder_;
      /**
       * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
       * @return Whether the def field is set.
       */
      public boolean hasDef() {
        return defBuilder_ != null || def_ != null;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
       * @return The def.
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean getDef() {
        if (defBuilder_ == null) {
          return def_ == null ? com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.getDefaultInstance() : def_;
        } else {
          return defBuilder_.getMessage();
        }
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
       */
      public Builder setDef(com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean value) {
        if (defBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          def_ = value;
          onChanged();
        } else {
          defBuilder_.setMessage(value);
        }

        return this;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
       */
      public Builder setDef(
          com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder builderForValue) {
        if (defBuilder_ == null) {
          def_ = builderForValue.build();
          onChanged();
        } else {
          defBuilder_.setMessage(builderForValue.build());
        }

        return this;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
       */
      public Builder mergeDef(com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean value) {
        if (defBuilder_ == null) {
          if (def_ != null) {
            def_ =
              com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.newBuilder(def_).mergeFrom(value).buildPartial();
          } else {
            def_ = value;
          }
          onChanged();
        } else {
          defBuilder_.mergeFrom(value);
        }

        return this;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
       */
      public Builder clearDef() {
        if (defBuilder_ == null) {
          def_ = null;
          onChanged();
        } else {
          def_ = null;
          defBuilder_ = null;
        }

        return this;
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder getDefBuilder() {
        
        onChanged();
        return getDefFieldBuilder().getBuilder();
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder getDefOrBuilder() {
        if (defBuilder_ != null) {
          return defBuilder_.getMessageOrBuilder();
        } else {
          return def_ == null ?
              com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.getDefaultInstance() : def_;
        }
      }
      /**
       * <code>.qiaoqian.QiaoQianFighterBean def = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder> 
          getDefFieldBuilder() {
        if (defBuilder_ == null) {
          defBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianFighterBeanOrBuilder>(
                  getDef(),
                  getParentForChildren(),
                  isClean());
          def_ = null;
        }
        return defBuilder_;
      }

      private boolean attWin_ ;
      /**
       * <code>bool attWin = 3;</code>
       * @return The attWin.
       */
      @java.lang.Override
      public boolean getAttWin() {
        return attWin_;
      }
      /**
       * <code>bool attWin = 3;</code>
       * @param value The attWin to set.
       * @return This builder for chaining.
       */
      public Builder setAttWin(boolean value) {
        
        attWin_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>bool attWin = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAttWin() {
        
        attWin_ = false;
        onChanged();
        return this;
      }

      private int newScore_ ;
      /**
       * <code>int32 newScore = 4;</code>
       * @return The newScore.
       */
      @java.lang.Override
      public int getNewScore() {
        return newScore_;
      }
      /**
       * <code>int32 newScore = 4;</code>
       * @param value The newScore to set.
       * @return This builder for chaining.
       */
      public Builder setNewScore(int value) {
        
        newScore_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 newScore = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearNewScore() {
        
        newScore_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.ResQiaoQianShouGouMessage)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.ResQiaoQianShouGouMessage)
    private static final com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage();
    }

    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResQiaoQianShouGouMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResQiaoQianShouGouMessage>() {
      @java.lang.Override
      public ResQiaoQianShouGouMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResQiaoQianShouGouMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResQiaoQianShouGouMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResQiaoQianShouGouMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.ResQiaoQianShouGouMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqQiaoQianViewMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.ReqQiaoQianViewMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ReqQiaoQianViewMessage' id='5' desc='请求乔迁场景' 
   * </pre>
   *
   * Protobuf type {@code qiaoqian.ReqQiaoQianViewMessage}
   */
  public static final class ReqQiaoQianViewMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.ReqQiaoQianViewMessage)
      ReqQiaoQianViewMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqQiaoQianViewMessage.newBuilder() to construct.
    private ReqQiaoQianViewMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqQiaoQianViewMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqQiaoQianViewMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqQiaoQianViewMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianViewMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianViewMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage other = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage) obj;

      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqQiaoQianViewMessage' id='5' desc='请求乔迁场景' 
     * </pre>
     *
     * Protobuf type {@code qiaoqian.ReqQiaoQianViewMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.ReqQiaoQianViewMessage)
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianViewMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianViewMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianViewMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage build() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage buildPartial() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage result = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage(this);
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage other) {
        if (other == com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.ReqQiaoQianViewMessage)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.ReqQiaoQianViewMessage)
    private static final com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage();
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqQiaoQianViewMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqQiaoQianViewMessage>() {
      @java.lang.Override
      public ReqQiaoQianViewMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqQiaoQianViewMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqQiaoQianViewMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqQiaoQianViewMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianViewMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResQiaoQianViewMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.ResQiaoQianViewMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> 
        getMembersList();
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean getMembers(int index);
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    int getMembersCount();
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    java.util.List<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> 
        getMembersOrBuilderList();
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder getMembersOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResQiaoQianViewMessage' id='6' desc='返回乔迁场景' 
   * </pre>
   *
   * Protobuf type {@code qiaoqian.ResQiaoQianViewMessage}
   */
  public static final class ResQiaoQianViewMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.ResQiaoQianViewMessage)
      ResQiaoQianViewMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResQiaoQianViewMessage.newBuilder() to construct.
    private ResQiaoQianViewMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResQiaoQianViewMessage() {
      members_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResQiaoQianViewMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResQiaoQianViewMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                members_ = new java.util.ArrayList<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              members_.add(
                  input.readMessage(com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          members_ = java.util.Collections.unmodifiableList(members_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianViewMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianViewMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage.class, com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage.Builder.class);
    }

    public static final int MEMBERS_FIELD_NUMBER = 1;
    private java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> members_;
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> getMembersList() {
      return members_;
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> 
        getMembersOrBuilderList() {
      return members_;
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    @java.lang.Override
    public int getMembersCount() {
      return members_.size();
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean getMembers(int index) {
      return members_.get(index);
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder getMembersOrBuilder(
        int index) {
      return members_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < members_.size(); i++) {
        output.writeMessage(1, members_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < members_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, members_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage other = (com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage) obj;

      if (!getMembersList()
          .equals(other.getMembersList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getMembersCount() > 0) {
        hash = (37 * hash) + MEMBERS_FIELD_NUMBER;
        hash = (53 * hash) + getMembersList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResQiaoQianViewMessage' id='6' desc='返回乔迁场景' 
     * </pre>
     *
     * Protobuf type {@code qiaoqian.ResQiaoQianViewMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.ResQiaoQianViewMessage)
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianViewMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianViewMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage.class, com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMembersFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (membersBuilder_ == null) {
          members_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          membersBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianViewMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage build() {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage buildPartial() {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage result = new com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage(this);
        int from_bitField0_ = bitField0_;
        if (membersBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            members_ = java.util.Collections.unmodifiableList(members_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.members_ = members_;
        } else {
          result.members_ = membersBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage other) {
        if (other == com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage.getDefaultInstance()) return this;
        if (membersBuilder_ == null) {
          if (!other.members_.isEmpty()) {
            if (members_.isEmpty()) {
              members_ = other.members_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureMembersIsMutable();
              members_.addAll(other.members_);
            }
            onChanged();
          }
        } else {
          if (!other.members_.isEmpty()) {
            if (membersBuilder_.isEmpty()) {
              membersBuilder_.dispose();
              membersBuilder_ = null;
              members_ = other.members_;
              bitField0_ = (bitField0_ & ~0x00000001);
              membersBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getMembersFieldBuilder() : null;
            } else {
              membersBuilder_.addAllMessages(other.members_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> members_ =
        java.util.Collections.emptyList();
      private void ensureMembersIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          members_ = new java.util.ArrayList<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean>(members_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> membersBuilder_;

      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> getMembersList() {
        if (membersBuilder_ == null) {
          return java.util.Collections.unmodifiableList(members_);
        } else {
          return membersBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public int getMembersCount() {
        if (membersBuilder_ == null) {
          return members_.size();
        } else {
          return membersBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean getMembers(int index) {
        if (membersBuilder_ == null) {
          return members_.get(index);
        } else {
          return membersBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public Builder setMembers(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean value) {
        if (membersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMembersIsMutable();
          members_.set(index, value);
          onChanged();
        } else {
          membersBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public Builder setMembers(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder builderForValue) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          members_.set(index, builderForValue.build());
          onChanged();
        } else {
          membersBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public Builder addMembers(com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean value) {
        if (membersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMembersIsMutable();
          members_.add(value);
          onChanged();
        } else {
          membersBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public Builder addMembers(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean value) {
        if (membersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMembersIsMutable();
          members_.add(index, value);
          onChanged();
        } else {
          membersBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public Builder addMembers(
          com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder builderForValue) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          members_.add(builderForValue.build());
          onChanged();
        } else {
          membersBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public Builder addMembers(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder builderForValue) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          members_.add(index, builderForValue.build());
          onChanged();
        } else {
          membersBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public Builder addAllMembers(
          java.lang.Iterable<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean> values) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, members_);
          onChanged();
        } else {
          membersBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public Builder clearMembers() {
        if (membersBuilder_ == null) {
          members_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          membersBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public Builder removeMembers(int index) {
        if (membersBuilder_ == null) {
          ensureMembersIsMutable();
          members_.remove(index);
          onChanged();
        } else {
          membersBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder getMembersBuilder(
          int index) {
        return getMembersFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder getMembersOrBuilder(
          int index) {
        if (membersBuilder_ == null) {
          return members_.get(index);  } else {
          return membersBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public java.util.List<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> 
           getMembersOrBuilderList() {
        if (membersBuilder_ != null) {
          return membersBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(members_);
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder addMembersBuilder() {
        return getMembersFieldBuilder().addBuilder(
            com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.getDefaultInstance());
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder addMembersBuilder(
          int index) {
        return getMembersFieldBuilder().addBuilder(
            index, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.getDefaultInstance());
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianMemberBean members = 1;</code>
       */
      public java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder> 
           getMembersBuilderList() {
        return getMembersFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder> 
          getMembersFieldBuilder() {
        if (membersBuilder_ == null) {
          membersBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianMemberBeanOrBuilder>(
                  members_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          members_ = null;
        }
        return membersBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.ResQiaoQianViewMessage)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.ResQiaoQianViewMessage)
    private static final com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage();
    }

    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResQiaoQianViewMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResQiaoQianViewMessage>() {
      @java.lang.Override
      public ResQiaoQianViewMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResQiaoQianViewMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResQiaoQianViewMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResQiaoQianViewMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.ResQiaoQianViewMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqQiaoQianLogMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.ReqQiaoQianLogMessage)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqQiaoQianLogMessage' id='7' desc='请求乔迁战斗日志' 
   * </pre>
   *
   * Protobuf type {@code qiaoqian.ReqQiaoQianLogMessage}
   */
  public static final class ReqQiaoQianLogMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.ReqQiaoQianLogMessage)
      ReqQiaoQianLogMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqQiaoQianLogMessage.newBuilder() to construct.
    private ReqQiaoQianLogMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqQiaoQianLogMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqQiaoQianLogMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqQiaoQianLogMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianLogMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianLogMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage other = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqQiaoQianLogMessage' id='7' desc='请求乔迁战斗日志' 
     * </pre>
     *
     * Protobuf type {@code qiaoqian.ReqQiaoQianLogMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.ReqQiaoQianLogMessage)
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianLogMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianLogMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianLogMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage build() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage buildPartial() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage result = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage other) {
        if (other == com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.ReqQiaoQianLogMessage)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.ReqQiaoQianLogMessage)
    private static final com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage();
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqQiaoQianLogMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqQiaoQianLogMessage>() {
      @java.lang.Override
      public ReqQiaoQianLogMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqQiaoQianLogMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqQiaoQianLogMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqQiaoQianLogMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianLogMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResQiaoQianLogMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.ResQiaoQianLogMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean> 
        getLogsList();
    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean getLogs(int index);
    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    int getLogsCount();
    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    java.util.List<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder> 
        getLogsOrBuilderList();
    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder getLogsOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResQiaoQianLogMessage' id='8' desc='返回乔迁战斗日志' 
   * </pre>
   *
   * Protobuf type {@code qiaoqian.ResQiaoQianLogMessage}
   */
  public static final class ResQiaoQianLogMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.ResQiaoQianLogMessage)
      ResQiaoQianLogMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResQiaoQianLogMessage.newBuilder() to construct.
    private ResQiaoQianLogMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResQiaoQianLogMessage() {
      logs_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResQiaoQianLogMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResQiaoQianLogMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                logs_ = new java.util.ArrayList<com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              logs_.add(
                  input.readMessage(com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          logs_ = java.util.Collections.unmodifiableList(logs_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianLogMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianLogMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage.class, com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage.Builder.class);
    }

    public static final int LOGS_FIELD_NUMBER = 1;
    private java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean> logs_;
    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean> getLogsList() {
      return logs_;
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder> 
        getLogsOrBuilderList() {
      return logs_;
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    @java.lang.Override
    public int getLogsCount() {
      return logs_.size();
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean getLogs(int index) {
      return logs_.get(index);
    }
    /**
     * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder getLogsOrBuilder(
        int index) {
      return logs_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < logs_.size(); i++) {
        output.writeMessage(1, logs_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < logs_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, logs_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage other = (com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage) obj;

      if (!getLogsList()
          .equals(other.getLogsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getLogsCount() > 0) {
        hash = (37 * hash) + LOGS_FIELD_NUMBER;
        hash = (53 * hash) + getLogsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResQiaoQianLogMessage' id='8' desc='返回乔迁战斗日志' 
     * </pre>
     *
     * Protobuf type {@code qiaoqian.ResQiaoQianLogMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.ResQiaoQianLogMessage)
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianLogMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianLogMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage.class, com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getLogsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (logsBuilder_ == null) {
          logs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          logsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ResQiaoQianLogMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage build() {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage buildPartial() {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage result = new com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage(this);
        int from_bitField0_ = bitField0_;
        if (logsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            logs_ = java.util.Collections.unmodifiableList(logs_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.logs_ = logs_;
        } else {
          result.logs_ = logsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage other) {
        if (other == com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage.getDefaultInstance()) return this;
        if (logsBuilder_ == null) {
          if (!other.logs_.isEmpty()) {
            if (logs_.isEmpty()) {
              logs_ = other.logs_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureLogsIsMutable();
              logs_.addAll(other.logs_);
            }
            onChanged();
          }
        } else {
          if (!other.logs_.isEmpty()) {
            if (logsBuilder_.isEmpty()) {
              logsBuilder_.dispose();
              logsBuilder_ = null;
              logs_ = other.logs_;
              bitField0_ = (bitField0_ & ~0x00000001);
              logsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getLogsFieldBuilder() : null;
            } else {
              logsBuilder_.addAllMessages(other.logs_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean> logs_ =
        java.util.Collections.emptyList();
      private void ensureLogsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          logs_ = new java.util.ArrayList<com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean>(logs_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder> logsBuilder_;

      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean> getLogsList() {
        if (logsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(logs_);
        } else {
          return logsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public int getLogsCount() {
        if (logsBuilder_ == null) {
          return logs_.size();
        } else {
          return logsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean getLogs(int index) {
        if (logsBuilder_ == null) {
          return logs_.get(index);
        } else {
          return logsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public Builder setLogs(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean value) {
        if (logsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLogsIsMutable();
          logs_.set(index, value);
          onChanged();
        } else {
          logsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public Builder setLogs(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder builderForValue) {
        if (logsBuilder_ == null) {
          ensureLogsIsMutable();
          logs_.set(index, builderForValue.build());
          onChanged();
        } else {
          logsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public Builder addLogs(com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean value) {
        if (logsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLogsIsMutable();
          logs_.add(value);
          onChanged();
        } else {
          logsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public Builder addLogs(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean value) {
        if (logsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLogsIsMutable();
          logs_.add(index, value);
          onChanged();
        } else {
          logsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public Builder addLogs(
          com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder builderForValue) {
        if (logsBuilder_ == null) {
          ensureLogsIsMutable();
          logs_.add(builderForValue.build());
          onChanged();
        } else {
          logsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public Builder addLogs(
          int index, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder builderForValue) {
        if (logsBuilder_ == null) {
          ensureLogsIsMutable();
          logs_.add(index, builderForValue.build());
          onChanged();
        } else {
          logsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public Builder addAllLogs(
          java.lang.Iterable<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean> values) {
        if (logsBuilder_ == null) {
          ensureLogsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, logs_);
          onChanged();
        } else {
          logsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public Builder clearLogs() {
        if (logsBuilder_ == null) {
          logs_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          logsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public Builder removeLogs(int index) {
        if (logsBuilder_ == null) {
          ensureLogsIsMutable();
          logs_.remove(index);
          onChanged();
        } else {
          logsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder getLogsBuilder(
          int index) {
        return getLogsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder getLogsOrBuilder(
          int index) {
        if (logsBuilder_ == null) {
          return logs_.get(index);  } else {
          return logsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public java.util.List<? extends com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder> 
           getLogsOrBuilderList() {
        if (logsBuilder_ != null) {
          return logsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(logs_);
        }
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder addLogsBuilder() {
        return getLogsFieldBuilder().addBuilder(
            com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.getDefaultInstance());
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder addLogsBuilder(
          int index) {
        return getLogsFieldBuilder().addBuilder(
            index, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.getDefaultInstance());
      }
      /**
       * <code>repeated .qiaoqian.QiaoQianLogBean logs = 1;</code>
       */
      public java.util.List<com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder> 
           getLogsBuilderList() {
        return getLogsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder> 
          getLogsFieldBuilder() {
        if (logsBuilder_ == null) {
          logsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBean.Builder, com.sh.game.protos.QiaoQianProtos.QiaoQianLogBeanOrBuilder>(
                  logs_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          logs_ = null;
        }
        return logsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.ResQiaoQianLogMessage)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.ResQiaoQianLogMessage)
    private static final com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage();
    }

    public static com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResQiaoQianLogMessage>
        PARSER = new com.google.protobuf.AbstractParser<ResQiaoQianLogMessage>() {
      @java.lang.Override
      public ResQiaoQianLogMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResQiaoQianLogMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResQiaoQianLogMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResQiaoQianLogMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.ResQiaoQianLogMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqQiaoQianDianZanMessageOrBuilder extends
      // @@protoc_insertion_point(interface_extends:qiaoqian.ReqQiaoQianDianZanMessage)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   ** class='ReqQiaoQianDianZanMessage' id='9' desc='请求乔迁点赞' 
   * </pre>
   *
   * Protobuf type {@code qiaoqian.ReqQiaoQianDianZanMessage}
   */
  public static final class ReqQiaoQianDianZanMessage extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:qiaoqian.ReqQiaoQianDianZanMessage)
      ReqQiaoQianDianZanMessageOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqQiaoQianDianZanMessage.newBuilder() to construct.
    private ReqQiaoQianDianZanMessage(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqQiaoQianDianZanMessage() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqQiaoQianDianZanMessage();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqQiaoQianDianZanMessage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              type_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianDianZanMessage_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianDianZanMessage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage)) {
        return super.equals(obj);
      }
      com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage other = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage) obj;

      if (getType()
          != other.getType()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqQiaoQianDianZanMessage' id='9' desc='请求乔迁点赞' 
     * </pre>
     *
     * Protobuf type {@code qiaoqian.ReqQiaoQianDianZanMessage}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:qiaoqian.ReqQiaoQianDianZanMessage)
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianDianZanMessage_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianDianZanMessage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage.class, com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage.Builder.class);
      }

      // Construct using com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.QiaoQianProtos.internal_static_qiaoqian_ReqQiaoQianDianZanMessage_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage getDefaultInstanceForType() {
        return com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage build() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage buildPartial() {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage result = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage(this);
        result.type_ = type_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage) {
          return mergeFrom((com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage other) {
        if (other == com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int type_ ;
      /**
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {
        
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        
        type_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:qiaoqian.ReqQiaoQianDianZanMessage)
    }

    // @@protoc_insertion_point(class_scope:qiaoqian.ReqQiaoQianDianZanMessage)
    private static final com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage();
    }

    public static com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqQiaoQianDianZanMessage>
        PARSER = new com.google.protobuf.AbstractParser<ReqQiaoQianDianZanMessage>() {
      @java.lang.Override
      public ReqQiaoQianDianZanMessage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqQiaoQianDianZanMessage(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqQiaoQianDianZanMessage> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqQiaoQianDianZanMessage> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.QiaoQianProtos.ReqQiaoQianDianZanMessage getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_QiaoQianFighterBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_QiaoQianFighterBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_QiaoQianMemberBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_QiaoQianMemberBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_QiaoQianLogBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_QiaoQianLogBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_ReqQiaoQianInfoMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_ReqQiaoQianInfoMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_ResQiaoQianInfoMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_ResQiaoQianInfoMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_ReqQiaoQianShouGouMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_ReqQiaoQianShouGouMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_ResQiaoQianShouGouMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_ResQiaoQianShouGouMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_ReqQiaoQianViewMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_ReqQiaoQianViewMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_ResQiaoQianViewMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_ResQiaoQianViewMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_ReqQiaoQianLogMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_ReqQiaoQianLogMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_ResQiaoQianLogMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_ResQiaoQianLogMessage_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_qiaoqian_ReqQiaoQianDianZanMessage_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_qiaoqian_ReqQiaoQianDianZanMessage_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016qiaoqian.proto\022\010qiaoqian\032\tabc.proto\"k\n" +
      "\023QiaoQianFighterBean\022\016\n\006roleId\030\001 \001(\003\022\022\n\n" +
      "robotCfgId\030\002 \001(\005\0220\n\tsecretary\030\003 \003(\0132\035.ab" +
      "c.CommonKeyValueStringBean\"\261\001\n\022QiaoQianM" +
      "emberBean\022\016\n\006roleId\030\001 \001(\003\022\022\n\nrobotCfgId\030" +
      "\002 \001(\005\022\r\n\005score\030\003 \001(\005\022\016\n\006income\030\004 \001(\t\022\014\n\004" +
      "name\030\005 \001(\t\022\r\n\005level\030\006 \001(\005\022%\n\010fashions\030\007 " +
      "\003(\0132\023.abc.CommonSlotBean\022\024\n\014secondIncome" +
      "\030\010 \001(\t\"\224\001\n\017QiaoQianLogBean\022\016\n\006roleId\030\001 \001" +
      "(\003\022\r\n\005score\030\002 \001(\005\022\020\n\010newScore\030\003 \001(\005\022\014\n\004t" +
      "ime\030\004 \001(\005\022\014\n\004name\030\005 \001(\t\022\r\n\005level\030\006 \001(\005\022%" +
      "\n\010fashions\030\007 \003(\0132\023.abc.CommonSlotBean\"\030\n" +
      "\026ReqQiaoQianInfoMessage\"\207\001\n\026ResQiaoQianI" +
      "nfoMessage\022\r\n\005score\030\001 \001(\005\022\020\n\010dianZans\030\002 " +
      "\003(\005\0220\n\nchallenges\030\003 \003(\0132\034.qiaoqian.QiaoQ" +
      "ianMemberBean\022\032\n\022hisMaxShouGouIndex\030\004 \001(" +
      "\005\"-\n\031ReqQiaoQianShouGouMessage\022\020\n\010target" +
      "Id\030\001 \001(\003\"\225\001\n\031ResQiaoQianShouGouMessage\022*" +
      "\n\003att\030\001 \001(\0132\035.qiaoqian.QiaoQianFighterBe" +
      "an\022*\n\003def\030\002 \001(\0132\035.qiaoqian.QiaoQianFight" +
      "erBean\022\016\n\006attWin\030\003 \001(\010\022\020\n\010newScore\030\004 \001(\005" +
      "\"&\n\026ReqQiaoQianViewMessage\022\014\n\004type\030\001 \001(\005" +
      "\"G\n\026ResQiaoQianViewMessage\022-\n\007members\030\001 " +
      "\003(\0132\034.qiaoqian.QiaoQianMemberBean\"\027\n\025Req" +
      "QiaoQianLogMessage\"@\n\025ResQiaoQianLogMess" +
      "age\022\'\n\004logs\030\001 \003(\0132\031.qiaoqian.QiaoQianLog" +
      "Bean\")\n\031ReqQiaoQianDianZanMessage\022\014\n\004typ" +
      "e\030\001 \001(\005B$\n\022com.sh.game.protosB\016QiaoQianP" +
      "rotosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.sh.game.protos.AbcProtos.getDescriptor(),
        });
    internal_static_qiaoqian_QiaoQianFighterBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_qiaoqian_QiaoQianFighterBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_QiaoQianFighterBean_descriptor,
        new java.lang.String[] { "RoleId", "RobotCfgId", "Secretary", });
    internal_static_qiaoqian_QiaoQianMemberBean_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_qiaoqian_QiaoQianMemberBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_QiaoQianMemberBean_descriptor,
        new java.lang.String[] { "RoleId", "RobotCfgId", "Score", "Income", "Name", "Level", "Fashions", "SecondIncome", });
    internal_static_qiaoqian_QiaoQianLogBean_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_qiaoqian_QiaoQianLogBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_QiaoQianLogBean_descriptor,
        new java.lang.String[] { "RoleId", "Score", "NewScore", "Time", "Name", "Level", "Fashions", });
    internal_static_qiaoqian_ReqQiaoQianInfoMessage_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_qiaoqian_ReqQiaoQianInfoMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_ReqQiaoQianInfoMessage_descriptor,
        new java.lang.String[] { });
    internal_static_qiaoqian_ResQiaoQianInfoMessage_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_qiaoqian_ResQiaoQianInfoMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_ResQiaoQianInfoMessage_descriptor,
        new java.lang.String[] { "Score", "DianZans", "Challenges", "HisMaxShouGouIndex", });
    internal_static_qiaoqian_ReqQiaoQianShouGouMessage_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_qiaoqian_ReqQiaoQianShouGouMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_ReqQiaoQianShouGouMessage_descriptor,
        new java.lang.String[] { "TargetId", });
    internal_static_qiaoqian_ResQiaoQianShouGouMessage_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_qiaoqian_ResQiaoQianShouGouMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_ResQiaoQianShouGouMessage_descriptor,
        new java.lang.String[] { "Att", "Def", "AttWin", "NewScore", });
    internal_static_qiaoqian_ReqQiaoQianViewMessage_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_qiaoqian_ReqQiaoQianViewMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_ReqQiaoQianViewMessage_descriptor,
        new java.lang.String[] { "Type", });
    internal_static_qiaoqian_ResQiaoQianViewMessage_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_qiaoqian_ResQiaoQianViewMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_ResQiaoQianViewMessage_descriptor,
        new java.lang.String[] { "Members", });
    internal_static_qiaoqian_ReqQiaoQianLogMessage_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_qiaoqian_ReqQiaoQianLogMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_ReqQiaoQianLogMessage_descriptor,
        new java.lang.String[] { });
    internal_static_qiaoqian_ResQiaoQianLogMessage_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_qiaoqian_ResQiaoQianLogMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_ResQiaoQianLogMessage_descriptor,
        new java.lang.String[] { "Logs", });
    internal_static_qiaoqian_ReqQiaoQianDianZanMessage_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_qiaoqian_ReqQiaoQianDianZanMessage_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_qiaoqian_ReqQiaoQianDianZanMessage_descriptor,
        new java.lang.String[] { "Type", });
    com.sh.game.protos.AbcProtos.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
