// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: zodiacCompound.proto

package com.sh.game.protos;

public final class ZodiacCompoundProtos {
  private ZodiacCompoundProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqSuccessRateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:zodiacCompound.ReqSuccessRate)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @return A list containing the lidList.
     */
    java.util.List<java.lang.Long> getLidListList();
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @return The count of lidList.
     */
    int getLidListCount();
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @param index The index of the element to return.
     * @return The lidList at the given index.
     */
    long getLidList(int index);
  }
  /**
   * <pre>
   ** class='ReqSuccessRate' id='1' desc='请求合成成功率' 
   * </pre>
   *
   * Protobuf type {@code zodiacCompound.ReqSuccessRate}
   */
  public static final class ReqSuccessRate extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:zodiacCompound.ReqSuccessRate)
      ReqSuccessRateOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqSuccessRate.newBuilder() to construct.
    private ReqSuccessRate(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqSuccessRate() {
      lidList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqSuccessRate();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqSuccessRate(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                lidList_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              lidList_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                lidList_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                lidList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          lidList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqSuccessRate_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqSuccessRate_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate.class, com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate.Builder.class);
    }

    public static final int LIDLIST_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList lidList_;
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @return A list containing the lidList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getLidListList() {
      return lidList_;
    }
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @return The count of lidList.
     */
    public int getLidListCount() {
      return lidList_.size();
    }
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @param index The index of the element to return.
     * @return The lidList at the given index.
     */
    public long getLidList(int index) {
      return lidList_.getLong(index);
    }
    private int lidListMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getLidListList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(lidListMemoizedSerializedSize);
      }
      for (int i = 0; i < lidList_.size(); i++) {
        output.writeInt64NoTag(lidList_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < lidList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(lidList_.getLong(i));
        }
        size += dataSize;
        if (!getLidListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        lidListMemoizedSerializedSize = dataSize;
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate other = (com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate) obj;

      if (!getLidListList()
          .equals(other.getLidListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getLidListCount() > 0) {
        hash = (37 * hash) + LIDLIST_FIELD_NUMBER;
        hash = (53 * hash) + getLidListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqSuccessRate' id='1' desc='请求合成成功率' 
     * </pre>
     *
     * Protobuf type {@code zodiacCompound.ReqSuccessRate}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:zodiacCompound.ReqSuccessRate)
        com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqSuccessRate_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqSuccessRate_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate.class, com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate.Builder.class);
      }

      // Construct using com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        lidList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqSuccessRate_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate getDefaultInstanceForType() {
        return com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate build() {
        com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate buildPartial() {
        com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate result = new com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          lidList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.lidList_ = lidList_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate) {
          return mergeFrom((com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate other) {
        if (other == com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate.getDefaultInstance()) return this;
        if (!other.lidList_.isEmpty()) {
          if (lidList_.isEmpty()) {
            lidList_ = other.lidList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureLidListIsMutable();
            lidList_.addAll(other.lidList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList lidList_ = emptyLongList();
      private void ensureLidListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          lidList_ = mutableCopy(lidList_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @return A list containing the lidList.
       */
      public java.util.List<java.lang.Long>
          getLidListList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(lidList_) : lidList_;
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @return The count of lidList.
       */
      public int getLidListCount() {
        return lidList_.size();
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @param index The index of the element to return.
       * @return The lidList at the given index.
       */
      public long getLidList(int index) {
        return lidList_.getLong(index);
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @param index The index to set the value at.
       * @param value The lidList to set.
       * @return This builder for chaining.
       */
      public Builder setLidList(
          int index, long value) {
        ensureLidListIsMutable();
        lidList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @param value The lidList to add.
       * @return This builder for chaining.
       */
      public Builder addLidList(long value) {
        ensureLidListIsMutable();
        lidList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @param values The lidList to add.
       * @return This builder for chaining.
       */
      public Builder addAllLidList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureLidListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, lidList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLidList() {
        lidList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:zodiacCompound.ReqSuccessRate)
    }

    // @@protoc_insertion_point(class_scope:zodiacCompound.ReqSuccessRate)
    private static final com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate();
    }

    public static com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqSuccessRate>
        PARSER = new com.google.protobuf.AbstractParser<ReqSuccessRate>() {
      @java.lang.Override
      public ReqSuccessRate parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqSuccessRate(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqSuccessRate> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqSuccessRate> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ZodiacCompoundProtos.ReqSuccessRate getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResSuccessRateOrBuilder extends
      // @@protoc_insertion_point(interface_extends:zodiacCompound.ResSuccessRate)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *成功率
     * </pre>
     *
     * <code>int32 successRate = 1;</code>
     * @return The successRate.
     */
    int getSuccessRate();
  }
  /**
   * <pre>
   ** class='ResSuccessRate' id='2' desc='返回合成成功率' 
   * </pre>
   *
   * Protobuf type {@code zodiacCompound.ResSuccessRate}
   */
  public static final class ResSuccessRate extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:zodiacCompound.ResSuccessRate)
      ResSuccessRateOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResSuccessRate.newBuilder() to construct.
    private ResSuccessRate(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResSuccessRate() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResSuccessRate();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResSuccessRate(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              successRate_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResSuccessRate_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResSuccessRate_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate.class, com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate.Builder.class);
    }

    public static final int SUCCESSRATE_FIELD_NUMBER = 1;
    private int successRate_;
    /**
     * <pre>
     *成功率
     * </pre>
     *
     * <code>int32 successRate = 1;</code>
     * @return The successRate.
     */
    @java.lang.Override
    public int getSuccessRate() {
      return successRate_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (successRate_ != 0) {
        output.writeInt32(1, successRate_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (successRate_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, successRate_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate other = (com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate) obj;

      if (getSuccessRate()
          != other.getSuccessRate()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SUCCESSRATE_FIELD_NUMBER;
      hash = (53 * hash) + getSuccessRate();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResSuccessRate' id='2' desc='返回合成成功率' 
     * </pre>
     *
     * Protobuf type {@code zodiacCompound.ResSuccessRate}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:zodiacCompound.ResSuccessRate)
        com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResSuccessRate_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResSuccessRate_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate.class, com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate.Builder.class);
      }

      // Construct using com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        successRate_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResSuccessRate_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate getDefaultInstanceForType() {
        return com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate build() {
        com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate buildPartial() {
        com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate result = new com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate(this);
        result.successRate_ = successRate_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate) {
          return mergeFrom((com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate other) {
        if (other == com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate.getDefaultInstance()) return this;
        if (other.getSuccessRate() != 0) {
          setSuccessRate(other.getSuccessRate());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int successRate_ ;
      /**
       * <pre>
       *成功率
       * </pre>
       *
       * <code>int32 successRate = 1;</code>
       * @return The successRate.
       */
      @java.lang.Override
      public int getSuccessRate() {
        return successRate_;
      }
      /**
       * <pre>
       *成功率
       * </pre>
       *
       * <code>int32 successRate = 1;</code>
       * @param value The successRate to set.
       * @return This builder for chaining.
       */
      public Builder setSuccessRate(int value) {
        
        successRate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *成功率
       * </pre>
       *
       * <code>int32 successRate = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuccessRate() {
        
        successRate_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:zodiacCompound.ResSuccessRate)
    }

    // @@protoc_insertion_point(class_scope:zodiacCompound.ResSuccessRate)
    private static final com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate();
    }

    public static com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResSuccessRate>
        PARSER = new com.google.protobuf.AbstractParser<ResSuccessRate>() {
      @java.lang.Override
      public ResSuccessRate parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResSuccessRate(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResSuccessRate> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResSuccessRate> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ZodiacCompoundProtos.ResSuccessRate getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqCompoundOrBuilder extends
      // @@protoc_insertion_point(interface_extends:zodiacCompound.ReqCompound)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @return A list containing the lidList.
     */
    java.util.List<java.lang.Long> getLidListList();
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @return The count of lidList.
     */
    int getLidListCount();
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @param index The index of the element to return.
     * @return The lidList at the given index.
     */
    long getLidList(int index);

    /**
     * <pre>
     *成功率
     * </pre>
     *
     * <code>int32 successRate = 2;</code>
     * @return The successRate.
     */
    int getSuccessRate();
  }
  /**
   * <pre>
   ** class='ReqCompound' id='3' desc='请求合成' 
   * </pre>
   *
   * Protobuf type {@code zodiacCompound.ReqCompound}
   */
  public static final class ReqCompound extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:zodiacCompound.ReqCompound)
      ReqCompoundOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqCompound.newBuilder() to construct.
    private ReqCompound(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqCompound() {
      lidList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqCompound();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqCompound(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                lidList_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              lidList_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                lidList_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                lidList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 16: {

              successRate_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          lidList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqCompound_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqCompound_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ZodiacCompoundProtos.ReqCompound.class, com.sh.game.protos.ZodiacCompoundProtos.ReqCompound.Builder.class);
    }

    public static final int LIDLIST_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList lidList_;
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @return A list containing the lidList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getLidListList() {
      return lidList_;
    }
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @return The count of lidList.
     */
    public int getLidListCount() {
      return lidList_.size();
    }
    /**
     * <pre>
     *道具唯一id列表
     * </pre>
     *
     * <code>repeated int64 lidList = 1;</code>
     * @param index The index of the element to return.
     * @return The lidList at the given index.
     */
    public long getLidList(int index) {
      return lidList_.getLong(index);
    }
    private int lidListMemoizedSerializedSize = -1;

    public static final int SUCCESSRATE_FIELD_NUMBER = 2;
    private int successRate_;
    /**
     * <pre>
     *成功率
     * </pre>
     *
     * <code>int32 successRate = 2;</code>
     * @return The successRate.
     */
    @java.lang.Override
    public int getSuccessRate() {
      return successRate_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (getLidListList().size() > 0) {
        output.writeUInt32NoTag(10);
        output.writeUInt32NoTag(lidListMemoizedSerializedSize);
      }
      for (int i = 0; i < lidList_.size(); i++) {
        output.writeInt64NoTag(lidList_.getLong(i));
      }
      if (successRate_ != 0) {
        output.writeInt32(2, successRate_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < lidList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(lidList_.getLong(i));
        }
        size += dataSize;
        if (!getLidListList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        lidListMemoizedSerializedSize = dataSize;
      }
      if (successRate_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, successRate_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ZodiacCompoundProtos.ReqCompound)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ZodiacCompoundProtos.ReqCompound other = (com.sh.game.protos.ZodiacCompoundProtos.ReqCompound) obj;

      if (!getLidListList()
          .equals(other.getLidListList())) return false;
      if (getSuccessRate()
          != other.getSuccessRate()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getLidListCount() > 0) {
        hash = (37 * hash) + LIDLIST_FIELD_NUMBER;
        hash = (53 * hash) + getLidListList().hashCode();
      }
      hash = (37 * hash) + SUCCESSRATE_FIELD_NUMBER;
      hash = (53 * hash) + getSuccessRate();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ZodiacCompoundProtos.ReqCompound prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqCompound' id='3' desc='请求合成' 
     * </pre>
     *
     * Protobuf type {@code zodiacCompound.ReqCompound}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:zodiacCompound.ReqCompound)
        com.sh.game.protos.ZodiacCompoundProtos.ReqCompoundOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqCompound_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqCompound_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ZodiacCompoundProtos.ReqCompound.class, com.sh.game.protos.ZodiacCompoundProtos.ReqCompound.Builder.class);
      }

      // Construct using com.sh.game.protos.ZodiacCompoundProtos.ReqCompound.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        lidList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        successRate_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ReqCompound_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ReqCompound getDefaultInstanceForType() {
        return com.sh.game.protos.ZodiacCompoundProtos.ReqCompound.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ReqCompound build() {
        com.sh.game.protos.ZodiacCompoundProtos.ReqCompound result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ReqCompound buildPartial() {
        com.sh.game.protos.ZodiacCompoundProtos.ReqCompound result = new com.sh.game.protos.ZodiacCompoundProtos.ReqCompound(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          lidList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.lidList_ = lidList_;
        result.successRate_ = successRate_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ZodiacCompoundProtos.ReqCompound) {
          return mergeFrom((com.sh.game.protos.ZodiacCompoundProtos.ReqCompound)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ZodiacCompoundProtos.ReqCompound other) {
        if (other == com.sh.game.protos.ZodiacCompoundProtos.ReqCompound.getDefaultInstance()) return this;
        if (!other.lidList_.isEmpty()) {
          if (lidList_.isEmpty()) {
            lidList_ = other.lidList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureLidListIsMutable();
            lidList_.addAll(other.lidList_);
          }
          onChanged();
        }
        if (other.getSuccessRate() != 0) {
          setSuccessRate(other.getSuccessRate());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ZodiacCompoundProtos.ReqCompound parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ZodiacCompoundProtos.ReqCompound) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList lidList_ = emptyLongList();
      private void ensureLidListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          lidList_ = mutableCopy(lidList_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @return A list containing the lidList.
       */
      public java.util.List<java.lang.Long>
          getLidListList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(lidList_) : lidList_;
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @return The count of lidList.
       */
      public int getLidListCount() {
        return lidList_.size();
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @param index The index of the element to return.
       * @return The lidList at the given index.
       */
      public long getLidList(int index) {
        return lidList_.getLong(index);
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @param index The index to set the value at.
       * @param value The lidList to set.
       * @return This builder for chaining.
       */
      public Builder setLidList(
          int index, long value) {
        ensureLidListIsMutable();
        lidList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @param value The lidList to add.
       * @return This builder for chaining.
       */
      public Builder addLidList(long value) {
        ensureLidListIsMutable();
        lidList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @param values The lidList to add.
       * @return This builder for chaining.
       */
      public Builder addAllLidList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureLidListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, lidList_);
        onChanged();
        return this;
      }
      /**
       * <pre>
       *道具唯一id列表
       * </pre>
       *
       * <code>repeated int64 lidList = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLidList() {
        lidList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private int successRate_ ;
      /**
       * <pre>
       *成功率
       * </pre>
       *
       * <code>int32 successRate = 2;</code>
       * @return The successRate.
       */
      @java.lang.Override
      public int getSuccessRate() {
        return successRate_;
      }
      /**
       * <pre>
       *成功率
       * </pre>
       *
       * <code>int32 successRate = 2;</code>
       * @param value The successRate to set.
       * @return This builder for chaining.
       */
      public Builder setSuccessRate(int value) {
        
        successRate_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *成功率
       * </pre>
       *
       * <code>int32 successRate = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuccessRate() {
        
        successRate_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:zodiacCompound.ReqCompound)
    }

    // @@protoc_insertion_point(class_scope:zodiacCompound.ReqCompound)
    private static final com.sh.game.protos.ZodiacCompoundProtos.ReqCompound DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ZodiacCompoundProtos.ReqCompound();
    }

    public static com.sh.game.protos.ZodiacCompoundProtos.ReqCompound getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqCompound>
        PARSER = new com.google.protobuf.AbstractParser<ReqCompound>() {
      @java.lang.Override
      public ReqCompound parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqCompound(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqCompound> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqCompound> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ZodiacCompoundProtos.ReqCompound getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResCompoundOrBuilder extends
      // @@protoc_insertion_point(interface_extends:zodiacCompound.ResCompound)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *合成结果 1成功 2失败
     * </pre>
     *
     * <code>int32 result = 1;</code>
     * @return The result.
     */
    int getResult();

    /**
     * <pre>
     *合成道具id
     * </pre>
     *
     * <code>int32 itemId = 2;</code>
     * @return The itemId.
     */
    int getItemId();
  }
  /**
   * <pre>
   ** class='ResCompound' id='4' desc='返回合成结果' 
   * </pre>
   *
   * Protobuf type {@code zodiacCompound.ResCompound}
   */
  public static final class ResCompound extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:zodiacCompound.ResCompound)
      ResCompoundOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResCompound.newBuilder() to construct.
    private ResCompound(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResCompound() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResCompound();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResCompound(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              result_ = input.readInt32();
              break;
            }
            case 16: {

              itemId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResCompound_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResCompound_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.ZodiacCompoundProtos.ResCompound.class, com.sh.game.protos.ZodiacCompoundProtos.ResCompound.Builder.class);
    }

    public static final int RESULT_FIELD_NUMBER = 1;
    private int result_;
    /**
     * <pre>
     *合成结果 1成功 2失败
     * </pre>
     *
     * <code>int32 result = 1;</code>
     * @return The result.
     */
    @java.lang.Override
    public int getResult() {
      return result_;
    }

    public static final int ITEMID_FIELD_NUMBER = 2;
    private int itemId_;
    /**
     * <pre>
     *合成道具id
     * </pre>
     *
     * <code>int32 itemId = 2;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (result_ != 0) {
        output.writeInt32(1, result_);
      }
      if (itemId_ != 0) {
        output.writeInt32(2, itemId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, result_);
      }
      if (itemId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, itemId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.ZodiacCompoundProtos.ResCompound)) {
        return super.equals(obj);
      }
      com.sh.game.protos.ZodiacCompoundProtos.ResCompound other = (com.sh.game.protos.ZodiacCompoundProtos.ResCompound) obj;

      if (getResult()
          != other.getResult()) return false;
      if (getItemId()
          != other.getItemId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (37 * hash) + ITEMID_FIELD_NUMBER;
      hash = (53 * hash) + getItemId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.ZodiacCompoundProtos.ResCompound prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResCompound' id='4' desc='返回合成结果' 
     * </pre>
     *
     * Protobuf type {@code zodiacCompound.ResCompound}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:zodiacCompound.ResCompound)
        com.sh.game.protos.ZodiacCompoundProtos.ResCompoundOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResCompound_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResCompound_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.ZodiacCompoundProtos.ResCompound.class, com.sh.game.protos.ZodiacCompoundProtos.ResCompound.Builder.class);
      }

      // Construct using com.sh.game.protos.ZodiacCompoundProtos.ResCompound.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        result_ = 0;

        itemId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.ZodiacCompoundProtos.internal_static_zodiacCompound_ResCompound_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ResCompound getDefaultInstanceForType() {
        return com.sh.game.protos.ZodiacCompoundProtos.ResCompound.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ResCompound build() {
        com.sh.game.protos.ZodiacCompoundProtos.ResCompound result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.ZodiacCompoundProtos.ResCompound buildPartial() {
        com.sh.game.protos.ZodiacCompoundProtos.ResCompound result = new com.sh.game.protos.ZodiacCompoundProtos.ResCompound(this);
        result.result_ = result_;
        result.itemId_ = itemId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.ZodiacCompoundProtos.ResCompound) {
          return mergeFrom((com.sh.game.protos.ZodiacCompoundProtos.ResCompound)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.ZodiacCompoundProtos.ResCompound other) {
        if (other == com.sh.game.protos.ZodiacCompoundProtos.ResCompound.getDefaultInstance()) return this;
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        if (other.getItemId() != 0) {
          setItemId(other.getItemId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.ZodiacCompoundProtos.ResCompound parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.ZodiacCompoundProtos.ResCompound) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int result_ ;
      /**
       * <pre>
       *合成结果 1成功 2失败
       * </pre>
       *
       * <code>int32 result = 1;</code>
       * @return The result.
       */
      @java.lang.Override
      public int getResult() {
        return result_;
      }
      /**
       * <pre>
       *合成结果 1成功 2失败
       * </pre>
       *
       * <code>int32 result = 1;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(int value) {
        
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *合成结果 1成功 2失败
       * </pre>
       *
       * <code>int32 result = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        
        result_ = 0;
        onChanged();
        return this;
      }

      private int itemId_ ;
      /**
       * <pre>
       *合成道具id
       * </pre>
       *
       * <code>int32 itemId = 2;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       *合成道具id
       * </pre>
       *
       * <code>int32 itemId = 2;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *合成道具id
       * </pre>
       *
       * <code>int32 itemId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        
        itemId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:zodiacCompound.ResCompound)
    }

    // @@protoc_insertion_point(class_scope:zodiacCompound.ResCompound)
    private static final com.sh.game.protos.ZodiacCompoundProtos.ResCompound DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.ZodiacCompoundProtos.ResCompound();
    }

    public static com.sh.game.protos.ZodiacCompoundProtos.ResCompound getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResCompound>
        PARSER = new com.google.protobuf.AbstractParser<ResCompound>() {
      @java.lang.Override
      public ResCompound parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResCompound(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResCompound> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResCompound> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.ZodiacCompoundProtos.ResCompound getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_zodiacCompound_ReqSuccessRate_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_zodiacCompound_ReqSuccessRate_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_zodiacCompound_ResSuccessRate_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_zodiacCompound_ResSuccessRate_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_zodiacCompound_ReqCompound_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_zodiacCompound_ReqCompound_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_zodiacCompound_ResCompound_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_zodiacCompound_ResCompound_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024zodiacCompound.proto\022\016zodiacCompound\"!" +
      "\n\016ReqSuccessRate\022\017\n\007lidList\030\001 \003(\003\"%\n\016Res" +
      "SuccessRate\022\023\n\013successRate\030\001 \001(\005\"3\n\013ReqC" +
      "ompound\022\017\n\007lidList\030\001 \003(\003\022\023\n\013successRate\030" +
      "\002 \001(\005\"-\n\013ResCompound\022\016\n\006result\030\001 \001(\005\022\016\n\006" +
      "itemId\030\002 \001(\005B*\n\022com.sh.game.protosB\024Zodi" +
      "acCompoundProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_zodiacCompound_ReqSuccessRate_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_zodiacCompound_ReqSuccessRate_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_zodiacCompound_ReqSuccessRate_descriptor,
        new java.lang.String[] { "LidList", });
    internal_static_zodiacCompound_ResSuccessRate_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_zodiacCompound_ResSuccessRate_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_zodiacCompound_ResSuccessRate_descriptor,
        new java.lang.String[] { "SuccessRate", });
    internal_static_zodiacCompound_ReqCompound_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_zodiacCompound_ReqCompound_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_zodiacCompound_ReqCompound_descriptor,
        new java.lang.String[] { "LidList", "SuccessRate", });
    internal_static_zodiacCompound_ResCompound_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_zodiacCompound_ResCompound_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_zodiacCompound_ResCompound_descriptor,
        new java.lang.String[] { "Result", "ItemId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
