// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: skillAwake.proto

package com.sh.game.protos;

public final class SkillAwakeProtos {
  private SkillAwakeProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SkillAwakeBeanOrBuilder extends
      // @@protoc_insertion_point(interface_extends:skillAwake.SkillAwakeBean)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *技能配置id
     * </pre>
     *
     * <code>int32 skillConfigId = 1;</code>
     * @return The skillConfigId.
     */
    int getSkillConfigId();

    /**
     * <pre>
     *觉醒等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    int getLevel();
  }
  /**
   * Protobuf type {@code skillAwake.SkillAwakeBean}
   */
  public static final class SkillAwakeBean extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:skillAwake.SkillAwakeBean)
      SkillAwakeBeanOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SkillAwakeBean.newBuilder() to construct.
    private SkillAwakeBean(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SkillAwakeBean() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SkillAwakeBean();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SkillAwakeBean(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              skillConfigId_ = input.readInt32();
              break;
            }
            case 16: {

              level_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_SkillAwakeBean_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_SkillAwakeBean_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.class, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder.class);
    }

    public static final int SKILLCONFIGID_FIELD_NUMBER = 1;
    private int skillConfigId_;
    /**
     * <pre>
     *技能配置id
     * </pre>
     *
     * <code>int32 skillConfigId = 1;</code>
     * @return The skillConfigId.
     */
    @java.lang.Override
    public int getSkillConfigId() {
      return skillConfigId_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     *觉醒等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (skillConfigId_ != 0) {
        output.writeInt32(1, skillConfigId_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (skillConfigId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, skillConfigId_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean other = (com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean) obj;

      if (getSkillConfigId()
          != other.getSkillConfigId()) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SKILLCONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getSkillConfigId();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code skillAwake.SkillAwakeBean}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:skillAwake.SkillAwakeBean)
        com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_SkillAwakeBean_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_SkillAwakeBean_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.class, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder.class);
      }

      // Construct using com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skillConfigId_ = 0;

        level_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_SkillAwakeBean_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean getDefaultInstanceForType() {
        return com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean build() {
        com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean buildPartial() {
        com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean result = new com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean(this);
        result.skillConfigId_ = skillConfigId_;
        result.level_ = level_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean) {
          return mergeFrom((com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean other) {
        if (other == com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.getDefaultInstance()) return this;
        if (other.getSkillConfigId() != 0) {
          setSkillConfigId(other.getSkillConfigId());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int skillConfigId_ ;
      /**
       * <pre>
       *技能配置id
       * </pre>
       *
       * <code>int32 skillConfigId = 1;</code>
       * @return The skillConfigId.
       */
      @java.lang.Override
      public int getSkillConfigId() {
        return skillConfigId_;
      }
      /**
       * <pre>
       *技能配置id
       * </pre>
       *
       * <code>int32 skillConfigId = 1;</code>
       * @param value The skillConfigId to set.
       * @return This builder for chaining.
       */
      public Builder setSkillConfigId(int value) {
        
        skillConfigId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *技能配置id
       * </pre>
       *
       * <code>int32 skillConfigId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkillConfigId() {
        
        skillConfigId_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       *觉醒等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       *觉醒等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *觉醒等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:skillAwake.SkillAwakeBean)
    }

    // @@protoc_insertion_point(class_scope:skillAwake.SkillAwakeBean)
    private static final com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean();
    }

    public static com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SkillAwakeBean>
        PARSER = new com.google.protobuf.AbstractParser<SkillAwakeBean>() {
      @java.lang.Override
      public SkillAwakeBean parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SkillAwakeBean(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SkillAwakeBean> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SkillAwakeBean> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqSkillAwakeInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:skillAwake.ReqSkillAwakeInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqSkillAwakeInfo' id='1' desc='请求技能觉醒信息' 
   * </pre>
   *
   * Protobuf type {@code skillAwake.ReqSkillAwakeInfo}
   */
  public static final class ReqSkillAwakeInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:skillAwake.ReqSkillAwakeInfo)
      ReqSkillAwakeInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqSkillAwakeInfo.newBuilder() to construct.
    private ReqSkillAwakeInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqSkillAwakeInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqSkillAwakeInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqSkillAwakeInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo.class, com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo other = (com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqSkillAwakeInfo' id='1' desc='请求技能觉醒信息' 
     * </pre>
     *
     * Protobuf type {@code skillAwake.ReqSkillAwakeInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:skillAwake.ReqSkillAwakeInfo)
        com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo.class, com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo getDefaultInstanceForType() {
        return com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo build() {
        com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo buildPartial() {
        com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo result = new com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo) {
          return mergeFrom((com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo other) {
        if (other == com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:skillAwake.ReqSkillAwakeInfo)
    }

    // @@protoc_insertion_point(class_scope:skillAwake.ReqSkillAwakeInfo)
    private static final com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo();
    }

    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqSkillAwakeInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqSkillAwakeInfo>() {
      @java.lang.Override
      public ReqSkillAwakeInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqSkillAwakeInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqSkillAwakeInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqSkillAwakeInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResSkillAwakeInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:skillAwake.ResSkillAwakeInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *技能觉醒套装配置id
     * </pre>
     *
     * <code>int32 suitId = 1;</code>
     * @return The suitId.
     */
    int getSuitId();

    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    java.util.List<com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean> 
        getSkillAwakeBeanListList();
    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean getSkillAwakeBeanList(int index);
    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    int getSkillAwakeBeanListCount();
    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    java.util.List<? extends com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder> 
        getSkillAwakeBeanListOrBuilderList();
    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder getSkillAwakeBeanListOrBuilder(
        int index);
  }
  /**
   * <pre>
   ** class='ResSkillAwakeInfo' id='2' desc='返回技能觉醒信息' 
   * </pre>
   *
   * Protobuf type {@code skillAwake.ResSkillAwakeInfo}
   */
  public static final class ResSkillAwakeInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:skillAwake.ResSkillAwakeInfo)
      ResSkillAwakeInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResSkillAwakeInfo.newBuilder() to construct.
    private ResSkillAwakeInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResSkillAwakeInfo() {
      skillAwakeBeanList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResSkillAwakeInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResSkillAwakeInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              suitId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                skillAwakeBeanList_ = new java.util.ArrayList<com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean>();
                mutable_bitField0_ |= 0x00000001;
              }
              skillAwakeBeanList_.add(
                  input.readMessage(com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.parser(), extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          skillAwakeBeanList_ = java.util.Collections.unmodifiableList(skillAwakeBeanList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo.class, com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo.Builder.class);
    }

    public static final int SUITID_FIELD_NUMBER = 1;
    private int suitId_;
    /**
     * <pre>
     *技能觉醒套装配置id
     * </pre>
     *
     * <code>int32 suitId = 1;</code>
     * @return The suitId.
     */
    @java.lang.Override
    public int getSuitId() {
      return suitId_;
    }

    public static final int SKILLAWAKEBEANLIST_FIELD_NUMBER = 2;
    private java.util.List<com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean> skillAwakeBeanList_;
    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean> getSkillAwakeBeanListList() {
      return skillAwakeBeanList_;
    }
    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder> 
        getSkillAwakeBeanListOrBuilderList() {
      return skillAwakeBeanList_;
    }
    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    @java.lang.Override
    public int getSkillAwakeBeanListCount() {
      return skillAwakeBeanList_.size();
    }
    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean getSkillAwakeBeanList(int index) {
      return skillAwakeBeanList_.get(index);
    }
    /**
     * <pre>
     *技能觉醒信息
     * </pre>
     *
     * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
     */
    @java.lang.Override
    public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder getSkillAwakeBeanListOrBuilder(
        int index) {
      return skillAwakeBeanList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (suitId_ != 0) {
        output.writeInt32(1, suitId_);
      }
      for (int i = 0; i < skillAwakeBeanList_.size(); i++) {
        output.writeMessage(2, skillAwakeBeanList_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (suitId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, suitId_);
      }
      for (int i = 0; i < skillAwakeBeanList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, skillAwakeBeanList_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo other = (com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo) obj;

      if (getSuitId()
          != other.getSuitId()) return false;
      if (!getSkillAwakeBeanListList()
          .equals(other.getSkillAwakeBeanListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SUITID_FIELD_NUMBER;
      hash = (53 * hash) + getSuitId();
      if (getSkillAwakeBeanListCount() > 0) {
        hash = (37 * hash) + SKILLAWAKEBEANLIST_FIELD_NUMBER;
        hash = (53 * hash) + getSkillAwakeBeanListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResSkillAwakeInfo' id='2' desc='返回技能觉醒信息' 
     * </pre>
     *
     * Protobuf type {@code skillAwake.ResSkillAwakeInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:skillAwake.ResSkillAwakeInfo)
        com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo.class, com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getSkillAwakeBeanListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        suitId_ = 0;

        if (skillAwakeBeanListBuilder_ == null) {
          skillAwakeBeanList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          skillAwakeBeanListBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo getDefaultInstanceForType() {
        return com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo build() {
        com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo buildPartial() {
        com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo result = new com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo(this);
        int from_bitField0_ = bitField0_;
        result.suitId_ = suitId_;
        if (skillAwakeBeanListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            skillAwakeBeanList_ = java.util.Collections.unmodifiableList(skillAwakeBeanList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.skillAwakeBeanList_ = skillAwakeBeanList_;
        } else {
          result.skillAwakeBeanList_ = skillAwakeBeanListBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo) {
          return mergeFrom((com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo other) {
        if (other == com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo.getDefaultInstance()) return this;
        if (other.getSuitId() != 0) {
          setSuitId(other.getSuitId());
        }
        if (skillAwakeBeanListBuilder_ == null) {
          if (!other.skillAwakeBeanList_.isEmpty()) {
            if (skillAwakeBeanList_.isEmpty()) {
              skillAwakeBeanList_ = other.skillAwakeBeanList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureSkillAwakeBeanListIsMutable();
              skillAwakeBeanList_.addAll(other.skillAwakeBeanList_);
            }
            onChanged();
          }
        } else {
          if (!other.skillAwakeBeanList_.isEmpty()) {
            if (skillAwakeBeanListBuilder_.isEmpty()) {
              skillAwakeBeanListBuilder_.dispose();
              skillAwakeBeanListBuilder_ = null;
              skillAwakeBeanList_ = other.skillAwakeBeanList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              skillAwakeBeanListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getSkillAwakeBeanListFieldBuilder() : null;
            } else {
              skillAwakeBeanListBuilder_.addAllMessages(other.skillAwakeBeanList_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int suitId_ ;
      /**
       * <pre>
       *技能觉醒套装配置id
       * </pre>
       *
       * <code>int32 suitId = 1;</code>
       * @return The suitId.
       */
      @java.lang.Override
      public int getSuitId() {
        return suitId_;
      }
      /**
       * <pre>
       *技能觉醒套装配置id
       * </pre>
       *
       * <code>int32 suitId = 1;</code>
       * @param value The suitId to set.
       * @return This builder for chaining.
       */
      public Builder setSuitId(int value) {
        
        suitId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *技能觉醒套装配置id
       * </pre>
       *
       * <code>int32 suitId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuitId() {
        
        suitId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean> skillAwakeBeanList_ =
        java.util.Collections.emptyList();
      private void ensureSkillAwakeBeanListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          skillAwakeBeanList_ = new java.util.ArrayList<com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean>(skillAwakeBeanList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder> skillAwakeBeanListBuilder_;

      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public java.util.List<com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean> getSkillAwakeBeanListList() {
        if (skillAwakeBeanListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(skillAwakeBeanList_);
        } else {
          return skillAwakeBeanListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public int getSkillAwakeBeanListCount() {
        if (skillAwakeBeanListBuilder_ == null) {
          return skillAwakeBeanList_.size();
        } else {
          return skillAwakeBeanListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean getSkillAwakeBeanList(int index) {
        if (skillAwakeBeanListBuilder_ == null) {
          return skillAwakeBeanList_.get(index);
        } else {
          return skillAwakeBeanListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public Builder setSkillAwakeBeanList(
          int index, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean value) {
        if (skillAwakeBeanListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSkillAwakeBeanListIsMutable();
          skillAwakeBeanList_.set(index, value);
          onChanged();
        } else {
          skillAwakeBeanListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public Builder setSkillAwakeBeanList(
          int index, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder builderForValue) {
        if (skillAwakeBeanListBuilder_ == null) {
          ensureSkillAwakeBeanListIsMutable();
          skillAwakeBeanList_.set(index, builderForValue.build());
          onChanged();
        } else {
          skillAwakeBeanListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public Builder addSkillAwakeBeanList(com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean value) {
        if (skillAwakeBeanListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSkillAwakeBeanListIsMutable();
          skillAwakeBeanList_.add(value);
          onChanged();
        } else {
          skillAwakeBeanListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public Builder addSkillAwakeBeanList(
          int index, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean value) {
        if (skillAwakeBeanListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSkillAwakeBeanListIsMutable();
          skillAwakeBeanList_.add(index, value);
          onChanged();
        } else {
          skillAwakeBeanListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public Builder addSkillAwakeBeanList(
          com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder builderForValue) {
        if (skillAwakeBeanListBuilder_ == null) {
          ensureSkillAwakeBeanListIsMutable();
          skillAwakeBeanList_.add(builderForValue.build());
          onChanged();
        } else {
          skillAwakeBeanListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public Builder addSkillAwakeBeanList(
          int index, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder builderForValue) {
        if (skillAwakeBeanListBuilder_ == null) {
          ensureSkillAwakeBeanListIsMutable();
          skillAwakeBeanList_.add(index, builderForValue.build());
          onChanged();
        } else {
          skillAwakeBeanListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public Builder addAllSkillAwakeBeanList(
          java.lang.Iterable<? extends com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean> values) {
        if (skillAwakeBeanListBuilder_ == null) {
          ensureSkillAwakeBeanListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, skillAwakeBeanList_);
          onChanged();
        } else {
          skillAwakeBeanListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public Builder clearSkillAwakeBeanList() {
        if (skillAwakeBeanListBuilder_ == null) {
          skillAwakeBeanList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          skillAwakeBeanListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public Builder removeSkillAwakeBeanList(int index) {
        if (skillAwakeBeanListBuilder_ == null) {
          ensureSkillAwakeBeanListIsMutable();
          skillAwakeBeanList_.remove(index);
          onChanged();
        } else {
          skillAwakeBeanListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder getSkillAwakeBeanListBuilder(
          int index) {
        return getSkillAwakeBeanListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder getSkillAwakeBeanListOrBuilder(
          int index) {
        if (skillAwakeBeanListBuilder_ == null) {
          return skillAwakeBeanList_.get(index);  } else {
          return skillAwakeBeanListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public java.util.List<? extends com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder> 
           getSkillAwakeBeanListOrBuilderList() {
        if (skillAwakeBeanListBuilder_ != null) {
          return skillAwakeBeanListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(skillAwakeBeanList_);
        }
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder addSkillAwakeBeanListBuilder() {
        return getSkillAwakeBeanListFieldBuilder().addBuilder(
            com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.getDefaultInstance());
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder addSkillAwakeBeanListBuilder(
          int index) {
        return getSkillAwakeBeanListFieldBuilder().addBuilder(
            index, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.getDefaultInstance());
      }
      /**
       * <pre>
       *技能觉醒信息
       * </pre>
       *
       * <code>repeated .skillAwake.SkillAwakeBean skillAwakeBeanList = 2;</code>
       */
      public java.util.List<com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder> 
           getSkillAwakeBeanListBuilderList() {
        return getSkillAwakeBeanListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder> 
          getSkillAwakeBeanListFieldBuilder() {
        if (skillAwakeBeanListBuilder_ == null) {
          skillAwakeBeanListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBean.Builder, com.sh.game.protos.SkillAwakeProtos.SkillAwakeBeanOrBuilder>(
                  skillAwakeBeanList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          skillAwakeBeanList_ = null;
        }
        return skillAwakeBeanListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:skillAwake.ResSkillAwakeInfo)
    }

    // @@protoc_insertion_point(class_scope:skillAwake.ResSkillAwakeInfo)
    private static final com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo();
    }

    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResSkillAwakeInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResSkillAwakeInfo>() {
      @java.lang.Override
      public ResSkillAwakeInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResSkillAwakeInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResSkillAwakeInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResSkillAwakeInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqSkillAwakeUpgradeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:skillAwake.ReqSkillAwakeUpgrade)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *技能配置id
     * </pre>
     *
     * <code>int32 skillConfigId = 1;</code>
     * @return The skillConfigId.
     */
    int getSkillConfigId();
  }
  /**
   * <pre>
   ** class='ReqSkillAwakeUpgrade' id='3' desc='请求技能觉醒升级' 
   * </pre>
   *
   * Protobuf type {@code skillAwake.ReqSkillAwakeUpgrade}
   */
  public static final class ReqSkillAwakeUpgrade extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:skillAwake.ReqSkillAwakeUpgrade)
      ReqSkillAwakeUpgradeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqSkillAwakeUpgrade.newBuilder() to construct.
    private ReqSkillAwakeUpgrade(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqSkillAwakeUpgrade() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqSkillAwakeUpgrade();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqSkillAwakeUpgrade(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              skillConfigId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeUpgrade_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeUpgrade_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade.class, com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade.Builder.class);
    }

    public static final int SKILLCONFIGID_FIELD_NUMBER = 1;
    private int skillConfigId_;
    /**
     * <pre>
     *技能配置id
     * </pre>
     *
     * <code>int32 skillConfigId = 1;</code>
     * @return The skillConfigId.
     */
    @java.lang.Override
    public int getSkillConfigId() {
      return skillConfigId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (skillConfigId_ != 0) {
        output.writeInt32(1, skillConfigId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (skillConfigId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, skillConfigId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade other = (com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade) obj;

      if (getSkillConfigId()
          != other.getSkillConfigId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SKILLCONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getSkillConfigId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqSkillAwakeUpgrade' id='3' desc='请求技能觉醒升级' 
     * </pre>
     *
     * Protobuf type {@code skillAwake.ReqSkillAwakeUpgrade}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:skillAwake.ReqSkillAwakeUpgrade)
        com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgradeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeUpgrade_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeUpgrade_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade.class, com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade.Builder.class);
      }

      // Construct using com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skillConfigId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ReqSkillAwakeUpgrade_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade getDefaultInstanceForType() {
        return com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade build() {
        com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade buildPartial() {
        com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade result = new com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade(this);
        result.skillConfigId_ = skillConfigId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade) {
          return mergeFrom((com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade other) {
        if (other == com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade.getDefaultInstance()) return this;
        if (other.getSkillConfigId() != 0) {
          setSkillConfigId(other.getSkillConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int skillConfigId_ ;
      /**
       * <pre>
       *技能配置id
       * </pre>
       *
       * <code>int32 skillConfigId = 1;</code>
       * @return The skillConfigId.
       */
      @java.lang.Override
      public int getSkillConfigId() {
        return skillConfigId_;
      }
      /**
       * <pre>
       *技能配置id
       * </pre>
       *
       * <code>int32 skillConfigId = 1;</code>
       * @param value The skillConfigId to set.
       * @return This builder for chaining.
       */
      public Builder setSkillConfigId(int value) {
        
        skillConfigId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *技能配置id
       * </pre>
       *
       * <code>int32 skillConfigId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkillConfigId() {
        
        skillConfigId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:skillAwake.ReqSkillAwakeUpgrade)
    }

    // @@protoc_insertion_point(class_scope:skillAwake.ReqSkillAwakeUpgrade)
    private static final com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade();
    }

    public static com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqSkillAwakeUpgrade>
        PARSER = new com.google.protobuf.AbstractParser<ReqSkillAwakeUpgrade>() {
      @java.lang.Override
      public ReqSkillAwakeUpgrade parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqSkillAwakeUpgrade(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqSkillAwakeUpgrade> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqSkillAwakeUpgrade> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SkillAwakeProtos.ReqSkillAwakeUpgrade getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResSkillAwakeUpgradeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:skillAwake.ResSkillAwakeUpgrade)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *技能配置id
     * </pre>
     *
     * <code>int32 skillConfigId = 1;</code>
     * @return The skillConfigId.
     */
    int getSkillConfigId();

    /**
     * <pre>
     *觉醒等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    int getLevel();
  }
  /**
   * <pre>
   ** class='ResSkillAwakeUpgrade' id='4' desc='返回技能觉醒升级信息' 
   * </pre>
   *
   * Protobuf type {@code skillAwake.ResSkillAwakeUpgrade}
   */
  public static final class ResSkillAwakeUpgrade extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:skillAwake.ResSkillAwakeUpgrade)
      ResSkillAwakeUpgradeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResSkillAwakeUpgrade.newBuilder() to construct.
    private ResSkillAwakeUpgrade(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResSkillAwakeUpgrade() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResSkillAwakeUpgrade();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResSkillAwakeUpgrade(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              skillConfigId_ = input.readInt32();
              break;
            }
            case 16: {

              level_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeUpgrade_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeUpgrade_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade.class, com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade.Builder.class);
    }

    public static final int SKILLCONFIGID_FIELD_NUMBER = 1;
    private int skillConfigId_;
    /**
     * <pre>
     *技能配置id
     * </pre>
     *
     * <code>int32 skillConfigId = 1;</code>
     * @return The skillConfigId.
     */
    @java.lang.Override
    public int getSkillConfigId() {
      return skillConfigId_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_;
    /**
     * <pre>
     *觉醒等级
     * </pre>
     *
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (skillConfigId_ != 0) {
        output.writeInt32(1, skillConfigId_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (skillConfigId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, skillConfigId_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade)) {
        return super.equals(obj);
      }
      com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade other = (com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade) obj;

      if (getSkillConfigId()
          != other.getSkillConfigId()) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SKILLCONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getSkillConfigId();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResSkillAwakeUpgrade' id='4' desc='返回技能觉醒升级信息' 
     * </pre>
     *
     * Protobuf type {@code skillAwake.ResSkillAwakeUpgrade}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:skillAwake.ResSkillAwakeUpgrade)
        com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgradeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeUpgrade_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeUpgrade_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade.class, com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade.Builder.class);
      }

      // Construct using com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        skillConfigId_ = 0;

        level_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.SkillAwakeProtos.internal_static_skillAwake_ResSkillAwakeUpgrade_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade getDefaultInstanceForType() {
        return com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade build() {
        com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade buildPartial() {
        com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade result = new com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade(this);
        result.skillConfigId_ = skillConfigId_;
        result.level_ = level_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade) {
          return mergeFrom((com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade other) {
        if (other == com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade.getDefaultInstance()) return this;
        if (other.getSkillConfigId() != 0) {
          setSkillConfigId(other.getSkillConfigId());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int skillConfigId_ ;
      /**
       * <pre>
       *技能配置id
       * </pre>
       *
       * <code>int32 skillConfigId = 1;</code>
       * @return The skillConfigId.
       */
      @java.lang.Override
      public int getSkillConfigId() {
        return skillConfigId_;
      }
      /**
       * <pre>
       *技能配置id
       * </pre>
       *
       * <code>int32 skillConfigId = 1;</code>
       * @param value The skillConfigId to set.
       * @return This builder for chaining.
       */
      public Builder setSkillConfigId(int value) {
        
        skillConfigId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *技能配置id
       * </pre>
       *
       * <code>int32 skillConfigId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkillConfigId() {
        
        skillConfigId_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <pre>
       *觉醒等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <pre>
       *觉醒等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {
        
        level_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *觉醒等级
       * </pre>
       *
       * <code>int32 level = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        
        level_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:skillAwake.ResSkillAwakeUpgrade)
    }

    // @@protoc_insertion_point(class_scope:skillAwake.ResSkillAwakeUpgrade)
    private static final com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade();
    }

    public static com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResSkillAwakeUpgrade>
        PARSER = new com.google.protobuf.AbstractParser<ResSkillAwakeUpgrade>() {
      @java.lang.Override
      public ResSkillAwakeUpgrade parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResSkillAwakeUpgrade(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResSkillAwakeUpgrade> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResSkillAwakeUpgrade> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.SkillAwakeProtos.ResSkillAwakeUpgrade getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_skillAwake_SkillAwakeBean_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_skillAwake_SkillAwakeBean_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_skillAwake_ReqSkillAwakeInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_skillAwake_ReqSkillAwakeInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_skillAwake_ResSkillAwakeInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_skillAwake_ResSkillAwakeInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_skillAwake_ReqSkillAwakeUpgrade_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_skillAwake_ReqSkillAwakeUpgrade_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_skillAwake_ResSkillAwakeUpgrade_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_skillAwake_ResSkillAwakeUpgrade_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\020skillAwake.proto\022\nskillAwake\"6\n\016SkillA" +
      "wakeBean\022\025\n\rskillConfigId\030\001 \001(\005\022\r\n\005level" +
      "\030\002 \001(\005\"\023\n\021ReqSkillAwakeInfo\"[\n\021ResSkillA" +
      "wakeInfo\022\016\n\006suitId\030\001 \001(\005\0226\n\022skillAwakeBe" +
      "anList\030\002 \003(\0132\032.skillAwake.SkillAwakeBean" +
      "\"-\n\024ReqSkillAwakeUpgrade\022\025\n\rskillConfigI" +
      "d\030\001 \001(\005\"<\n\024ResSkillAwakeUpgrade\022\025\n\rskill" +
      "ConfigId\030\001 \001(\005\022\r\n\005level\030\002 \001(\005B&\n\022com.sh." +
      "game.protosB\020SkillAwakeProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_skillAwake_SkillAwakeBean_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_skillAwake_SkillAwakeBean_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_skillAwake_SkillAwakeBean_descriptor,
        new java.lang.String[] { "SkillConfigId", "Level", });
    internal_static_skillAwake_ReqSkillAwakeInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_skillAwake_ReqSkillAwakeInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_skillAwake_ReqSkillAwakeInfo_descriptor,
        new java.lang.String[] { });
    internal_static_skillAwake_ResSkillAwakeInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_skillAwake_ResSkillAwakeInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_skillAwake_ResSkillAwakeInfo_descriptor,
        new java.lang.String[] { "SuitId", "SkillAwakeBeanList", });
    internal_static_skillAwake_ReqSkillAwakeUpgrade_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_skillAwake_ReqSkillAwakeUpgrade_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_skillAwake_ReqSkillAwakeUpgrade_descriptor,
        new java.lang.String[] { "SkillConfigId", });
    internal_static_skillAwake_ResSkillAwakeUpgrade_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_skillAwake_ResSkillAwakeUpgrade_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_skillAwake_ResSkillAwakeUpgrade_descriptor,
        new java.lang.String[] { "SkillConfigId", "Level", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
