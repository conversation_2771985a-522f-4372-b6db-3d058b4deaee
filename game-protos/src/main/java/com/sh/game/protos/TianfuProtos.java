// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tianfu.proto

package com.sh.game.protos;

public final class TianfuProtos {
  private TianfuProtos() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ReqTianFuInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:tianfu.ReqTianFuInfo)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   ** class='ReqTianFuInfo' id='1' desc='请求当前封号信息' 
   * </pre>
   *
   * Protobuf type {@code tianfu.ReqTianFuInfo}
   */
  public static final class ReqTianFuInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:tianfu.ReqTianFuInfo)
      ReqTianFuInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqTianFuInfo.newBuilder() to construct.
    private ReqTianFuInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqTianFuInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqTianFuInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqTianFuInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.TianfuProtos.ReqTianFuInfo.class, com.sh.game.protos.TianfuProtos.ReqTianFuInfo.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.TianfuProtos.ReqTianFuInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.TianfuProtos.ReqTianFuInfo other = (com.sh.game.protos.TianfuProtos.ReqTianFuInfo) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.TianfuProtos.ReqTianFuInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqTianFuInfo' id='1' desc='请求当前封号信息' 
     * </pre>
     *
     * Protobuf type {@code tianfu.ReqTianFuInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:tianfu.ReqTianFuInfo)
        com.sh.game.protos.TianfuProtos.ReqTianFuInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.TianfuProtos.ReqTianFuInfo.class, com.sh.game.protos.TianfuProtos.ReqTianFuInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.TianfuProtos.ReqTianFuInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ReqTianFuInfo getDefaultInstanceForType() {
        return com.sh.game.protos.TianfuProtos.ReqTianFuInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ReqTianFuInfo build() {
        com.sh.game.protos.TianfuProtos.ReqTianFuInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ReqTianFuInfo buildPartial() {
        com.sh.game.protos.TianfuProtos.ReqTianFuInfo result = new com.sh.game.protos.TianfuProtos.ReqTianFuInfo(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.TianfuProtos.ReqTianFuInfo) {
          return mergeFrom((com.sh.game.protos.TianfuProtos.ReqTianFuInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.TianfuProtos.ReqTianFuInfo other) {
        if (other == com.sh.game.protos.TianfuProtos.ReqTianFuInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.TianfuProtos.ReqTianFuInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.TianfuProtos.ReqTianFuInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:tianfu.ReqTianFuInfo)
    }

    // @@protoc_insertion_point(class_scope:tianfu.ReqTianFuInfo)
    private static final com.sh.game.protos.TianfuProtos.ReqTianFuInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.TianfuProtos.ReqTianFuInfo();
    }

    public static com.sh.game.protos.TianfuProtos.ReqTianFuInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqTianFuInfo>
        PARSER = new com.google.protobuf.AbstractParser<ReqTianFuInfo>() {
      @java.lang.Override
      public ReqTianFuInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqTianFuInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqTianFuInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqTianFuInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.TianfuProtos.ReqTianFuInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTianFuInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:tianfu.ResTianFuInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *攻击属性天赋的当前等级
     * </pre>
     *
     * <code>int32 tf1 = 1;</code>
     * @return The tf1.
     */
    int getTf1();

    /**
     * <pre>
     * 血量天赋的当前等级
     * </pre>
     *
     * <code>int32 tf2 = 2;</code>
     * @return The tf2.
     */
    int getTf2();

    /**
     * <pre>
     *悟性当前等级配置id
     * </pre>
     *
     * <code>int32 perceptionConfigId = 3;</code>
     * @return The perceptionConfigId.
     */
    int getPerceptionConfigId();
  }
  /**
   * <pre>
   ** class='ResTianFuInfo' id='2' desc='返回当前天赋等级信息' 
   * </pre>
   *
   * Protobuf type {@code tianfu.ResTianFuInfo}
   */
  public static final class ResTianFuInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:tianfu.ResTianFuInfo)
      ResTianFuInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTianFuInfo.newBuilder() to construct.
    private ResTianFuInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTianFuInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTianFuInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTianFuInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              tf1_ = input.readInt32();
              break;
            }
            case 16: {

              tf2_ = input.readInt32();
              break;
            }
            case 24: {

              perceptionConfigId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.TianfuProtos.ResTianFuInfo.class, com.sh.game.protos.TianfuProtos.ResTianFuInfo.Builder.class);
    }

    public static final int TF1_FIELD_NUMBER = 1;
    private int tf1_;
    /**
     * <pre>
     *攻击属性天赋的当前等级
     * </pre>
     *
     * <code>int32 tf1 = 1;</code>
     * @return The tf1.
     */
    @java.lang.Override
    public int getTf1() {
      return tf1_;
    }

    public static final int TF2_FIELD_NUMBER = 2;
    private int tf2_;
    /**
     * <pre>
     * 血量天赋的当前等级
     * </pre>
     *
     * <code>int32 tf2 = 2;</code>
     * @return The tf2.
     */
    @java.lang.Override
    public int getTf2() {
      return tf2_;
    }

    public static final int PERCEPTIONCONFIGID_FIELD_NUMBER = 3;
    private int perceptionConfigId_;
    /**
     * <pre>
     *悟性当前等级配置id
     * </pre>
     *
     * <code>int32 perceptionConfigId = 3;</code>
     * @return The perceptionConfigId.
     */
    @java.lang.Override
    public int getPerceptionConfigId() {
      return perceptionConfigId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (tf1_ != 0) {
        output.writeInt32(1, tf1_);
      }
      if (tf2_ != 0) {
        output.writeInt32(2, tf2_);
      }
      if (perceptionConfigId_ != 0) {
        output.writeInt32(3, perceptionConfigId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (tf1_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, tf1_);
      }
      if (tf2_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, tf2_);
      }
      if (perceptionConfigId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, perceptionConfigId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.TianfuProtos.ResTianFuInfo)) {
        return super.equals(obj);
      }
      com.sh.game.protos.TianfuProtos.ResTianFuInfo other = (com.sh.game.protos.TianfuProtos.ResTianFuInfo) obj;

      if (getTf1()
          != other.getTf1()) return false;
      if (getTf2()
          != other.getTf2()) return false;
      if (getPerceptionConfigId()
          != other.getPerceptionConfigId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TF1_FIELD_NUMBER;
      hash = (53 * hash) + getTf1();
      hash = (37 * hash) + TF2_FIELD_NUMBER;
      hash = (53 * hash) + getTf2();
      hash = (37 * hash) + PERCEPTIONCONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getPerceptionConfigId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.TianfuProtos.ResTianFuInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResTianFuInfo' id='2' desc='返回当前天赋等级信息' 
     * </pre>
     *
     * Protobuf type {@code tianfu.ResTianFuInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:tianfu.ResTianFuInfo)
        com.sh.game.protos.TianfuProtos.ResTianFuInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.TianfuProtos.ResTianFuInfo.class, com.sh.game.protos.TianfuProtos.ResTianFuInfo.Builder.class);
      }

      // Construct using com.sh.game.protos.TianfuProtos.ResTianFuInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        tf1_ = 0;

        tf2_ = 0;

        perceptionConfigId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuInfo_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ResTianFuInfo getDefaultInstanceForType() {
        return com.sh.game.protos.TianfuProtos.ResTianFuInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ResTianFuInfo build() {
        com.sh.game.protos.TianfuProtos.ResTianFuInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ResTianFuInfo buildPartial() {
        com.sh.game.protos.TianfuProtos.ResTianFuInfo result = new com.sh.game.protos.TianfuProtos.ResTianFuInfo(this);
        result.tf1_ = tf1_;
        result.tf2_ = tf2_;
        result.perceptionConfigId_ = perceptionConfigId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.TianfuProtos.ResTianFuInfo) {
          return mergeFrom((com.sh.game.protos.TianfuProtos.ResTianFuInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.TianfuProtos.ResTianFuInfo other) {
        if (other == com.sh.game.protos.TianfuProtos.ResTianFuInfo.getDefaultInstance()) return this;
        if (other.getTf1() != 0) {
          setTf1(other.getTf1());
        }
        if (other.getTf2() != 0) {
          setTf2(other.getTf2());
        }
        if (other.getPerceptionConfigId() != 0) {
          setPerceptionConfigId(other.getPerceptionConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.TianfuProtos.ResTianFuInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.TianfuProtos.ResTianFuInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int tf1_ ;
      /**
       * <pre>
       *攻击属性天赋的当前等级
       * </pre>
       *
       * <code>int32 tf1 = 1;</code>
       * @return The tf1.
       */
      @java.lang.Override
      public int getTf1() {
        return tf1_;
      }
      /**
       * <pre>
       *攻击属性天赋的当前等级
       * </pre>
       *
       * <code>int32 tf1 = 1;</code>
       * @param value The tf1 to set.
       * @return This builder for chaining.
       */
      public Builder setTf1(int value) {
        
        tf1_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *攻击属性天赋的当前等级
       * </pre>
       *
       * <code>int32 tf1 = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTf1() {
        
        tf1_ = 0;
        onChanged();
        return this;
      }

      private int tf2_ ;
      /**
       * <pre>
       * 血量天赋的当前等级
       * </pre>
       *
       * <code>int32 tf2 = 2;</code>
       * @return The tf2.
       */
      @java.lang.Override
      public int getTf2() {
        return tf2_;
      }
      /**
       * <pre>
       * 血量天赋的当前等级
       * </pre>
       *
       * <code>int32 tf2 = 2;</code>
       * @param value The tf2 to set.
       * @return This builder for chaining.
       */
      public Builder setTf2(int value) {
        
        tf2_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 血量天赋的当前等级
       * </pre>
       *
       * <code>int32 tf2 = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTf2() {
        
        tf2_ = 0;
        onChanged();
        return this;
      }

      private int perceptionConfigId_ ;
      /**
       * <pre>
       *悟性当前等级配置id
       * </pre>
       *
       * <code>int32 perceptionConfigId = 3;</code>
       * @return The perceptionConfigId.
       */
      @java.lang.Override
      public int getPerceptionConfigId() {
        return perceptionConfigId_;
      }
      /**
       * <pre>
       *悟性当前等级配置id
       * </pre>
       *
       * <code>int32 perceptionConfigId = 3;</code>
       * @param value The perceptionConfigId to set.
       * @return This builder for chaining.
       */
      public Builder setPerceptionConfigId(int value) {
        
        perceptionConfigId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *悟性当前等级配置id
       * </pre>
       *
       * <code>int32 perceptionConfigId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPerceptionConfigId() {
        
        perceptionConfigId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:tianfu.ResTianFuInfo)
    }

    // @@protoc_insertion_point(class_scope:tianfu.ResTianFuInfo)
    private static final com.sh.game.protos.TianfuProtos.ResTianFuInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.TianfuProtos.ResTianFuInfo();
    }

    public static com.sh.game.protos.TianfuProtos.ResTianFuInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTianFuInfo>
        PARSER = new com.google.protobuf.AbstractParser<ResTianFuInfo>() {
      @java.lang.Override
      public ResTianFuInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTianFuInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTianFuInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTianFuInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.TianfuProtos.ResTianFuInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReqTianFuUpgradeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:tianfu.ReqTianFuUpgrade)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *需要升级哪个天赋，1：天赋攻击  2: 根骨血量 3: 悟性切割
     * </pre>
     *
     * <code>int32 idx = 1;</code>
     * @return The idx.
     */
    int getIdx();

    /**
     * <pre>
     *传入升级次数， 不传或传0，默认升级10次
     * </pre>
     *
     * <code>int32 cnt = 2;</code>
     * @return The cnt.
     */
    int getCnt();
  }
  /**
   * <pre>
   ** class='ReqTianFuUpgrade' id='3' desc='请求封号升级' 
   * </pre>
   *
   * Protobuf type {@code tianfu.ReqTianFuUpgrade}
   */
  public static final class ReqTianFuUpgrade extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:tianfu.ReqTianFuUpgrade)
      ReqTianFuUpgradeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReqTianFuUpgrade.newBuilder() to construct.
    private ReqTianFuUpgrade(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReqTianFuUpgrade() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReqTianFuUpgrade();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReqTianFuUpgrade(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              idx_ = input.readInt32();
              break;
            }
            case 16: {

              cnt_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuUpgrade_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuUpgrade_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade.class, com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade.Builder.class);
    }

    public static final int IDX_FIELD_NUMBER = 1;
    private int idx_;
    /**
     * <pre>
     *需要升级哪个天赋，1：天赋攻击  2: 根骨血量 3: 悟性切割
     * </pre>
     *
     * <code>int32 idx = 1;</code>
     * @return The idx.
     */
    @java.lang.Override
    public int getIdx() {
      return idx_;
    }

    public static final int CNT_FIELD_NUMBER = 2;
    private int cnt_;
    /**
     * <pre>
     *传入升级次数， 不传或传0，默认升级10次
     * </pre>
     *
     * <code>int32 cnt = 2;</code>
     * @return The cnt.
     */
    @java.lang.Override
    public int getCnt() {
      return cnt_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (idx_ != 0) {
        output.writeInt32(1, idx_);
      }
      if (cnt_ != 0) {
        output.writeInt32(2, cnt_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (idx_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, idx_);
      }
      if (cnt_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, cnt_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade)) {
        return super.equals(obj);
      }
      com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade other = (com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade) obj;

      if (getIdx()
          != other.getIdx()) return false;
      if (getCnt()
          != other.getCnt()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + IDX_FIELD_NUMBER;
      hash = (53 * hash) + getIdx();
      hash = (37 * hash) + CNT_FIELD_NUMBER;
      hash = (53 * hash) + getCnt();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ReqTianFuUpgrade' id='3' desc='请求封号升级' 
     * </pre>
     *
     * Protobuf type {@code tianfu.ReqTianFuUpgrade}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:tianfu.ReqTianFuUpgrade)
        com.sh.game.protos.TianfuProtos.ReqTianFuUpgradeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuUpgrade_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuUpgrade_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade.class, com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade.Builder.class);
      }

      // Construct using com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        idx_ = 0;

        cnt_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ReqTianFuUpgrade_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade getDefaultInstanceForType() {
        return com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade build() {
        com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade buildPartial() {
        com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade result = new com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade(this);
        result.idx_ = idx_;
        result.cnt_ = cnt_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade) {
          return mergeFrom((com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade other) {
        if (other == com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade.getDefaultInstance()) return this;
        if (other.getIdx() != 0) {
          setIdx(other.getIdx());
        }
        if (other.getCnt() != 0) {
          setCnt(other.getCnt());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int idx_ ;
      /**
       * <pre>
       *需要升级哪个天赋，1：天赋攻击  2: 根骨血量 3: 悟性切割
       * </pre>
       *
       * <code>int32 idx = 1;</code>
       * @return The idx.
       */
      @java.lang.Override
      public int getIdx() {
        return idx_;
      }
      /**
       * <pre>
       *需要升级哪个天赋，1：天赋攻击  2: 根骨血量 3: 悟性切割
       * </pre>
       *
       * <code>int32 idx = 1;</code>
       * @param value The idx to set.
       * @return This builder for chaining.
       */
      public Builder setIdx(int value) {
        
        idx_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *需要升级哪个天赋，1：天赋攻击  2: 根骨血量 3: 悟性切割
       * </pre>
       *
       * <code>int32 idx = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIdx() {
        
        idx_ = 0;
        onChanged();
        return this;
      }

      private int cnt_ ;
      /**
       * <pre>
       *传入升级次数， 不传或传0，默认升级10次
       * </pre>
       *
       * <code>int32 cnt = 2;</code>
       * @return The cnt.
       */
      @java.lang.Override
      public int getCnt() {
        return cnt_;
      }
      /**
       * <pre>
       *传入升级次数， 不传或传0，默认升级10次
       * </pre>
       *
       * <code>int32 cnt = 2;</code>
       * @param value The cnt to set.
       * @return This builder for chaining.
       */
      public Builder setCnt(int value) {
        
        cnt_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *传入升级次数， 不传或传0，默认升级10次
       * </pre>
       *
       * <code>int32 cnt = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCnt() {
        
        cnt_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:tianfu.ReqTianFuUpgrade)
    }

    // @@protoc_insertion_point(class_scope:tianfu.ReqTianFuUpgrade)
    private static final com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade();
    }

    public static com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ReqTianFuUpgrade>
        PARSER = new com.google.protobuf.AbstractParser<ReqTianFuUpgrade>() {
      @java.lang.Override
      public ReqTianFuUpgrade parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReqTianFuUpgrade(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReqTianFuUpgrade> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReqTianFuUpgrade> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.TianfuProtos.ReqTianFuUpgrade getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResTianFuUpgradeOrBuilder extends
      // @@protoc_insertion_point(interface_extends:tianfu.ResTianFuUpgrade)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *当前操作的天赋：1：天赋攻击  2: 根骨血量 3: 悟性切割
     * </pre>
     *
     * <code>int32 idx = 1;</code>
     * @return The idx.
     */
    int getIdx();

    /**
     * <pre>
     *升级成功次数
     * </pre>
     *
     * <code>int32 upSuccess = 2;</code>
     * @return The upSuccess.
     */
    int getUpSuccess();

    /**
     * <pre>
     *升级失败次数
     * </pre>
     *
     * <code>int32 upFaield = 3;</code>
     * @return The upFaield.
     */
    int getUpFaield();

    /**
     * <pre>
     *总升级次数
     * </pre>
     *
     * <code>int32 count = 4;</code>
     * @return The count.
     */
    int getCount();

    /**
     * <pre>
     *攻击属性天赋的当前等级
     * </pre>
     *
     * <code>int32 tf1 = 5;</code>
     * @return The tf1.
     */
    int getTf1();

    /**
     * <pre>
     * 血量天赋的当前等级
     * </pre>
     *
     * <code>int32 tf2 = 6;</code>
     * @return The tf2.
     */
    int getTf2();

    /**
     * <pre>
     *悟性当前等级配置id
     * </pre>
     *
     * <code>int32 perceptionConfigId = 7;</code>
     * @return The perceptionConfigId.
     */
    int getPerceptionConfigId();
  }
  /**
   * <pre>
   ** class='ResTianFuUpgrade' id='4' desc='返回升级后的等级情况' 
   * </pre>
   *
   * Protobuf type {@code tianfu.ResTianFuUpgrade}
   */
  public static final class ResTianFuUpgrade extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:tianfu.ResTianFuUpgrade)
      ResTianFuUpgradeOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResTianFuUpgrade.newBuilder() to construct.
    private ResTianFuUpgrade(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResTianFuUpgrade() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResTianFuUpgrade();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResTianFuUpgrade(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {

              idx_ = input.readInt32();
              break;
            }
            case 16: {

              upSuccess_ = input.readInt32();
              break;
            }
            case 24: {

              upFaield_ = input.readInt32();
              break;
            }
            case 32: {

              count_ = input.readInt32();
              break;
            }
            case 40: {

              tf1_ = input.readInt32();
              break;
            }
            case 48: {

              tf2_ = input.readInt32();
              break;
            }
            case 56: {

              perceptionConfigId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuUpgrade_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuUpgrade_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.sh.game.protos.TianfuProtos.ResTianFuUpgrade.class, com.sh.game.protos.TianfuProtos.ResTianFuUpgrade.Builder.class);
    }

    public static final int IDX_FIELD_NUMBER = 1;
    private int idx_;
    /**
     * <pre>
     *当前操作的天赋：1：天赋攻击  2: 根骨血量 3: 悟性切割
     * </pre>
     *
     * <code>int32 idx = 1;</code>
     * @return The idx.
     */
    @java.lang.Override
    public int getIdx() {
      return idx_;
    }

    public static final int UPSUCCESS_FIELD_NUMBER = 2;
    private int upSuccess_;
    /**
     * <pre>
     *升级成功次数
     * </pre>
     *
     * <code>int32 upSuccess = 2;</code>
     * @return The upSuccess.
     */
    @java.lang.Override
    public int getUpSuccess() {
      return upSuccess_;
    }

    public static final int UPFAIELD_FIELD_NUMBER = 3;
    private int upFaield_;
    /**
     * <pre>
     *升级失败次数
     * </pre>
     *
     * <code>int32 upFaield = 3;</code>
     * @return The upFaield.
     */
    @java.lang.Override
    public int getUpFaield() {
      return upFaield_;
    }

    public static final int COUNT_FIELD_NUMBER = 4;
    private int count_;
    /**
     * <pre>
     *总升级次数
     * </pre>
     *
     * <code>int32 count = 4;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    public static final int TF1_FIELD_NUMBER = 5;
    private int tf1_;
    /**
     * <pre>
     *攻击属性天赋的当前等级
     * </pre>
     *
     * <code>int32 tf1 = 5;</code>
     * @return The tf1.
     */
    @java.lang.Override
    public int getTf1() {
      return tf1_;
    }

    public static final int TF2_FIELD_NUMBER = 6;
    private int tf2_;
    /**
     * <pre>
     * 血量天赋的当前等级
     * </pre>
     *
     * <code>int32 tf2 = 6;</code>
     * @return The tf2.
     */
    @java.lang.Override
    public int getTf2() {
      return tf2_;
    }

    public static final int PERCEPTIONCONFIGID_FIELD_NUMBER = 7;
    private int perceptionConfigId_;
    /**
     * <pre>
     *悟性当前等级配置id
     * </pre>
     *
     * <code>int32 perceptionConfigId = 7;</code>
     * @return The perceptionConfigId.
     */
    @java.lang.Override
    public int getPerceptionConfigId() {
      return perceptionConfigId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (idx_ != 0) {
        output.writeInt32(1, idx_);
      }
      if (upSuccess_ != 0) {
        output.writeInt32(2, upSuccess_);
      }
      if (upFaield_ != 0) {
        output.writeInt32(3, upFaield_);
      }
      if (count_ != 0) {
        output.writeInt32(4, count_);
      }
      if (tf1_ != 0) {
        output.writeInt32(5, tf1_);
      }
      if (tf2_ != 0) {
        output.writeInt32(6, tf2_);
      }
      if (perceptionConfigId_ != 0) {
        output.writeInt32(7, perceptionConfigId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (idx_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, idx_);
      }
      if (upSuccess_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, upSuccess_);
      }
      if (upFaield_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, upFaield_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, count_);
      }
      if (tf1_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, tf1_);
      }
      if (tf2_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, tf2_);
      }
      if (perceptionConfigId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, perceptionConfigId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.sh.game.protos.TianfuProtos.ResTianFuUpgrade)) {
        return super.equals(obj);
      }
      com.sh.game.protos.TianfuProtos.ResTianFuUpgrade other = (com.sh.game.protos.TianfuProtos.ResTianFuUpgrade) obj;

      if (getIdx()
          != other.getIdx()) return false;
      if (getUpSuccess()
          != other.getUpSuccess()) return false;
      if (getUpFaield()
          != other.getUpFaield()) return false;
      if (getCount()
          != other.getCount()) return false;
      if (getTf1()
          != other.getTf1()) return false;
      if (getTf2()
          != other.getTf2()) return false;
      if (getPerceptionConfigId()
          != other.getPerceptionConfigId()) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + IDX_FIELD_NUMBER;
      hash = (53 * hash) + getIdx();
      hash = (37 * hash) + UPSUCCESS_FIELD_NUMBER;
      hash = (53 * hash) + getUpSuccess();
      hash = (37 * hash) + UPFAIELD_FIELD_NUMBER;
      hash = (53 * hash) + getUpFaield();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (37 * hash) + TF1_FIELD_NUMBER;
      hash = (53 * hash) + getTf1();
      hash = (37 * hash) + TF2_FIELD_NUMBER;
      hash = (53 * hash) + getTf2();
      hash = (37 * hash) + PERCEPTIONCONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getPerceptionConfigId();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.sh.game.protos.TianfuProtos.ResTianFuUpgrade prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     ** class='ResTianFuUpgrade' id='4' desc='返回升级后的等级情况' 
     * </pre>
     *
     * Protobuf type {@code tianfu.ResTianFuUpgrade}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:tianfu.ResTianFuUpgrade)
        com.sh.game.protos.TianfuProtos.ResTianFuUpgradeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuUpgrade_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuUpgrade_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.sh.game.protos.TianfuProtos.ResTianFuUpgrade.class, com.sh.game.protos.TianfuProtos.ResTianFuUpgrade.Builder.class);
      }

      // Construct using com.sh.game.protos.TianfuProtos.ResTianFuUpgrade.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        idx_ = 0;

        upSuccess_ = 0;

        upFaield_ = 0;

        count_ = 0;

        tf1_ = 0;

        tf2_ = 0;

        perceptionConfigId_ = 0;

        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.sh.game.protos.TianfuProtos.internal_static_tianfu_ResTianFuUpgrade_descriptor;
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ResTianFuUpgrade getDefaultInstanceForType() {
        return com.sh.game.protos.TianfuProtos.ResTianFuUpgrade.getDefaultInstance();
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ResTianFuUpgrade build() {
        com.sh.game.protos.TianfuProtos.ResTianFuUpgrade result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.sh.game.protos.TianfuProtos.ResTianFuUpgrade buildPartial() {
        com.sh.game.protos.TianfuProtos.ResTianFuUpgrade result = new com.sh.game.protos.TianfuProtos.ResTianFuUpgrade(this);
        result.idx_ = idx_;
        result.upSuccess_ = upSuccess_;
        result.upFaield_ = upFaield_;
        result.count_ = count_;
        result.tf1_ = tf1_;
        result.tf2_ = tf2_;
        result.perceptionConfigId_ = perceptionConfigId_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.sh.game.protos.TianfuProtos.ResTianFuUpgrade) {
          return mergeFrom((com.sh.game.protos.TianfuProtos.ResTianFuUpgrade)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.sh.game.protos.TianfuProtos.ResTianFuUpgrade other) {
        if (other == com.sh.game.protos.TianfuProtos.ResTianFuUpgrade.getDefaultInstance()) return this;
        if (other.getIdx() != 0) {
          setIdx(other.getIdx());
        }
        if (other.getUpSuccess() != 0) {
          setUpSuccess(other.getUpSuccess());
        }
        if (other.getUpFaield() != 0) {
          setUpFaield(other.getUpFaield());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        if (other.getTf1() != 0) {
          setTf1(other.getTf1());
        }
        if (other.getTf2() != 0) {
          setTf2(other.getTf2());
        }
        if (other.getPerceptionConfigId() != 0) {
          setPerceptionConfigId(other.getPerceptionConfigId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.sh.game.protos.TianfuProtos.ResTianFuUpgrade parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.sh.game.protos.TianfuProtos.ResTianFuUpgrade) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      private int idx_ ;
      /**
       * <pre>
       *当前操作的天赋：1：天赋攻击  2: 根骨血量 3: 悟性切割
       * </pre>
       *
       * <code>int32 idx = 1;</code>
       * @return The idx.
       */
      @java.lang.Override
      public int getIdx() {
        return idx_;
      }
      /**
       * <pre>
       *当前操作的天赋：1：天赋攻击  2: 根骨血量 3: 悟性切割
       * </pre>
       *
       * <code>int32 idx = 1;</code>
       * @param value The idx to set.
       * @return This builder for chaining.
       */
      public Builder setIdx(int value) {
        
        idx_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *当前操作的天赋：1：天赋攻击  2: 根骨血量 3: 悟性切割
       * </pre>
       *
       * <code>int32 idx = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIdx() {
        
        idx_ = 0;
        onChanged();
        return this;
      }

      private int upSuccess_ ;
      /**
       * <pre>
       *升级成功次数
       * </pre>
       *
       * <code>int32 upSuccess = 2;</code>
       * @return The upSuccess.
       */
      @java.lang.Override
      public int getUpSuccess() {
        return upSuccess_;
      }
      /**
       * <pre>
       *升级成功次数
       * </pre>
       *
       * <code>int32 upSuccess = 2;</code>
       * @param value The upSuccess to set.
       * @return This builder for chaining.
       */
      public Builder setUpSuccess(int value) {
        
        upSuccess_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *升级成功次数
       * </pre>
       *
       * <code>int32 upSuccess = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpSuccess() {
        
        upSuccess_ = 0;
        onChanged();
        return this;
      }

      private int upFaield_ ;
      /**
       * <pre>
       *升级失败次数
       * </pre>
       *
       * <code>int32 upFaield = 3;</code>
       * @return The upFaield.
       */
      @java.lang.Override
      public int getUpFaield() {
        return upFaield_;
      }
      /**
       * <pre>
       *升级失败次数
       * </pre>
       *
       * <code>int32 upFaield = 3;</code>
       * @param value The upFaield to set.
       * @return This builder for chaining.
       */
      public Builder setUpFaield(int value) {
        
        upFaield_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *升级失败次数
       * </pre>
       *
       * <code>int32 upFaield = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUpFaield() {
        
        upFaield_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       *总升级次数
       * </pre>
       *
       * <code>int32 count = 4;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       *总升级次数
       * </pre>
       *
       * <code>int32 count = 4;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {
        
        count_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *总升级次数
       * </pre>
       *
       * <code>int32 count = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        
        count_ = 0;
        onChanged();
        return this;
      }

      private int tf1_ ;
      /**
       * <pre>
       *攻击属性天赋的当前等级
       * </pre>
       *
       * <code>int32 tf1 = 5;</code>
       * @return The tf1.
       */
      @java.lang.Override
      public int getTf1() {
        return tf1_;
      }
      /**
       * <pre>
       *攻击属性天赋的当前等级
       * </pre>
       *
       * <code>int32 tf1 = 5;</code>
       * @param value The tf1 to set.
       * @return This builder for chaining.
       */
      public Builder setTf1(int value) {
        
        tf1_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *攻击属性天赋的当前等级
       * </pre>
       *
       * <code>int32 tf1 = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearTf1() {
        
        tf1_ = 0;
        onChanged();
        return this;
      }

      private int tf2_ ;
      /**
       * <pre>
       * 血量天赋的当前等级
       * </pre>
       *
       * <code>int32 tf2 = 6;</code>
       * @return The tf2.
       */
      @java.lang.Override
      public int getTf2() {
        return tf2_;
      }
      /**
       * <pre>
       * 血量天赋的当前等级
       * </pre>
       *
       * <code>int32 tf2 = 6;</code>
       * @param value The tf2 to set.
       * @return This builder for chaining.
       */
      public Builder setTf2(int value) {
        
        tf2_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 血量天赋的当前等级
       * </pre>
       *
       * <code>int32 tf2 = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTf2() {
        
        tf2_ = 0;
        onChanged();
        return this;
      }

      private int perceptionConfigId_ ;
      /**
       * <pre>
       *悟性当前等级配置id
       * </pre>
       *
       * <code>int32 perceptionConfigId = 7;</code>
       * @return The perceptionConfigId.
       */
      @java.lang.Override
      public int getPerceptionConfigId() {
        return perceptionConfigId_;
      }
      /**
       * <pre>
       *悟性当前等级配置id
       * </pre>
       *
       * <code>int32 perceptionConfigId = 7;</code>
       * @param value The perceptionConfigId to set.
       * @return This builder for chaining.
       */
      public Builder setPerceptionConfigId(int value) {
        
        perceptionConfigId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *悟性当前等级配置id
       * </pre>
       *
       * <code>int32 perceptionConfigId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearPerceptionConfigId() {
        
        perceptionConfigId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:tianfu.ResTianFuUpgrade)
    }

    // @@protoc_insertion_point(class_scope:tianfu.ResTianFuUpgrade)
    private static final com.sh.game.protos.TianfuProtos.ResTianFuUpgrade DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.sh.game.protos.TianfuProtos.ResTianFuUpgrade();
    }

    public static com.sh.game.protos.TianfuProtos.ResTianFuUpgrade getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ResTianFuUpgrade>
        PARSER = new com.google.protobuf.AbstractParser<ResTianFuUpgrade>() {
      @java.lang.Override
      public ResTianFuUpgrade parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResTianFuUpgrade(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResTianFuUpgrade> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResTianFuUpgrade> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.sh.game.protos.TianfuProtos.ResTianFuUpgrade getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_tianfu_ReqTianFuInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_tianfu_ReqTianFuInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_tianfu_ResTianFuInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_tianfu_ResTianFuInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_tianfu_ReqTianFuUpgrade_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_tianfu_ReqTianFuUpgrade_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_tianfu_ResTianFuUpgrade_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_tianfu_ResTianFuUpgrade_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014tianfu.proto\022\006tianfu\"\017\n\rReqTianFuInfo\"" +
      "E\n\rResTianFuInfo\022\013\n\003tf1\030\001 \001(\005\022\013\n\003tf2\030\002 \001" +
      "(\005\022\032\n\022perceptionConfigId\030\003 \001(\005\",\n\020ReqTia" +
      "nFuUpgrade\022\013\n\003idx\030\001 \001(\005\022\013\n\003cnt\030\002 \001(\005\"\211\001\n" +
      "\020ResTianFuUpgrade\022\013\n\003idx\030\001 \001(\005\022\021\n\tupSucc" +
      "ess\030\002 \001(\005\022\020\n\010upFaield\030\003 \001(\005\022\r\n\005count\030\004 \001" +
      "(\005\022\013\n\003tf1\030\005 \001(\005\022\013\n\003tf2\030\006 \001(\005\022\032\n\022percepti" +
      "onConfigId\030\007 \001(\005B\"\n\022com.sh.game.protosB\014" +
      "TianfuProtosb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_tianfu_ReqTianFuInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_tianfu_ReqTianFuInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_tianfu_ReqTianFuInfo_descriptor,
        new java.lang.String[] { });
    internal_static_tianfu_ResTianFuInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_tianfu_ResTianFuInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_tianfu_ResTianFuInfo_descriptor,
        new java.lang.String[] { "Tf1", "Tf2", "PerceptionConfigId", });
    internal_static_tianfu_ReqTianFuUpgrade_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_tianfu_ReqTianFuUpgrade_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_tianfu_ReqTianFuUpgrade_descriptor,
        new java.lang.String[] { "Idx", "Cnt", });
    internal_static_tianfu_ResTianFuUpgrade_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_tianfu_ResTianFuUpgrade_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_tianfu_ResTianFuUpgrade_descriptor,
        new java.lang.String[] { "Idx", "UpSuccess", "UpFaield", "Count", "Tf1", "Tf2", "PerceptionConfigId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}
