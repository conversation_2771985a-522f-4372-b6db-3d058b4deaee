package com.sh.game.merge.checker;

import com.sh.common.jdbc.JdbcTemplate;
import com.sh.commons.util.Cast;
import com.sh.game.common.entity.User;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.data.mysql.mapper.UserMapper;
import com.sh.game.merge.tool.MergeContext;
import com.sh.game.merge.util.MergeUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class NameChecker {


    private static JdbcTemplate gameTemplate = MergeContext.getGameTemplate();

    public static void checkName() {
        log.info("开始检查角色名字重复");

        //查出所有的有重复的名字
        String QUERY_DUPLICATE_NAME = "select name from p_summary GROUP BY name having count(name) > 1";
        List<Map<String, Object>> nameList = gameTemplate.queryList(QUERY_DUPLICATE_NAME, JdbcTemplate.MAP_MAPPER);

        addNamePrefix(nameList);
        log.info("结束检查角色名字重复");
    }

    /**
     * 找出所有同名玩家
     *
     * @param nameList
     */
    private static void addNamePrefix(List<Map<String, Object>> nameList) {
        for (Map<String, Object> nameMap : nameList) {
            String name = (String) nameMap.get("name");
            String QUERY_RID_AND_NAME_BY_NAMES = "select id, name from p_summary where name = ?";
            List<Map<String, Object>> nameUsedList = gameTemplate.queryList(QUERY_RID_AND_NAME_BY_NAMES, JdbcTemplate.MAP_MAPPER, name);
            changeRepeatRoleName(nameUsedList);
        }
    }

    /**
     * 修改同名玩家的名字
     *
     * @param nameUsedList
     */
    private static void changeRepeatRoleName(List<Map<String, Object>> nameUsedList) {
        Map<Long, Role> roleMap = new HashMap<>(10);
        for (Map<String, Object> nameMap : nameUsedList) {
            long id = Cast.toLong(nameMap.get("id"));
            if (id == 0) {
                continue;
            }
            Role role = MergeUtil.getRole(gameTemplate, id);
            if (role == null) {
                log.error("找不到id为:{} 的角色", id);
                continue;
            }

            roleMap.put(role.getId(), role);
        }

        String QUERY_USER = "select * from p_user where id = ?";
        for (Role role : roleMap.values()) {
            String oldName = role.getName();

            User user = gameTemplate.query(QUERY_USER, new UserMapper(), role.getUid());

            if (user == null) {
                log.error("玩家: {} {} 找不到user，uid {}", role.getId(), oldName, role.getUid());
                continue;
            }

            String newName = "S" + user.getSid() + "." + oldName;
            role.setName(newName);
            MergeUtil.updateRole(gameTemplate, role);
            updateUsedName(role, oldName);
            log.info("玩家: {} {} 角色名重复, 被修改为: {}", role.getId(), oldName, newName);
        }
    }

    /**
     * 更新已用名字
     *
     * @param role
     * @param oldName
     */
    private static void updateUsedName(Role role, String oldName) {
        Map<String, Object> map = gameTemplate.query("select * from p_summary where id = ?", JdbcTemplate.MAP_MAPPER,
                role.getId());
        if (map == null || map.isEmpty()) {
            log.error("玩家:{} {} 在已用名中找不到", role.getId(), oldName);
            return;
        }
        gameTemplate.update("update p_summary set name =? where id = ?", role.getName(), role.getId());
        gameTemplate.update("update p_name set name =? where roleId = ?", role.getName(), role.getId());
    }

}
