package com.sh.game.merge.checker;

import com.sh.common.jdbc.JdbcTemplate;
import com.sh.common.jdbc.SerializerUtil;
import com.sh.game.common.entity.SysDataType;
import com.sh.game.common.entity.sys.*;
import com.sh.game.data.SysDataProvider;
import com.sh.game.merge.tool.MergeContext;
import com.sh.game.merge.util.MergeUtil;
import com.sh.game.system.activity.entity.role.XinChunJinLiData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class SystemChecker {

    private static JdbcTemplate gameTemplate = MergeContext.getGameTemplate();

    public static void check() {
        SysDataProvider.init();

//        log.info("开始合并夺宝数据");
//        checkRobTreasure();
//        log.info("合并天下第一数据");

        //先清空表 再添加需要的进去
        MergeUtil.clearSystemData(gameTemplate);

        checkAuction();
        log.info("合并拍卖行数据");
        checkMainSysData(SysDataType.SYS_ACTIVITY_DATA);
        log.info("保留全服活动记录");
        checkCommonData();
        log.info("保留主服大陆开启时间");
        checkOtherData();
        log.info("保留主服的CommonData的一些数据");
        checkBan();
        log.info("开始合并 BAN_DATA 数据");
        checkMainSysData(SysDataType.ACTIVITY_EQUIP_RECYCLE);
        log.info("保留装备回收数量");
        //checkGlobalStoreData();
        //log.info("合并GLOBAL_STORE_DATA 数据");
        checkZhuanshuActivity();
        log.info("合并专属装备获取数量 数据");
    }


    /**
     * 合并夺宝数据
     */
    private static void checkRobTreasure() {

    }

    private static void checkGlobalStoreData() {
        Map<Integer, JdbcTemplate> gameDbMap = MergeContext.getGameDbMap();
        GlobalStoreData sysData = new GlobalStoreData();
        sysData.setId(SysDataType.GLOBAL_STORE_DATA);

        for (Map.Entry<Integer, JdbcTemplate> entry : gameDbMap.entrySet()) {
            Integer serverId = entry.getKey();
            JdbcTemplate template = entry.getValue();
            GlobalStoreData data = MergeUtil.getSystemData(template, GlobalStoreData.class, SysDataType.GLOBAL_STORE_DATA, serverId);
            if (data == null) {
                continue;
            }
            addToMap(sysData.getSoldCounts(), data.getSoldCounts());
        }

        byte[] encode = SerializerUtil.encode(sysData, GlobalStoreData.class);
        MergeUtil.insertSystemData(MergeContext.getGameTemplate(), encode, SysDataType.GLOBAL_STORE_DATA);
    }

    /**
     * 合并装备首爆系统信息
     */
    private static void checkZhuanshuActivity() {
        Map<Integer, JdbcTemplate> gameDbMap = MergeContext.getGameDbMap();
        ActivityZhuanshuSysData sysData = new ActivityZhuanshuSysData();
        sysData.setId(SysDataType.ZHUANSHU_ACTIVITY);

        for (Map.Entry<Integer, JdbcTemplate> entry : gameDbMap.entrySet()) {
            Integer serverId = entry.getKey();
            JdbcTemplate template = entry.getValue();
            ActivityZhuanshuSysData data = MergeUtil.getSystemData(template, ActivityZhuanshuSysData.class, SysDataType.ZHUANSHU_ACTIVITY, serverId);
            if (data == null) {
                continue;
            }

            addToMap(sysData.getResidueCount(), data.getResidueCount());
        }

        byte[] encode = SerializerUtil.encode(sysData, ActivityZhuanshuSysData.class);
        MergeUtil.insertSystemData(MergeContext.getGameTemplate(), encode, SysDataType.ZHUANSHU_ACTIVITY);
    }

    /**
     * 保留主服的数据
     */
    private static void checkMainSysData(long sysDataType){
        Map<Integer, JdbcTemplate> gameDbMap = MergeContext.getGameDbMap();
        //只取主服
        JdbcTemplate template = gameDbMap.get(1);

        Class<? extends AbstractSysData> clazz = SysDataProvider.get(sysDataType);
        AbstractSysData data = MergeUtil.getSystemData(template, clazz, sysDataType,1);
        if (data == null) {
            return;
        }

        byte[] encode = SerializerUtil.encode(data);
        MergeUtil.insertSystemData(gameTemplate, encode, data.getId());
    }

    /**
     * 合并拍卖行数据
     */
    private static void checkAuction() {
        AuctionData auctionDataRet = new AuctionData();
        Map<Integer, JdbcTemplate> gameDbMap = MergeContext.getGameDbMap();
        for (Map.Entry<Integer, JdbcTemplate> entry : gameDbMap.entrySet()) {
            Integer serverId = entry.getKey();
            JdbcTemplate template = entry.getValue();
            AuctionData auctionData = MergeUtil.getSystemData(template, AuctionData.class, SysDataType.AUCTION,serverId);
            if (auctionData == null) {
                continue;
            }
            MergeUtil.mergeSysData(auctionDataRet, auctionData);
        }

        byte[] encode = SerializerUtil.encode(auctionDataRet, AuctionData.class);
        MergeUtil.insertSystemData(gameTemplate, encode, auctionDataRet.getId());
    }

    private static void checkBan() {
        Map<Integer, JdbcTemplate> gameDbMap = MergeContext.getGameDbMap();
        BanData sysData = new BanData();
        sysData.setId(SysDataType.BAN_DATA);

        for (Map.Entry<Integer, JdbcTemplate> entry : gameDbMap.entrySet()) {
            Integer serverId = entry.getKey();
            JdbcTemplate template = entry.getValue();
            BanData data = MergeUtil.getSystemData(template, BanData.class, SysDataType.BAN_DATA,serverId);
            if (data == null) {
                continue;
            }

            sysData.getBanUsers().putAll(data.getBanUsers());
            sysData.getBanRoles().putAll(data.getBanRoles());
            sysData.getBanChats().putAll(data.getBanChats());
            sysData.getBanIPs().putAll(data.getBanIPs());
            sysData.getBanDevices().putAll(data.getBanDevices());
        }

        byte[] encode = SerializerUtil.encode(sysData, BanData.class);
        MergeUtil.insertSystemData(MergeContext.getGameTemplate(), encode, SysDataType.BAN_DATA);
    }

    /**
     * COMMON_DATA 只保留主服的大陆开启时间
     */
    private static void checkCommonData() {
        Map<Integer, JdbcTemplate> gameDbMap = MergeContext.getGameDbMap();
        //只取主服
        JdbcTemplate mainTemplate = gameDbMap.get(1);

        CommonData commonDataRet = new CommonData();
        CommonData dataMain = MergeUtil.getSystemData(mainTemplate, CommonData.class, SysDataType.COMMON_DATA,1);

        commonDataRet.setId(SysDataType.COMMON_DATA);
        if (dataMain != null) {
            commonDataRet.setLandOpenTime(dataMain.getLandOpenTime());
            commonDataRet.setTitles(dataMain.getTitles());
            commonDataRet.setServerDianQiu(dataMain.getServerDianQiu());
            commonDataRet.setBattleInfoMap(dataMain.getBattleInfoMap());
            commonDataRet.setBattleInfoUpdateTime(dataMain.getBattleInfoUpdateTime());
            commonDataRet.setDflsRestTime(dataMain.getDflsRestTime());
            commonDataRet.setDflsJuanXianJieSuan(dataMain.isDflsJuanXianJieSuan());
            commonDataRet.setDflsUnionMap(dataMain.getDflsUnionMap());
            commonDataRet.setSeason(dataMain.getSeason());
            commonDataRet.setSeasonTime(dataMain.getSeasonTime());
            commonDataRet.setSeasonMap(dataMain.getSeasonMap());
            commonDataRet.setAtmosphereMap(dataMain.getAtmosphereMap());
        }

        for (Map.Entry<Integer, JdbcTemplate> entry : gameDbMap.entrySet()) {
            Integer serverId = entry.getKey();
            JdbcTemplate template = entry.getValue();
            CommonData data = MergeUtil.getSystemData(template, CommonData.class, SysDataType.COMMON_DATA, serverId);
            if (data == null) {
                continue;
            }
            commonDataRet.getDflsJuanXianJoin().addAll(data.getDflsJuanXianJoin());
            // 新春锦鲤处理
            for (Map.Entry<Integer, XinChunJinLiData> dataEntry : data.getJinLiDataMap().entrySet()) {
                Integer key = dataEntry.getKey();
                XinChunJinLiData value = dataEntry.getValue();
                commonDataRet.getJinLiDataMap().compute(key,(k,v) -> {
                    if (v == null) {
                        return value;
                    }
                    v.getIdSet().addAll(value.getIdSet());
                    return v;
                });
            }
            for (Map.Entry<Integer, List<Long>> dataEntry : data.getPeakGuessingTicketRoleMap().entrySet()) {
                int key = dataEntry.getKey();
                List<Long> roleIdList = dataEntry.getValue();
                commonDataRet.getPeakGuessingTicketRoleMap().compute(key, (k, v) -> {
                    if (v == null) {
                        v = new ArrayList<>();
                    }
                    v.addAll(roleIdList);
                    return v;
                });
            }
        }

        byte[] encode = SerializerUtil.encode(commonDataRet);
        MergeUtil.insertSystemData(gameTemplate, encode, commonDataRet.getId());
    }

    /**
     * OTHER_DATA 只保留主服的一些数据
     */
    private static void checkOtherData() {
        Map<Integer, JdbcTemplate> gameDbMap = MergeContext.getGameDbMap();
        //只取主服
        JdbcTemplate mainTemplate = gameDbMap.get(1);

        OtherData otherDataRet = new OtherData();
        OtherData dataMain = MergeUtil.getSystemData(mainTemplate, OtherData.class, SysDataType.OTHER,1);
        otherDataRet.setId(SysDataType.OTHER);
        if (dataMain != null) {
            otherDataRet.getFunction().putAll(dataMain.getFunction());
            otherDataRet.setHasSetCreateRoleLimit(dataMain.isHasSetCreateRoleLimit());
        }
        //累充当前轮数之类的 不确定
        //otherDataRet.setTotalRechargeRound(dataMain.getTotalRechargeRound());
        //otherDataRet.setTotalRechargeEndTime(dataMain.getTotalRechargeEndTime());
        //otherDataRet.setDaysRechargeRound(dataMain.getDaysRechargeRound());
        //otherDataRet.setDaysRechargeEndTime(dataMain.getDaysRechargeEndTime());

        for (Map.Entry<Integer, JdbcTemplate> entry : gameDbMap.entrySet()) {
            Integer serverId = entry.getKey();
            JdbcTemplate template = entry.getValue();
            OtherData data = MergeUtil.getSystemData(template, OtherData.class, SysDataType.OTHER,serverId);
            if (data == null) {
                continue;
            }

            /**
             * Tag(58) 装备首爆 配置id-> 上榜玩家id ->是否领奖
             */
            data.getFirstGet().forEach((id, map) -> {
                otherDataRet.getFirstGet().computeIfAbsent(id, k -> new HashMap<>()).putAll(map);
            });
        }

        byte[] encode = SerializerUtil.encode(otherDataRet);
        MergeUtil.insertSystemData(gameTemplate, encode, otherDataRet.getId());
    }

    public static void addToMap(Map<Integer,Integer> target, Map<Integer,Integer>plus) {
        Object[] os = plus.keySet().toArray();
        Integer key;
        for (Object o : os) {
            key = (Integer) o;
            if (target.containsKey(key)) {
                target.put(key, target.get(key) + plus.get(key));
            } else {
                target.put(key, plus.get(key));
            }
        }
    }


}
