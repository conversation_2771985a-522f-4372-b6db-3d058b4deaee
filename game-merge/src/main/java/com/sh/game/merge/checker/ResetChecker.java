package com.sh.game.merge.checker;

import com.sh.common.jdbc.JdbcTemplate;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.merge.tool.MergeContext;
import com.sh.game.merge.util.MergeUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR> xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2018/4/12 16:14
 * desc  :
 * Copyright(©) 2017 by xiaomo.
 */
@Slf4j
public class ResetChecker {
    private static JdbcTemplate gameTemplate = MergeContext.getGameTemplate();

    //private static NamedParameterJdbcTemplate namedGameTemplate = MergeContext.getNamedGameTemplate();

//    public static void checkReset() {
//        long lastLoginTime = TimeUtil.getNowOfSeconds() - (60 * 24 * 60 * 60);
//        List<Long> rids = gameTemplate.queryList("select id from p_summary where loginTime > ?", JdbcTemplate.LONG_MAPPER, lastLoginTime);
//        Map<String,Object> paramMap = new HashMap<>();
//        paramMap.put("rids", rids);
//        List<Role> roleList = Lists.newArrayListWithExpectedSize(rids.size());
//        /*namedGameTemplate.query("select data from p_role where id in (:rids)", paramMap, rs -> {
//             byte[] bytes = rs.getBytes(1);
//            Role role = SerializerUtil.decode(bytes, Role.class);
//            if(role != null) {
//                roleList.add(role);
//            }
//        });*/
//
//        for (Role role : roleList) {
//            //重置退出帮会时间
//            role.getUnion().setQuitTime(0);
//            MergeUtil.updateRole(gameTemplate, role);
//        }
//
//    }

    public static void checkReset() {
        long lastLoginTime = TimeUtil.getNowOfSeconds() - (24 * 60 * 60 * 1000);
        List<Long> rids = gameTemplate.queryList("select id from p_summary where loginTime > ?", JdbcTemplate.LONG_MAPPER, lastLoginTime);

        for (long rid : rids) {
            Role role = MergeUtil.getRole(gameTemplate, rid);
            if (role == null) {
                continue;
            }
            // 重置退出帮会时间
            if (role.getUnion().getQuitTime() > 0) {
                role.getUnion().setQuitTime(0);
                MergeUtil.updateRole(gameTemplate, role);
            }
        }
    }
}
