#!/bin/bash
#配置方式
#1#127.0.0.1#3306#mt_game#mt_log#root#680010
#1#127.0.0.1#3306#xy_1#xy_1_log#root#680010
#2#127.0.0.1#3306#xy_2#xy_2_log#root#680010

lineCount=0


while read line
do
  let lineCount++; 
  if [ ${lineCount} == 1 ]
  then
    tsid=`echo ${line} | cut -d '#' -f 1`
    tip=`echo ${line} | cut -d '#' -f 2`
    tport=`echo ${line} | cut -d '#' -f 3`
    tgame=`echo ${line} | cut -d '#' -f 4`
    tuser=`echo ${line} | cut -d '#' -f 5`
    tpass=`echo ${line} | cut -d '#' -f 6`
    targetGameUrl="******************************************************"
    echo "目标游戏数据库->$targetGameUrl"

  else
    sid=`echo ${line} | cut -d '#' -f 1`
    ip=`echo ${line} | cut -d '#' -f 2`
    port=`echo ${line} | cut -d '#' -f 3`
    game=`echo ${line} | cut -d '#' -f 4`
    user=`echo ${line} | cut -d '#' -f 5`
    pass=`echo ${line} | cut -d '#' -f 6`
    gameUrl="***************************************************"
	
    echo "开始导入$sid 服游戏库->$gameUrl"
	if [ ${lineCount} == 2 ]
	then
		mysqldump -u${user} -p${pass} -h${ip} -P${port} ${game} > temp_game.sql 2>/dev/null
		[ $? -ne 0 ] && echo "${game}数据库导出失败 error"
	else
		mysqldump -u${user} -p${pass} -h${ip} -P${port} -t ${game} --ignore-table=${game}.s_data --ignore-table=${game}.s_announce > temp_game.sql 2>/dev/null
		[ $? -ne 0 ] && echo "${game}数据库导出失败 error"
	fi
    mysql -u${tuser} -p${tpass} -h${tip} -P${tport}  ${tgame} < temp_game.sql 2>&1>/dev/null
	[ $? -ne 0 ] && echo "${log}数据库导入失败 error"

    echo "$sid 服导入完毕"
    echo ""
  fi

done < merge.cfg

#rm -f temp_game.sql

echo "数据导入完毕"


echo "开始执行数据清理和检查程序"
java -jar  game-merge-0.0.1.jar

rm -rf *.properties

exit 0

