package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/6/15
 * 挖宝奖励
 */
@Getter
@Setter
@ConfigData(file = "cfg_treasury_reward")
public class WaBaoRewardConfig extends AbstractConfigData {
    private int id;
    private int actType;
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> conditions;
    private int costGroup;
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> itemId;

    private int num;
    private int showNum;
    private int reguest;
    private int selfMax;
    private int selfEnd;

    private int max;
    private int wight;
    private int announceId;
    private int firstTime;

    /**
     * 是否重置次数
     */
    private int reset;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> boxId;

    /**
     * 活动结束时是否清除数据
     */
    private int clearNum;

    /**
     * 不参与权重次数
     */
    private int ignoreTimes;

    private int bonus;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> jifen;

    private int log;

    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] showItem;

    /**
     * 积分区间
     */
    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] score;
}
