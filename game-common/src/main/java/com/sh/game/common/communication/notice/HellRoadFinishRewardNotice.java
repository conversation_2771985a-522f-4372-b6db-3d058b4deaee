package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 地狱之路-结算奖励
 *
 * <AUTHOR>
 * @date 2022/09/20 23:21
 */
@Getter
@Setter
@Notice
public class HellRoadFinishRewardNotice extends ProcessNotice {

    /**
     * 榜单类型
     */
    private int type;

    private Map<Long, Integer> rank;
}
