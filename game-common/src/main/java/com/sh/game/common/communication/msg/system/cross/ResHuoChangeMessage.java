package com.sh.game.common.communication.msg.system.cross;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>圣火值变化 发给所有人</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResHuoChangeMessage extends ProtobufMessage {

    private com.sh.game.protos.CrossProtos.ResHuoChange proto;

    private com.sh.game.protos.CrossProtos.ResHuoChange.Builder builder;

	
	@Override
	public int getId() {
		return 84009;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.CrossProtos.ResHuoChange.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.CrossProtos.ResHuoChange.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.CrossProtos.ResHuoChange.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.CrossProtos.ResHuoChange getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.CrossProtos.ResHuoChange proto) {
        this.proto = proto;
    }

}
