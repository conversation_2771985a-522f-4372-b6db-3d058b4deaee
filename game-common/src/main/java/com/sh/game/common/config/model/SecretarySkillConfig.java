package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.array.JinghaoDoubleArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/17 19:26
 */
@Getter
@Setter
@ConfigData(file = "cfg_secretary_skill")
public class SecretarySkillConfig extends AbstractConfigData {
    private int id;
    /** 1技能 2副业 */
    private int type;
    private int star;
    private int levelMax;
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> openCondition;
    private int initLevel;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> levelCost1;
    @ConfigField(converter = JinghaoDoubleArrayConverter.class)
    private double[] levelCost2;

    /** 1自己加(资质) 2同类型加(资质) 3委任中的宗门加(赚钱) */
    private int effectTarget;
    private int effect;

    public interface EffectTargetType {
        int SELF = 1;
        int COMMON_TYPE = 2;
        int ZONG_MEN = 3;
    }
}
