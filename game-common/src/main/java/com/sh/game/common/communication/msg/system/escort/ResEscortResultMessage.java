package com.sh.game.common.communication.msg.system.escort;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>押镖结果</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ResEscortResultMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 105003;
	}
	
	/**
	 * 1成功，0失败
	 */
	private int result;
	/**
	 * 接镖时是否双倍 1双倍 0单倍
	 */
	private int isDouble;

	public int getResult() {
		return result;
	}

	public void setResult(int result) {
		this.result = result;
	}

		public int getIsDouble() {
		return isDouble;
	}

	public void setIsDouble(int isDouble) {
		this.isDouble = isDouble;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.result = readInt(buf, false);
		this.isDouble = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, result, false);
		this.writeInt(buf, isDouble, false);
		return true;
	}
}
