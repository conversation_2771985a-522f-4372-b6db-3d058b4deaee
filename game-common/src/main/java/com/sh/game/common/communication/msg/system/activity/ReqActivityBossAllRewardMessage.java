package com.sh.game.common.communication.msg.system.activity;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求boss积分一键领取奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 **/
@RPC("toLogic")
public class ReqActivityBossAllRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.ActivityProtos.ReqActivityBossAllReward proto;

    private com.sh.game.protos.ActivityProtos.ReqActivityBossAllReward.Builder builder;

	
	@Override
	public int getId() {
		return 4353;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ActivityProtos.ReqActivityBossAllReward.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ActivityProtos.ReqActivityBossAllReward.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ActivityProtos.ReqActivityBossAllReward.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ActivityProtos.ReqActivityBossAllReward getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ActivityProtos.ReqActivityBossAllReward proto) {
        this.proto = proto;
    }

}
