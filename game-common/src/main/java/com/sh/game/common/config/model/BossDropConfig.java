package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;

/**
 * boss掉落
 */

@lombok.Getter
@lombok.Setter
@ConfigData(file="cfg_boss_drop")
public class BossDropConfig extends AbstractConfigData {

    private int id;

    private int mid;

    private int itemId;

    private int group;

    private int priority;

    private int dropType;

    private int dropParam;

    private int activityId;

    private int bossType;

    private int activityItemCount;

    private int probability;

    private int rate;

    private int boxid;

    private int personalcd;

    private int personalmax;

    private int belongtime;

}
