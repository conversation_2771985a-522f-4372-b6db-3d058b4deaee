package com.sh.game.common.config.cache;

import com.google.common.collect.HashBasedTable;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.KingConfig;
import lombok.Getter;

import java.util.*;

/**
 * 帝王系统配置缓存
 *
 * <AUTHOR>
 * @date 2022/10/18 16:16
 */
@ConfigCache
@Getter
public class KingCache implements IConfigCache {

    /**
     * 帝王配置
     * key:     type 大类
     * value:   rowKey:     subType     小类
     *          columnKey:  level       等级
     *          value:      kingConfig  配置
     */
    private final Map<Integer, HashBasedTable<Integer, Integer, KingConfig>> kingConfigMap = new HashMap<>();

    @Override
    public void build() {
        kingConfigMap.clear();
        List<KingConfig> kingConfigList = ConfigDataManager.getInstance().getList(KingConfig.class);
        for (KingConfig config : kingConfigList) {
            HashBasedTable<Integer, Integer, KingConfig> table = kingConfigMap.get(config.getType());
            if (table == null) {
                table = HashBasedTable.create();
            }
            table.put(config.getSubType(), config.getLv(), config);
            kingConfigMap.put(config.getType(), table);
        }
    }

    /**
     * 根据大类、小类、等级获取帝王配置
     *
     * @param type      大类
     * @param subType   小类
     * @param level     等级
     * @return KingConfig   帝王配置
     */
    public KingConfig getConfig(int type, int subType, int level) {
        HashBasedTable<Integer, Integer, KingConfig> table = kingConfigMap.get(type);
        if (table == null) {
            return null;
        }
        return table.get(subType, level);
    }

    /**
     * 获取大类列表
     *
     * @return Set<Integer> 大类
     */
    public Set<Integer> getTypeSet() {
        return kingConfigMap.keySet();
    }

    /**
     * 根据大类获取小类列表
     *
     * @return Set<Integer> 大类
     */
    public Set<Integer> getSubTypeSet(int type) {
        HashBasedTable<Integer, Integer, KingConfig> table = kingConfigMap.get(type);
        if (table == null) {
            return new HashSet<>();
        }
        Set<Integer> subTypeSet = table.rowKeySet();
        if (subTypeSet == null) {
            return new HashSet<>();
        }
        return subTypeSet;
    }
}
