package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 在线豪礼
 *
 * <AUTHOR> <PERSON>o
 * @Email <EMAIL>
 * @since 2021-11-30
 **/
@Getter
@Setter
@ConfigData(file = "cfg_online_time")
public class OnlineTimeConfig extends AbstractConfigData {

    /**
     * id
     */
    private int id;

    /**
     * 活动类型
     */
    private int actType;

    /**
     * 奖励条件，用于区分不同时间段的奖励
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    /**
     * 在线时长（秒）
     */
    private int times;

    /**
     * 奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 盒子奖励
     */
    private int boxId;

    /**
     * 是否零点重置
     */
    private int resetType;

    /**
     * 领取条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> extraCondition;
}
