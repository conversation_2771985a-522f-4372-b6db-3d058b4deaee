package com.sh.game.common.communication.msg.system.chaoneng;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回超能图鉴升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResResonanceInfo extends ProtobufMessage {

    private com.sh.game.protos.ChaoNengProtos.ResResonanceInfo proto;

    private com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.Builder builder;

	
	@Override
	public int getId() {
		return 411014;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ChaoNengProtos.ResResonanceInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ChaoNengProtos.ResResonanceInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ChaoNengProtos.ResResonanceInfo proto) {
        this.proto = proto;
    }

}
