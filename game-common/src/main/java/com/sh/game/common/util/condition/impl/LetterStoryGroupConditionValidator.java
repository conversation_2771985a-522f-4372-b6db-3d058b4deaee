package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/11/6 13:52
 */
public class LetterStoryGroupConditionValidator extends IConditionValidatorDefault {
    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params.length < 2) {
            return false;
        }
        return avatar.hasLetterStoryGroupComplete(params[1]);
    }
}
