package com.sh.game.common.config.cache;

import com.google.common.collect.ImmutableMap;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.SchoolStudentNameConfig;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ConfigCache
public class SchoolStudentNameConfigCache implements IConfigCache {
    private Map<Integer, List<String>> typeMap = new HashMap<>();

    @Override
    public void build() {
        Map<Integer, List<String>> tmpTypeMap = new HashMap<>();
        List<SchoolStudentNameConfig> list = ConfigDataManager.getInstance().getList(SchoolStudentNameConfig.class);
        for (SchoolStudentNameConfig config : list) {
            tmpTypeMap.computeIfAbsent(config.getType(), v -> new ArrayList<>()).add(config.getName());
        }
        typeMap = ImmutableMap.copyOf(tmpTypeMap);
    }

    public List<String> getCfgByType(int type) {
        return typeMap.getOrDefault(type, null);
    }
}
