package com.sh.game.common.config.cache;

import com.google.common.collect.Range;
import com.google.common.collect.RangeMap;
import com.google.common.collect.TreeRangeMap;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.TianXuanConfig;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/23 9:47
 */
@ConfigCache
public class TianXuanCache implements IConfigCache {

    private RangeMap<Integer, TianXuanConfig> joinReward;

    private List<TianXuanConfig> reward;

    @Override
    public void build() {
        joinReward = TreeRangeMap.create();
        reward = new ArrayList<>();

        for (TianXuanConfig config : ConfigDataManager.getInstance().getList(TianXuanConfig.class)) {
            if (config.getType() == 1) {
                reward.add(config);
            } else {
                int[] goals = config.getGoals();
                if (goals.length > 1) {
                    joinReward.put(Range.closed(goals[0], goals[1]), config);
                }
            }
        }
    }

    public List<TianXuanConfig> findRewardList() {
        return reward;
    }

    public TianXuanConfig findJoinReward(int numSize) {
        return joinReward.get(numSize);
    }
}
