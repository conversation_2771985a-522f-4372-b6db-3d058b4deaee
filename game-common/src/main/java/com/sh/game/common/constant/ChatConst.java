package com.sh.game.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR> xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2017/7/29 15:44
 * desc  :
 * Copyright(©) 2017 by xiaomo.
 * <AUTHOR>
 */
public interface ChatConst {

    List<Integer> NEED_CROSS_SYNC_ANNOUNCE_ID = Arrays.asList(6147, 6148, 112, 10137, 10138, 10139, 118);

    /**
     * 聊天类型
     */
    interface ChatType {
        int SYSTEM = 0;     // 系统
        int PERSON = 1;     // 私聊
        int WORLD = 2;      // 世界
        int YELL = 3;       // 喊话
        int NEAR = 4;       // 附近
        int UNION = 5;      // 行会
        int GROUP = 6;      // 队伍
        int REMOTE = 7;     // 跨服
        int MAP = 8;        // 本地(场景)
        int CAMP = 9;        // 阵营(跨服)
    }

    /**
     * 聊天限制
     */
    interface ChatLimit {
        int MINUTE = 60;             // 分钟秒数
        int REPEAT_COUNT = 10;       // 重复次数
        int BAN_TIME = 60 * 60 * 24; // 禁言时间
    }

    interface AnnounceId {

        /**
         * 障碍赛进入奖励地图
         */
        int HINDER_WINNER = 1013;

        /**
         * 障碍赛BOSS死亡
         */
        int HINDER_WINNER_END = 1014;

        /**
         * 沙巴克攻破城门
         */
        int SHABAKE_DOOR_BREAK = 1027;

        /**
         * 沙巴克占领皇宫
         */
        int SHABAKE_WIN = 1028;

        /**
         * 城主上线
         */
        int SHABAKE_LEADER_LOGIN = 1029;

        /**
         * 沙巴克归属变成无主
         */
        int SHABAKE_NONE = 1032;

        /**
         * 沙巴克胜利归属
         */
        int SHABAKE_END_WIN = 1033;

        /**
         * 沙巴克结束无主
         */
        int SHABAKE_END_NONE = 1034;

        /**
         * 沙巴克传送
         */
        int SHABAKE_TRANSPORT = 1035;

        /**
         * 沙巴克红包
         */
        int SHABAKE_REDPACK = 1036;

        /**
         * 跨服沙巴克攻破城门
         */
        int CROSS_SHABAKE_DOOR_BREAK = 9106;

        /**
         * 跨服沙巴克占领皇宫
         */
        int CROSS_SHABAKE_WIN = 9107;

        /**
         * 跨服城主上线
         */
        int CROSS_SHABAKE_LEADER_LOGIN = 9108;

        /**
         * 跨服沙巴克归属变成无主
         */
        int CROSS_SHABAKE_NONE = 9101;

        /**
         * 跨服沙巴克胜利归属
         */
        int CROSS_SHABAKE_END_WIN = 9102;

        /**
         * 跨服沙巴克结束无主
         */
        int CROSS_SHABAKE_END_NONE = 9103;

        /**
         * 跨服沙巴克传送
         */
        int CROSS_SHABAKE_TRANSPORT = 9104;

        /**
         * 跨服沙巴克红包
         */
        int CROSS_SHABAKE_REDPACK = 9105;

        /**
         * 特权
         */
        int MONTH_CARD = 1101;

        /**
         * 神武训练营
         */
        int CAMP = 1110;

        /**
         * 抢公主邀请公主
         */
        int PRINCESS_INVITE = 2035;

        /**
         * 抢公主公主返回
         */
        int PRINCESS_BACK = 2036;
        /**
         * 抢公主邀请公主
         */
        int PRINCESS_SENT = 2037;
        /**
         * 行会boss第一名
         */
        int UNION_BOSS_WINNER = 2038;

        /**
         * 召唤怪物
         */
        int CALL_MONSTER = 2097;

        /**
         * 行会红包收奖励
         */
        int UNION_REDPACK_RECIEVE = 3003;

        /**
         * 行会红包领奖
         */
        int UNION_REDPACK_SEND = 3004;

        /**
         * 通关地牢
         */
        int UNION_DILAO = 3005;

        /**
         * 通关地牢
         */
        int GOLDEN_PIG = 3015;

        /**
         * 幻影魔王幸运奖
         */
        int SHADOW_DEVIL_LUCK_REWARD = 3021;

        /**
         * 幻影魔王幸运奖汇总
         */
        int SHADOW_DEVIL_END_LUCK_REWARD = 3022;

        /**
         * 幻影魔王特殊幸运奖与幸运奖汇总
         */
        int SHADOW_DEVIL_END_SPECIAL_LUCK_REWARD = 3023;

        /**
         * 幻影魔王没有玩家获得幸运大奖
         */
        int SHADOW_DEVIL_END_NOT_REWARD = 3024;

        /**
         * 本轮魔王宝盒拥有者失联
         */
        int SHADOW_DEVIL_LEAVE_MAP = 3025;

        /**
         * 神塔全部击杀奖励
         */
        int SHEN_TA_All_KILL_ANNOUNCE = 3026;

        /**
         * 神塔未全部击杀奖励
         */
        int SHEN_TA_ANNOUNCE = 3027;

        /**
         * 捐献奖励公告
         */
        int DONATE_REWARD_ANNOUNCE = 3028;

        /**
         * 神塔开启下一层
         */
        int SHEN_TA_PASS = 3029;

        /**
         * 皇宫乱斗boss刷新公告
         */
        int BRAWLING_BOSS_REFRESH = 3032;

        /**
         * 皇宫乱斗buff刷新公告
         */
        int BRAWLING_BUFF_REFRESH = 3033;
        /**
         * 行会拍卖
         */
        int UNION_PAIMAI = 3034;

        /**
         * 皇宫乱斗排名公告
         */
        int BRAWLING_RANK = 3035;

        /**
         * 皇宫乱斗boss击杀公告
         */
        int BRAWLING_KILL_BOSS = 3036;

        /**
         * 祭坛boss击杀无掉落公告
         */
        int JITAN_KILL_NO_DROP = 3039;

        /**
         * 跨服竞技场怪物出现公告
         */
        int CROSS_ARENA_MONSTER = 3045;

        /**
         * 进入隐藏房间
         */
        int ENTER_HIDDEN_ROOM = 2073;

        /**
         * 怪物死亡公告
         */
        int MONSTER_DIE = 3110;

        /**
         * 整点怪物公告
         */
        int HOUR_MONSTER_ANNOUNCE = 3111;

        /**
         * 封魔战意排行
         */
        int FENG_MO_ZHAN_YI_RANK = 3801;

        /**
         * 经验传功
         */
        int JIN_YAN_CHUAN_GONG = 9004;

        /**
         * 个人称号升级公告
         */
        int SELF_CHENGHAO_UP_ANNOUNCE = 3923;

        /**
         * 藏宝秘境公告
         */
        int XUN_BAO_MI_JING_ANNOUNCE = 20057;

        /**
         * Boss来袭副本创建公告
         */
        int BOSS_ASSAULT_START = 27000;

        /**
         * 3v3首杀
         */
        int XIU_LUO_FIRST_KILL = 29002;

        /**
         * 藏宝图商人公告
         */
        int CANG_BAO_TU_SHANG_REN = 20058;

        /**
         * 行会神皇升级
         */
        int UNION_SHENHUANG_LEVEL_UP = 30101;

        /**
         * 中秋boss公告
         */
        int ZHONGQIU_BOSS_ANNOUNCE = 30201;

        /**
         * 国庆boss公告
         */
        int GUOQING_BOSS_ANNOUNCE = 30211;

        /**
         * 行会宝地开启公告
         */
        int HANG_HUI_BAO_DI = 29102;

        /**
         * 巅峰联赛淘汰赛开始
         */
        int DFLS_TAO_TAI_BEGIN = 30901;

        /**
         * 巅峰联赛海选赛开始
         */
        int DFLS_HAI_XUAN_BEGIN = 30902;

        /**
         * 国庆boss公告
         */
        int XINCHUN_BOSS_ANNOUNCE = 30221;


        /**
         * 财神庙红包领奖
         */
        int CAISHENMIAO_REDPACK_SEND = 32002;

        /**
         * 境界排行榜公告
         */
        int JING_JIE_RANK_ANNOUNCE = 33001;
    }

    public static void main(String[] args) {
        for (int i = 1019; i < 1027; i++) {
            System.out.println("int BRAWL_KILL" + (i - 1016) + " = " + i + ";");
        }
    }
}
