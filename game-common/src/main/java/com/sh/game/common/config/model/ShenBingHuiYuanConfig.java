package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import com.sh.game.common.config.converter.list.YuHaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 神兵会员特权配置表
 */
@Getter
@Setter
@ConfigData(file = "cfg_shenbinghuiyuan")
public class ShenBingHuiYuanConfig extends AbstractConfigData {
    /**
     * 神兵会员等级
     */
    private int id;

    /**
     * 神兵名称
     */
    private String name;

    /**
     * 会员等级
     */
    private int vipLevel;

    /**
     * 需完成的任务列表
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> task;

    /**
     * 对应装扮id
     */
    private int fashion;

    /**
     * 下一神兵会员等级，最后一级的下一级为 0
     */
    private int next;

    /**
     * 每日会员工资
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> dayreward;

    /**
     * 使用道具上限额外增加
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> addnumtoplimit;

    /**
     * 随身回收
     */
    private int recovery;

    /**
     * 随身仓库
     */
    private int warehouse;

    /**
     * 自动回收
     */
    private int automaticrecovery;

    /**
     * 拾取范围提升
     */
    private int pickingregion;

    /**
     * 自动使用道具
     */
    @ConfigField(converter = YuHaoIntegerListConverter.class)
    private List<int[]> automatic_item;


    /**
     * 神兵特权等级所拥有的功能
     * @file cfg_condition
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<int[]> powerCondition;

    /**
     * 特权链接
     */
    private int hyperlink;

    /**
     * 奖励<[物品Id][数量]>
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> rewards;

    /**
     * 神兵每日额外经验上限
     */
    private long experience;

    /**
     * 经验加成倍率
     */
    private float pickexperience;

    /**
     * 充值解锁
     */
    private int recharge;

    /**
     * 秒得神兵购买额度
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> secondquota;

    /**
     * 灵符购买的可获取额外奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> secondrewards;

    /**
     * 该档神兵接取条件
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> showcondition;

    /**
     * 每日交易次数限制
     */
    private int business;

    /**
     * 泡点功能
     */
    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] gainPerSec;

    /**
     * 每日免费复活次数
     */
    private int revive;

    /**
     * 专属礼包奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> gift;

    /**
     * 购买专属礼包消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;


//    /**
//     * 幸运掉落数量限制（小）
//     */
//    private int luckyDropSmall;
//
//    /**
//     * 幸运掉落数量限制（大）
//     */
//    private int luckyDropBig;

    /**
     * 同步解锁装扮表id
     */
    private int xialvFashion;

    /**
     * 泡点经验获取条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> perCondition;

}
