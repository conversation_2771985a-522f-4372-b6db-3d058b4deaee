package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 人物装备套装
 */
@Getter
@Setter
@ConfigData(file = "cfg_equip_suit")
public class EquipSuitConfig extends AbstractConfigData {
    /**
     * id
     */
    private int id;

    /**
     * 装备类型
     */
    private int type;

    /**
     * 穿戴部位
     */
    private int[] position;

    /**
     * 指定部位(必须)
     */
    private int[] need_position;

    /**
     * 除了集齐位置之外还需要条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    /**
     * 穿戴数量
     */
    private int num;

    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] att;

    /**
     * 套装技能
     */
    private int[] skill;

    /**
     * 技能特殊效果
     */
    private int[] skillspecial;

    /**
     * buff特殊效果
     */
    private int[] buffspecial;

    private int[] buffid;

    /**
     * 父类ID
     */
    private int superClass;

    /**
     * 子类ID
     */
    private int subClass;

    /**
     * 表示为神兵套装
     */
    private int shenBingSuit;

    /**
     * 套装需要检查重复装备
     */
    private int itemCheck;

    /**
     * 当前套装需要的道具
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> suitItem;
}
