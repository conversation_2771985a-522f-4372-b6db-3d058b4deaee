package com.sh.game.common.config.cache;

import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.ChaoticPowerConfig;
import com.sh.game.common.config.model.ChaoticPowerSuitConfig;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/11/10 13:43
 */
@ConfigCache
public class ChaoticPowerCache implements IConfigCache {

    /**
     * rowKey:      装备部位type
     * columnKey:   等级
     * value:       混沌灵源-赋灵配置
     */
    Table<Integer, Integer, ChaoticPowerConfig> powerConfigTable = HashBasedTable.create();

    /**
     * 类型部位
     * key:     装备类型 1、帝装 2、王者
     * value:   部位列表
     */
    Map<Integer, Set<Integer>> typeMap = new HashMap<>();

    @Override
    public void build() {
        powerConfigTable.clear();
        typeMap.clear();

        List<ChaoticPowerConfig> powerConfigList = ConfigDataManager.getInstance().getList(ChaoticPowerConfig.class);
        for (ChaoticPowerConfig powerConfig : powerConfigList) {
            int equipType = powerConfig.getEquip_type();
            Set<Integer> typeSet = typeMap.getOrDefault(equipType, new HashSet<>());
            typeSet.addAll(powerConfig.getType());
            typeMap.put(equipType, typeSet);
            for (Integer type : powerConfig.getType()) {
                powerConfigTable.put(type, powerConfig.getLevel(), powerConfig);
            }
        }
    }

    /**
     * 根据装备部位type、等级获取赋灵配置
     *
     * @param type      装备部位type
     * @param level     等级
     * @return ChaoticPowerConfig   赋灵配置
     */
    public ChaoticPowerConfig getPowerConfig(int type, int level) {
        return powerConfigTable.get(type, level);
    }

    /**
     * 获取类型部位
     *
     * @return Map<Integer,Set<Integer>> 类型部位
     */
    public Map<Integer, Set<Integer>> getTypeMap() {
        return typeMap;
    }

    /**
     * 根据命格总等级获取命格套装组合配置id
     *
     * @param level 命格总等级
     * @return int 命格套装组合配置id
     */
    public int getSuitId(int level, int equipType) {
        ChaoticPowerSuitConfig suitConfig = ConfigDataManager.getInstance().getList(ChaoticPowerSuitConfig.class).stream()
                .filter(config -> level >= config.getAllLevel() && equipType == config.getType())
                .max(Comparator.comparing(ChaoticPowerSuitConfig::getAllLevel)).orElse(null);
        if (Objects.nonNull(suitConfig)) {
            return suitConfig.getId();
        }
        return 0;
    }
}
