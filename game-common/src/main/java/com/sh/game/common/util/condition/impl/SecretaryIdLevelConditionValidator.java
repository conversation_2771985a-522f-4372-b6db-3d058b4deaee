package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/18 10:45
 */
public class SecretaryIdLevelConditionValidator extends IConditionValidatorDefault {
    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params.length < 3) {
            return false;
        }
        return avatar.getSecretaryIdLevel(params[1]) >= params[2];
    }
}
