package com.sh.game.common.communication.msg.system.trade;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>发送交易请求</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResTradeApplyMessage extends ProtobufMessage {

    private com.sh.game.protos.TradeProtos.ResTradeApply proto;

    private com.sh.game.protos.TradeProtos.ResTradeApply.Builder builder;

	
	@Override
	public int getId() {
		return 104011;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TradeProtos.ResTradeApply.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TradeProtos.ResTradeApply.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TradeProtos.ResTradeApply.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TradeProtos.ResTradeApply getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TradeProtos.ResTradeApply proto) {
        this.proto = proto;
    }

}
