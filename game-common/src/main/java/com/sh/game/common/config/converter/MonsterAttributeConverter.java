package com.sh.game.common.config.converter;

import com.sh.common.config.IConverter;
import com.sh.commons.util.Cast;
import com.sh.commons.util.StringUtil;
import com.sh.commons.util.Symbol;

import java.util.HashMap;
import java.util.Map;

public class MonsterAttributeConverter implements IConverter {
    @Override
    public Map<Integer, Long> convert(Object source) {
        String str = (String) source;

        Map<Integer, Long> attributeMap = new HashMap<>();

        if (!StringUtil.isEmpty(str)) {
            String[] arr = str.split(Symbol.SHUXIAN);
            if (arr.length > 0) {
                for (String v : arr) {
                    String[] temp = v.split(Symbol.JINHAO);
                    if (temp.length == 3) {
                        int attrId = Cast.toInteger(temp[1]);
                        long count = Cast.toLong(temp[2]);
                        long oldCount = attributeMap.getOrDefault(attrId, 0L);
                        attributeMap.put(attrId, oldCount + count);
                    }
                }
            }
        }

        return attributeMap;
    }
}
