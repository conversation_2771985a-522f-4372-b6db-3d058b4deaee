package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * 拾取配置
 */
@Getter
@Setter
@ConfigData(file = "cfg_shiquguolv")
public class PickUpConfig extends AbstractConfigData {
    private int id;

    private int itemid;

    private int type;

    private int leveldown;

    private int levelup;
}
