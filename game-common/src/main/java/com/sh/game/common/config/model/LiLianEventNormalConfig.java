package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapIntLongConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 历练随机事件-普通
 */
@Getter
@Setter
@ConfigData(file = "cfg_lilian_event_normal")
public class LiLianEventNormalConfig extends AbstractConfigData {
    private int id;
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> reward;
}
