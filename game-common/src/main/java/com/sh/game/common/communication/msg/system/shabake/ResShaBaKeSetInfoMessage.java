package com.sh.game.common.communication.msg.system.shabake;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>下发沙巴克设置信息 登录或者非活动期间</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResShaBaKeSetInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.ShabakeProtos.ResShaBaKeSetInfo proto;

    private com.sh.game.protos.ShabakeProtos.ResShaBaKeSetInfo.Builder builder;

	
	@Override
	public int getId() {
		return 275017;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ShabakeProtos.ResShaBaKeSetInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ShabakeProtos.ResShaBaKeSetInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ShabakeProtos.ResShaBaKeSetInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ShabakeProtos.ResShaBaKeSetInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ShabakeProtos.ResShaBaKeSetInfo proto) {
        this.proto = proto;
    }

}
