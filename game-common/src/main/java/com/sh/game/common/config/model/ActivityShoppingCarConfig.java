package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.*;

import java.util.List;

@Getter
@Setter
@ConfigData(file = "cfg_shopping_cart")
public class ActivityShoppingCarConfig extends AbstractConfigData {
    /**
     *编号
     */
    private int id;

    /**
     * 活动id
     **/
    private int activityID;

    /**
     * 活动gruop
     **/
    private int gruop;

    /**
     * 购买条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> conditions;

    /**
     * 每日购买上限
     */
    private int payLimit;

    /**
     *价格(灵符)
     */
    private int cost;

    /**
     *折扣
     */
    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> discount;

    /**
     *商品道具
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> item;

}
