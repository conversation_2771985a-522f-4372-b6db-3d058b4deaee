package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 10:14
 */
@Getter
@Setter
@ConfigData(file = "cfg_valuecard_recharge",keys = "rechargeId")
public class ValueCardRechargeConfig extends AbstractConfigData {
    private int rechargeId;

    /**
     * 周卡1 月卡2 权益卡3
     */
    private int type;

    /**
     * 权益
     * 针对月卡类型3
     * 1：挑战妖王每日扫荡次数
     * 2：砍树加速
     * 3：跳过广告（前端功能）
     * 4：斗法挑战状态存储上限增加
     * 5：自动砍树(前端)
     * 6：血脉自动抽取（前端）
     * 7：血脉抽取概率
     * 8：伙伴商店刷新必出
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> equity;

    /**
     * 购买时立刻返还的灵符
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reback;

    private int announce;

    private int mail;

    /**
     * 激活时间 单位秒
     */
    private int last;

    private int actId;
}
