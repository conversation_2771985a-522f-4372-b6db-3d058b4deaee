package com.sh.game.common.communication.msg.system.jinjiezhilu;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>进阶之路跳到下一格子</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

@RPC("toServer")
public class ReqJingJieGotoNextMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 4319;
	}
	
	/**
	 * 活动id
	 */
	private int actId;

	public int getActId() {
		return actId;
	}

	public void setActId(int actId) {
		this.actId = actId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.actId = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, actId, false);
		return true;
	}
}
