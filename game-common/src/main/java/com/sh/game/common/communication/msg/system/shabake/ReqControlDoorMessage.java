package com.sh.game.common.communication.msg.system.shabake;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求控制沙巴克城门</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqControlDoorMessage extends ProtobufMessage {

    private com.sh.game.protos.ShabakeProtos.ReqControlDoor proto;

    private com.sh.game.protos.ShabakeProtos.ReqControlDoor.Builder builder;

	
	@Override
	public int getId() {
		return 275009;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ShabakeProtos.ReqControlDoor.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ShabakeProtos.ReqControlDoor.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ShabakeProtos.ReqControlDoor.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ShabakeProtos.ReqControlDoor getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ShabakeProtos.ReqControlDoor proto) {
        this.proto = proto;
    }

}
