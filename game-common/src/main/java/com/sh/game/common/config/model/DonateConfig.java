package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/14 19:35<br>;
 * 版本：1.0<br>;
 * 描述：
 */
@Getter
@Setter
@ConfigData(file = "cfg_donate")
public class DonateConfig extends AbstractConfigData {

    /**
     * 排名
     */
    private int id;

    /**
     * 保底发红包额度
     */
    private int money;

    /**
     * 可获得的称号
     */
    private int titleId;

}
