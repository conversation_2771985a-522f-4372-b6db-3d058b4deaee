package com.sh.game.common.communication.msg.system.task;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>今日活跃值变更通知</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResActiveChangeMessage extends ProtobufMessage {

    private com.sh.game.protos.TaskProtos.ResActiveChange proto;

    private com.sh.game.protos.TaskProtos.ResActiveChange.Builder builder;

	
	@Override
	public int getId() {
		return 102005;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TaskProtos.ResActiveChange.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TaskProtos.ResActiveChange.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TaskProtos.ResActiveChange.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TaskProtos.ResActiveChange getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TaskProtos.ResActiveChange proto) {
        this.proto = proto;
    }

}
