package com.sh.game.common.communication.msg.system.jiutian;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回最近一次的九天之巅女神眷顾奖励时间</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResJiuTianTimeRewardTimeMessage extends ProtobufMessage {

    private com.sh.game.protos.JiutianProtos.ResJiuTianTimeRewardTime proto;

    private com.sh.game.protos.JiutianProtos.ResJiuTianTimeRewardTime.Builder builder;

	
	@Override
	public int getId() {
		return 351007;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.JiutianProtos.ResJiuTianTimeRewardTime.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.JiutianProtos.ResJiuTianTimeRewardTime.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.JiutianProtos.ResJiuTianTimeRewardTime.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.JiutianProtos.ResJiuTianTimeRewardTime getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.JiutianProtos.ResJiuTianTimeRewardTime proto) {
        this.proto = proto;
    }

}
