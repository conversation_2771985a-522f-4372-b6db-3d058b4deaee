package com.sh.game.common.config.cache;

import com.google.common.collect.HashBasedTable;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.MedalConfig;

import java.util.List;

/**
 * 修罗勋章配置缓存
 *
 * <AUTHOR>
 * @date 2022/08/22 17:06
 */
@ConfigCache
public class MedalCache implements IConfigCache {

    /**
     * rowKey:      level 等级
     * columnKey:   star 星级
     * value:       config
     */
    HashBasedTable<Integer, Integer, MedalConfig> medalTable = HashBasedTable.create();

    @Override
    public void build() {
        medalTable.clear();

        List<MedalConfig> medalConfigList = ConfigDataManager.getInstance().getList(MedalConfig.class);
        medalConfigList.forEach(config -> {
            medalTable.put(config.getLevel(), config.getStar(), config);
        });
    }

    /**
     * 根据等级、星级获取勋章配置
     *
     * @param level 等级
     * @param star  星级
     * @return MedalConfig 勋章配置
     */
    public MedalConfig getConfig(int level, int star) {
        return medalTable.get(level, star);
    }
}
