package com.sh.game.common.util;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.SpecialGlobalConfig;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2018/12/22 15:01
 */
public class StringUtil extends com.sh.commons.util.StringUtil {

    public static String serverName(int sid, String name) {
        String pattern = "^[sS]\\d+\\.";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(name);
        if (m.find()) {
            return name;
        } else {
            return "S" + sid + "." + name;
        }
    }

    /**
     * ip正则表达式
     */
    public static final String IP_REGEX = "([1-9]|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])(\\."
            + "(\\d|[1-9]\\d|1\\d{2}|2[0-4]\\d|25[0-5])){3}";


    public static boolean isIp(String address) {
        Pattern pat = Pattern.compile(IP_REGEX);
        Matcher mat = pat.matcher(address);
        return mat.find();
    }

    /**
     * 是否包含有HTML标签
     *
     * @param str
     * @return
     */
    public static boolean containsHTMLTag(String str) {
        if (com.sh.commons.util.StringUtil.isBlank(str)) {
            return false;
        }

        String pattern = "<\\s*(\\S+)(\\s[^>]*)?>[\\s\\S]*<\\s*/\\1\\s*>";
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(str);
        return m.find();
    }

    /**
     * 验证日期字符串是否是YYYY-MM-dd格式或者YYYYMMdd
     *
     * @param str str
     * @return boolean
     */
    public static boolean checkDateFormat(String str) {
        boolean flag = false;
        String regex = "^((\\d{2}(([02468][048])|([13579][26]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[\\-\\/\\s]?((((0?[13578])|(1[02]))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[\\-\\/\\s]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[\\-\\/\\s]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s(((0?[0-9])|([1-2][0-3]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$";
        Pattern pattern1 = Pattern.compile(regex);
        Matcher isNo = pattern1.matcher(str);
        if (isNo.matches()) {
            flag = true;
        }
        return flag;
    }

    /**
     * 起名只支持汉字、英文、数字
     */
    public static boolean containsSpecialChar(String str) {
        // return false;
        SpecialGlobalConfig config = ConfigDataManager.getInstance().getById(SpecialGlobalConfig.class, 1001);
        if (config == null || config.getValue() == null) {
            return false;
        }
        String pattern = config.getValue();
        return !Pattern.matches(pattern, str);
    }

    @SuppressWarnings("unchecked")
    public static <T> T getValue(Object obj, T defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        return (T) obj;
    }

    public static String concat(List<String> strings, String separator) {
        if (separator == null) separator = "";

        String data = "";
        if (strings != null) {
            for (int i = 0; i < strings.size(); i++) {
                if (i > 0) data += separator;
                data += strings.get(i);
            }
        }
        return data;
    }

    /**
     * 驼峰法转下划线
     *
     * @return 转换后的字符串
     */
    public static String camelToUnderline(String param) {
        if (param == null || "".equals(param.trim())) {
            return "";
        }
        char UNDERLINE = '_';

        int len = param.length();
        StringBuilder sb = new StringBuilder(len);
        for (int i = 0; i < len; i++) {
            char c = param.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i != 0) {
                    sb.append(UNDERLINE);
                }
                sb.append(Character.toLowerCase(c));
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * <p>
     * List&lt;int[]> 类型转为 String.
     * <p>
     * 与 {@link StringUtil#strToIntArrayList(String, String, String)} 功能相反
     *
     *
     * <pre>
     *         List<int[]> list = new ArrayList<>();
     *         list.add(new int[]{1, 2, 3});
     *         list.add(new int[]{1, 3, 6});
     *         list.add(new int[]{2, 3, 5});
     *         intArrayListToStr(list, '|', '#')    =   1#2#3|1#3#6|2#3#5
     * </pre>
     * @param list 需要转换的参数
     * @param cp1  外层分隔符
     * @param cp2  内层分隔符
     * @return String 结果字符串
     */
    public static String intArrayListToStr(List<int[]> list, char cp1, char cp2) {
        StringBuilder sb = new StringBuilder();
        for (int[] ints : list) {
            sb.append(StringUtils.join(ints, cp2))
                    .append(cp1);
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        return sb.toString();
    }
}
