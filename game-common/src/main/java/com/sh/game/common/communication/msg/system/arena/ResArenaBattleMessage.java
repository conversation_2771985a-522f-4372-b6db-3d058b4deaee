package com.sh.game.common.communication.msg.system.arena;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>返回竞技场战斗信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResArenaBattleMessage extends ProtobufMessage {

    private com.sh.game.protos.ArenaProtos.ResArenaBattleMessage proto;

    private com.sh.game.protos.ArenaProtos.ResArenaBattleMessage.Builder builder;

	
	@Override
	public int getId() {
		return 394004;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ArenaProtos.ResArenaBattleMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ArenaProtos.ResArenaBattleMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ArenaProtos.ResArenaBattleMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ArenaProtos.ResArenaBattleMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ArenaProtos.ResArenaBattleMessage proto) {
        this.proto = proto;
    }

}
