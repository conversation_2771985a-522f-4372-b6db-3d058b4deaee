package com.sh.game.common.communication.msg.system.miJiGem;

import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求一键镶嵌</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqClickCompletionMiJiGemMessage extends ProtobufMessage {

    private com.sh.game.protos.MiJiGemProtos.ReqClickCompletionMiJiGem proto;

    private com.sh.game.protos.MiJiGemProtos.ReqClickCompletionMiJiGem.Builder builder;

	
	@Override
	public int getId() {
		return 356004;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MiJiGemProtos.ReqClickCompletionMiJiGem.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MiJiGemProtos.ReqClickCompletionMiJiGem.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MiJiGemProtos.ReqClickCompletionMiJiGem.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MiJiGemProtos.ReqClickCompletionMiJiGem getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MiJiGemProtos.ReqClickCompletionMiJiGem proto) {
        this.proto = proto;
    }

}
