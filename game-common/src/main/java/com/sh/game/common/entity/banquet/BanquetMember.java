package com.sh.game.common.entity.banquet;

import io.protostuff.Tag;
import lombok.Data;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/12/12 14:56
 */
@Data
public class BanquetMember {
    @Tag(1250)
    private long roleId;
    @Tag(1251)
    private int place;
    @Tag(1252)
    private int gift;

    public static BanquetMember valueOf(long roleId, int place, int gift) {
        BanquetMember data = new BanquetMember();
        data.setRoleId(roleId);
        data.setPlace(place);
        data.setGift(gift);
        return data;
    }
}
