package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/3/8 9:51
 */
@Getter
@Setter
@ConfigData(file = "cfg_shilian")
public class ShiLianConfig extends AbstractConfigData {

    private int id;

    private int activityType;

    private int type;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    private int[] goals;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;
}
