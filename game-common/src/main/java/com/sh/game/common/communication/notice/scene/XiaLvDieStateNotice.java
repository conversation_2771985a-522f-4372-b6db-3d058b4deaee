package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 侠侣死亡通知逻辑服
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-18
 **/
@Setter
@Getter
@Notice
@NoArgsConstructor
@AllArgsConstructor
public class XiaLvDieStateNotice extends ProcessNotice {
    /**
     * 侠侣主人rid
     */
    private long rid;

    /**
     * 侠侣死亡时间戳
     */
    private long lastDieTime;
}
