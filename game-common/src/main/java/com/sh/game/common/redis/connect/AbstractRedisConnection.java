package com.sh.game.common.redis.connect;

import com.sh.game.common.redis.factory.RedisConnectionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisCommands;

import java.util.List;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by Silence on 2019/3/20.
 */
public abstract class AbstractRedisConnection implements RedisConnection {
    private static final Logger logger = LoggerFactory.getLogger(AbstractRedisConnection.class);


    /**
     * 默认选择数据库
     */
    private int selectedDB = 0;

    private JedisCommands sources;

    private RedisConnectionFactory factory;

    public AbstractRedisConnection(RedisConnectionFactory factory, JedisCommands sources) {
        this.sources = sources;
        this.factory = factory;
    }


    @Override
    public Object getNativeConnection() {
        return this.sources;
    }

    @Override
    public boolean isQueueing() {
        return false;
    }

    @Override
    public boolean isPipelined() {
        return false;
    }

    @Override
    public void openPipeline() {

    }

    @Override
    public List<Object> closePipeline() {
        return null;
    }

    public int getSelectedDB() {
        return selectedDB;
    }

    @Override
    public void setSelectedDB(int selectedDB) {
        this.selectedDB = selectedDB;
    }

    public boolean resetDB() throws Exception {
        Long db = null;
        try {
            db = getDB();
        } catch (Exception e) {
            if (sources instanceof JedisCluster) {
                return false;
            } else {
                throw e;
            }
        }
        return db.intValue() != this.selectedDB;
    }

    @Override
    public RedisConnectionFactory getRedisConnectionFactory() {
        return factory;
    }

}
