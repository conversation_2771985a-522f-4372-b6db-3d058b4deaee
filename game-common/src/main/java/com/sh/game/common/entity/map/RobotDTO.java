package com.sh.game.common.entity.map;


import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class RobotDTO {

    @Tag(1)
    private String name;

    @Tag(2)
    private int career;

    @Tag(3)
    private int sex;

    @Tag(4)
    private int level;

    @Tag(5)
    private int rein;

    @Tag(6)
    private Map<Integer, Integer> equips = new HashMap<>();

    @Tag(7)
    private Map<Integer, Integer> skills = new HashMap<>();



}
