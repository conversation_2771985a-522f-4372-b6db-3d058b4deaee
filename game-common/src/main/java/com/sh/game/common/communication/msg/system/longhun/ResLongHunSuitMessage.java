package com.sh.game.common.communication.msg.system.longhun;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回龙魂宝石套装信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResLongHunSuitMessage extends ProtobufMessage {

    private com.sh.game.protos.LonghunProtos.ResLongHunSuit proto;

    private com.sh.game.protos.LonghunProtos.ResLongHunSuit.Builder builder;

	
	@Override
	public int getId() {
		return 352006;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.LonghunProtos.ResLongHunSuit.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.LonghunProtos.ResLongHunSuit.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.LonghunProtos.ResLongHunSuit.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.LonghunProtos.ResLongHunSuit getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.LonghunProtos.ResLongHunSuit proto) {
        this.proto = proto;
    }

}
