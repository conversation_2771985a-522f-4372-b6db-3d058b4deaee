package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/6/15
 * 天池宫奖励随机规则
 */
@Getter
@Setter
@ConfigData(file = "cfg_tianguan_pot_control")
public class TianChiPotControlConfig extends AbstractConfigData {
    private int id;
    private int group;
    private int amout;
    private int weight;
    private int goal_type;
    private int parttake_times;
    private int need;
}
