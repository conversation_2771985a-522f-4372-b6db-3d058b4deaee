package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 特殊累登
 *
 * <AUTHOR>
 * @date 2022/6/28/028 15:53
 */
@Getter
@Setter
@ConfigData(file = "cfg_special_login")
public class ActivitySpecialLoginConfig extends AbstractConfigData {

    private int id;

    /**
     * 活动id
     */
    private int activityId;

    /**
     * 登录天数
     */
    private int day;

    /**
     * 类型
     * 0表示免费 1充值额度 2消费额度 -1不显示
     */
    private int type;

    /**
     * 目标
     * 单位: 灵符
     */
    private int golas;

    /**
     * 奖励道具
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 公告id
     */
    private int announce;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;
}
