package com.sh.game.common.communication.msg.system.rank.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class RankDataBean extends KryoBean {

	/**
	 * 排名
	 */
	private int rank;
	/**
	 * 名字
	 */
	private String name;
	/**
	 * 职业
	 */
	private int parameter;
	/**
	 * 排名数据
	 */
	private int dataValue;
	/**
	 * 排名数据2
	 */
	private int dataValue2;
	/**
	 * 行会名称
	 */
	private String unionName;
	/**
	 * 玩家编号
	 */
	private long roleId;
	/**
	 * 参数列表
	 */
	private List<Integer> parameterList = new ArrayList<>();

	public int getRank() {
		return rank;
	}

	public void setRank(int rank) {
		this.rank = rank;
	}

		public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

		public int getParameter() {
		return parameter;
	}

	public void setParameter(int parameter) {
		this.parameter = parameter;
	}

		public int getDataValue() {
		return dataValue;
	}

	public void setDataValue(int dataValue) {
		this.dataValue = dataValue;
	}

		public int getDataValue2() {
		return dataValue2;
	}

	public void setDataValue2(int dataValue2) {
		this.dataValue2 = dataValue2;
	}

		public String getUnionName() {
		return unionName;
	}

	public void setUnionName(String unionName) {
		this.unionName = unionName;
	}

		public long getRoleId() {
		return roleId;
	}

	public void setRoleId(long roleId) {
		this.roleId = roleId;
	}

		public List<Integer> getParameterList() {
		return parameterList;
	}

	public void setParameterList(List<Integer> parameterList) {
		this.parameterList = parameterList;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.rank = readInt(buf, false);
		this.name = readString(buf);
		this.parameter = readInt(buf, false);
		this.dataValue = readInt(buf, false);
		this.dataValue2 = readInt(buf, false);
		this.unionName = readString(buf);
		this.roleId = readLong(buf);
		int parameterListLength = readShort(buf);
		for (int parameterListI = 0; parameterListI < parameterListLength; parameterListI++) {
			this.parameterList.add(this.readInt(buf, false));
		}
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, rank, false);
		this.writeString(buf, name);
		this.writeInt(buf, parameter, false);
		this.writeInt(buf, dataValue, false);
		this.writeInt(buf, dataValue2, false);
		this.writeString(buf, unionName);
		this.writeLong(buf, roleId);
		writeShort(buf, this.parameterList.size());
		for (int parameterListI = 0; parameterListI < this.parameterList.size(); parameterListI++) {
			this.writeInt(buf, this.parameterList.get(parameterListI), false);
		}
		return true;
	}
}
