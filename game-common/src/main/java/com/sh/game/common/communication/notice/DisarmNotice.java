package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 缴械
 *
 * <AUTHOR>
 * @date 2022/08/12 11:59
 */
@Getter
@Setter
@Notice
public class DisarmNotice extends ProcessNotice {

    /**
     * 被缴械角色id
     */
    private long roleId;

    /**
     * 缴械部位
     */
    private int pos;

    /**
     * 缴械效果持续时间(ms)
     */
    private long time;
}
