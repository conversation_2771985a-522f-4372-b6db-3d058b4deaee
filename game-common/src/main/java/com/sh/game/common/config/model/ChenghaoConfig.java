package com.sh.game.common.config.model;


import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * @description:
 * @author: fuge
 * @create: 2022/04/11 15:28
 */
@Getter
@Setter
@ConfigData(file = "cfg_chenghao")
public class ChenghaoConfig extends AbstractConfigData {

    private int id;

    private int level;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> costitem;

    private String levelinfo;

    private int fashionid;

    /**
     * 公告id
     */
    private int announce;

}
