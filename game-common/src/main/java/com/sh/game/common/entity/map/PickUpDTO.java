package com.sh.game.common.entity.map;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sh.common.jdbc.ProtostuffSerializable;
import com.sh.commons.tuple.TwoTuple;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/8/5
 * @Desc : to do anything
 */
@Getter
@Setter
public class PickUpDTO implements ProtostuffSerializable {

    @Tag(1)
    private Set<Integer> pickUpFilterItems = Sets.newHashSet();

    @Tag(2)
    private List<TwoTuple<Integer, Integer>> pickUpFilterEquips = Lists.newArrayList();

    @Tag(3)
    private boolean autoPickUpEquip;
}
