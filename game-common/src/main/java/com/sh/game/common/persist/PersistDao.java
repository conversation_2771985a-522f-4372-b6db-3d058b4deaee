package com.sh.game.common.persist;

import com.sh.common.jdbc.JdbcTemplate;
import com.sh.common.jdbc.RowMapper;
import com.sh.common.jdbc.SerializerUtil;
import com.sh.common.persist.Persistable;
import com.sh.game.common.util.StringUtil;
import net.sf.cglib.proxy.Factory;

import java.util.ArrayList;
import java.util.List;

/**
 * 数据库操作层
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/7/7.
 */
public abstract class PersistDao implements IPersistMapper {
    /**
     * 处理的实体
     */
    private final Class<? extends Persistable> entity;


    /**
     * 表名前缀
     */
    private String prefix = "";

    /**
     * 需要处理的tableName
     */
    protected String tableName;

    /**
     * 存储间隔时间
     */
    private int period = 30 * 1000;


    /**
     * jdbc模板
     */
    protected final JdbcTemplate jdbcTemplate;


    public PersistDao(JdbcTemplate jdbcTemplate, Class<? extends Persistable> clazz) {

        this.jdbcTemplate = jdbcTemplate;
        this.entity = clazz;
        init();
    }


    /**
     * 初始化
     */
    private void init() {

        if (entity.isAnnotationPresent(PersistPrefix.class)) {
            PersistPrefix persistName = entity.getAnnotation(PersistPrefix.class);
            if (!persistName.value().isEmpty()) {
                this.prefix = persistName.value();
            }
        }


        /**
         * 初始化映射表名
         */
        if (entity.isAnnotationPresent(PersistName.class)) {
            PersistName persistName = entity.getAnnotation(PersistName.class);
            if (!persistName.value().isEmpty()) {
                this.tableName = persistName.value();
            }
        } else {
            this.tableName = StringUtil.camelToUnderline(this.entity.getSimpleName());
        }

        //最终表名，需要加上前缀
        this.tableName = prefix + tableName;


        /**
         * 初始化表存储间隔
         */
        if (entity.isAnnotationPresent(PersistPeriod.class)) {

            PersistPeriod table = entity.getAnnotation(PersistPeriod.class);
            if (table.value() != 0) {
                this.period = table.value();
            }
        }

    }


    /**
     * 插入操作
     *
     * @param object
     */
    @Override
    public int insert(Persistable object) {
        String sqlStr = buildInsterSql();
        return jdbcTemplate.update(sqlStr, createInsertParameters(object));
    }

    @Override
    public int update(Persistable object) {
        String sqlStr = buildUpdateSql();
        return jdbcTemplate.update(sqlStr, createUpdateParameters(object));
    }

    @Override
    public int delete(Persistable object) {
        String sqlStr = buildDeleteSql();
        return jdbcTemplate.update(sqlStr, createDeleteParameters(object));
    }


    /**
     * @param clazz 当获取类为子类的时候，使用指定类型
     * @param id
     * @param <T>
     * @return
     */
    public <T extends Persistable> T selectById(Class<T> clazz, long id) {
        String sqlStr = buildSelectByIdSql();
        return this.jdbcTemplate.query(sqlStr, selectRowMapper(clazz), id);
    }


    /**
     * 自定义 select
     *
     * @param sqlStr     查询sql语句
     * @param rowMapper  返回rowMapper
     * @param parameters 参数列表
     */
    public <T> T select(String sqlStr, RowMapper<T> rowMapper, Object... parameters) {
        return this.jdbcTemplate.query(sqlStr, rowMapper, parameters);
    }

    /**
     * 自定义查询范围查询
     */
    public <T> List<T> queryList(String sqlStr, RowMapper<T> rowMapper, Object... parameters) {
        return this.jdbcTemplate.queryList(sqlStr, rowMapper, parameters);
    }


    /**
     * 批量插入
     *
     * @param obj
     */
    public void batchInsert(List<Persistable> obj) {
        String sqlStr = buildInsterSql();
        List<Object[]> objs = new ArrayList<>();
        for (Persistable t : obj) {
            objs.add(createInsertParameters(t));
        }
        jdbcTemplate.batchUpdate(sqlStr, objs);
    }


    public void insertByParameters(List<Object[]> parameters) {
        String sqlStr = buildInsterSql();
        jdbcTemplate.batchUpdate(sqlStr, parameters);
    }


    public void update(List<Persistable> objs) {
        String sqlStr = buildUpdateSql();
        List<Object[]> objList = new ArrayList<>();
        for (Persistable obj : objs) {
            objList.add(createUpdateParameters(obj));

        }
        jdbcTemplate.batchUpdate(sqlStr, objList);
    }


    /**
     * 根据参数批量更新
     *
     * @param parameters
     */
    public void updateByParameters(List<Object[]> parameters) {
        String sqlStr = buildUpdateSql();
        jdbcTemplate.batchUpdate(sqlStr, parameters);
    }

    /**
     * 根据参数删除
     *
     * @param parameters 删除的参数
     */
    public void deleteByParameters(List<Object[]> parameters) {
        String sqlStr = buildDeleteSql();
        jdbcTemplate.batchUpdate(sqlStr, parameters);
    }


    protected byte[] serializer(Persistable persistable) {
        byte[] bytes;
        //判断是否被代理
        if (persistable instanceof Factory) {
            Class clazz = persistable.getClass().getSuperclass();
            bytes = SerializerUtil.encode(persistable, clazz);
        } else {
            bytes = SerializerUtil.encode(persistable);
        }
        return bytes;


    }




    public Class<? extends Persistable> getEntity() {
        return entity;
    }

    public int getPeriod() {
        return period;
    }

    public String getTableName() {
        return tableName;
    }


    /**
     * 构建插入语句
     *
     * @return
     */
    public abstract String buildInsterSql();


    /**
     * 构建更新语句
     *
     * @return
     */
    public abstract String buildUpdateSql();


    /**
     * 构建删除语句
     *
     * @return
     */
    public abstract String buildDeleteSql();


    /**
     * 构建单id查询语句
     *
     * @return
     */
    public abstract String buildSelectByIdSql();

    /**
     * 构建参数
     *
     * @param persistable
     * @return
     */
    //构建参数 考虑泛型
    public abstract Object[] createInsertParameters(Persistable persistable);

    public abstract Object[] createUpdateParameters(Persistable persistable);

    public abstract Object[] createDeleteParameters(Persistable persistable);

    /**
     * 如果有需要重写
     *
     * @param clazz
     * @param <T>
     * @return
     */
    public <T extends Persistable> RowMapper<T> selectRowMapper(Class<T> clazz) {
        return selectRowMapper();
    }


    public abstract <T extends Persistable> RowMapper<T> selectRowMapper();


}
