package com.sh.game.common.communication.msg.system.work;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求领取建筑生产产出</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqReceiveWorkProduce extends ProtobufMessage {

    private com.sh.game.protos.WorkProtos.ReqReceiveWorkProduce proto;

    private com.sh.game.protos.WorkProtos.ReqReceiveWorkProduce.Builder builder;

	
	@Override
	public int getId() {
		return 408008;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.WorkProtos.ReqReceiveWorkProduce.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.WorkProtos.ReqReceiveWorkProduce.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.WorkProtos.ReqReceiveWorkProduce.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.WorkProtos.ReqReceiveWorkProduce getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.WorkProtos.ReqReceiveWorkProduce proto) {
        this.proto = proto;
    }

}
