package com.sh.game.common.communication.msg.system.hinder;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>障碍赛使用技能</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toScene")
public class ReqHinderUseSkillMessage extends ProtobufMessage {

    private com.sh.game.protos.HinderProtos.ReqHinderUseSkill proto;

    private com.sh.game.protos.HinderProtos.ReqHinderUseSkill.Builder builder;

	
	@Override
	public int getId() {
		return 190005;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.HinderProtos.ReqHinderUseSkill.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.HinderProtos.ReqHinderUseSkill.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.HinderProtos.ReqHinderUseSkill.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.HinderProtos.ReqHinderUseSkill getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.HinderProtos.ReqHinderUseSkill proto) {
        this.proto = proto;
    }

}
