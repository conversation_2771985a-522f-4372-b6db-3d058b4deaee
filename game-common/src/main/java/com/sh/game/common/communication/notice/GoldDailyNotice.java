package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.TimeProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * 刀刀元宝
 *
 * <AUTHOR>
 * @date 2022/08/26 13:38
 */
@Getter
@Setter
@Notice
public class GoldDailyNotice extends TimeProcessNotice {

    /**
     * 角色id
     */
    private long roleId;

    /**
     * 增加的金币
     */
    private long addCount;

    /**
     * 是否是流程的发起者，即第一个notice
     */
    @Override
    public boolean first() {
        return true;
    }
}
