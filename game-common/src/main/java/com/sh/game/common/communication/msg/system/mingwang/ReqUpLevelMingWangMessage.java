package com.sh.game.common.communication.msg.system.mingwang;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求升级江湖名望</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqUpLevelMingWangMessage extends ProtobufMessage {

    private com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang proto;

    private com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.Builder builder;

	
	@Override
	public int getId() {
		return 315001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MingwangProtos.ReqUpLevelMingWang proto) {
        this.proto = proto;
    }

}
