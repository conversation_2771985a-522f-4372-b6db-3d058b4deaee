package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;


@Setter
@Getter
@Notice
public class NpcAddNotice extends ProcessNotice {

    private long mapId;

    private List<int[]> npcs = new ArrayList<>();

    private List<Object> params = new ArrayList<>();

    /**
     * 过期时间
     */
    private int overTime;

    /**
     * 所属玩家rid
     */
    private long belongRid;

    /**
     * 需要检查位置，防止跟同类型npc重叠
     */
    private boolean checkPoint;

    private int range;

    /**
     * 是否替换同id的NPC
     */
    private boolean isReplace;
}
