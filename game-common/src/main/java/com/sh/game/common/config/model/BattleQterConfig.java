package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/22 13:33
 */
@lombok.Getter
@lombok.Setter
@ConfigData(file="cfg_battle_qter")
public class BattleQterConfig extends AbstractConfigData {
    private int id;
    private int effectId;
    private int stageType;
    private int stageId;
}
