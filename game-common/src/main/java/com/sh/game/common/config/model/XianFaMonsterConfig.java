package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ConfigData(file = "cfg_xianfa_monster")
public class XianFaMonsterConfig extends AbstractConfigData {

    private long id;

    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> attitude;

    /**
     * 机器人时装
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> monster;

    private String name;

    private int touxiang;

    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> level;

    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] restrainType;
}
