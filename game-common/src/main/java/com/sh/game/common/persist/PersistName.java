package com.sh.game.common.persist;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 实体存储名
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/7/7.
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)

public @interface PersistName {
    //默认使用实体名
    String value() default "";
}
