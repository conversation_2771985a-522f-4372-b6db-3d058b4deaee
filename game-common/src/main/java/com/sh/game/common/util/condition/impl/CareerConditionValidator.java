package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.common.util.condition.IConditionValidator;

public class CareerConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        int condition = params.length > 1 ? params[1] : 0;
        if (condition != 0 && condition != avatar.getCareer()) {
            return false;
        }

        return true;
    }
}
