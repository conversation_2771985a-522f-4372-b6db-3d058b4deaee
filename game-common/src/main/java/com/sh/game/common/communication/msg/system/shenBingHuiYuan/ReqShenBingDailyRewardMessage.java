package com.sh.game.common.communication.msg.system.shenBingHuiYuan;

import com.sh.game.common.msg.RPC;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

/**
 * 请求每日会员工资
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-08-02
 **/
@RPC("toLogic")
public class ReqShenBingDailyRewardMessage extends AbstractMessage {
    @Override
    public int getId() {
        return 200007;
    }


    @Override
    public boolean read(KryoInput buf) {

        return true;
    }

    @Override
    public boolean write(KryoOutput buf) {

        return true;
    }
}
