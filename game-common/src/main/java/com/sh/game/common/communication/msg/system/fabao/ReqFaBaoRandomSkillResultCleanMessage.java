package com.sh.game.common.communication.msg.system.fabao;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
* <p>请求法宝技能随机结果清除</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toLogic")
public class ReqFaBaoRandomSkillResultCleanMessage extends ProtobufMessage {

    private com.sh.game.protos.FaBaoProtos.ReqFaBaoRandomSkillResultCleanMessage proto;

    private com.sh.game.protos.FaBaoProtos.ReqFaBaoRandomSkillResultCleanMessage.Builder builder;


    @Override
    public int getId() {
        return 520010;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FaBaoProtos.ReqFaBaoRandomSkillResultCleanMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FaBaoProtos.ReqFaBaoRandomSkillResultCleanMessage.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.FaBaoProtos.ReqFaBaoRandomSkillResultCleanMessage.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FaBaoProtos.ReqFaBaoRandomSkillResultCleanMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FaBaoProtos.ReqFaBaoRandomSkillResultCleanMessage proto) {
        this.proto = proto;
    }

}
