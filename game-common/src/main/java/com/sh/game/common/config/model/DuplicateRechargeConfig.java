package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.ShuXianIntArrayConverter;
import lombok.Getter;
import lombok.Setter;

/**
 * 每日副本-充值副本
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/11/1.
 */
@Setter
@Getter
@ConfigData(file = "cfg_duplicate_recharge", keys = "id")
public class DuplicateRechargeConfig extends AbstractConfigData {

    //自增id
    private int id;

    /**
     * 范围
     */
    @ConfigField(converter = ShuXianIntArrayConverter.class)
    private int[] recharge;


    //免费次数
    private int freetime;

    //充值次数
    private int rechargetime;


}
