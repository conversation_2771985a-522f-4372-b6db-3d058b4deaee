package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.ShuXianIntArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> <PERSON>o
 * @Email <EMAIL>
 * @since 2022-03-02
 **/
@Getter
@Setter
@ConfigData(file = "cfg_wheel_reward")
public class ActivityLuckWheelRewardConfig extends AbstractConfigData {
    /**
     * id
     */
    private int id;

    /**
     * 活动id
     */
    private int activityId;

    /**
     * 活动类型
     */
    private int type;

    /**
     * 奖励内容根据开服天数区分，开服天数=活动开启时间-服务器开启时间，配置使用竖线分割，开服天数下限|上限
     */
    @ConfigField(converter = ShuXianIntArrayConverter.class)
    private int[] opentime;

    /**
     * 权重
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> reward;

    /**
     * 转盘奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> rewardshow;

    /**
     * 次数，根据次数读奖励
     */
    private int count;

    /**
     * 公告
     */
    private int announce;
}
