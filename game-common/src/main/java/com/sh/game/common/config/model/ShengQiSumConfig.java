package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/30 9:49
 */
@Getter
@Setter
@ConfigData(file = "cfg_shengqi_sum")
public class ShengQiSumConfig extends AbstractConfigData {


    private int id;

    private int lv;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attribute;

}
