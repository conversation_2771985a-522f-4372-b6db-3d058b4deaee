package com.sh.game.common.config.converter;

import com.sh.common.config.IConverter;
import com.sh.commons.util.Cast;

import java.util.HashMap;

/**
 * #和& 解析成 一个Map 例如 1#1&2#3->  1->1,2->3的map
 *
 * <AUTHOR>
 */
public class JinHaoAndYuHaoMapConverter implements IConverter {

    @Override
    public HashMap<Integer, Integer> convert(Object source) {
        return parse((String) source);
    }

    /**
     * 解析字符串
     *
     * @param content
     * @return
     */
    public static HashMap<Integer, Integer> parse(String content) {
        HashMap<Integer, Integer> map = new HashMap<>();
        if (content != null && !"".equals(content)) {
            String[] arr = content.split("&");
            if (arr.length > 0) {
                for (String v : arr) {
                    String[] temp = v.split("#");
                    if (temp.length == 2) {
                        int itemId = Cast.toInteger(temp[0]);
                        int itemCount = Cast.toInteger(temp[1]);
                        map.merge(itemId, itemCount, Integer::sum);
                    }
                }
            }
        }
        return map;
    }

}
