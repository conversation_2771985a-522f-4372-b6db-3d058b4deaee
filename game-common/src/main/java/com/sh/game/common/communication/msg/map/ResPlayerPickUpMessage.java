package com.sh.game.common.communication.msg.map;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>通知周围玩家该玩家拾取物品</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResPlayerPickUpMessage extends ProtobufMessage {

    private com.sh.game.protos.MapProtos.ResPlayerPickUp proto;

    private com.sh.game.protos.MapProtos.ResPlayerPickUp.Builder builder;

	
	@Override
	public int getId() {
		return 67062;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MapProtos.ResPlayerPickUp.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MapProtos.ResPlayerPickUp.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MapProtos.ResPlayerPickUp.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MapProtos.ResPlayerPickUp getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MapProtos.ResPlayerPickUp proto) {
        this.proto = proto;
    }

}
