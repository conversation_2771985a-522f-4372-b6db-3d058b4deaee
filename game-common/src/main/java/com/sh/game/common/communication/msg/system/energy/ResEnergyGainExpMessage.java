package com.sh.game.common.communication.msg.system.energy;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回玩家十倍经验提示 客户端需要展示</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResEnergyGainExpMessage extends ProtobufMessage {

    private com.sh.game.protos.EnergyProtos.ResEnergyGainExp proto;

    private com.sh.game.protos.EnergyProtos.ResEnergyGainExp.Builder builder;

	
	@Override
	public int getId() {
		return 178001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.EnergyProtos.ResEnergyGainExp.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.EnergyProtos.ResEnergyGainExp.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.EnergyProtos.ResEnergyGainExp.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.EnergyProtos.ResEnergyGainExp getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.EnergyProtos.ResEnergyGainExp proto) {
        this.proto = proto;
    }

}
