package com.sh.game.common.communication.msg.system.team;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>离队惩罚截止时间通知</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResArenaPunishMessage extends ProtobufMessage {

    private com.sh.game.protos.TeamProtos.ResArenaPunish proto;

    private com.sh.game.protos.TeamProtos.ResArenaPunish.Builder builder;

	
	@Override
	public int getId() {
		return 101019;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TeamProtos.ResArenaPunish.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TeamProtos.ResArenaPunish.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TeamProtos.ResArenaPunish.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TeamProtos.ResArenaPunish getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TeamProtos.ResArenaPunish proto) {
        this.proto = proto;
    }

}
