package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 共铸神剑
 *
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2021-12-13
 **/
@Getter
@Setter
@ConfigData(file = "cfg_gongzhushenjian")
public class ZhuJianConfig extends AbstractConfigData {
    /**
     * id
     */
    private int id;

    /**
     * 消耗材料
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    /**
     * 奖励材料
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 铸剑条件
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> condition;

}
