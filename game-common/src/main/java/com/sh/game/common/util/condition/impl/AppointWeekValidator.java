package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.condition.IConditionValidator;

/**
 * ATO：yumo<br>;
 * 时间：2021/3/16 13:16<br>;
 * 版本：1.0<br>;
 * 描述：指定时间之后，间隔偶数周
 */
public class AppointWeekValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (params == null || params.length < 2) {
            return true;
        }
        int seconds = TimeUtil.getNowOfSeconds() - params[1];
        long passDays = seconds / TimeUtil.ONE_DAY_IN_SECONDS + ctx.getOffsetDays();
        if (passDays < 0) {
            return false;
        }
        return passDays / 7 % 2 == 0;
    }
}
