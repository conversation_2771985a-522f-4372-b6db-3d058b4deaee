package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ConfigData(file = "cfg_pk")
public class PkConfig extends AbstractConfigData {

    /**
     * id
     */
    private int id;

    /**
     * pk值
     */
    private int stage;

    /**
     * 掉落数量
     */
    private int[] dropCount;

    /**
     * 掉落概率
     */
    private int[] dropRate;

    /**
     * 增加的pk值
     */
    private int addPk;
}
