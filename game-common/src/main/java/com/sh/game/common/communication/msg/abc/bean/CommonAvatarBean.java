package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


import java.util.ArrayList;
import java.util.List;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class CommonAvatarBean extends KryoBean {

	/**
	 * 
	 */
	private int sex;
	/**
	 * 
	 */
	private int hair;
	/**
	 * 
	 */
	private List<CommonSlotBean> equips = new ArrayList<>();
	/**
	 * 
	 */
	private List<CommonSlotBean> wears = new ArrayList<>();

	public int getSex() {
		return sex;
	}

	public void setSex(int sex) {
		this.sex = sex;
	}

		public int getHair() {
		return hair;
	}

	public void setHair(int hair) {
		this.hair = hair;
	}

		public List<CommonSlotBean> getEquips() {
		return equips;
	}

	public void setEquips(List<CommonSlotBean> equips) {
		this.equips = equips;
	}
	public List<CommonSlotBean> getWears() {
		return wears;
	}

	public void setWears(List<CommonSlotBean> wears) {
		this.wears = wears;
	}

	@Override
	public boolean read(KryoInput buf) {

		this.sex = readInt(buf, false);
		this.hair = readInt(buf, false);
		int equipsLength = readShort(buf);
		for (int equipsI = 0; equipsI < equipsLength; equipsI++) {
			if (readByte(buf) == 0) { 
				this.equips.add(null);
			} else {
				CommonSlotBean commonSlotBean = new CommonSlotBean();
				commonSlotBean.read(buf);
				this.equips.add(commonSlotBean);
			}
		}
		int wearsLength = readShort(buf);
		for (int wearsI = 0; wearsI < wearsLength; wearsI++) {
			if (readByte(buf) == 0) { 
				this.wears.add(null);
			} else {
				CommonSlotBean commonSlotBean = new CommonSlotBean();
				commonSlotBean.read(buf);
				this.wears.add(commonSlotBean);
			}
		}
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, sex, false);
		this.writeInt(buf, hair, false);
		writeShort(buf, this.equips.size());
		for (int equipsI = 0; equipsI < this.equips.size(); equipsI++) {
			this.writeBean(buf, this.equips.get(equipsI));
		}
		writeShort(buf, this.wears.size());
		for (int wearsI = 0; wearsI < this.wears.size(); wearsI++) {
			this.writeBean(buf, this.wears.get(wearsI));
		}
		return true;
	}
}
