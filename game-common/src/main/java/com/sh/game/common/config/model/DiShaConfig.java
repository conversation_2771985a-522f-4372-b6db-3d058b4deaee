package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/7/6 14:51
 */
@Getter
@Setter
@ConfigData(file = "cfg_disha",keys = "mid")
public class DiShaConfig extends AbstractConfigData {

    private int id;

    private int mapid;

    private int mid;

    private int x;

    private int y;

    private int r;

    private int dir;

    private int remove;

    private int group;
}
