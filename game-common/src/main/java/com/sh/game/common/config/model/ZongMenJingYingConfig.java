package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapIntLongConverter;
import com.sh.game.common.config.converter.array.JinghaoDoubleArrayConverter;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/2/14 10:19
 */
@Getter
@Setter
@ConfigData(file = "cfg_zongmen_jingying")
public class ZongMenJingYingConfig extends AbstractConfigData {
    private int id;
    private int type;
    private int diziNum;
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> zhuanQian;
    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] seatOpen;
    @ConfigField(converter = JinghaoDoubleArrayConverter.class)
    private double[] formulaNum;
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> cost;

}
