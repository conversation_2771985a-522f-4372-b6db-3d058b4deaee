package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ConfigData(file = "cfg_sbkreward")
public class SBKRewardConfig extends AbstractConfigData {

    private int id;

    private int serverType;

    private int type;

    private int order;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    private int ascription;

    private int mail;
}
