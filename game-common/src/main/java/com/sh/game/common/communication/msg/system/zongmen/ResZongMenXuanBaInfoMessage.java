package com.sh.game.common.communication.msg.system.zongmen;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
* <p>返回宗门选拔信息</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toClient")
public class ResZongMenXuanBaInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.ZongMenProtos.ResZongMenXuanBaInfo proto;

    private com.sh.game.protos.ZongMenProtos.ResZongMenXuanBaInfo.Builder builder;


    @Override
    public int getId() {
        return 527012;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ZongMenProtos.ResZongMenXuanBaInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ZongMenProtos.ResZongMenXuanBaInfo.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.ZongMenProtos.ResZongMenXuanBaInfo.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ZongMenProtos.ResZongMenXuanBaInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ZongMenProtos.ResZongMenXuanBaInfo proto) {
        this.proto = proto;
    }

}
