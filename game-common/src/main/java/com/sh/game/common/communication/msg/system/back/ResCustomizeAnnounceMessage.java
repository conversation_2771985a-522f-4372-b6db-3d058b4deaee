package com.sh.game.common.communication.msg.system.back;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>发送自定义公告(后台用)</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResCustomizeAnnounceMessage extends ProtobufMessage {

    private com.sh.game.protos.BackProtos.ResCustomizeAnnounce proto;

    private com.sh.game.protos.BackProtos.ResCustomizeAnnounce.Builder builder;

	
	@Override
	public int getId() {
		return 43023;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.BackProtos.ResCustomizeAnnounce.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.BackProtos.ResCustomizeAnnounce.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.BackProtos.ResCustomizeAnnounce.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.BackProtos.ResCustomizeAnnounce getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.BackProtos.ResCustomizeAnnounce proto) {
        this.proto = proto;
    }

}
