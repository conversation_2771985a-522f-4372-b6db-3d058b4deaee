package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;

import java.util.List;

/**
 * description: 道具合成表
 * date: 2024/4/18
 * author: chenbin
 */
@lombok.Getter
@lombok.Setter
@ConfigData(file="cfg_merge")
public class ItemMergeConfig extends AbstractConfigData {
    /**
     * 唯一id
     */
    private int id;

    /**
     * 合成类型
     */
    private int type;

    /**
     * 目标物品id
     */
    private int targetId;

    /**
     * 要消耗的材料  targetId#count&targetId#count
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> needItem;

    /**
     * 公告
     */
    private int announce;

    /**
     * 备注
     */
    private String beizhu;
}
