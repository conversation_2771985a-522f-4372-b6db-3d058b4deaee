package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoYuHaoAndShuXianListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ConfigData(file = "cfg_practicechose")

public class PracticeChoseConfig extends AbstractConfigData {

    private int id;

    /**
     * 赛季
     */
    private int season;

    /**
     * 起始名次
     */
    private int rankMin;

    /**
     * 结尾名次
     */
    private int rankMax;

    /**
     * 自选奖励
     */
    @ConfigField(converter = JinHaoYuHaoAndShuXianListConverter.class)
    private List<List<int[]>> reward;

}
