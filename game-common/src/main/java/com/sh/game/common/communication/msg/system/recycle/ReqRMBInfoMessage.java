package com.sh.game.common.communication.msg.system.recycle;


import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求人民币兑换信息， 主要是获取玩家累计充值剩余可用金额</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqRMBInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.RecycleProtos.ReqRMBInfo proto;

    private com.sh.game.protos.RecycleProtos.ReqRMBInfo.Builder builder;

	
	@Override
	public int getId() {
		return 184006;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.RecycleProtos.ReqRMBInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.RecycleProtos.ReqRMBInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.RecycleProtos.ReqRMBInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.RecycleProtos.ReqRMBInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.RecycleProtos.ReqRMBInfo proto) {
        this.proto = proto;
    }

}
