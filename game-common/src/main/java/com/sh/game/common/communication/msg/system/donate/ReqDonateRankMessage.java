package com.sh.game.common.communication.msg.system.donate;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>捐献排行榜请求</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqDonateRankMessage extends ProtobufMessage {

    private com.sh.game.protos.DonateProtos.ReqDonateRank proto;

    private com.sh.game.protos.DonateProtos.ReqDonateRank.Builder builder;

	
	@Override
	public int getId() {
		return 51001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DonateProtos.ReqDonateRank.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DonateProtos.ReqDonateRank.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DonateProtos.ReqDonateRank.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DonateProtos.ReqDonateRank getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DonateProtos.ReqDonateRank proto) {
        this.proto = proto;
    }

}
