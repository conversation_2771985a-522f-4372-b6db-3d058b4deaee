package com.sh.game.common.communication.msg.system.function;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>神兵打造活动信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResFunctionInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.FunctionProtos.ResFunctionInfo proto;

    private com.sh.game.protos.FunctionProtos.ResFunctionInfo.Builder builder;

	
	@Override
	public int getId() {
		return 202002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FunctionProtos.ResFunctionInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FunctionProtos.ResFunctionInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FunctionProtos.ResFunctionInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FunctionProtos.ResFunctionInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FunctionProtos.ResFunctionInfo proto) {
        this.proto = proto;
    }

}
