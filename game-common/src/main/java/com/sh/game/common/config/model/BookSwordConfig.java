package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 天书神剑配置
 *
 * <AUTHOR>
 * @date 2022/08/24 16:52
 */
@Getter
@Setter
@ConfigData(file = "cfg_skysword")
public class BookSwordConfig extends AbstractConfigData {

    private int id;

    /**
     * 天书1 神剑2
     */
    private int type;

    /**
     * 类型
     */
    private int sub;

    /**
     * 等级
     */
    private int lv;

    /**
     * 升级消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attribute;

    /**
     * 下一级id
     */
    private int nextId;
}
