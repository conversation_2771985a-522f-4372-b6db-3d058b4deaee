package com.sh.game.common.communication.msg.system.query;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求查看玩家列表</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqQueryListMessage extends ProtobufMessage {

    private com.sh.game.protos.QueryProtos.ReqQueryList proto;

    private com.sh.game.protos.QueryProtos.ReqQueryList.Builder builder;

	
	@Override
	public int getId() {
		return 173003;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.QueryProtos.ReqQueryList.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.QueryProtos.ReqQueryList.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.QueryProtos.ReqQueryList.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.QueryProtos.ReqQueryList getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.QueryProtos.ReqQueryList proto) {
        this.proto = proto;
    }

}
