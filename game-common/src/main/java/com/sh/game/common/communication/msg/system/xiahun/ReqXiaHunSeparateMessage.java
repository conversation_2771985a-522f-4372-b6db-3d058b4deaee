package com.sh.game.common.communication.msg.system.xiahun;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求侠魂分离</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqXiaHunSeparateMessage extends ProtobufMessage {

    private com.sh.game.protos.XiahunProtos.ReqXiaHunSeparate proto;

    private com.sh.game.protos.XiahunProtos.ReqXiaHunSeparate.Builder builder;

	
	@Override
	public int getId() {
		return 374002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.XiahunProtos.ReqXiaHunSeparate.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.XiahunProtos.ReqXiaHunSeparate.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.XiahunProtos.ReqXiaHunSeparate.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.XiahunProtos.ReqXiaHunSeparate getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.XiahunProtos.ReqXiaHunSeparate proto) {
        this.proto = proto;
    }

}
