package com.sh.game.common.communication.msg.map.duplicate;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>同步player和monster血量</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResPerformTotalHpMessage extends ProtobufMessage {

    private com.sh.game.protos.DuplicateProtos.ResPerformTotalHp proto;

    private com.sh.game.protos.DuplicateProtos.ResPerformTotalHp.Builder builder;

	
	@Override
	public int getId() {
		return 71006;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DuplicateProtos.ResPerformTotalHp.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DuplicateProtos.ResPerformTotalHp.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DuplicateProtos.ResPerformTotalHp.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DuplicateProtos.ResPerformTotalHp getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DuplicateProtos.ResPerformTotalHp proto) {
        this.proto = proto;
    }

}
