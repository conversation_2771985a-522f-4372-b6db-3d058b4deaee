package com.sh.game.common.communication.msg.system.drop;

import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回掉落记录列表</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ResDropRecordMessage extends ProtobufMessage {

    private com.sh.game.protos.DropProtos.ResDropRecord proto;

    private com.sh.game.protos.DropProtos.ResDropRecord.Builder builder;

	
	@Override
	public int getId() {
		return 114002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DropProtos.ResDropRecord.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DropProtos.ResDropRecord.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DropProtos.ResDropRecord.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DropProtos.ResDropRecord getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DropProtos.ResDropRecord proto) {
        this.proto = proto;
    }

}
