package com.sh.game.common.communication.msg.system.xianfa;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
* <p>请求战斗激励</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toLogic")
public class ReqBattleJiLiMessage extends ProtobufMessage {

    private com.sh.game.protos.XianFaProtos.ReqBattleJiLi proto;

    private com.sh.game.protos.XianFaProtos.ReqBattleJiLi.Builder builder;


    @Override
    public int getId() {
        return 528005;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.XianFaProtos.ReqBattleJiLi.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.XianFaProtos.ReqBattleJiLi.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.XianFaProtos.ReqBattleJiLi.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.XianFaProtos.ReqBattleJiLi getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.XianFaProtos.ReqBattleJiLi proto) {
        this.proto = proto;
    }

}
