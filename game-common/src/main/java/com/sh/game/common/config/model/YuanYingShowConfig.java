package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ConfigData(file = "cfg_yuanying_show")
public class YuanYingShowConfig extends AbstractConfigData {
	/**
	 * 配置id
	 */
	private int id;
	/**
	 * 下一级id
	 */
	private int nextid;
	/**
	 * 类型
	 */
	private int type;
	/**
	 * 总等级下限
	 */
	private int minlv1;
	/**
	 * 总等级上限
	 */
	private int maxlv1;
	/**
	 * 元婴外显
	 */
	private int appearance;
}
