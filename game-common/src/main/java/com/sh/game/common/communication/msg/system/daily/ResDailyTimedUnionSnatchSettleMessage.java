package com.sh.game.common.communication.msg.system.daily;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>行会夺旗结算</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResDailyTimedUnionSnatchSettleMessage extends ProtobufMessage {

    private com.sh.game.protos.DailyProtos.ResDailyTimedUnionSnatchSettle proto;

    private com.sh.game.protos.DailyProtos.ResDailyTimedUnionSnatchSettle.Builder builder;

	
	@Override
	public int getId() {
		return 175079;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DailyProtos.ResDailyTimedUnionSnatchSettle.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DailyProtos.ResDailyTimedUnionSnatchSettle.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DailyProtos.ResDailyTimedUnionSnatchSettle.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DailyProtos.ResDailyTimedUnionSnatchSettle getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DailyProtos.ResDailyTimedUnionSnatchSettle proto) {
        this.proto = proto;
    }

}
