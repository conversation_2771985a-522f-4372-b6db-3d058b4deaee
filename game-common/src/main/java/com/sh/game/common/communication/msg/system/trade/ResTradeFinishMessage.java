package com.sh.game.common.communication.msg.system.trade;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResTradeFinishMessage extends ProtobufMessage {

    private com.sh.game.protos.TradeProtos.ResTradeFinish proto;

    private com.sh.game.protos.TradeProtos.ResTradeFinish.Builder builder;

	
	@Override
	public int getId() {
		return 104014;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TradeProtos.ResTradeFinish.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TradeProtos.ResTradeFinish.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TradeProtos.ResTradeFinish.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TradeProtos.ResTradeFinish getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TradeProtos.ResTradeFinish proto) {
        this.proto = proto;
    }

}
