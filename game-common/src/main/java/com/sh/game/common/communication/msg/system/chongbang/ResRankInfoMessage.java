package com.sh.game.common.communication.msg.system.chongbang;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
* <p>返回排行榜信息</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toClient")
public class ResRankInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.ChongBangProtos.ResRankInfo proto;

    private com.sh.game.protos.ChongBangProtos.ResRankInfo.Builder builder;


    @Override
    public int getId() {
        return 523002;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ChongBangProtos.ResRankInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ChongBangProtos.ResRankInfo.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.ChongBangProtos.ResRankInfo.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ChongBangProtos.ResRankInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ChongBangProtos.ResRankInfo proto) {
        this.proto = proto;
    }

}
