package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import com.sh.game.common.config.converter.list.JinHaoShuXianAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/6/15
 * 领取称号刷npc
 */
@Getter
@Setter
@ConfigData(file = "cfg_title_statue")
public class TitleStatueConfig extends AbstractConfigData {
    /**
     * 称号id
     */
    private int id;
    /**
     * npc表id
     */
    private int npcid;
    /**
     * 领取称号所需装备
     */
    @ConfigField(converter = JinHaoShuXianAndYuHaoListConverter.class)
    private List<List<int[]>> items;
    /**
     * 雕像位置
     */
    private int[] statuemap;
    /**
     * 雕像id
     */
    private int[] statueid;
    /**
     * 公告
     */
    private int announce;
    /**
     * 首位奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;
    /**
     * 邮件
     */
    private int mail;
}
