package com.sh.game.common.util.condition.impl;

import com.sh.game.common.constant.TaskConst;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

/**
 * @Description: 任务判断提交
 * <AUTHOR>
 * @Date 2024/11/6 13:52
 */
public class TaskStateCompleteConditionValidator extends IConditionValidatorDefault {
    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params.length > 2) {
            return avatar.checkTaskState(params[1], params[2]);
        } else {
            return avatar.checkTaskState(params[1], TaskConst.State.HAS_SUBMIT);
        }
    }
}
