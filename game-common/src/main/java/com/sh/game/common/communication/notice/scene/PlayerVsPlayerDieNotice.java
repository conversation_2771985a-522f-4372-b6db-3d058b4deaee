package com.sh.game.common.communication.notice.scene;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * pvp 击杀玩家
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-03
 **/
@Getter
@Setter
@Notice
@NoArgsConstructor
@AllArgsConstructor
public class PlayerVsPlayerDieNotice extends ProcessNotice {
    /**
     * 杀人者id
     */
    private long killerRid;

    /**
     * 被杀者
     */
    private long victimRid;

    /**
     * 案发地图cid
     */
    private int dieSceneMapId;

    /**
     * 被杀者阵营类型
     */
    private int victimRidCampType;
}
