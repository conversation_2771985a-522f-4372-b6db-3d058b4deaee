package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-09-13
 */
@Getter
@Setter
@ConfigData(file = "cfg_kuanggong")
public class KuangGongConfig extends AbstractConfigData {
    private int id;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> money;
}
