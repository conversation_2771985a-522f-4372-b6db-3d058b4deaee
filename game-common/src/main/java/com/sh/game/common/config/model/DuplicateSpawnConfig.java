package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


@Getter
@Setter
@ConfigData(file = "cfg_duplicate_wave")
public class DuplicateSpawnConfig extends AbstractConfigData {

    private int id;

    private int mapId;

    private int monster;

    private int count;

    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> birthPos;

    private int range;

    private int orientation;

    private int[] arrivePos;

    private int waveId;

    private int[] waveCondition;

    private int activity;

    private int waveIdNum;

    private int qxtmType;
}
