package com.sh.game.common.config.converter.list;

import com.sh.common.config.IConverter;
import com.sh.commons.util.Cast;
import com.sh.commons.util.Symbol;
import com.sh.game.common.config.entity.IntervalValueParam;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2022-05-09
 **/
public class IntervalValueListConvert implements IConverter {
    @Override
    public Object convert(Object source) {
        String v = (String) source;
        if (v == null || StringUtils.isEmpty(v)) {
            return Collections.emptyList();
        }
        List<IntervalValueParam> ret = new ArrayList<>();
        String[] outers = v.split(Symbol.SHUXIAN);
        for (String outer : outers) {
            String[] allArray = outer.split(Symbol.JINHAO);
            if (allArray.length < 3) {
                throw new RuntimeException("配置表校验出错，长度不足");
            }

            int min = Cast.toInteger(allArray[0]);
            int max = Cast.toInteger(allArray[1]);
            int value = Cast.toInteger(allArray[2]);
            if (min >= max) {
                throw new RuntimeException("配置表校验出错，最小值不可大于最大值");
            }

            IntervalValueParam param = new IntervalValueParam();
            param.setMin(min);
            param.setMax(max);
            param.setValue(value);
            ret.add(param);
        }

        return ret;
    }
}
