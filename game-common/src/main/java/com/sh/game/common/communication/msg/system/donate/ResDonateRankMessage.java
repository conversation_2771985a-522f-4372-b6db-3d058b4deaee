package com.sh.game.common.communication.msg.system.donate;

import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>捐献排行榜响应</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ResDonateRankMessage extends ProtobufMessage {

    private com.sh.game.protos.DonateProtos.ResDonateRank proto;

    private com.sh.game.protos.DonateProtos.ResDonateRank.Builder builder;

	
	@Override
	public int getId() {
		return 51002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DonateProtos.ResDonateRank.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DonateProtos.ResDonateRank.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DonateProtos.ResDonateRank.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DonateProtos.ResDonateRank getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DonateProtos.ResDonateRank proto) {
        this.proto = proto;
    }

}
