package com.sh.game.common.communication.msg.abc.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class CommonKeyValueBean extends KryoBean {

	/**
	 * 
	 */
	private int key;
	/**
	 * 
	 */
	private int value;

	public int getKey() {
		return key;
	}

	public void setKey(int key) {
		this.key = key;
	}

		public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.key = readInt(buf, false);
		this.value = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, key, false);
		this.writeInt(buf, value, false);
		return true;
	}
}
