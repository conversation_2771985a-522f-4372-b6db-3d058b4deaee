package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@ConfigData(file = "cfg_miji_suit")
public class MiJiStoneSuitConfig extends AbstractConfigData {

    private int id;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attribute;

    private int[] buff;

    private int level;

    /**
     * 达到条件等级
     */
    private int allLevel;
}
