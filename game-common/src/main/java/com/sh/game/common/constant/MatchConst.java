package com.sh.game.common.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/15 10:54
 */
public interface MatchConst {


    interface ServerType {
        int GAME = 1;
        int BATTLE = 3;
    }

    class PVPType {
        public static final int _TianTi_3v3 = 1;
        public static final int _2v2 = 2;
        public static final int _3v3 = 3;
        public static final int _5v5 = 5;
        public static final int _UnionPK = 7;
        //天下第一冒泡赛
        public static final int BubbleMatch = 11;
        //天下第一二阶段 指定匹配玩家 淘汰赛
        public static final int Knockout = 12;
        public static final int _15v15 = 15;

        //50人混战
        public static final int _50DogFight = 50;

        /**
         * 巅峰联赛海选赛
         */
        public static final int DFLS_HAI_XUAN = 60;

        /**
         * 巅峰联赛淘汰赛
         */
        public static final int DFLS_TAO_TAI_SAI = 61;

        public static boolean contains(int type) {
            return type == _2v2 ||
                    type == _3v3 ||
                    type == _5v5 ||
                    type == _UnionPK ||
                    type == _TianTi_3v3 ||
                    type == _15v15 ||
                    type == _50DogFight ||
                    type == BubbleMatch ||
                    type == Knockout ||
                    type == DFLS_HAI_XUAN ||
                    type == DFLS_TAO_TAI_SAI
                    ;
        }
    }

    interface BattleState {
        int MATCHED = 1;
        int REQ = 2;
        int CREATED = 3;
        int END = 4;
    }

    interface MapId {
        int _5V5 = 51001;
        int _TianTi_3v3= 121352;
        int _15v15 = 69001;
        int _50DogFight = 71001;
        int BubbleMatch = 82001;
        int Knockout = 82002;
        int _3v3 = 172002;
    }

    interface PVP_GROUP {
        int A = 1;
        int B = 2;
    }

    interface PVP_PROGRESS {
        int REQ = 1;
        int MATCH = 2;
        int BATTLE = 3;
        int END = 4;
    }

    interface UnionMatchConst{

        int MAXMATCHCOUNT = 5;

        int PROCESSCODE_SUCESS = 1;

        int PROCESSCODE_ERR = 0;

        int PROCESSCODE_MATCH_ERR = 2;

        int PALACE_INNER_MAPID = 72001;

        int PALACE_OUT_MAPID = 72002;

        List<Integer> matchLimists = new ArrayList<>();
    }

    enum matchLimits {
        one(45),
        two(76),
        three(106),
        four(135);
        public int type;

        matchLimits(int type) {
            this.type = type;
        }
    }
}
