package com.sh.game.common.communication.msg.system.clientdata;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>请求修改客户端存储数据</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqClientDataUpdateMessage extends ProtobufMessage {

    private com.sh.game.protos.ClientDataProtos.ReqClientDataUpdateMessage proto;

    private com.sh.game.protos.ClientDataProtos.ReqClientDataUpdateMessage.Builder builder;

	
	@Override
	public int getId() {
		return 391002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ClientDataProtos.ReqClientDataUpdateMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ClientDataProtos.ReqClientDataUpdateMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ClientDataProtos.ReqClientDataUpdateMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ClientDataProtos.ReqClientDataUpdateMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ClientDataProtos.ReqClientDataUpdateMessage proto) {
        this.proto = proto;
    }

}
