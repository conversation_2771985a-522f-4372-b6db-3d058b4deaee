package com.sh.game.common.communication.msg.system.role;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.system.role.bean.RoleSettingBean;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>角色设置消息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ResRoleSettingInfoMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 8025;
	}
	
	/**
	 * 角色设置
	 */
	private List<RoleSettingBean> roleSettingBean = new ArrayList<>();

	public List<RoleSettingBean> getRoleSettingBean() {
		return roleSettingBean;
	}

	public void setRoleSettingBean(List<RoleSettingBean> roleSettingBean) {
		this.roleSettingBean = roleSettingBean;
	}

	@Override
	public boolean read(KryoInput buf) {

		int roleSettingBeanLength = readShort(buf);
		for (int roleSettingBeanI = 0; roleSettingBeanI < roleSettingBeanLength; roleSettingBeanI++) {
			if (readByte(buf) == 0) { 
				this.roleSettingBean.add(null);
			} else {
				RoleSettingBean roleSettingBean = new RoleSettingBean();
				roleSettingBean.read(buf);
				this.roleSettingBean.add(roleSettingBean);
			}
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		writeShort(buf, this.roleSettingBean.size());
		for (int roleSettingBeanI = 0; roleSettingBeanI < this.roleSettingBean.size(); roleSettingBeanI++) {
			this.writeBean(buf, this.roleSettingBean.get(roleSettingBeanI));
		}
		return true;
	}
}
