package com.sh.game.common.constant;

/**
 * 时装表常量
 */
public interface FashionConst {


    /**
     * 类型
     */
    interface AppearanceType{

        //时装
        int DRESS = 2;

        //称号
        int APPEARANCE_TYPE = 4;

        /**
         * 法阵
         */
        int CIRCLE = 7;

        /**
         *
         */
        int PIT = 12;

        /**
         * 个人坐骑
         */
        int ROLE_MOUNT = 14;

        /**
         * 个人签名
         */
        int QIANMING = 20;
    }

    /**
     * fashion表里的一个字段，用来区分不同的系统
     */
    interface IS_SYS{
        /**
         * 时装
         */
        int LATEST_FASHION = 1;
        /**
         * 个人称号系统
         */
        int PERSONAL_TITLE = 2;
    }


}
