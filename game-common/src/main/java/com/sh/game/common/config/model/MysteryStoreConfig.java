package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 神秘商店配置表
 *
 * <AUTHOR> <PERSON>o
 * @Email <EMAIL>
 * @since 2021-11-20
 **/
@Getter
@Setter
@ConfigData(file = "cfg_mystery_store")
public class MysteryStoreConfig extends AbstractConfigData {

    /**
     * id
     */
    private int id;

    /**
     * 可被抽取条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    /**
     * 权重
     */
    private int value;

    /**
     * 价格
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> price;

    /**
     * 保底次数
     */
    private int num;

    /**
     * 道具
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> itemId;

    /**
     * 当日购买次数上限
     */
    private int dayLimit;

}
