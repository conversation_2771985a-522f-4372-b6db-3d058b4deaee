package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;

import java.util.List;

/**
 * 技能配置
 *
 * <AUTHOR>
 */

@lombok.Getter
@lombok.Setter
@ConfigData(file="cfg_skills")
public class SkillConfig extends AbstractConfigData {

    /**
     * 技能id
     */
    private int id;

    /**
     * 技能名字
     */
    private String name;

    /**
     * 用于区分技能
     * @see com.sh.game.common.constant.SkillConst.SkillCls
     */
    private int cls;

    /**
     * 关联condition表中的id 技能学习条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    /**
     * 技能处理类型
     * @see com.sh.game.common.constant.SkillConst.HandlerType
     */
    private int handlerType;

    /**
     * 技能职业（战法道）1物理技能2魔法技能3道术技能
     */
    private int career;

    /**
     * 作用多个目标配1
     */
    private int aoe;

    /**
     * 最多作用目标
     */
    private int maxtarget;

    /**
     * 攻击方伤害取值 (0：取战法道自身的 (物魔道)  1：取物理攻击 2:取魔法攻击 3：取道术攻击  )
     */
    private int careeratk;

    /**
     * 是否可以升级
     */
    private int canup;

    /**
     * 目标类型
     * @see com.sh.game.common.constant.FightConst.TargetType
     */
    private int targetType;

    /**
     * 是否是被动技能
     */
    private int passive;

    /**
     * 仇恨修正值 伤害*hatep%
     */
    private int hatep;

    /**
     * 技能释放距离
     */
    private int releaseDis;

    /**
     * 是否参与公共CD
     */
    private int commonCd;

    /**
     * 释放目标类型
     *  1、方向(不自动选择目标，野蛮)
     *  2、自己坐标(抗拒火环/魔法盾/地狱雷光)
     *  3、方向与目标
     *  4、自己坐标和传入坐标
     *  5、必须有目标
     */
    private int releaseType;

    /**
     * 作用区域朝向 1、自己的朝向，2、目标的朝向
     */
    private int areaDir;

    /**
     * 作用区域范围大小
     */
    private int[] areaDis;

    /**
     * 1.点 2. 方形 3. 十字 4. 竖型 5. 横线型 6. 前方半圆 7. 野蛮冲撞 8.指定顶点的等腰三角形 9.前方N个格子 10.菱形
     */
    private int areaType;

    /**
     * 飘字
     */
    private int piaozi;

    /**
     * 是否自动开启
     */
    private int isAuto;

    /**
     * 是否吸血
     */
    private int suckper;

    /**
     * 额外消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> extracost;

    /**
     * 武器耐久消耗
     */
    private int durablecost;

    /**
     * 阻挡
     */
    private int block;

    /**
     * 技能cd组
     */
    private int cdgroup;

    /**
     * 技能对buff的控制
     */
    private int buffcontrol;

    /**
     * 是否是普攻
     */
    private int normalhurt;

    /**
     * 技能参数
     */
    private String params;

    /**
     * 是否为特殊技能
     */
    private int spSkill;

    /**
     * 是否有切割效果（0否）
     */
    private int zhansha;

    /**
     * 是否对人（0否）
     */
    private int duiren;

    /**
     * 是否是吸怪技能
     */
    private int xiguai;

    /**
     * 是否不飘字，1不飘字，默认飘字
     */
    private int bupiaozi;

    /**
     * 分身术是否克隆技能 1:可克隆技能
     */
    private int clone;
}