package com.sh.game.common.communication.msg.system.zhuansheng;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
* <p>返回新转生信息</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toClient")
public class ResStructureInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.ZhuanshengProtos.ResStructureInfo proto;

    private com.sh.game.protos.ZhuanshengProtos.ResStructureInfo.Builder builder;


    @Override
    public int getId() {
        return 208008;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ZhuanshengProtos.ResStructureInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ZhuanshengProtos.ResStructureInfo.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.ZhuanshengProtos.ResStructureInfo.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ZhuanshengProtos.ResStructureInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ZhuanshengProtos.ResStructureInfo proto) {
        this.proto = proto;
    }

}
