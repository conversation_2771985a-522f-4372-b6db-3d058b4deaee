package com.sh.game.common.communication.msg.system.secretary;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
* <p>请求秘书(仙友)升级时间完成</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toLogic")
public class ReqSecretaryLevelTimeCompleteMessage extends ProtobufMessage {

    private com.sh.game.protos.SecretaryProtos.ReqSecretaryLevelTimeComplete proto;

    private com.sh.game.protos.SecretaryProtos.ReqSecretaryLevelTimeComplete.Builder builder;


    @Override
    public int getId() {
        return 512009;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.SecretaryProtos.ReqSecretaryLevelTimeComplete.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.SecretaryProtos.ReqSecretaryLevelTimeComplete.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.SecretaryProtos.ReqSecretaryLevelTimeComplete.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.SecretaryProtos.ReqSecretaryLevelTimeComplete getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.SecretaryProtos.ReqSecretaryLevelTimeComplete proto) {
        this.proto = proto;
    }

}
