package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2018/6/12 20:48
 */
@Getter
@Setter
@Slf4j
@Notice
public class BuffAddNotice extends ProcessNotice {

    private int globalId;

    private int buffId;

    private long rid;
}
