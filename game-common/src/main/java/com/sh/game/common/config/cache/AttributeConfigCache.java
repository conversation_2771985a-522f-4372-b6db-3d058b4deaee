package com.sh.game.common.config.cache;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.AttributeConfig;
import com.sh.game.common.constant.AttributeEnum;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/7/26 14:07
 */
@Slf4j
@ConfigCache
public class AttributeConfigCache implements IConfigCache {

    private Map<AttributeEnum, AttributeConfig> map;

    @Override
    public void build() {
        map = new HashMap<>();
        for (AttributeConfig config : ConfigDataManager.getInstance().getList(AttributeConfig.class)) {
            AttributeEnum attr = AttributeEnum.valueOf(config.getId());
            if (attr == null) {
                log.error("cfg_attribute表，id 为 {} 的属性后端代码没有处理",config.getId());
                continue;
            }
            map.put(attr,config);
        }
    }

    public Map<AttributeEnum, AttributeConfig> findMap() {
        return map;
    }
}
