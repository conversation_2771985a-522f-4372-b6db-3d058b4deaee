package com.sh.game.common.communication.msg.system.task;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>限时任务信息通知</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResLimitInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.TaskProtos.ResLimitInfo proto;

    private com.sh.game.protos.TaskProtos.ResLimitInfo.Builder builder;

	
	@Override
	public int getId() {
		return 102007;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TaskProtos.ResLimitInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TaskProtos.ResLimitInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TaskProtos.ResLimitInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TaskProtos.ResLimitInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TaskProtos.ResLimitInfo proto) {
        this.proto = proto;
    }

}
