package com.sh.game.common.communication.msg.system.store;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>出售道具</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqSellItemsMessage extends ProtobufMessage {

    private com.sh.game.protos.StoreProtos.ReqSellItems proto;

    private com.sh.game.protos.StoreProtos.ReqSellItems.Builder builder;

	
	@Override
	public int getId() {
		return 16005;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.StoreProtos.ReqSellItems.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.StoreProtos.ReqSellItems.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.StoreProtos.ReqSellItems.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.StoreProtos.ReqSellItems getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.StoreProtos.ReqSellItems proto) {
        this.proto = proto;
    }

}
