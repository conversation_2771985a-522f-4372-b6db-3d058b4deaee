package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025-03-05
 */
@Getter
@Setter
@ConfigData(file="cfg_xianfa_lingdi")
public class XianFaLingDiConfig extends AbstractConfigData {

    private int id;

    private int level;

    private int nextlevel;

    /**
     * 加成万分比
     */
    private int xiuweijiacheng;

    /**
     * 上限时间 分钟
     */
    private int time;

    /**
     * 消耗
     */
    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> cost;


}
