package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.BoxConvert;
import com.sh.game.common.config.converter.BoxReplaceConvert;
import com.sh.game.common.config.entity.BoxReplacement;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 宝箱配置表
 */

@Getter
@Setter
@ConfigData(file="cfg_box", checkKey = false)
public class BoxConfig extends AbstractConfigData  {

    private int id;

    @ConfigField(converter = BoxConvert.class)
    private List<int[][]> sItems;

    private int isopen;

    @ConfigField(converter = BoxReplaceConvert.class)
    private BoxReplacement[] conditiontype;
}
