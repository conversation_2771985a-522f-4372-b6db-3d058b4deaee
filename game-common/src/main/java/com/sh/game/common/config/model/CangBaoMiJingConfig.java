package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 藏宝秘境
 */
@Getter
@Setter
@ConfigData(file = "cfg_cangbaomijing")
public class CangBaoMiJingConfig extends AbstractConfigData {
    private int id;
    private int mapID;
    private int inletNum;
    private int npcID;
    private int mapInletID;
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> inletPlace;
}
