package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/2/12 10:54
 */
@Getter
@Setter
@ConfigData(file = "cfg_minigame_cost")
public class SmallGameCostConfig extends AbstractConfigData {
    private int id;
    /** 登录重置消耗状态 */
    private int loginClear;
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;
}
