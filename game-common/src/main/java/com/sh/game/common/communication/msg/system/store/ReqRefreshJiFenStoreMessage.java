package com.sh.game.common.communication.msg.system.store;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>刷新积分商店</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqRefreshJiFenStoreMessage extends ProtobufMessage {

    private com.sh.game.protos.StoreProtos.ReqRefreshJiFenStore proto;

    private com.sh.game.protos.StoreProtos.ReqRefreshJiFenStore.Builder builder;

	
	@Override
	public int getId() {
		return 16013;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.StoreProtos.ReqRefreshJiFenStore.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.StoreProtos.ReqRefreshJiFenStore.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.StoreProtos.ReqRefreshJiFenStore.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.StoreProtos.ReqRefreshJiFenStore getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.StoreProtos.ReqRefreshJiFenStore proto) {
        this.proto = proto;
    }

}
