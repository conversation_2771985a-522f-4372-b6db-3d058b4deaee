package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ConfigData(file="cfg_paychallenge")
public class PayChallengeConfig extends AbstractConfigData {

    private int id;

    private int mapid;

    private int activityId;

    /**
     * 副本bossId
     */
    private int monster;

    /**
     * boss坐标
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> xy;

    /**
     * 充值目标
     */
    private int rechargeGoal;

    /**
     * 出生点
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> born;

}
