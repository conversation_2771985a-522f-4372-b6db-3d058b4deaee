package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 觉醒
 */
@Setter
@Getter
@ConfigData(file = "cfg_juexing", keys = {"stdMode#level"})
public class EquipJueXingConfig extends AbstractConfigData {

    private int id;

    /**
     * 装备部位
     */
    private int stdMode;

    private int level;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] att_score;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] special_att_score;

    /**
     * 洗练消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> need_item;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;

    /**
     * 觉醒公告
     */
    private int announce;

    private int station;
}