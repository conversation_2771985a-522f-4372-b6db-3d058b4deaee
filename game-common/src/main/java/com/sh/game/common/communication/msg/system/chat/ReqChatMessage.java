package com.sh.game.common.communication.msg.system.chat;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求聊天</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqChatMessage extends ProtobufMessage {

    private com.sh.game.protos.ChatProtos.ReqChat proto;

    private com.sh.game.protos.ChatProtos.ReqChat.Builder builder;

	
	@Override
	public int getId() {
		return 6001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ChatProtos.ReqChat.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ChatProtos.ReqChat.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ChatProtos.ReqChat.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ChatProtos.ReqChat getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ChatProtos.ReqChat proto) {
        this.proto = proto;
    }

}
