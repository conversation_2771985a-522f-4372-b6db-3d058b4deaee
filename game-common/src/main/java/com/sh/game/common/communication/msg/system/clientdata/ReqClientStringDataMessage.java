package com.sh.game.common.communication.msg.system.clientdata;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>请求客户端存储字符串数据</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqClientStringDataMessage extends ProtobufMessage {

    private com.sh.game.protos.ClientDataProtos.ReqClientStringDataMessage proto;

    private com.sh.game.protos.ClientDataProtos.ReqClientStringDataMessage.Builder builder;

	
	@Override
	public int getId() {
		return 391005;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ClientDataProtos.ReqClientStringDataMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ClientDataProtos.ReqClientStringDataMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ClientDataProtos.ReqClientStringDataMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ClientDataProtos.ReqClientStringDataMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ClientDataProtos.ReqClientStringDataMessage proto) {
        this.proto = proto;
    }

}
