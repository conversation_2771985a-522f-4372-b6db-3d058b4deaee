package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/19 21:25
 */
@Getter
@Setter
@ConfigData(file = "cfg_baozangzhidi_cost")
public class SpecialWaBaoCostConfig extends AbstractConfigData {

    private int id;

    private int actType;

    private int rewardGroup;

    private int times;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    private int limitType;

    private int limit;

    private int mapid;

    private int bonusTimes;

    private int fashion;

    private int actId;
}
