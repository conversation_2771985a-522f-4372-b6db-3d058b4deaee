package com.sh.game.common.communication.msg.system.role;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;

import com.sh.game.common.communication.msg.system.role.bean.InnerPowerInfoBean;

/**
 * <p>内功信息变化</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ResInnerPowerInfoChangeMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 8029;
	}
	
	/**
	 * 变化内功信息
	 */
	private InnerPowerInfoBean changeBean;

	public InnerPowerInfoBean getChangeBean() {
		return changeBean;
	}

	public void setChangeBean(InnerPowerInfoBean changeBean) {
		this.changeBean = changeBean;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		if (readByte(buf) != 0) {
			InnerPowerInfoBean innerPowerInfoBean = new InnerPowerInfoBean();
			innerPowerInfoBean.read(buf);
			this.changeBean = innerPowerInfoBean;
		}
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeBean(buf, changeBean);
		return true;
	}
}
