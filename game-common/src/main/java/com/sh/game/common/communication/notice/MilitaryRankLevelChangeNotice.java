package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 军衔等级
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-05-10
 **/
@Getter
@Setter
@Notice
@AllArgsConstructor
@NoArgsConstructor
public class MilitaryRankLevelChangeNotice extends ProcessNotice {
    private long roleId;
    private int militaryRankCfgId;
}
