package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;

/**
 * description: 黑洞技能配置表
 * date: 2024/6/25
 * author: chenBin
 */
@Getter
@Setter
@ConfigData(file = "cfg_blackhole_skill")
public class BlackHoleSkillConfig extends AbstractConfigData {
    private int id;

    private int type;

    private String name;

    private int level;

    private int effect;

    private int cost;
}
