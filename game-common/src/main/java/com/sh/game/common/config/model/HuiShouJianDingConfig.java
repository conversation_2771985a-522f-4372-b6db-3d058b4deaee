package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/30 20:44
 */
@Getter
@Setter
@ConfigData(file = "cfg_huishou_jianding")
public class HuiShouJianDingConfig extends AbstractConfigData {

    private int id;

    private int itemid;

    private int count;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> morerecycle;
}
