package com.sh.game.common.communication.msg.system.storygd;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
* <p>请求已触发剧情list</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toLogic")
public class ReqStoryListMessage extends ProtobufMessage {

    private com.sh.game.protos.StoryGdProtos.ReqStoryListMessage proto;

    private com.sh.game.protos.StoryGdProtos.ReqStoryListMessage.Builder builder;


    @Override
    public int getId() {
        return 503005;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.StoryGdProtos.ReqStoryListMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.StoryGdProtos.ReqStoryListMessage.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.StoryGdProtos.ReqStoryListMessage.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.StoryGdProtos.ReqStoryListMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.StoryGdProtos.ReqStoryListMessage proto) {
        this.proto = proto;
    }

}
