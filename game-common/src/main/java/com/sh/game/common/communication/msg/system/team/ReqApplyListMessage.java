package com.sh.game.common.communication.msg.system.team;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>队伍申请列表</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqApplyListMessage extends ProtobufMessage {

    private com.sh.game.protos.TeamProtos.ReqApplyList proto;

    private com.sh.game.protos.TeamProtos.ReqApplyList.Builder builder;

	
	@Override
	public int getId() {
		return 101012;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TeamProtos.ReqApplyList.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TeamProtos.ReqApplyList.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TeamProtos.ReqApplyList.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TeamProtos.ReqApplyList getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TeamProtos.ReqApplyList proto) {
        this.proto = proto;
    }

}
