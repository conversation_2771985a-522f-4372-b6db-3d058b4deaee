package com.sh.game.common.util.condition.impl;

import com.sh.game.common.constant.TaskConst;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

/**
 * 支线任务
 *
 * <AUTHOR>
 * @date 2022/6/15 11:44
 */
public class BranchTaskConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params.length > 2) {
            return avatar.checkBranchTask(params[1], params[2]);
        } else {
            return avatar.checkBranchTask(params[1], TaskConst.State.HAS_SUBMIT);
        }
    }
}
