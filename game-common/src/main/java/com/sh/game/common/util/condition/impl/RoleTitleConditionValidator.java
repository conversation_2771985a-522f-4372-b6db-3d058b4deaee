package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.common.util.condition.IConditionValidator;

public class RoleTitleConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) return false;
        if (params.length <= 1) return false;
        int level = avatar.getTitleLevel();
        if (params.length > 2 && level < params[2]) return false;
        if (params.length > 3 && level > params[3]) return false;

        return true;
    }
}
