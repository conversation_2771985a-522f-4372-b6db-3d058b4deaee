package com.sh.game.common.sync;


import net.sf.cglib.proxy.Enhancer;

import java.util.HashMap;
import java.util.Map;

public class SyncEnhancer {

    private boolean created = false;

    public Enhancer enhancer;

    public Map<String, SyncField> interceptorFields = new HashMap<>();

    /**
     * 创建entity
     * cglib有bug，所以在首次创建的时候同步创建，规避该bug
     *
     * @param <T>
     * @return
     */
    public <T extends SyncData> T create() {
        if (!created) {
            synchronized (this) {
                T entity = (T) enhancer.create();
                created = true;
                return entity;
            }
        }

        return (T) enhancer.create();
    }
}
