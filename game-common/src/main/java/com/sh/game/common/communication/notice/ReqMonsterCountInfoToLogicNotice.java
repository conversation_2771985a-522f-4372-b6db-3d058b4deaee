package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2022-02-15
 **/
@Getter
@Setter
@Notice
@NoArgsConstructor
@AllArgsConstructor
public class ReqMonsterCountInfoToLogicNotice extends ProcessNotice {
    long roleId;
    int monsterCid;
}
