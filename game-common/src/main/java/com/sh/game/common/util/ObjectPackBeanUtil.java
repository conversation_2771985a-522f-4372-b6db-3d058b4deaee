package com.sh.game.common.util;

import com.sh.game.common.communication.notice.entity.RoleSimpleData;
import com.sh.game.protos.AbcProtos;

public class ObjectPackBeanUtil {

    public static RoleSimpleData packRoleSimpleDataByBean(AbcProtos.RoleSimpleBean bean) {
        RoleSimpleData roleSimpleData = new RoleSimpleData();
        roleSimpleData.setRid(bean.getRid());
        roleSimpleData.setName(bean.getName());
        roleSimpleData.setLevel(bean.getLevel());
        roleSimpleData.setCareer(bean.getCareer());
        roleSimpleData.setSex(bean.getSex());
        roleSimpleData.setHair(bean.getHair());
        roleSimpleData.setUnionName(bean.getUnionName());
        for (AbcProtos.CommonSlotBean fashion : bean.getFashionsList()) {
            roleSimpleData.getFashions().put(fashion.getIndex(), fashion.getId());
        }
        for (AbcProtos.CommonEquipBean equip : bean.getEquipsList()) {
            // TODO 装备过滤
            AbcProtos.CommonItemBean item = equip.getItem();
            if (item == null) {
                continue;
            }
            roleSimpleData.getEquips().put(equip.getIndex(), item.getItemId());
        }
        return roleSimpleData;
    }

    public static AbcProtos.RoleSimpleBean packRoleSimpleBeanByData(RoleSimpleData data) {
        AbcProtos.RoleSimpleBean.Builder bean = AbcProtos.RoleSimpleBean.newBuilder();
        bean.setRid(data.getRid());
        bean.setLevel(data.getLevel());
        bean.setCareer(data.getCareer());
        bean.setSex(data.getSex());
        bean.setUnionName(data.getUnionName());
        bean.setHair(data.getHair());

        data.getFashions().forEach((index, fashionId) -> {
            AbcProtos.CommonSlotBean.Builder slotBean = AbcProtos.CommonSlotBean.newBuilder();
            slotBean.setIndex(index);
            slotBean.setId(fashionId);
            bean.addFashions(slotBean);
        });

        data.getEquips().forEach((index, cid) -> {
            AbcProtos.CommonEquipBean.Builder equipBean = AbcProtos.CommonEquipBean.newBuilder();
            equipBean.setIndex(index);
            AbcProtos.CommonItemBean.Builder itemBean = AbcProtos.CommonItemBean.newBuilder();
            itemBean.setItemId(cid);
            equipBean.setItem(itemBean);
            bean.addEquips(equipBean);
        });
        return bean.build();
    }
}
