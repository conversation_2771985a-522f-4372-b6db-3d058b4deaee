package com.sh.game.common.communication.msg.system.tuiche;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
* <p>请求推车游戏开始</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toLogic")
public class ReqTuiCheGameStartMessage extends ProtobufMessage {

    private com.sh.game.protos.TuiCheProtos.ReqTuiCheGameStartMessage proto;

    private com.sh.game.protos.TuiCheProtos.ReqTuiCheGameStartMessage.Builder builder;


    @Override
    public int getId() {
        return 507001;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TuiCheProtos.ReqTuiCheGameStartMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TuiCheProtos.ReqTuiCheGameStartMessage.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.TuiCheProtos.ReqTuiCheGameStartMessage.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TuiCheProtos.ReqTuiCheGameStartMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TuiCheProtos.ReqTuiCheGameStartMessage proto) {
        this.proto = proto;
    }

}
