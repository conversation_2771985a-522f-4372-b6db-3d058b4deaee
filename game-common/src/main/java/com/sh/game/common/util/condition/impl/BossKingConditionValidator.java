package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

public class BossKingConditionValidator extends IConditionValidatorDefault {
    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params.length < 2) {
            return false;
        }

        int targetCount = params[1];
        int count = avatar.findBossKingId();
        return count >= targetCount;
    }
}
