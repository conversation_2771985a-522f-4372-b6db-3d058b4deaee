package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/6/15
 * 武道
 */
@Getter
@Setter
@ConfigData(file = "cfg_wudaozengyi", keys = "zytype#level")
public class WuDaoConfig extends AbstractConfigData {
    private int id;
    private int zytype;
    private int level;
    private String name;
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attribute;
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 武道增益公告
     */
    private int announce;

}
