package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/18 10:45
 */
public class LunWuRankConditionValidator extends IConditionValidatorDefault {
    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params.length < 2) {
            return false;
        }
        return avatar.getLunWuRank() >= params[1];
    }
}
