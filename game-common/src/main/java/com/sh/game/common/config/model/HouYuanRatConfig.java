package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/7 9:57
 */
@Getter
@Setter
@ConfigData(file = "cfg_houyuan_rat")
public class HouYuanRatConfig extends AbstractConfigData {

    private int id;

    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] energy;

    private int speed;
}
