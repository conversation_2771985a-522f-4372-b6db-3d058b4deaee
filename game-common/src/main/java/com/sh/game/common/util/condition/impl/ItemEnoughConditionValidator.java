package com.sh.game.common.util.condition.impl;

import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.common.util.condition.IConditionValidator;

public class ItemEnoughConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }

        if (params.length >= 3 && avatar.getItemCount(params[1], BackpackConst.Browse.BACKPACK_AND_EQUIP) < params[2]) {
            return false;
        }

        return true;
    }
}
