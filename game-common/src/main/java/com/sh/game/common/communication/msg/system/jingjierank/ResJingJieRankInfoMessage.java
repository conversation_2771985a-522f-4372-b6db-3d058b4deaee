package com.sh.game.common.communication.msg.system.jingjierank;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
* <p>返回境界排行榜信息</p>
* <p>Created by MessageUtil</p>
* <p>该类是自动生成的，不允许手动修改</p>
*/
@RPC("toClient")
public class ResJingJieRankInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.JingJieRankProtos.ResJingJieRankInfo proto;

    private com.sh.game.protos.JingJieRankProtos.ResJingJieRankInfo.Builder builder;


    @Override
    public int getId() {
        return 532002;
    }

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.JingJieRankProtos.ResJingJieRankInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.JingJieRankProtos.ResJingJieRankInfo.Builder newBuilder() {
        if (builder == null) {
            builder = com.sh.game.protos.JingJieRankProtos.ResJingJieRankInfo.newBuilder();
        }
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.JingJieRankProtos.ResJingJieRankInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.JingJieRankProtos.ResJingJieRankInfo proto) {
        this.proto = proto;
    }

}
