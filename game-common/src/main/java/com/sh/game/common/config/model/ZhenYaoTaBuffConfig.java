package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 阵营塔配置
 * <AUTHOR>
 * @date 2023/12/14
 */
@Getter
@Setter
@ConfigData(file="cfg_theurgybuff")
public class ZhenYaoTaBuffConfig extends AbstractConfigData {
    private int id;
    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer,Long>[] attribute;
}
