package com.sh.game.common.communication.msg.system.fangPian;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求领取玩家防骗奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqFangPianRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.FangPianProtos.ReqFangPianReward proto;

    private com.sh.game.protos.FangPianProtos.ReqFangPianReward.Builder builder;

	
	@Override
	public int getId() {
		return 350003;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.FangPianProtos.ReqFangPianReward.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.FangPianProtos.ReqFangPianReward.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.FangPianProtos.ReqFangPianReward.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.FangPianProtos.ReqFangPianReward getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.FangPianProtos.ReqFangPianReward proto) {
        this.proto = proto;
    }

}
