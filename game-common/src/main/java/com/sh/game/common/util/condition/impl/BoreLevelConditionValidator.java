package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.common.util.condition.IConditionValidator;

public class BoreLevelConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }

        int level = avatar.getBoreMaxLevel();
        int len = params.length;
        if (len > 1 && level < params[1]) return false;
        if (len > 2 && level > params[2]) return false;

        return true;
    }
}
