package com.sh.game.common.entity.backpack.committype.impl;


import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.notice.ChongBangUpdateNotice;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackCommitController;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.committype.AbstractCommitHandler;
import com.sh.game.common.entity.backpack.item.CoinGain;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.util.ItemCoinUtil;
import com.sh.game.protos.BackPackProtos;

import java.math.BigInteger;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class Coin<PERSON>ommitHandler extends AbstractCommitHandler {
    private static final int BIG_NUM_CARRY_LIMIT = 10000;
    @Override
    public void commit(IAvatar avatar, LogAction action, boolean tips, int flag, BackpackStash backpackStash, BackPackProtos.ResBackpackChanged.Builder backPackChange, BackpackCommitController controller) {
        Map<Integer, Long> coinStashMap = backpackStash.getCoinStashMap();
        Backpack backpack = avatar.getBackpack();
        Map<Integer, Long> coinChanges = new HashMap<>();
        Map<Integer, Long> oldValues = new HashMap<>();
        CoinGain gain = new CoinGain(action);
        backpackStash.setCoinGain(gain);

        BigInteger oldCoin = ItemCoinUtil.getCoinBigInteger(backpack.getCoin());
        Map<Integer, Long> changeMap = new TreeMap<>();
        for (Map.Entry<Integer, Long> entry : coinStashMap.entrySet()) {
            int id = entry.getKey();
            long ov = backpack.getCoin().getOrDefault(id, 0L);
            long nv = entry.getValue();
            long ac = nv - ov;
            if (ac > 0) {
                switch (id) {
                    case BagConst.ItemId.ROLE_EXP:
                    case BagConst.ItemId.HERO_EXP:
                        gain.getReward().merge(id, ac, Long::sum);
                        break;
                }
            }
            coinChanges.put(id, ac);
            oldValues.put(id, ov);
            backpack.getCoin().put(id, nv);

            changeMap.put(id, nv);
            //大数进位计算
            ItemConfig coinConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, id);
            if(nv >= BIG_NUM_CARRY_LIMIT && coinConfig.getCarryCfgId() > 0) {
                refreshCoinBigNumCarry(backpack, coinStashMap, id, changeMap);
            }
        }

        //货币-冲榜
        BigInteger newCoin = ItemCoinUtil.getCoinBigInteger(backpack.getCoin());
        if (newCoin.compareTo(oldCoin) < 0) {
            ChongBangUpdateNotice notice = new ChongBangUpdateNotice();
            notice.setRid(avatar.getId());
            notice.setValue(oldCoin.subtract(newCoin).toString());
            avatar.sendNotice(ProcessorId.SERVER_PLAYER, notice);
        }

        //推送通知
        changeMap.forEach((itemId,count)-> {
            BackPackProtos.BackpackCoinBean.Builder bean = BackPackProtos.BackpackCoinBean.newBuilder();
            bean.setItemId(itemId);
            bean.setCount(count);
            backPackChange.addChangedCoins(bean);
        });

        backpackStash.setCoinChanges(coinChanges);
        backpackStash.setOldValues(oldValues);
    }

    /** 大数进位计算 */
    private void refreshCoinBigNumCarry(Backpack backpack, Map<Integer, Long> coinStashMap, int coinCfgId, Map<Integer, Long> changeMap){
        int bigNumCfgId = coinCfgId;
        while (bigNumCfgId > 0) {
            ItemConfig tempCoinConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, bigNumCfgId);
            long oldValue = backpack.getCoin().getOrDefault(bigNumCfgId, 0L);
            int carryCfgId = tempCoinConfig.getCarryCfgId();
            if(oldValue < BIG_NUM_CARRY_LIMIT || carryCfgId <= 0) {
                break;
            }

            long value = oldValue / BIG_NUM_CARRY_LIMIT;
            long newValue = oldValue - (value * BIG_NUM_CARRY_LIMIT);
            backpack.getCoin().put(bigNumCfgId, newValue);
            changeMap.put(bigNumCfgId, newValue);

            coinStashMap.merge(carryCfgId, value, Long::sum);
            long carryOv = backpack.getCoin().getOrDefault(carryCfgId, 0L);
            long carryNv = carryOv + value;
            backpack.getCoin().put(carryCfgId, carryNv);
            changeMap.put(carryCfgId, carryNv);
            bigNumCfgId = carryCfgId;
        }
    }

    @Override
    public Boolean check(IAvatar avatar, LogAction action, boolean tips, int flag, BackpackStash backpackStash, BackPackProtos.ResBackpackChanged.Builder backPackChange, BackpackCommitController controller) {
        Map<Integer, Long> coinStashMap = backpackStash.getCoinStashMap();
        Map<Integer, Long> coinRequired = new HashMap<>();
        Backpack backpack = avatar.getBackpack();
        //当消耗货币的时候做处理 如果有替代道具把上面扣除掉自己不够的部分用替代道具扣
        for (Map.Entry<Integer, Long> entry : coinStashMap.entrySet()) {
            if (entry.getValue() < 0) {
                coinRequired.put(entry.getKey(), -entry.getValue());
                coinStashMap.replace(entry.getKey(), 0L);
            }
        }

        for (Map.Entry<Integer, Long> entry : coinRequired.entrySet()) {
            int coinId = entry.getKey();
            long required = entry.getValue();

            ItemConfig coinConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, coinId);
            if (coinConfig != null && coinConfig.getBind() > 0) {
                int replaceItem = coinConfig.getBind();
                long available = coinStashMap.getOrDefault(replaceItem, backpack.getCoin().getOrDefault(replaceItem, 0L));
                long count = Math.min(available, required);
                required -= count;
                coinStashMap.put(replaceItem, available - count);
            }

            if (required > 0) {
                if (coinConfig != null) {
                    //大数退位计算
                    boolean bigNumCalSucc = calBigNumBack(backpack, coinStashMap, coinConfig, required);
                    if(!bigNumCalSucc) {
                        if (tips) {
                            avatar.sendErrorTips(String.format("需要%s*%s", coinConfig.getName(), required));
                        }
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /** 大数退位计算 */
    private static boolean calBigNumBack(Backpack backpack, Map<Integer, Long> coinStashMap, ItemConfig coinConfig, long required) {
        Set<Integer> temp = new HashSet<>();
        int bigNumCfgId = coinConfig.getCarryCfgId();
        while (bigNumCfgId > 0) {
            long available = coinStashMap.getOrDefault(bigNumCfgId, 0L);
            if (available <= 0 && !temp.contains(bigNumCfgId)) {
                available = backpack.getCoin().getOrDefault(bigNumCfgId, 0L);
                temp.add(bigNumCfgId);
            }
            if (available > 0) {
                coinStashMap.put(bigNumCfgId, available - 1);
                ItemConfig tempConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, bigNumCfgId);
                while (tempConfig.getBackCfgId() != coinConfig.getId()) {
                    // TODO coinStashMap里可能也存在有tempConfig.getBackCfgId()
                    long oldCount = coinStashMap.getOrDefault(tempConfig.getBackCfgId(), 0L);
                    long newValue = oldCount + (BIG_NUM_CARRY_LIMIT - 1);
                    coinStashMap.put(tempConfig.getBackCfgId(), newValue);
                    tempConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, tempConfig.getBackCfgId());
                }

                if (required > BIG_NUM_CARRY_LIMIT) {
                    required -= BIG_NUM_CARRY_LIMIT;
                } else {
                    coinStashMap.put(coinConfig.getId(), BIG_NUM_CARRY_LIMIT - required);
                    required = 0;
                    return true;
                }
            } else {
                ItemConfig tempConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, bigNumCfgId);
                bigNumCfgId = tempConfig.getCarryCfgId();
            }
        }

        return false;
    }

    /** 判断货币大数当前背包是否满足 */
    public static boolean checkCoinBackpackBigNum(Backpack backpack, int itemCfgId, long count) {
        long curCount = backpack.getCoin().getOrDefault(itemCfgId, 0L);
        if(curCount >= count) {
            return true;
        }
        Map<Integer, Long> coinStashMap = new HashMap<>();
        ItemConfig targetItemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemCfgId);
        return calBigNumBack(backpack, coinStashMap, targetItemConfig, count - curCount);
    }

    /** 大数进位计算 */
    public static void refreshCoinBigNumCarry(Map<Integer, Long> bigNumItems){
        ConcurrentHashMap<Integer, Long> tempMap = new ConcurrentHashMap<>(bigNumItems);
        for (Map.Entry<Integer, Long> entry : tempMap.entrySet()) {
            int bigNumCfgId = entry.getKey();
            while (bigNumCfgId > 0) {
                long oldValue = tempMap.getOrDefault(bigNumCfgId, 0L);
                if (oldValue < BIG_NUM_CARRY_LIMIT ) {
                    break;
                }
                ItemConfig tempCoinConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, bigNumCfgId);
                int carryCfgId = tempCoinConfig.getCarryCfgId();
                if (carryCfgId <= 0) {
                    break;
                }

                long value = oldValue / BIG_NUM_CARRY_LIMIT;
                long newValue = oldValue - (value * BIG_NUM_CARRY_LIMIT);
                tempMap.put(bigNumCfgId, newValue);

                long carryOv = tempMap.getOrDefault(carryCfgId, 0L);
                long carryNv = carryOv + value;
                tempMap.put(carryCfgId, carryNv);
                bigNumCfgId = carryCfgId;
            }
        }

        //扣减 负数
        for (Map.Entry<Integer, Long> entry : tempMap.entrySet()) {
            int bigNumCfgId = entry.getKey();
            while (bigNumCfgId > 0) {
                ItemConfig tempCoinConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, bigNumCfgId);
                long oldValue = tempMap.getOrDefault(bigNumCfgId, 0L);
                int carryCfgId = tempCoinConfig.getCarryCfgId();
                if (oldValue >= 0 || carryCfgId <= 0) {
                    break;
                }
                oldValue = Math.abs(oldValue);
                long value = oldValue / BIG_NUM_CARRY_LIMIT;
                long newValue = oldValue - (value * BIG_NUM_CARRY_LIMIT);
                tempMap.put(bigNumCfgId, Math.negateExact(newValue));

                long carryOv = tempMap.getOrDefault(carryCfgId, 0L);
                long carryNv = carryOv + Math.negateExact(value);
                tempMap.put(carryCfgId, carryNv);
                bigNumCfgId = carryCfgId;
            }
        }
        tempMap.entrySet().removeIf(entry-> entry.getValue() == 0);

        bigNumItems.clear();
        bigNumItems.putAll(tempMap);
    }

    /** 判断货币大数当前map是否满足 */
    public static boolean checkCoinMapBigNum(Map<Integer, Long> coinStashMap, int itemCfgId, long count){
        long required = count;
        required -= coinStashMap.getOrDefault(itemCfgId, 0L);
        ItemConfig targetItemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemCfgId);
        int bigNumCfgId = targetItemConfig.getCarryCfgId();
        while (bigNumCfgId > 0) {
            long available = coinStashMap.getOrDefault(bigNumCfgId, 0L);
            if (available > 0) {
                coinStashMap.put(bigNumCfgId, available - 1);
                ItemConfig tempConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, bigNumCfgId);
                while (tempConfig.getBackCfgId() != targetItemConfig.getId()) {
                    // TODO coinStashMap里可能也存在有tempConfig.getBackCfgId()
                    long oldCount = coinStashMap.getOrDefault(tempConfig.getBackCfgId(), 0L);
                    long newValue = oldCount + (BIG_NUM_CARRY_LIMIT - 1);
                    coinStashMap.put(tempConfig.getBackCfgId(), newValue);
                    tempConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, tempConfig.getBackCfgId());
                }

                if (required > BIG_NUM_CARRY_LIMIT) {
                    required -= BIG_NUM_CARRY_LIMIT;
                } else {
                    coinStashMap.put(targetItemConfig.getId(), BIG_NUM_CARRY_LIMIT - required);
                    required = 0;
                    return true;
                }
            } else {
                ItemConfig tempConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, bigNumCfgId);
                bigNumCfgId = tempConfig.getCarryCfgId();
            }
        }
        return false;
    }
}
