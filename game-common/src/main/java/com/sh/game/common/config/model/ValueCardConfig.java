package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 10:14
 */
@Getter
@Setter
@ConfigData(file = "cfg_valuecard")
public class ValueCardConfig extends AbstractConfigData {

    private int id;

    private int rechargeId;

    /**
     * 周卡1 月卡2 权益卡3
     */
    private int type;

    private int day;

    /**
     * 对应档位累充奖励
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> rate;

    private int announce;

    private int mail;

    private int actId;
}
