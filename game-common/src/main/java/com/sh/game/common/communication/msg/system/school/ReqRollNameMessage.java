package com.sh.game.common.communication.msg.system.school;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>请求随机学生名字</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqRollNameMessage extends ProtobufMessage {

    private com.sh.game.protos.SchoolProtos.ReqRollNameMessage proto;

    private com.sh.game.protos.SchoolProtos.ReqRollNameMessage.Builder builder;

	
	@Override
	public int getId() {
		return 419002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.SchoolProtos.ReqRollNameMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.SchoolProtos.ReqRollNameMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.SchoolProtos.ReqRollNameMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.SchoolProtos.ReqRollNameMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.SchoolProtos.ReqRollNameMessage proto) {
        this.proto = proto;
    }

}
