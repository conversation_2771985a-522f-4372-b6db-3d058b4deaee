package com.sh.game.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 活动类型常量
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/3/2.
 */
public interface ActivityConst {

    int TOU_ZI_LI_CAI = 1077;

    int XIA_LV_RUSH_LEVEL = 1081;

    int EQUIP_COLLECT = 1083;

    /**
     * 隐形斗篷
     */
    int INVISIBILITY_CLOAK = 1082;
    /**
     * 成长基金活动
     */
    int CHENG_ZHANG_JIJIN = 1085;

    /**
     * 圣火令-特惠礼包
     */
    int SHENG_HUO_LING_GIFT = 1091;

    /**
     * 中原寻宝
     */
    int CENTRAL_PLAINS_TREASURED_PLACE = 1092;

    /**
     * 秘宝令-礼包
     */
    int MI_MAO_LING_GIFT = 1093;

    /**
     * 累充回馈
     */
    int LEICHONGHUIKUI_ACT = 1094;

    /**
     * 合服特惠礼包-圣火令
     */
    int SHENG_HUO_LING_MERGE_GIFT = 1095;

    /**
     * 节日连充
     */
    int JIE_RI_LIAN_CHONG = 1096;

    /**
     * 合服特殊登录豪礼
     */
    int MERGE_SPECIAL_LOGIN_REWARD = 1199;

    /**
     * 登录大礼（节日登录领奖
     */
    int LOGIN_DALI = 2001;

    /**
     * 论剑基金
     */
    int LUNJIAN_JIJIN = 2002;


    /**
     * 秘宝豪礼
     */
    int THINGS_TREASURED = 2003;

    /**
     * 特惠礼包
     */
    int TEHUI_GIFT = 2005;

    /**
     * 大侠活动
     */
    int ACTIVITY_DAXIA = 2008;

    /**
     * 额外在线豪礼
     */
    int EXTRA_ONLINE_GIFT = 2009;

    /**
     * 每日特惠礼包
     */
    int MEIRI_TEHUI_GIFT = 2010;

    /**
     * 七日累充
     */
    int ACTIVITY_QIRILEICHONG2 = 3000;

    /**
     * 直购礼包
     */
    int DIRECT_PURCHASE_GIFT = 4002;

    /**
     * 全服夺宝
     */
    int QUAN_FU_DUO_BAO = 4005;

    /**
     * 天选之人
     */
    int TIAN_XUAN_ZHI_REN = 4006;

    /**
     * 周末限时累充
     */
    int WEEK_LIMITED_TIME_RECHARGE = 4003;

    /**
     * 周末特惠礼包
     */
    int WEEKEND_TEHUI_GIFT = 4007;

    /**
     * 赞助狂欢(周末限时累充)
     */
    int WEEKEND_LIMITED_TIME_RECHARGE = 4008;

    /**
     * 进阶之路
     */
    int JINGJIEZHILU_ACT = 2011;

    /**
     * 特殊登录豪礼
     */
    int SPECIAL_LOGIN_REWARD = 5004;

    /**
     * 节日特惠礼包
     */
    int FESTIVAL_GIFT_PACK = 5010;

    /**
     * 充值挑战
     */
    int RECHARGE_CHALLENGE = 2505;

    /**
     * 双倍充值礼包
     */
    int SHUANGBEI_GIFTPACK = 5011;

    /**
     * 积分转盘-福利
     */
    int WELFARE_WHEEL = 1112;

    /**
     * 积分转盘-龙脉
     */
    int DRAGON_VEIN_WHEEL = 4009;

    /**
     * 投资豪礼
     */
    int TOUZI_HAOLI = 1210;

    /**
     * 灵符转盘
     */
    int LINGFU_ZHUANPAN = 4011;

    /**
     * 周末大礼
     */
    int ZHOUMO_LOGIN = 4012;

    /**
     * 多倍充值直购礼包
     */
    int DUOBEI_RECHARGE_GIFT = 1211;

    /**
     * 回馈礼包
     */
    int FEEDBACK_GIFT_PACK = 5016;

    /**
     * 新幸运转盘
     */
    int SECOND_LUCK_WHEEL = 5017;

    /**
     * 幸运转盘
     */
    int FIRST_LUCK_WHEEL = 1078;

    /**
     * 每日累充
     */
    int PURCHASE_DAILY = 1115;

    /**
     * 节日挖宝
     */
    int FESTIVAL_WA_BAO = 5013;

    /**
     * 复刻节日挖宝
     */
    int FESTIVAL_WA_BAO_COPY = 9145013;

    /**
     * 节日-购物车
     */
    int FESTIVAL_SHOPPING_CART = 5018;

    /**
     * 免费神装
     */
    int FREE_EQUIP = 1116;

    /**
     * 节日投资
     */
    int JIERI_TOUZI = 7003;

    /**
     * 天使投资
     */
    int TIANSHI_TOUZI = 5019;

    /**
     * 中秋登录
     */
    int ZHONGQIU_LOGIN = 7001;

    int ZHONG_QIU_LIAN_CHONG = 7004;

    /**
     * 中秋boss
     */
    int ZHONG_QIU_BOSS = 7005;

    /**
     * 节日神秘商店
     */
    int JIERI_MYSTERYSTORE = 5020;

    /**
     * 中秋礼包
     */
    int ZHONGQIU_GIFT = 7002;

    /**
     * 免费神装2
     */
    int FREE_EQUIP_COPY = 1117;

    /**
     * 阵营-登录豪礼
     */
    int ZHENYING_SPECIAL_LOGIN_REWARD = 8001;

    /**
     * 阵营投资
     */
    int ZHENYING_TOUZI = 8003;

    /**
     * 阵营礼包
     */
    int ZHENYING_GIFT = 8002;

    /**
     * 天堂累充
     */
    int HEAVEN = 1118;

    /**
     * 跨服投资
     */
    int KUAFU_TOUZI = 2012;

    /**
     * 抽奖豪礼
     */
    int CHOUJIANG_HAOLI = 8004;

    /**
     * 时装大卖场
     */
    int SHI_ZHUANG_DA_MAI_CHANG = 8980;

    /**
     * 阵营转盘
     */
    int ZHENYING_ZHUANPAN = 8007;

    /**
     * 阵营任务
     */
    int ZHEN_YING_TASK = 8006;

    /**
     * 国庆登录
     */
    int GUOQING_LOGIN = 10001;

    /**
     * 国庆-在线豪礼
     */
    int NATIONAL_DAY_ONLINE_GIFT = 10002;

    /**
     * 国庆特惠礼包
     */
    int GUOQING_TEHUI_GIFTPACK = 10003;

    /**
     * 国庆投资
     */
    int GUOQING_TOUZI = 10005;

    /**
     * 国庆多倍充值
     */
    int GUOQING_DUOBEI_CHONGZHI = 10006;

    /**
     * 连充豪礼
     */
    int LIAN_CHONG_HAO_LI = 10007;

    /**
     * 超值双卡
     */
    int VALUE_CARD = 1120;

    /**
     * 国庆-假日夺宝
     */
    int NATIONAL_DAY_TREASURED = 10009;

    /**
     * 百万灵符
     */
    int MILLION = 1119;

    /**
     * 国庆新新幸运转盘
     */
    int THIRD_LUCK_WHEEL = 10008;

    /**
     * 国庆boss
     */
    int GUOQING_BOSS = 10004;

    /**
     * 国庆金卷狂欢
     */
    int GUOQING_KUANGHUAN = 10010;

    /**
     * 多倍充值礼包
     */
    int DUOBEI_CHONGZHI = 12008;

    /**
     * 帝王-登录豪礼
     */
    int DIWANG_SPECIAL_LOGIN_REWARD = 12001;

    /**
     * 帝王特惠
     */
    int DIWANG_TEHUI = 12002;

    /**
     * 帝王投资
     */
    int DIWANG_TOUZI = 12003;

    /**
     * 帝王砸金蛋
     */
    int JIWANG_ZAJINDAN = 12007;

    /**
     * 幸运抽奖
     */
    int XING_YUN_CHOU_JIANG = 12004;

    /**
     * 命运之轮
     */
    int MING_YUN_ZHI_LUN = 13007;

    /**
     * 疯狂星期五-boss盛宴
     */
    int BOSS_FEAST_CRAZY_FRIDAY = 13008;

    /**
     * 帝王任务
     */
    int DI_WANG_TASK = 12006;

    /**
     * 疯狂星期五-灵符投资
     */
    int LINGFU_TOUZI_FRIDAY = 13002;

    /**
     * 疯狂星期五-龙装投资
     */
    int LONGZHUANG_TOUZI_FRIDAY = 13003;

    /**
     * 疯狂星期五-龙魂投资
     */
    int LONGHUN_TOUZI_FRIDAY = 13004;

    /**
     * 疯狂星期五-特戒投资
     */
    int TEJIE_TOUZI_FRIDAY = 13005;

    /**
     * 自选礼包
     */
    int ZIXUAN_GIFT_FRIDAY = 13001;

    /**
     * 永恒-登录豪礼
     */
    int YONGHENG_SPECIAL_LOGIN_REWARD = 14001;

    /**
     * 永恒抽奖
     */
    int YONG_HEN_CHOU_JIANG = 14004;

    /**
     * 永恒任务
     */
    int YONG_HEN_TASK = 14006;

    /**
     * 帝王特惠
     */
    int DIWANG_TEHUI_GIFT = 14002;

    /**
     * 永恒双倍充值
     */
    int YONGHENG_DOUBLE_GIFT = 14008;

    /**
     * 永恒砸金蛋
     */
    int YONGHENG_ZAJINDAN = 14007;

    /**
     * 永恒投资
     */
    int YONGHENG_TOUZI = 14003;

    /**
     * 世界杯签到活动
     */
    int WORLD_CUP_LOGIN = 16001;

    /**
     * 世界杯点球大作战
     */
    int WORLD_CUP_DIAN_QIU = 16005;

    /**
     * 世界杯金卷狂欢
     */
    int WORLDCUP_KUANGHUAN = 16007;

    /**
     * 世界杯消费排行榜
     */
    int WORLDCUP_CONSUME_RANK = 16003;

    /**
     * 世界杯投资
     */
    int WORLDCUP_TOUZI = 16004;


    /**
     * 荣耀足球
     */
    int WORLD_CUP_RONG_YAO_ZU_QIU = 16008;

    /**
     * 世界杯专属时装
     */
    int WORLD_CUP_FASHION = 16009;

    /**
     * 世界杯商店
     */
    int WORLD_CUP_STORE = 16002;

    /**
     * 境界-灵宝神通直购
     */
    int GIFT_PACK_REALM = 19010;

    /**
     * 真言系统-天命
     */
    int TIANMING_GIFT = 17001;

    /**
     * 真言系统-狂暴
     */
    int KUANGBAO_GIFT = 17002;

    /**
     * 真言系统-守护
     */
    int SHOUHU_GIFT = 17003;

    /**
     * 王者神器
     */
    int WANG_ZHE_SHEN_QI = 4013;

    /**
     * 圣诞登录
     */
    int CHRISTMAS_LOGIN = 16101;

    /**
     * 圣诞特殊登录
     */
    int CHRISTMAS_SP_LOGIN = 16102;

    /**
     * 圣诞挖宝
     */
    int CHRISTMAS_WABAO = 16103;

    /**
     * 圣诞挖宝次数排行
     */
    int CHRISTMAS_WABAO_RANK = 16104;

    /**
     * 圣诞礼物
     */
    int CHRISTMAS_GIFT = 16108;

    /**
     * 圣诞-在线豪礼
     */
    int CHRISTMAS_ONLINE_GIFT = 16109;

    /**
     * 圣诞-专属商店
     */
    int CHRISTMAS_STORE = 16107;

    /**
     * 春节基金
     */
    int CHUN_JIE_JI_JIN = 16203;

    /**
     * 新春锦鲤
     */
    int XIN_CHUN_JIN_LI = 16205;

    /**
     * 新春登录
     */
    int XIN_CHUN_LOGIN = 16201;

    /**
     * 新春礼包
     */
    int XIN_CHUN_GIFT = 16207;

    /**
     * 新春累充
     */
    int XIN_CHUN_LEI_CHONG = 16206;

    /**
     * 新春节日连充
     */
    int XIN_CHUN_LIAN_CHONG = 16202;

    /**
     * 新春秘境
     */
    int XIN_CHUN_MIJING = 16209;

    /**
     * 金鼓迎财
     */
    int JIN_GU_YIN_CAI = 16211;

    /*终审活动*/
    int ZHONG_SHEN_LOGIN = 14021;
    int ZHONG_SHEN_TE_HUI = 14022;
    int ZHONG_SHEN_TOU_ZI = 14023;
    int ZHONG_SHEN_CHOU_JIANG = 14024;
    int ZHONG_SHEN_HAO_LI = 14025;
    int ZHONG_SHEN_ZHEN_YING = 14026;
    int ZHONG_SHEN_JIN_DAN = 14027;
    int ZHONG_SHEN_CHONG_ZHI = 14028;

    /**
     * 新春跨服排行榜
     */
    int XIN_CHUN_CROSS_RANK = 16208;

    /**
     * 新春跨服排行榜
     */
    int XIN_CHUN_BOSS = 16204;

    /**
     * 神魔召唤
     */
    int SHEN_MO_SUMMON = 201;

    int YUN_SHI_CHOU_JIANG = 11;
    int YUN_SHI_TASK = 12;
    int YUN_SHI_PACK = 13;

    // 神魔历练
    int SHENMO_LILIAN = 250001;
    // 神魔历练任务
    int SHENMO_LILIAN_TASK = 250002;
    // 神魔历练礼包
    int SHENMO_LILIAN_PACK = 250003;
    // 神魔历练累充
    int SHENMO_LILIAN_RECHARGE = 250004;

    // 推送礼包
    int TUI_SONG_PACK = 260001;

    /**
     * 战令
     */
    int ZHAN_LING = 220001; //战令及奖励
    int ZHAN_LING_TASK = 220002; //战令任务及奖励
    int ZHAN_LING_PACK = 220003; //战令礼包

    /**
     * 免费礼包
     */
    int FREE_PACK = 220004;

    /**
     * 周刊
     */
    int ZHOU_KAN_TASK = 14;

    int ZHOU_KAN_PACK = 15;

    /**
     * 广告礼包
     *
     */
    int ADVERTISE = 240001;

    /**
     * 超值礼包
     */
    int CHAO_ZHI_LI_BAO = 270001;

    /**
     * 猫狗首充礼包
     */
    int MG_SHOU_CHONG_LI_BAO = 1001;

    /**
     * 功德礼包
     */
    int GONG_DE_PACK = 310001;

    /**
     * 钻石充值
     */
    int ZUAN_SHI_CHONG_ZHI = 300002;

    /**
     * 天赐鸿福
     */
    int TIAN_CI_HONG_FU = 340001;

    /**
     * 最强修行者
     */
    int ZUI_QIANG_XIU_XING = 5100;

    /**
     * 超能
     */
    int CHAO_NENG = 401;

    /**
     * 每日特惠礼包
     */
    int DAY_GIFT_PACK = 360001;

    /**
     * 最强修行者任务
     */
    int ZUI_QIANG_XIU_XING_TASK = 5103;

    /**
     * 倒计时剧情
     */
    int COUNT_DOWN = 410001;

    /**
     * 官斗成就任务
     */
    int GD_ATTAINMENT = 380001;

    /**
     * 官斗财神成就任务
     */
    int GD_CAISHEN_ATTAINMENT = 380002;

    /**
     * 广告卡池许愿
     */
    int MAKE_WISH = 280050;

    /**
     * 主线任务榜单
     */
    int MAIN_TASK_RANK = 390001;

    /**
     * 等级涨幅榜
     */
    int LEVEL_RANK = 390002;

    /**
     * 秒修涨幅榜
     */
    int SECOND_RANK = 390003;

    /**
     * 真币消耗榜
     */
    int COIN_RANK = 390004;

    /**
     * 资质涨幅榜
     */
    int ZI_ZHI_RANK = 390005;

    /**
     * 宴会积分榜
     */
    int BANQUET_RANK = 390006;

    /**
     * 徒弟培养榜
     */
    int SCHOOL_RANK = 390007;

    /**
     * 灵晶消耗榜
     */
    int LING_JING_RANK = 390008;

    /**
     * 联盟宴会榜
     */
    int UNION_BANQUET_RANK = 390009;

    /**
     * 联盟徒弟榜
     */
    int UNION_SCHOOL_RANK = 390010;

    /**
     * 主线进度榜榜单任务
     */
    int MAIN_TASK = 10000001;

    /**
     * 等级涨幅榜榜单任务
     */
    int LEVEL_TASK = 10100002;

    /**
     * 秒修涨幅榜榜单任务
     */
    int SECOND_TASK = 10200003;

    /**
     * 真币消耗榜榜单任务
     */
    int COIN_TASK = 10300004;

    /**
     * 资质涨幅榜榜单任务
     */
    int ZI_ZHI_TASK = 10300005;

    /**
     * 宴会积分榜榜单任务
     */
    int BANQUET_TASK = 10300006;

    /**
     * 徒弟培养榜榜单任务
     */
    int SCHOOL_TASK = 10300007;

    /**
     * 灵晶消耗榜榜单任务
     */
    int LING_JING_TASK = 10300008;

    /**
     * 联盟宴会榜榜单任务
     */
    int UNION_BANQUET_TASK = 10300009;

    /**
     * 联盟徒弟榜榜单任务
     */
    int UNION_SCHOOL_TASK = 10300010;

    /**
     * 仙法积分榜
     */
    int XIAN_FA_RANK = 400000;

    /**
     * 里程碑活动
     */
    int MILESTONE = 370001;

    /**
     * 灵晶礼包
     */
    int LING_JING_GIFT_PACK = 410000;

    /**
     * 规则类型
     */
    interface RuleType {
        /**
         * 无条件
         */
        int FREE = 0;

        /**
         * 充值额度
         */
        int RECHARGE = 1;

        /**
         * 消费额度
         */
        int CONSUME = 2;

        /**
         * 在线时长
         */
        int ONLINE_TIME = 3;

        /**
         * 不显示
         */
        int BLANK = -1;
    }

    /**
     * 转盘积分次数来源类型
     */
    interface WheelSourceType {

        /**
         * 消耗道具获得积分
         */
        int CONSUME_ITEM = 1;

        /**
         * 充值获得积分
         */
        int RECHARGE_SCORE = 2;
    }

    interface GiftPackType {
        int LING_FU = 1;

        int RECHARGE = 2;

        int ADVERTISE = 3;
    }

    interface ActivityConstGroup {
        /**
         * 每日不重置
         */
        List<Integer> NOT_DAILY_RESET = Arrays.asList(ActivityConst.GD_ATTAINMENT, ActivityConst.GD_CAISHEN_ATTAINMENT, ActivityConst.MILESTONE);
    }
}
