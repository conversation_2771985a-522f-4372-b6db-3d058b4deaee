package com.sh.game.common.communication.msg.system.trade;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResTradeTradingMessage extends ProtobufMessage {

    private com.sh.game.protos.TradeProtos.ResTradeTrading proto;

    private com.sh.game.protos.TradeProtos.ResTradeTrading.Builder builder;

	
	@Override
	public int getId() {
		return 104012;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TradeProtos.ResTradeTrading.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TradeProtos.ResTradeTrading.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TradeProtos.ResTradeTrading.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TradeProtos.ResTradeTrading getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TradeProtos.ResTradeTrading proto) {
        this.proto = proto;
    }

}
