package com.sh.game.common.communication.msg.system.dunPaiShengJi.bean;

import com.sh.net.kryo.KryoBean;
import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class SpecialUpBean extends KryoBean {

	/**
	 * 类型
	 */
	private int type;
	/**
	 * 强化id
	 */
	private int cfgId;

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

		public int getCfgId() {
		return cfgId;
	}

	public void setCfgId(int cfgId) {
		this.cfgId = cfgId;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.type = readInt(buf, false);
		this.cfgId = readInt(buf, false);
		return true;
	}
	
	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, type, false);
		this.writeInt(buf, cfgId, false);
		return true;
	}
}
