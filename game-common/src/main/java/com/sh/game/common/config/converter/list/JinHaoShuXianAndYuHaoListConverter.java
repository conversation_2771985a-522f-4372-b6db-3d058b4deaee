package com.sh.game.common.config.converter.list;

import com.sh.common.config.IConverter;
import com.sh.commons.util.StringUtil;
import com.sh.commons.util.Symbol;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class JinHaoShuXianAndYuHaoListConverter implements IConverter {

	/**
	 * 根据#，|，&分割数据获得list
	 * @param source
	 * @return
	 */
	@Override
	public List<List<int[]>> convert(Object source) {
		String strVlaue = (String) source;
		List<List<int[]>> list = new ArrayList<>();
		if (StringUtils.isEmpty(strVlaue)) {
			return new ArrayList<>();
		} else {
			String[] outers  = strVlaue.split(Symbol.AND);
			for(String outer : outers){
				List<int[]> list1 = new ArrayList<>();
				String[] allArray = outer.split(Symbol.SHUXIAN);
				for (String s : allArray) {
					String[] typeArray = s.split(Symbol.JINHAO);
					list1.add(StringUtil.strArrToIntArr(typeArray));
				}
				list.add(list1);
			}
		}
		return list;
	}
}