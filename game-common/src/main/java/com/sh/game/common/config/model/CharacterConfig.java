package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 角色创建
 */
@Getter
@Setter
@ConfigData(file = "cfg_create_role", keys = "career#sex")
public class CharacterConfig extends AbstractConfigData {
    private int id;

    private int career;

    private int sex;

    private int map;

    private int birthX;

    private int birthY;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> init_item;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> initHeroEquip;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> initHeroSkill;

}