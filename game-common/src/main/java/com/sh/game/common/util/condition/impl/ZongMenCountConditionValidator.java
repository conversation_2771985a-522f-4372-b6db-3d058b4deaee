package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

/**
 * @Description：
 * @Author：zhangchao
 * @Date：2025/5/12 16:41
 */
public class ZongMenCountConditionValidator extends IConditionValidatorDefault {
    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (ctx == null) {
            return false;
        }
        if (params.length < 2) {
            return false;
        }
        return avatar.getZongMenCount() >= params[1];
    }
}
