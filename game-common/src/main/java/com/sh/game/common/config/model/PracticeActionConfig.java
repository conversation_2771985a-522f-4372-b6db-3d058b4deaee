package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@ConfigData(file = "cfg_practiceaction")
public class PracticeActionConfig extends AbstractConfigData {
    /**
     * 1、砍树 2、雷达消耗 3、神魔召唤次数 4、神识抽取 5、超能抽取
     */
    private int id;

    /**
     * 参数
     */
    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> param;

    /**
     * 目标数量，达成该值才可获得积分
     */
    private int count;

    /**
     * 获得积分
     */
    private int score;
}
