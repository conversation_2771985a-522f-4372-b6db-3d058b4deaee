package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;

import java.util.List;

/**
 * 背包合成
 *
 * <AUTHOR> zhao<PERSON><PERSON>
 * @date : 2018-08-22 9:34
 */

@lombok.Getter
@lombok.Setter
@ConfigData(file="cfg_compound")
public class CompoundConfig extends AbstractConfigData {

    private int id;

    private int menu_type;


    /**
     * 合成消耗物（必须）
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> itemid1;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> com_condition;

    /**
     * itemsCid#数量
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> product;

    private int probability;

    private int announce;

    private int next; // 本体

    /**
     * 合成消耗物（可选）
     */
    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> item_career;
    /**
     * 0默认消耗背包1消耗主角及其装备2消耗英雄及其装备3是终极四套合成
     */
    private int dressing;

    private int page3;

    /**
     * 是否需要记录
     */
    private int record;

    /**
     * 快捷合成消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> quick_compound;

    /**
     * 快捷合成分组
     */
    private int quick_group;

    /**
     * 百分百合成消耗材料
     */
    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> baifenbai;

    /**
     * 失败后的保留道具
     */
    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> itemSave;

    /**
     * 法器元宝减免消耗
     */
    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] faqijianmian;

    /**
     * 龙装合成升级是否继承龙魂属性
     */
    private int inherit;

    /**
     * 特戒升级是否继承淬炼属性、淬炼额外成功率
     */
    private int tejieInherit;

    /**
     * 失败后增加成功率
     */
    private int probabilityUp;

    /**
     * 合成升级是否继承侠魂属性
     */
    private int xiahunInherit;

    /**
     * 合成是否继承赋灵属性
     */
    private int fulingInherit;

    /**
     * 该合成配置是否是混沌附灵装备分解，带返还的
     */
    private int fulingfenjie;
}
