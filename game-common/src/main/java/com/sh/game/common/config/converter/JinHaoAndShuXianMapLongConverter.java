package com.sh.game.common.config.converter;

import com.sh.common.config.IConverter;
import com.sh.commons.util.Cast;
import com.sh.commons.util.StringUtil;
import com.sh.commons.util.Symbol;

import java.util.TreeMap;

public class JinHaoAndShuXianMapLongConverter implements IConverter {
    @Override
    public Object convert(Object source) {
        String str = (String) source;
        TreeMap<Integer, Long> map = new TreeMap<>();
        if (!StringUtil.isEmpty(str)) {
            String[] arr = str.split(Symbol.SHUXIAN);
            if (arr.length > 0) {
                for (String v : arr) {
                    String[] temp = v.split(Symbol.JINHAO);
                    if (temp.length == 2) {

                        Integer itemId = Cast.toInteger( temp[0]);
                        Long count = map.get(itemId);
                        Long itemCount = Cast.toLong(temp[1]);
                        map.put(itemId, (count == null ? 0 : count) + itemCount);
                    }
                }
            }
        }
        return map;
    }
}
