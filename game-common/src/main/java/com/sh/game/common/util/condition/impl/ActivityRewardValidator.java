package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

/**
 * description: 活动关联奖池
 * date: 2024/7/8
 * author: chenBin
 */
public class ActivityRewardValidator extends IConditionValidatorDefault{
    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null || params.length < 2) {
            return false;
        }

        return avatar.isActivityOpen(params[1]);
    }
}
