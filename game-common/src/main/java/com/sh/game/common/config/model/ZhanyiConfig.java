package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/6/15
 * 战意
 */
@Getter
@Setter
@ConfigData(file = "cfg_zhanyi", keys = {"id", "level#subId"})
public class ZhanyiConfig extends AbstractConfigData {
    private int id;
    private int level;
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;
    private int itemId;
    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> costitem;
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    private int levellimit;
    private int nextId;
    private int subId;
    private int probability;
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attr;

    /**
     * 战意名称
     */
    private String levelinfo;

    /**
     * 战意升级公告
     */
    private int announce;

    private int[] buffer;

}
