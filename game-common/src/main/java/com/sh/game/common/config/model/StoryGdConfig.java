package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapIntLongConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/28 10:32
 */
@Getter
@Setter
@ConfigData(file="cfg_story")
public class StoryGdConfig extends AbstractConfigData {
    private int id;

    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> triggerCondition;
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> reward;
}
