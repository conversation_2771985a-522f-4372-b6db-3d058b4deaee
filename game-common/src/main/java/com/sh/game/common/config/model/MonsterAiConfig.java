package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
@ConfigData(file = "cfg_monster_ai")
public class MonsterAiConfig extends AbstractConfigData {

    private int id;

    private int monsterid;

    private int condtion;

    private int rate;

    private int create;

    private int order;

    private int removeorder;

    private int[] conditonvalue;

    private int delay;

    private int times;

    private int[] addbuff;

    private int[] removebuff;

    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> createsfx;

    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> deletesfx;

    @ConfigField(converter = JinHaoAndShuXianListConverter.class)
    private List<int[]> summon;

    private String animation;

    private int mode;

    private int audio;
}
