package com.sh.game.common.communication.msg.system.turnbased;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求回合制战斗信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqTurnBasedBattleMessage extends ProtobufMessage {

    private com.sh.game.protos.TurnBasedProtos.ReqTurnBasedBattleMessage proto;

    private com.sh.game.protos.TurnBasedProtos.ReqTurnBasedBattleMessage.Builder builder;

	
	@Override
	public int getId() {
		return 389001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TurnBasedProtos.ReqTurnBasedBattleMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TurnBasedProtos.ReqTurnBasedBattleMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TurnBasedProtos.ReqTurnBasedBattleMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TurnBasedProtos.ReqTurnBasedBattleMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TurnBasedProtos.ReqTurnBasedBattleMessage proto) {
        this.proto = proto;
    }

}
