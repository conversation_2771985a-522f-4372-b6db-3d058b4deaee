package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.array.ShuXianIntArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/5 15:58
 */
@Getter
@Setter
@ConfigData(file = "cfg_jierilianchong")
public class JieRiLianChongConfig extends AbstractConfigData {

    private int id;

    private int activityId;

    private int type;

    /**
     * 奖励内容根据开服天数区分，开服天数=活动开启时间-服务器开启时间，配置使用竖线分割，开服天数下限|上限
     */
    @ConfigField(converter = ShuXianIntArrayConverter.class)
    private int[] opentime;

    private int day;

    private int recharge;

    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> reward;

    /**
     * 公告
     */
    private int announce;

    /**
     * 活动条件
     */
    @ConfigField(converter = ConditionConvert.class)
    private List<int[]> condition;
}
