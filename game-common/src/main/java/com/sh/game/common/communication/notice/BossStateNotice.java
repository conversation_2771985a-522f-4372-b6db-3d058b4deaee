package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2018/6/21 18:17
 */
@Getter
@Setter
@Notice
public class BossStateNotice extends ProcessNotice {

    /**
     * id
     */
    private int bossId;

    private int mapId;
    /**
     * 血量
     */
    private long hp;
    /**
     * 总血量
     */
    private long totalHp;
    /**
     * 复活时间
     */
    private long reliveTime;

    /**
     * boss归属
     */
    private long owner;

    private int playerNumber;

    private int x;

    private int y;

}
