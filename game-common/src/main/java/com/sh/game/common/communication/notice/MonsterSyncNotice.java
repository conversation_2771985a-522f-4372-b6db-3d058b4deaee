package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.map.MonsterCount;
import com.sh.game.common.entity.map.MonsterState;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;


@Getter
@Setter
@Notice
public class MonsterSyncNotice extends ProcessNotice {

    private int spaceUType;

    private long mapId;

    private List<MonsterState> states = new ArrayList<>();

    private List<MonsterCount> counts = new ArrayList<>();
}
