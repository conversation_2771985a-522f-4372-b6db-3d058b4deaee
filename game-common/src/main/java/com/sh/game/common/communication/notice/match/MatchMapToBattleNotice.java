package com.sh.game.common.communication.notice.match;

import com.sh.game.common.communication.msg.pvp.bean.BattleGroupBean;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 通知创建战斗服地图
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/13.
 */
@Notice
@Getter
@Setter
public class MatchMapToBattleNotice extends ProcessNotice {
    /**
     * 地图id
     */
    private int mapId;
    /**
     * pvpid
     */
    private int battleId;
    /**
     * group列表
     */
    private List<BattleGroupBean> groupList = new ArrayList<>();

}
