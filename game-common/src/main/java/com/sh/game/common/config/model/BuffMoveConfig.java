package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;

/**
 * buff移动表
 * 该效果为buff立即性
 * id关联buffconfig的参数
 */
@lombok.Getter
@lombok.Setter
@ConfigData(file = "cfg_buffMove")
public class BuffMoveConfig extends AbstractConfigData {

    /**
     * id
     */
    private int id;
    /**
     * 移动类型
     * 对应 AttackArea id
     * 使用的时候需要-1 对应数组从0开始
     */
    private int type;
    /**
     * 移动距离
     */
    private int distance;
    /**
     * 移动时间
     */
    private int time;

}
