package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.common.util.condition.IConditionValidator;

/**
 * <AUTHOR>
 * @version V1.0
 * @Package com.sh.game.common.util.condition.impl
 * @date 2021/5/25 11:02
 * @Copyright © SH
 * @Describe 战意等级
 */
public class ZhanYiLevelConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params == null || params.length < 2) {
            return true;
        }
        int level = avatar.getZhanYiLevel();
        if (params.length == 2) {
            return level >= params[1];
        }
        return level >= params[1] && level <= params[2];
    }
}
