package com.sh.game.common.communication.msg.system.shabake;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>沙巴克分配返回</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResShaBaKeAllocMessage extends ProtobufMessage {

    private com.sh.game.protos.ShabakeProtos.ResShaBaKeAlloc proto;

    private com.sh.game.protos.ShabakeProtos.ResShaBaKeAlloc.Builder builder;

	
	@Override
	public int getId() {
		return 275034;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ShabakeProtos.ResShaBaKeAlloc.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ShabakeProtos.ResShaBaKeAlloc.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ShabakeProtos.ResShaBaKeAlloc.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ShabakeProtos.ResShaBaKeAlloc getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ShabakeProtos.ResShaBaKeAlloc proto) {
        this.proto = proto;
    }

}
