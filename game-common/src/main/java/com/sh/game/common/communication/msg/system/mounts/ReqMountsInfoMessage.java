package com.sh.game.common.communication.msg.system.mounts;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求马匹信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqMountsInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.MountProtos.ReqMountsInfo proto;

    private com.sh.game.protos.MountProtos.ReqMountsInfo.Builder builder;

	
	@Override
	public int getId() {
		return 195001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.MountProtos.ReqMountsInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.MountProtos.ReqMountsInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.MountProtos.ReqMountsInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.MountProtos.ReqMountsInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.MountProtos.ReqMountsInfo proto) {
        this.proto = proto;
    }

}
