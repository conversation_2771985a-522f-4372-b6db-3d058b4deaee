package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/6/30
 * @Desc : to do anything
 */
@Getter
@Setter
@Notice
public class AnnounceToAllianceReturnNotice extends ProcessNotice {

    private int announceId;

    private long unionId;

    private List<String> paramList;

    public AnnounceToAllianceReturnNotice() {
    }

    public AnnounceToAllianceReturnNotice(int announceId, long unionId, List<String> paramList) {
        this.announceId = announceId;
        this.unionId = unionId;
        this.paramList = paramList;
    }
}
