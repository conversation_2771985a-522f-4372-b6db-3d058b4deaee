package com.sh.game.common.config.cache;

import com.google.common.collect.ImmutableSet;
import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.RechargeConfig;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

@Getter
@ConfigCache
public class RechargeConfigCache implements IConfigCache {
    Set<RechargeConfig> limitCfgSet;

    @Override
    public void build() {
        Set<RechargeConfig> limitSet = new HashSet<>();
        for (RechargeConfig config : ConfigDataManager.getInstance().getList(RechargeConfig.class)) {
            if (config.getBuyLimit() != null && config.getBuyLimit().length > 1) {
                limitSet.add(config);
            }
        }
        limitCfgSet = ImmutableSet.copyOf(limitSet);
    }

}
