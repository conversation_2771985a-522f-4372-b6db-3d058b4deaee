package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ShuXianAndFenHaoMapConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 技能特殊属性配置
 */
@Getter
@Setter
@ConfigData(file = "cfg_skill_special")
public class SkillSpecialConfig extends AbstractConfigData {

    private int id;

    private int[] skill;

    private int rate;

    private int extraHurt;

    private int pkHurt;

    private int hurt;

    private int monHurtAdd;

    private int monHurtAddFix;

    private int cdTime;

    private int buffTime;

    private int maxtarget;

    private int skillCdClearRate;

    private int nbvalue;

    private int piaozi;

    private int skillSpecialCdTime;

    private int[] paramorder;

    private int paramvalue;

    @ConfigField(converter = ShuXianAndFenHaoMapConverter.class)
    private Map<Integer, int[]> buffadd;
}
