package com.sh.game.common.config.converter.list;

import com.sh.common.config.IConverter;
import com.sh.commons.util.Cast;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
public class JinghaoDoubleListConverter implements IConverter {

    @Override
    public List<Double> convert(Object source) {
        String v = (String) source;
        if (v == null || StringUtils.isEmpty(v)) {
            return Collections.emptyList();
        }

        List<Double> ret = new ArrayList<>();
        String[] strArray = v.split("#");
        for (String str : strArray) {
            ret.add(Cast.toDouble(str));
        }
        return ret;
    }
}
