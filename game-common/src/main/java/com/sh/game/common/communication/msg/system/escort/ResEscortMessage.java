package com.sh.game.common.communication.msg.system.escort;

import com.sh.net.kryo.KryoInput;
import com.sh.net.kryo.KryoOutput;
import com.sh.server.AbstractMessage;


/**
 * <p>押镖信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ResEscortMessage extends AbstractMessage {
	
	@Override
	public int getId() {
		return 105002;
	}
	
	/**
	 * 押镖类型 1本服 2跨服
	 */
	private int type;
	/**
	 * 今日押镖次数上限
	 */
	private int countLimit;
	/**
	 * 今日押镖次数
	 */
	private int count;
	/**
	 * 今日跨服押镖次数上限
	 */
	private int crossCountLimit;
	/**
	 * 今日跨服押镖次数
	 */
	private int crossCount;
	/**
	 * 1已投保，0未投保
	 */
	private int insure;
	/**
	 * 镖车ID
	 */
	private long lid;
	/**
	 * 本服配置ID
	 */
	private int localCfgId;
	/**
	 * 跨服配置ID
	 */
	private int crossCfgId;
	/**
	 * 结束时间
	 */
	private int endTime;
	/**
	 * 自动押镖
	 */
	private int auto;

	public int getType() {
		return type;
	}

	public void setType(int type) {
		this.type = type;
	}

		public int getCountLimit() {
		return countLimit;
	}

	public void setCountLimit(int countLimit) {
		this.countLimit = countLimit;
	}

		public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

		public int getCrossCountLimit() {
		return crossCountLimit;
	}

	public void setCrossCountLimit(int crossCountLimit) {
		this.crossCountLimit = crossCountLimit;
	}

		public int getCrossCount() {
		return crossCount;
	}

	public void setCrossCount(int crossCount) {
		this.crossCount = crossCount;
	}

		public int getInsure() {
		return insure;
	}

	public void setInsure(int insure) {
		this.insure = insure;
	}

		public long getLid() {
		return lid;
	}

	public void setLid(long lid) {
		this.lid = lid;
	}

		public int getLocalCfgId() {
		return localCfgId;
	}

	public void setLocalCfgId(int localCfgId) {
		this.localCfgId = localCfgId;
	}

		public int getCrossCfgId() {
		return crossCfgId;
	}

	public void setCrossCfgId(int crossCfgId) {
		this.crossCfgId = crossCfgId;
	}

		public int getEndTime() {
		return endTime;
	}

	public void setEndTime(int endTime) {
		this.endTime = endTime;
	}

		public int getAuto() {
		return auto;
	}

	public void setAuto(int auto) {
		this.auto = auto;
	}

	
	@Override
	public boolean read(KryoInput buf) {

		this.type = readInt(buf, false);
		this.countLimit = readInt(buf, false);
		this.count = readInt(buf, false);
		this.crossCountLimit = readInt(buf, false);
		this.crossCount = readInt(buf, false);
		this.insure = readInt(buf, false);
		this.lid = readLong(buf);
		this.localCfgId = readInt(buf, false);
		this.crossCfgId = readInt(buf, false);
		this.endTime = readInt(buf, false);
		this.auto = readInt(buf, false);
		return true;
	}

	@Override
	public boolean write(KryoOutput buf) {

		this.writeInt(buf, type, false);
		this.writeInt(buf, countLimit, false);
		this.writeInt(buf, count, false);
		this.writeInt(buf, crossCountLimit, false);
		this.writeInt(buf, crossCount, false);
		this.writeInt(buf, insure, false);
		this.writeLong(buf, lid);
		this.writeInt(buf, localCfgId, false);
		this.writeInt(buf, crossCfgId, false);
		this.writeInt(buf, endTime, false);
		this.writeInt(buf, auto, false);
		return true;
	}
}
