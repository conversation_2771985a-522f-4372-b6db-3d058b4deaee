package com.sh.game.common.config.cache;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.IConfigCache;
import com.sh.common.config.annotation.ConfigCache;
import com.sh.game.common.config.model.WaBaoCostConfig;
import com.sh.game.common.config.model.WaBaoRewardConfig;
import lombok.Getter;

import java.util.*;

/**
 * 挖宝
 */
@Getter
@ConfigCache
public class WaBaoConfigCache implements IConfigCache {

    private Map<Integer, List<Integer>> groups = new HashMap<>();

    private Map<Integer, List<WaBaoRewardConfig>> rewards = new HashMap<>();

    private Map<Integer, List<Integer>> ends = new HashMap<>();

    private Set<Integer> actTypes = new HashSet<>();

	/**
	 * key:costGroup
	 * value:cfg_treasury_reward配置信息
	 */
	private Map<Integer, List<WaBaoRewardConfig>> fristRewards = new HashMap<>();


    @Override
    public void build() {
        groups.clear();
        rewards.clear();
        ends.clear();
        actTypes.clear();
		fristRewards.clear();
        for (WaBaoCostConfig config : ConfigDataManager.getInstance().getList(WaBaoCostConfig.class)) {
            if (!groups.containsKey(config.getActType())) {
                groups.put(config.getActType(), new ArrayList<>());
            }
            groups.get(config.getActType()).add(config.getRewardGroup());
            actTypes.add(config.getActType());
        }

        for (WaBaoRewardConfig config : ConfigDataManager.getInstance().getList(WaBaoRewardConfig.class)) {
            if (!rewards.containsKey(config.getCostGroup())) {
                rewards.put(config.getCostGroup(), new ArrayList<>());
            }
            rewards.get(config.getCostGroup()).add(config);
            if (!ends.containsKey(config.getActType())) {
                ends.put(config.getActType(), new ArrayList<>());
            }
            if (config.getSelfEnd() == 1) {
                ends.get(config.getActType()).add(config.getId());
            }
			if (!fristRewards.containsKey(config.getCostGroup())) {
				fristRewards.put(config.getCostGroup(), new ArrayList<>());
			}
			if (config.getFirstTime() == 1) {
				fristRewards.get(config.getCostGroup()).add(config);
			}

        }
    }

    public List<WaBaoRewardConfig> getRewards(int group) {
        return rewards.getOrDefault(group, Collections.emptyList());
    }

	public WaBaoRewardConfig getFristReward(int group) {
		if (!fristRewards.containsKey(group)) {
			return null;
		}
		if (fristRewards.get(group).size() > 0) {
			return fristRewards.get(group).get(0);
		}
		return null;
	}
}
