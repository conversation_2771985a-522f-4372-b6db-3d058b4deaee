package com.sh.game.common.util;

import java.io.File;
import java.io.IOException;

/**
 * author: wiley
 * QQ   : 510600102
 * Date: 2020/8/1 15:55
 * desc: 打包线上版本
 * 该程序支持windows坏境下运行
 */
public class DeployUtil {

    public static void main(String[] args) throws IOException {
        //发布的版本号信息
        String version = "1.0.3";
        //本地配置文件svn地址
        String localDataUrl = "F:\\hjcq\\branch\\online\\doc\\data\\miracle-data";
        //本地打包svn地址
        String localUrl = "F:\\hjcq\\server\\" + version;
        //138服务器下载地址
        String downloadJarUrl = "http://*************:9999/deploy/";
        //需要发布的包
        String[] list = {"game-api-0.0.1.jar", "game-common-0.0.1.jar", "game-basic-0.0.1.jar", "game-scene-0.0.1.jar", "game-script-0.0.1.jar", "game-server-0.0.1.jar"};
        //创建版本文件夹
        File file = new File(localUrl);
        if (file.exists()) {//如果文件夹存在
            System.out.println("当前版本已发布，请检查版本号");
            return;
        }
        System.out.println("---------------开始打包---------------------");
        //更新本地配置文件 调用windows命令窗口执行命令
        System.out.println("---------------开始更新online下的配置表---------------------");
        try {
            Runtime.getRuntime().exec("svn up " + localDataUrl);
        } catch (IOException e) {
            System.out.println("更新本地配置表失败，请检查本地配置文件svn地址是否正确");
            return;
        }
        System.out.println("---------------更新配置表完成---------------------");
        file.mkdir();//创建文件夹
        //拷贝配置表
        System.out.println("---------------开始拷贝online下的配置表---------------------");
        String dataFileUrl = localUrl + "\\data";
        try {
            FileUtil.copyDir(localDataUrl, dataFileUrl);
        } catch (IOException e) {
            System.out.println("拷贝本地配置表失败");
            return;
        }
        System.out.println("---------------拷贝配置表完成---------------------");
        //下载jar包
        System.out.println("---------------开始下载jar包---------------------");
        String jarUrl = localUrl + "\\core\\";
        File jarFile = new File(jarUrl);
        jarFile.mkdir();
        for (String fileName : list) {
            try {
                FileUtil.downloadFile(downloadJarUrl + fileName, jarUrl + fileName);
            } catch (Exception e) {
                System.out.println("下载jar包失败：" +downloadJarUrl + fileName);
                return;
            }
        }
        System.out.println("---------------下载jar包结束---------------");
        //写入版本号
        System.out.println("---------------开始写入版本文件---------------");
        FileUtil.writeFile(localUrl + "\\version.txt", version);
        System.out.println("---------------写入版本文件结束---------------");
        System.out.println("---------------打包成功请检查后提交---------------");
        Runtime.getRuntime().exec("cmd /c start explorer "+localUrl);
    }
}
