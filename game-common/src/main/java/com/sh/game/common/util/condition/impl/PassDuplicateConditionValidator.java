package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;
import com.sh.game.common.util.condition.IConditionValidator;

import java.util.Map;

/**
 * 副本通关
 */
public class PassDuplicateConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        int len = params.length;
        if (len < 2) {
            return false;
        }
        Map<Integer, Integer> bossChallengeInfo = avatar.getBossChallengeInfo();
        int mapId = bossChallengeInfo.getOrDefault(params[1], 0);
        if (mapId + 1 < params[2]) {
            return false;
        }
        return true;
    }
}
