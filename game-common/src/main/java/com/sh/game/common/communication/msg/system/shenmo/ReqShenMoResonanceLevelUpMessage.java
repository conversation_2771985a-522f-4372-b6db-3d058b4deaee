package com.sh.game.common.communication.msg.system.shenmo;

import com.google.protobuf.InvalidProtocolBufferException;
import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;


/**
 * <p>请求神魔共鸣升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqShenMoResonanceLevelUpMessage extends ProtobufMessage {

    private com.sh.game.protos.ShenMoProtos.ReqShenMoResonanceLevelUpMessage proto;

    private com.sh.game.protos.ShenMoProtos.ReqShenMoResonanceLevelUpMessage.Builder builder;

	
	@Override
	public int getId() {
		return 390004;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ShenMoProtos.ReqShenMoResonanceLevelUpMessage.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ShenMoProtos.ReqShenMoResonanceLevelUpMessage.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ShenMoProtos.ReqShenMoResonanceLevelUpMessage.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ShenMoProtos.ReqShenMoResonanceLevelUpMessage getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ShenMoProtos.ReqShenMoResonanceLevelUpMessage proto) {
        this.proto = proto;
    }

}
