package com.sh.game.common.config.check.configCheck;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.check.ConfigCheckHelper;
import com.sh.game.common.config.check.IConfigCheck;
import com.sh.game.common.config.model.ItemConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/6/5
 * @Desc : to do anything
 */
@Slf4j
public class ItemConfigCheck implements IConfigCheck {
    @Override
    public void check() {
        List<ItemConfig> list = ConfigDataManager.getInstance().getList(ItemConfig.class);
        for (ItemConfig config : list) {
            if(config.getBind() > 0) {
                ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, config.getBind());
//                ConfigCheckHelper.assertNotNull(itemConfig, "表【cfg_items】中 id【{}】的bind【{}】在道具表中不存在", config.getId(), config.getBind());
            }
        }
    }
}
