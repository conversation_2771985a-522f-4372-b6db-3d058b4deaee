package com.sh.game.common.constant;

public interface ItemConst {

    enum Announce {
        /**
         * 怪物掉落
         */
        MONSTER_DROP(0x0001),

        /**
         * 寻宝
         */
        TREASURE(0x0002),

        /**
         * 交易
         */
        FUSION(0x0004),

        /**
         * 获得
         */
        GET(0x0008),

        /**
         * 萃取获得
         */
        EXTRACT_GET(0x0009),

        ;
        private final int value;

        Announce(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        /**
         * 是否有公告
         *
         * @param value
         * @param announce
         * @return
         */
        public static boolean isAnnounceOf(int value, Announce announce) {
            return (value & announce.getValue()) > 0;
        }
    }

    interface ItemId {

        int SCORE = 958;
    }
}
