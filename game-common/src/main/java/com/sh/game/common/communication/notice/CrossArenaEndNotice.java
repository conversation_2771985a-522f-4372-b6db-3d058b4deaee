package com.sh.game.common.communication.notice;

import com.sh.game.common.entity.daily.TeamMember;
import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * ATO：yumo<br>;
 * 时间：2021/3/25 19:30<br>;
 * 版本：1.0<br>;
 * 描述：跨服竞技场结束通知
 */
@Getter
@Setter
@Notice
public class CrossArenaEndNotice extends ProcessNotice {

    /**
     * 队长编号
     */
    private long leader;

    /**
     * 队伍成员列表
     */
    private List<TeamMember> memberList;
}
