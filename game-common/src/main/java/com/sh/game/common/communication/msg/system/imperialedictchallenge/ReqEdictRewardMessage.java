package com.sh.game.common.communication.msg.system.imperialedictchallenge;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求领取奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqEdictRewardMessage extends ProtobufMessage {

    private com.sh.game.protos.ImperialEdictChallengeProtos.ReqEdictReward proto;

    private com.sh.game.protos.ImperialEdictChallengeProtos.ReqEdictReward.Builder builder;

	
	@Override
	public int getId() {
		return 335005;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ImperialEdictChallengeProtos.ReqEdictReward.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ImperialEdictChallengeProtos.ReqEdictReward.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ImperialEdictChallengeProtos.ReqEdictReward.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ImperialEdictChallengeProtos.ReqEdictReward getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ImperialEdictChallengeProtos.ReqEdictReward proto) {
        this.proto = proto;
    }

}
