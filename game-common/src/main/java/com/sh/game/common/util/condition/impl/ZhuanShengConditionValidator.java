package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

public class ZhuanShengConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }

        int zhuanShengId = avatar.getZhuanShengId() / 1000;
        int len = params.length;
        if (len > 1 && zhuanShengId < params[1]) {
            return false;
        }
        if (len > 2 && zhuanShengId > params[2]) {
            return false;
        }
        return true;
    }
}
