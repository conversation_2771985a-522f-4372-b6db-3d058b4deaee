package com.sh.game.common.util.condition.impl;

import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

/**
 * <AUTHOR>
 * @date 2022/7/26 15:36
 */
public class AttrCheckConditionValidator extends IConditionValidatorDefault {

    @Override
    boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params == null || params.length < 3) {
            return false;
        }
        int attr = params[1];
        int count = params[2];
        Long attrValue = avatar.getFinalAttribute().getAttributeMap().getOrDefault(attr, 0L);
        return attrValue >= count;
    }
}
