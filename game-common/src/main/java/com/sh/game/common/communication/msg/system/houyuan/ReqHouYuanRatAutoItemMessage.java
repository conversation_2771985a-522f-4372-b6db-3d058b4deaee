package com.sh.game.common.communication.msg.system.houyuan;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求设置老鼠自动派遣的道具规则</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqHouYuanRatAutoItemMessage extends ProtobufMessage {

    private com.sh.game.protos.HouYuanProtos.ReqHouYuanRatAutoItem proto;

    private com.sh.game.protos.HouYuanProtos.ReqHouYuanRatAutoItem.Builder builder;

	
	@Override
	public int getId() {
		return 401010;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.HouYuanProtos.ReqHouYuanRatAutoItem.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.HouYuanProtos.ReqHouYuanRatAutoItem.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.HouYuanProtos.ReqHouYuanRatAutoItem.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.HouYuanProtos.ReqHouYuanRatAutoItem getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.HouYuanProtos.ReqHouYuanRatAutoItem proto) {
        this.proto = proto;
    }

}
