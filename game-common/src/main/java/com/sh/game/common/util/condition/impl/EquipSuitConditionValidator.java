package com.sh.game.common.util.condition.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.EquipSuitConfig;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @Package com.sh.game.common.util.condition.impl
 * @date 2021/5/25 11:02
 * @Copyright © SH
 * @Describe 成就等级校验器
 */
public class EquipSuitConditionValidator extends IConditionValidatorDefault {

    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        Set<Integer> equipSuits = avatar.getEquipSuits();
        if (equipSuits == null || equipSuits.isEmpty()) {
            return false;
        }
        int targetSuitId = params[1];
        int type = params[2];
        if (type == 2) {
            return equipSuits.contains(targetSuitId);
        } else if (type == 1) {
            if (equipSuits.contains(targetSuitId)) {
                return true;
            }
            return findSuit(targetSuitId, equipSuits);
        }
        return false;
    }

    private boolean findSuit(int targetSuitId, Set<Integer> equipSuits) {
        EquipSuitConfig config = ConfigDataManager.getInstance().getById(EquipSuitConfig.class, targetSuitId);
        if (config == null) {
            return false;
        }
        int superSuit = config.getSuperClass();
        // 父套装不存在,直接返回false
        if (superSuit == 0) {
            return false;
        }
        // 夫套装存在,且玩家有,返回true
        if (equipSuits.contains(superSuit)) {
            return true;
        }
        // 父套装存在,但是玩家没有,继续向上找父套装
        return findSuit(superSuit, equipSuits);
    }
}
