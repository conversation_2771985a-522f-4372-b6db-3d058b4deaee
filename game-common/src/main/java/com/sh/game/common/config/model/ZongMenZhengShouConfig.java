package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import lombok.Getter;
import lombok.Setter;

/**
 * description:
 * create: 2025/5/21
 * author: chen bin
 */
@Getter
@Setter
@ConfigData(file = "cfg_zongmen_zhengshou")
public class ZongMenZhengShouConfig extends AbstractConfigData {

    private int id;

    private int type;

    @ConfigField(converter = JinghaoIntArrayConverter.class)
    private int[] num;

    private int time;
}
