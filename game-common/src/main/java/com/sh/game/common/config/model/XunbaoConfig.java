package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 寻宝事件配置
 */
@Getter
@Setter
@ConfigData(file = "cfg_xunbao")
public class XunbaoConfig extends AbstractConfigData {

    private int id;

    /**
     * 主题
     */
    private String theme;

    /**
     * 转生次数
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> layer;

    /**
     * 等级下限
     */
    private int levelMin;

    /**
     * 等级上限
     */
    private int levelMax;

    /**
     * 次数组
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> num;

    /**
     * 普通奖励组
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> rewards;

    /**
     * 特抽奖励组
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> rewardsSpecial;

    /**
     * 展示奖励组
     */
    private int rewardsShow;


}
