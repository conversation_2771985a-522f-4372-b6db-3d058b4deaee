package com.sh.game.common.util.condition.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.FriendsLevelConfig;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.intf.IGameContext;

public class FriendLevelConditionValidator extends IConditionValidatorDefault {
    @Override
    public boolean validate(IGameContext ctx, IAvatar avatar, int[] params) {
        if (avatar == null) {
            return false;
        }
        if (params.length < 3) {
            return false;
        }

        int friendId = params[1];
        int level = params[2];
        int friendLevel = avatar.findFriendLevel(friendId);
        FriendsLevelConfig config = ConfigDataManager.getInstance().getById(FriendsLevelConfig.class, friendLevel);
        if (config == null) {
            return false;
        }
        return config.getLike_grade() >= level;
    }
}
