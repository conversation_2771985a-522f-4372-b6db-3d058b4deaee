package com.sh.game.common.communication.msg.system.door;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>返回玄界之门信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResDoorInfoMessage extends ProtobufMessage {

    private com.sh.game.protos.DoorProtos.ResDoorInfo proto;

    private com.sh.game.protos.DoorProtos.ResDoorInfo.Builder builder;

	
	@Override
	public int getId() {
		return 373002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DoorProtos.ResDoorInfo.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DoorProtos.ResDoorInfo.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DoorProtos.ResDoorInfo.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DoorProtos.ResDoorInfo getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DoorProtos.ResDoorInfo proto) {
        this.proto = proto;
    }

}
