package com.sh.game.common.communication.notice.logic.player;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/16 10:35
 */
@Getter
@Setter
@Notice
public class DFLSLingFuJieSuanNotice extends ProcessNotice {

    private long lingFu;

    /**
     * key 行会id
     * value 赛区
     */
    private Map<Long, Integer> winUnionIdMap;
}
