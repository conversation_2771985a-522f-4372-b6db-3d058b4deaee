package com.sh.game.common.communication.notice;

import com.sh.game.notice.Notice;
import com.sh.game.notice.ProcessNotice;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/4/9
 * @Desc : to do anything
 */
@Getter
@Setter
@Notice
public class PlayerReliveNotice extends ProcessNotice {

    private long rid;

    /**
     * 主人名字(若为空则没有主人)
     */
    private String masterName;

    private String killerName;

    private int dieCount;

    private int autoReliveTime;

    private int serverType;

    private int dieTime;

    public PlayerReliveNotice() {
    }
}
