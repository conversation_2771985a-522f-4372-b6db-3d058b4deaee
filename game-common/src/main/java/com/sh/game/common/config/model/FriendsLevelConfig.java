package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 道具配置
 */
@Getter
@Setter
@ConfigData(file = "cfg_friendslevel")
public class FriendsLevelConfig extends AbstractConfigData {

    private int id;

    private int gf_id;

    private int like_grade;

    private int like_exp;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attr;

    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] spe_attr;

    @ConfigField(converter = JinhaoIntegerListConverter.class)
    private List<Integer> apecial;

}
