package com.sh.game.common.entity.attr;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.config.cache.AttributeScoreCache;
import com.sh.game.common.config.model.AttributeScoreConfig;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.constant.RoleConst;
import com.sh.game.common.entity.backpack.item.EquipAttribute;
import com.sh.game.common.entity.backpack.item.IdentifyAttribute;
import com.sh.game.common.entity.backpack.item.IdentifyAttributeValue;
import com.sh.game.protos.AbcProtos;
import io.protostuff.Tag;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;

/**
 * 属性
 *
 * <AUTHOR>
 * 2017年6月6日 下午9:49:08
 */
@lombok.Getter
@lombok.Setter
public class Attribute {

    /**
     * 属性
     */
    @Tag(111)
    private Map<Integer, Long> attributeMap = new HashMap<>();

    /**
     * 增加的技能 id - level
     */
    @Tag(112)
    private Map<Integer, Integer> skillMap = new HashMap<>(0);

    /**
     * 增加的技能效果 效果id
     */
    @Tag(113)
    private Set<Integer> skillSpecialSet = new HashSet<>(0);

    /**
     * buffTriggerId
     */
    @Tag(114)
    private List<Integer> buffTriggerList = new ArrayList<>(0);

    /**
     * buff参数效果增强 效果id
     */
    @Tag(115)
    private Set<Integer> buffParamEnhanceSet = new HashSet<>(0);

    @Tag(116)
    private int effectId;


    public long get(AttributeEnum attributeEnum) {
        if (attributeEnum == null) {
            return 0L;
        }
        return attributeMap.getOrDefault(attributeEnum.getType(), 0L);
    }

    public void set(AttributeEnum attributeEnum, long value) {
        if (attributeEnum == null) {
            return;
        }
        attributeMap.put(attributeEnum.getType(), value);
    }


    public long findMaxHp() {
        return get(AttributeEnum.HP);
    }

    public long findMaxMp() {
        return get(AttributeEnum.MP);
    }

    public long findMaxShield() {
        return get(AttributeEnum.SHIELD_ATTR);
    }

    public long findPhyAtk() {
        return get(AttributeEnum.PHY_ATK);
    }

    public long findPhyAtkMax() {
        return get(AttributeEnum.PHY_ATK_MAX);
    }

    public long findMagicAtk() {
        return get(AttributeEnum.MAGIC_ATK);
    }

    public long findMagicAtkMax() {
        return get(AttributeEnum.MAGIC_ATK_MAX);
    }

    public long findTaoAtk() {
        return get(AttributeEnum.TAO_ATK);
    }

    public long findTaoAtkMax() {
        return get(AttributeEnum.TAO_ATK_MAX);
    }


    public static Attribute copy(Attribute attr) {
        Attribute attribute = new Attribute();
        attribute.fixAdd(attr);
        return attribute;
    }


    public Attribute fixAdd(Attribute attr) {
        if (attr != null) {
            attr.getAttributeMap().forEach((k, v) -> attributeMap.merge(k, v, Long::sum));
            attr.getSkillMap().forEach(this::skillAdd);
            this.skillSpecialSet.addAll(attr.getSkillSpecialSet());
            this.buffTriggerList.addAll(attr.getBuffTriggerList());
            this.buffParamEnhanceSet.addAll(attr.getBuffParamEnhanceSet());
        }
        return this;
    }

    public Attribute fixAdd(List<Attribute> attrList) {
        if (CollectionUtils.isNotEmpty(attrList)) {
            for (Attribute attr : attrList) {
                attr.getAttributeMap().forEach((k, v) -> attributeMap.merge(k, v, Long::sum));
                attr.getSkillMap().forEach(this::skillAdd);
                this.skillSpecialSet.addAll(attr.getSkillSpecialSet());
                this.buffTriggerList.addAll(attr.getBuffTriggerList());
                this.buffParamEnhanceSet.addAll(attr.getBuffParamEnhanceSet());
            }
        }

        return this;
    }

    /**
     * @param attributeArray
     * @param career
     * @param addition       额外增加的万分比分子
     * @return
     */
    public Attribute fixAdd(Map<Integer, Long>[] attributeArray, int career, int addition) {
        if (attributeArray == null || attributeArray.length == 0 || Arrays.stream(attributeArray).allMatch(MapUtils::isEmpty)) {
            return this;
        }
        int[] careers = career == RoleConst.Career.COMMON ? new int[]{career} : new int[]{career, RoleConst.Career.COMMON};
        double add = addition / 10000.0 + 1.0;
        int attributeArrayLen = attributeArray.length;
        for (int i : careers) {
            if (attributeArrayLen > i) {
                Map<Integer, Long> attribute = attributeArray[i];
                if (attribute == null) {
                    continue;
                }
                attribute.forEach((k, v) -> attributeMap.merge(k, (long) (v * add), Long::sum));
            }
        }

        return this;
    }

    public Attribute fixAddRate(Map<Integer, Long>[] attributeArray, int career, double rate) {
        if (attributeArray == null || attributeArray.length == 0 || Arrays.stream(attributeArray).allMatch(MapUtils::isEmpty)) {
            return this;
        }
        int[] careers = career == RoleConst.Career.COMMON ? new int[]{career} : new int[]{career, RoleConst.Career.COMMON};
        double add = (rate / 10000);
        int attributeArrayLen = attributeArray.length;
        for (int i : careers) {
            if (attributeArrayLen > i) {
                Map<Integer, Long> attribute = attributeArray[i];
                if (attribute == null) {
                    continue;
                }
                attribute.forEach((k, v) -> attributeMap.merge(k, (long) (v * add), Long::sum));
            }
        }

        return this;
    }

    public Attribute fixAdd(Map<Integer, Map<Integer, Long>> attributeMap, int career, int addition) {
        int[] careers = career == RoleConst.Career.COMMON ? new int[]{career} : new int[]{career, RoleConst.Career.COMMON};
        double add = addition / 10000.0 + 1;
        for (int i : careers) {
            Map<Integer, Long> attribute = attributeMap.get(i);
            if (attribute == null) {
                continue;
            }
            attribute.forEach((k, v) -> this.attributeMap.merge(k, (long) (v * add), Long::sum));
        }

        return this;
    }

    public Attribute fixAdd(Map<Integer, Long>[] attributeArray, int career) {
        return fixAdd(attributeArray, career, 0);
    }

    public Attribute fixAdd(Map<Integer, Long>[] attributeArray) {
        return fixAdd(attributeArray, RoleConst.Career.COMMON, 0);
    }

    public Attribute fixAdd(Map<Integer, Map<Integer, Long>> attributeMap, int career) {
        return fixAdd(attributeMap, career, 0);
    }

    public Attribute fixAdd(EquipAttribute attribute, int career) {
        if (attribute != null) {
            fixAdd(attribute.getAttributes(), career, 0);
        }
        return this;
    }

    public Attribute fixAdd(IdentifyAttribute attribute, int career) {
        if (attribute != null) {
            for (IdentifyAttributeValue attr : attribute.getAttributes().values()) {
                fixAdd(attr.getType(), attr.getValue());
            }
            for (IdentifyAttributeValue attr : attribute.getBuffs().values()) {
                buffTriggerList.add(attr.getType());
            }
        }
        return this;
    }

    /**
     * attribute属性累加
     *
     * @param attribute
     */
    public Attribute fixAdd(Map<Integer, Long> attribute) {
        if (attribute != null) {
            attribute.forEach((k, v) -> this.attributeMap.merge(k, v, Long::sum));
        }
        return this;
    }

    public Attribute fixAdd(int[][] attribute) {
        if (attribute != null) {
            for (int[] entry : attribute) {
                this.attributeMap.merge(entry[0], (long) entry[1], Long::sum);
            }
        }
        return this;
    }

    public Attribute fixAdd(int attr, long value) {
        this.attributeMap.merge(attr, value, Long::sum);
        return this;
    }

    /**
     * attribute属性相减
     */
    public Attribute fixSubtract(Attribute attribute) {
        if (attribute != null) {
            fixSubtract(attribute.getAttributeMap());
        }
        return this;
    }

    /**
     * attribute属性相减
     */
    public Attribute fixSubtract(Map<Integer, Long> attribute) {
        if (attribute != null) {
            attribute.forEach((k, v) -> this.attributeMap.merge(k, v, (v1, v2) -> Math.max(v1 - v2, 0)));
        }
        return this;
    }

    public Attribute skillAdd(int id, int level) {
        if (this.skillMap.getOrDefault(id, 0) < level) {
            this.skillMap.put(id, level);
        }
        return this;
    }

    public Attribute skillSpecialAdd(int id) {
        this.skillSpecialSet.add(id);
        return this;
    }

    public Attribute buffTriggerAdd(int id) {
        if (id > 0) {
            this.buffTriggerList.add(id);
        }
        return this;
    }

    public Attribute buffTriggerAdd(List<Integer> buffList) {
        for (Integer buffId : buffList) {
            buffTriggerAdd(buffId);
        }
        return this;
    }

    public Attribute buffTriggerAdd(int[] buffArray) {
        for (Integer buffId : buffArray) {
            buffTriggerAdd(buffId);
        }
        return this;
    }

    public Attribute buffParamEnhanceAdd(int id) {
        this.buffParamEnhanceSet.add(id);
        return this;
    }

    /**
     * 计算最终结果
     */
    public void calculate(int career,int level) {
        for (Map.Entry<Integer, Long> entry : this.attributeMap.entrySet()) {
            if (entry.getValue() == 0) {
                continue;
            }
            AttributeScoreConfig config = ConfigDataManager.getInstance().getById(AttributeScoreConfig.class, entry.getKey());
            if (config == null) {
                continue;
            }
            int[] addpercent = config.getAddpercent();
            if (addpercent != null) {
                for (int attribute : addpercent) {
                    addExtraAttr(attribute, entry.getValue());
                }
            }
        }
        AttributeEnum.FIGHT_POWER.setAttribute(this, calculateFightPower(career, level));
    }

    public void addExtraAttr(int attribute, long addPercent) {
        AttributeEnum attributeEnum = AttributeEnum.valueOf(attribute);
        if (attributeEnum == null) {
            return;
        }
        long value = attributeEnum.getAttrValue(this);
        if (value <= 0) {
            return;
        }
        attributeEnum.setAttribute(this, Math.round(value * (10000 + addPercent) / 10000D));
    }

    public long calculateFightPower(int career, int level) {
        Map<Integer, Double> scoreMap = ConfigCacheManager.getInstance().getCache(AttributeScoreCache.class).getAttrScoreMapByCareer(career);
        double power = 0D;
        for (Map.Entry<Integer, Long> entry : this.attributeMap.entrySet()) {
            if (!scoreMap.containsKey(entry.getKey())) {
                continue;
            }
            AttributeScoreConfig config = ConfigDataManager.getInstance().getById(AttributeScoreConfig.class, entry.getKey());
            if (config == null) {
                continue;
            }
            power += (scoreMap.get(entry.getKey()) * entry.getValue() * (level + config.getLevelParam()));
        }

        return Math.round(power);
    }

    /**
     * @param cur 当前属性
     * @param ori 旧的属性
     *            移除的buff
     */
    public static TwoTuple<List<Integer>, List<Integer>> compareBuff(Attribute cur, Attribute ori) {
        List<Integer> addList = new ArrayList<>();
        List<Integer> removeList = new ArrayList<>();
        TwoTuple<List<Integer>, List<Integer>> tuple = new TwoTuple<>(addList, removeList);
        if (ori != null) {
            ori.getBuffTriggerList().forEach(buffId -> {
                if (!cur.getBuffTriggerList().contains(buffId)) {
                    removeList.add(buffId);
                }
            });
        }

        cur.getBuffTriggerList().forEach(buffId -> {
            if (ori == null || !ori.getBuffTriggerList().contains(buffId)) {
                addList.add(buffId);
            }
        });

        return tuple;
    }

    /**
     * @param cur 当前属性
     * @param ori 旧的属性
     * @return 变化的属性，mapValue为属性当前值
     */
    public static Map<Integer, Long> compare(Attribute cur, Attribute ori) {
        Map<Integer, Long> changes = new HashMap<>();
        if (cur != null) {
            cur.getAttributeMap().forEach((k, v) -> {
                if (ori == null || !v.equals(ori.getAttributeMap().get(k))) {
                    changes.put(k, v);
                }
            });
        }
        if (ori != null) {
            ori.getAttributeMap().forEach((k, v) -> {
                if (cur == null || !cur.getAttributeMap().containsKey(k)) {
                    changes.put(k, 0L);
                }
            });
        }

        AttributeScoreCache cache = ConfigCacheManager.getInstance().getCache(AttributeScoreCache.class);
        changes.entrySet().removeIf(entry -> entry.getKey() > 10000 && !cache.getPercentAttrs().contains(entry.getKey()));
        return changes;
    }

    public static Map<Integer, Long> mgCompare(Attribute cur, Attribute ori) {
        Map<Integer, Long> changes = new HashMap<>();
        for (AttributeEnum mgAttr : AttributeEnum.getMgAttrSet()) {
            long curValue = mgAttr.getAttrValue(cur);
            long oriValue = mgAttr.getAttrValue(ori);
            long diffValue = curValue - oriValue;
            if (diffValue != 0) {
                changes.put(mgAttr.getType(), diffValue);
            }
        }
        return changes;
    }

    public TwoTuple<Integer, Integer> getAtkByCareer(int career) {
        int atkMin = 0, atkMax = 0;
        if (career == RoleConst.Career.ZHAN) {
            atkMin = (int) (findPhyAtk());
            atkMax = (int) (findPhyAtkMax());
        } else if (career == RoleConst.Career.FA) {
            atkMin = (int) (findMagicAtk());
            atkMax = (int) (findMagicAtkMax());
        } else if (career == RoleConst.Career.DAO) {
            atkMin = (int) (findTaoAtk());
            atkMax = (int) (findTaoAtkMax());
        }
        atkMin += get(AttributeEnum.ATK_MODAO_MIN);
        atkMax += get(AttributeEnum.ATK_MODAO_MAX);
        return new TwoTuple<>(atkMin, atkMax);
    }

    public List<AbcProtos.RandAttributeBean> buildRandAttributeBean() {
        List<AbcProtos.RandAttributeBean> list = new ArrayList<>();
        for (AttributeEnum anEnum : AttributeEnum.values()) {
            AttributeScoreConfig scoreConfig = ConfigDataManager.getInstance().getById(AttributeScoreConfig.class, anEnum.getType());
            if (scoreConfig == null || scoreConfig.getTransmit() != 1) {
                continue;
            }
            long attrValue = anEnum.getAttrValue(this);
            if (attrValue > 0) {
                AbcProtos.RandAttributeBean.Builder bean = AbcProtos.RandAttributeBean.newBuilder();
                bean.setAttributeType(anEnum.getType());
                bean.setAttributeValue(attrValue);
                list.add(bean.build());
            }
        }
        return list;
    }
}
