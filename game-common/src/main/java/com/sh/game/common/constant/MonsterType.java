package com.sh.game.common.constant;

/**
 * 怪物类型
 *
 * <AUTHOR>
 * 2017年6月6日 下午9:33:51
 */
public interface MonsterType {

    /**
     * 普通怪
     */
    int NORMAL = 1;

    /**
     * 静态怪
     */
    int STATIC = 3;

    /**
     * 普通boss
     */
    int COMMON_BOSS = 4;

    /**
     * 暂时只有世界boss
     */
    int WORLD_BOSS = 5;

    /**
     * 采集怪,消耗的道具固定
     */
    int COLLECT = 7;

    /**
     * 技能召唤怪
     */
    int SKILL_CALL = 9;

    /**
     * 道具召唤怪
     */
    int ITEM_CALL = 10;

    /**
     * 精英
     */
    int JING_YING = 17;

    /**
     * 新增采集怪
     */
    int COLLECT_NEW = 18;

    /**
     * 侠侣
     */
    int XIALV = 19;

    /**
     * 超级BOOS
     */
    int SUPER_BOOS = 20;

    /**
     * 攻城怪物
     */
    int CITY_BOSS = 22;

    /**
     * 通天之路怪物
     */
    int BABEL_ROAD_BOSS = 23;

    /**
     * 镖车
     */
    int BIAO_CHE = 6;

    /**
     * 特殊怪物-1
     */
    int SPECIAL_ONE = 110;

    /**
     * 特殊怪物-2
     */
    int SPECIAL_TWO = 111;

    /**
     * 特殊怪物-3
     */
    int SPECIAL_THREE = 112;

    /**
     * 特殊怪物-4
     */
    int SPECIAL_FOUR = 113;

    /**
     * 特殊怪物-5
     */
    int SPECIAL_FIVE = 114;

    /**
     * 特殊怪物-6
     */
    int SPECIAL_SIX = 115;

    /**
     * 特殊怪物-7
     */
    int SPECIAL_SEVEN = 116;

    /**
     * 特殊怪物-8
     */
    int SPECIAL_EIGHT = 117;

    /**
     * 特殊怪物-9
     */
    int SPECIAL_NINE = 118;

    /**
     * 特殊怪物-10
     */
    int SPECIAL_TEN = 119;

    /**
     * 特殊怪物-11
     */
    int SPECIAL_ELEVEN = 120;

    /**
     * 特殊怪物-12
     */
    int SPECIAL_TWELVE = 121;

    /**
     * 特殊怪物-13
     */
    int SPECIAL_THIRTEEN = 122;

    /**
     * 特殊怪物-14
     */
    int SPECIAL_FOURTEEN = 123;

    /**
     * 特殊怪物-15
     */
    int SPECIAL_FIFTEEN = 124;

    /**
     * 特殊怪物-16
     */
    int SPECIAL_SIXTEEN = 125;

    /**
     * 特殊怪物-17
     */
    int SPECIAL_SEVENTEEN = 126;

    /**
     * 特殊怪物-18
     */
    int SPECIAL_EIGHTEEN = 127;

    /**
     * 特殊怪物-19
     */
    int SPECIAL_NINETEEN = 128;

    /**
     * 特殊怪物-20
     */
    int SPECIAL_TWENTY = 129;

    /**
     * 特殊怪物-21
     */
    int SPECIAL_TWENTY_ONE = 130;

    /**
     * 特殊怪物-22
     */
    int SPECIAL_TWENTY_TWO = 131;

    /**
     * 特殊怪物-23
     */
    int SPECIAL_TWENTY_THREE = 132;

    /**
     * 特殊怪物-24
     */
    int SPECIAL_TWENTY_FOUR = 133;

    /**
     * 事件召唤怪物
     */
    int EVENT_CALL = 134;
}
