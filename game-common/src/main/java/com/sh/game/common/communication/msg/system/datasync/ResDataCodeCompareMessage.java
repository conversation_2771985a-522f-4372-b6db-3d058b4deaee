package com.sh.game.common.communication.msg.system.datasync;

import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ResDataCodeCompareMessage extends ProtobufMessage {

    private com.sh.game.protos.DatasyncProtos.ResDataCodeCompare proto;

    private com.sh.game.protos.DatasyncProtos.ResDataCodeCompare.Builder builder;

	
	@Override
	public int getId() {
		return 172002;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.DatasyncProtos.ResDataCodeCompare.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.DatasyncProtos.ResDataCodeCompare.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.DatasyncProtos.ResDataCodeCompare.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.DatasyncProtos.ResDataCodeCompare getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.DatasyncProtos.ResDataCodeCompare proto) {
        this.proto = proto;
    }

}
