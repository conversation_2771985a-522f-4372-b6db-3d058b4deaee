package com.sh.game.common.config.converter;

import com.sh.common.config.IConverter;

public class PointConverter implements IConverter {

    @Override
    public Object convert(Object arg) {
        int[] p = {0, 0};

        String source = (String) arg;
        if (source == null || source.trim().isEmpty()) {
            return p;
        }

        String[] outerArray = source.split("#");
        if (outerArray.length >= 1) {
            p[0] = Integer.parseInt(outerArray[0]);
        }
        if (outerArray.length >= 2) {
            p[1] = Integer.parseInt(outerArray[1]);
        }

        return p;
    }
}
