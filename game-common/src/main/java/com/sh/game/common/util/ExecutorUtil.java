package com.sh.game.common.util;

import com.sh.concurrent.QueueExecutor;

import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 */
public class ExecutorUtil {
    public static final int COMMON_DRIVER_COUNT = 16;

    public static final ScheduledExecutorService COMMON_LOGIC_EXECUTOR = Executors.newScheduledThreadPool(4, new ThreadFactory() {

        AtomicInteger count = new AtomicInteger(0);

        @Override
        public Thread newThread(Runnable r) {
            int curCount = count.incrementAndGet();
            return new Thread(r, "公共业务线程（线程池）-" + curCount);
        }
    });

    public static final ScheduledExecutorService EVENT_DISPATCHER_EXECUTOR = Executors.newScheduledThreadPool(4,
            new ThreadFactory() {
                AtomicInteger count = new AtomicInteger(0);

                @Override
                public Thread newThread(Runnable r) {
                    int curCount = count.incrementAndGet();
                    return new Thread(r, "场景定时事件派发线程-" + curCount);
                }
            });

    /**
     * 游戏驱动主线程池
     */
    public static final QueueExecutor[] COMMON_DRIVER_EXECUTOR = new QueueExecutor[COMMON_DRIVER_COUNT];
    static {
        for (int i = 0; i < COMMON_DRIVER_COUNT; i++) {
            final int index = i + 1;
            COMMON_DRIVER_EXECUTOR[i] = new QueueExecutor("场景公共驱动线程" + index, 1, 1);
        }
    }

    /**
     * 玩家驱动主线程池
     */
    public static final QueueExecutor PLAYER_DRIVER_EXECUTOR = new QueueExecutor("玩家主逻辑驱动线程",16,16);

    /**
     * 游戏登录线程池
     */
    public static final QueueExecutor SERVER_AUTH_EXECUTOR = new QueueExecutor("登录线程", 1, 1);

    /**
     * 游戏内部逻辑处理线程
     */
    public static final QueueExecutor SERVER_COMMON_EXECUTOR = new QueueExecutor("内部逻辑处理线程", 1, 1);

    /**
     * 游戏社交逻辑线程
     */
    public static final QueueExecutor SERVER_SOCIAL_EXECUTOR = new QueueExecutor("社交逻辑线程", 1, 1);

    /**
     * 场景公共处理线程
     */
    public static final QueueExecutor MAP_COMMON_EXECUTOR = new QueueExecutor("场景公共处理线程",1,1);


    //===============对Executor的方法包装==========================================

    /**
     * 定时事件
     *
     * @param command      具体要执行的内容
     * @param initialDelay 延迟时间
     * @param period       间隔
     *                     默认单位为毫秒
     * @return ScheduledFuture
     */
    public static ScheduledFuture<?> scheduleAtFixedRate(Runnable command, long initialDelay, long period) {
        return COMMON_LOGIC_EXECUTOR.scheduleAtFixedRate(command, initialDelay, period, TimeUnit.MILLISECONDS);
    }

    public static ScheduledFuture<?> scheduleAtFixedRate(Runnable command, long initialDelay, long period,
                                                         TimeUnit unit) {
        return COMMON_LOGIC_EXECUTOR.scheduleAtFixedRate(command, initialDelay, period, unit);
    }

    public static ScheduledFuture<?> scheduleWithFixedDelay(Runnable command, long initialDelay, long period,
                                                            TimeUnit unit) {
        return COMMON_LOGIC_EXECUTOR.scheduleWithFixedDelay(command, initialDelay, period, unit);
    }

    public static ScheduledFuture<?> schedule(Runnable command, long delay, TimeUnit unit) {
        return COMMON_LOGIC_EXECUTOR.schedule(command, delay, unit);
    }

    public static Future<?> submit(Runnable task) {
        return COMMON_LOGIC_EXECUTOR.submit(task);
    }

}
