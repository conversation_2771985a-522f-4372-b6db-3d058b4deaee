package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.list.ShuXianIntegerListConverter;

import java.util.List;

/**
 * 怪物是否需要同步状态
 *
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2021-11-04
 **/
@lombok.Getter
@lombok.Setter
@ConfigData(file = "cfg_monster_count", keys = "monstertype")
public class MonsterCountConfig extends AbstractConfigData {
    /**
     * id
     */
    private int id;

    /**
     * 怪物类型
     */
    private int monstertype;

    /**
     * map表的cls
     */
    @ConfigField(converter = ShuXianIntegerListConverter.class)
    private List<Integer> mapcls;

    /**
     * 是否同步数量
     */
    private int numcount;

    /**
     * 是否同步状态
     */
    private int state;
}
