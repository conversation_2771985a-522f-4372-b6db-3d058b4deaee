package com.sh.game.common.config.converter.list;

import com.sh.common.config.IConverter;
import com.sh.commons.util.Symbol;

import java.util.*;

/**
 * key=value&key2=val2 转换成 Map<String, String>
 *
 * <AUTHOR>
 */
public class KeyValueYuHaoListConverter implements IConverter {
    @Override
    public Map<String, String> convert(Object source) {
        String strVlaue = (String) source;
        Map<String, String> retMap = new HashMap<>();
        String[] outers  = strVlaue.split(Symbol.AND);
        for(String outer : outers) {
            List<int[]> list1 = new ArrayList<>();
            String[] allArray = outer.split(Symbol.DENGHAO);
            if (allArray.length > 0){
                if (allArray.length == 1) {
                    retMap.put(allArray[0], "");
                }else {
                    retMap.put(allArray[0], allArray[1]);
                }
            }
        }
        return retMap;
    }
}
