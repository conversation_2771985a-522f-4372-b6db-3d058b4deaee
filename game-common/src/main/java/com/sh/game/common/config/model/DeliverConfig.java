package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.ConditionConvert;
import com.sh.game.common.config.converter.list.JinHaoAndShuXianListConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 传送
 */

@Getter
@Setter
@ConfigData(file = "cfg_deliver")
public class DeliverConfig extends AbstractConfigData {
	/**
	 * id
	 */
	private int id;

	/**
	 * 目标场景
	 */
	private int toMapId;

	private int mapcondition;

	/**
	 * 传送位置
	 */
	@ConfigField(converter = JinHaoAndShuXianListConverter.class)
	private List<int[]> position;

	/**
	 * 目标npcID(如果toMapId==0，那么就传送到toNpcId)
	 */
	private int toNpcId;

	/**
	 * 消耗小飞鞋
	 */
	private int costXFX;

	/**
	 * 传送消耗
	 */
	@ConfigField(converter = JinHaoAndYuHaoListConverter.class)
	private List<int[]> cost;


	/**
	 * 是否不可以通过请求传送
	 */
	private int buttonMove;

	@ConfigField(converter = ConditionConvert.class)
	private List<int[]> replacecondition;

	private int replace;
}
