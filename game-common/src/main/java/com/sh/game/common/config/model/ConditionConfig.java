package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
@ConfigData(file = "cfg_conditions", keys = "type")
public class ConditionConfig extends AbstractConfigData {

    private int type;

    private String maxtip;

    private String alltip;

    private String twotip;
}
