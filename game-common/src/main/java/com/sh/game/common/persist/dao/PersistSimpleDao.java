package com.sh.game.common.persist.dao;

import com.sh.common.jdbc.JdbcTemplate;
import com.sh.common.jdbc.ProtostuffRowMapper;
import com.sh.common.jdbc.RowMapper;
import com.sh.common.persist.Persistable;
import com.sh.game.common.persist.PersistDao;
import com.sh.game.common.sync.SyncData;
import com.sh.game.common.sync.SyncDataRowMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/7/7.
 */
public class PersistSimpleDao extends PersistDao {

    public PersistSimpleDao(JdbcTemplate jdbcTemplate, Class<? extends Persistable> clazz) {
        super(jdbcTemplate, clazz);
    }


    @Override
    public String buildInsterSql() {
        return "INSERT INTO " + tableName + " (id, data) VALUES (?, ?) " +
                "ON DUPLICATE KEY UPDATE id=VALUES(id), data=VALUES(data);";
    }

    @Override
    public String buildUpdateSql() {
        return "INSERT INTO " + tableName + " (id, data) VALUES (?, ?) " +
                "ON DUPLICATE KEY UPDATE id=VALUES(id), data=VALUES(data);";
    }

    @Override
    public String buildDeleteSql() {
        return "delete  from " +
                tableName + " where id = ?";
    }

    @Override
    public String buildSelectByIdSql() {
        return "select data from " + tableName + " where id = ?";
    }

    @Override
    public void ddlCreateTable() {
        String sqlStr = "CREATE TABLE IF NOT EXISTS  `" + tableName + "`(" +
                "  `id` bigint(20) NOT NULL," +
                "  `data` mediumblob" +
                ", PRIMARY KEY (`id`)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='基于自动建表' ;";
        jdbcTemplate.update(sqlStr);
    }

    @Override
    public Object[] createInsertParameters(Persistable persistable) {
        return new Object[]{persistable.getId(), serializer(persistable)};

    }

    @Override
    public Object[] createUpdateParameters(Persistable persistable) {
        return new Object[]{persistable.getId(), serializer(persistable)};
    }

    @Override
    public Object[] createDeleteParameters(Persistable persistable) {

        return new Object[]{persistable.getId()};

    }

    @Override
    public <T extends Persistable> RowMapper<T> selectRowMapper() {
        if (SyncData.class.isAssignableFrom(this.getEntity())) {
            return new SyncDataRowMapper(this.getEntity());
        }

        return new ProtostuffRowMapper(this.getEntity());
    }

    @Override
    public <T extends Persistable> RowMapper<T> selectRowMapper(Class<T> clazz) {
        if (SyncData.class.isAssignableFrom(clazz)) {
            return new SyncDataRowMapper(clazz);
        }
        return new ProtostuffRowMapper(clazz);

    }

    public <T> List<T> queryAll() {
        String sql = "select data from " + getTableName();
        return (List<T>) jdbcTemplate.queryList(sql, selectRowMapper());
    }

    public List<Long> queryAllId() {
        String sql = "select id from " + getTableName();
        return jdbcTemplate.queryList(sql, JdbcTemplate.LONG_MAPPER);
    }
}
