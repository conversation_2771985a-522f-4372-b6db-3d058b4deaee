package com.sh.game.common.communication.msg.system.climbTower;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求转盘数据</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqClimbTowerTurnMessage extends ProtobufMessage {

    private com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn proto;

    private com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.Builder builder;

	
	@Override
	public int getId() {
		return 309008;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.ClimbtowerProtos.ReqClimbTowerTurn proto) {
        this.proto = proto;
    }

}
