package com.sh.game.common.communication.msg.system.team;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>队员离线通知</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toClient")
public class ResPlayerGroupChangeMessage extends ProtobufMessage {

    private com.sh.game.protos.TeamProtos.ResPlayerGroupChange proto;

    private com.sh.game.protos.TeamProtos.ResPlayerGroupChange.Builder builder;

	
	@Override
	public int getId() {
		return 101105;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TeamProtos.ResPlayerGroupChange.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TeamProtos.ResPlayerGroupChange.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TeamProtos.ResPlayerGroupChange.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TeamProtos.ResPlayerGroupChange getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TeamProtos.ResPlayerGroupChange proto) {
        this.proto = proto;
    }

}
