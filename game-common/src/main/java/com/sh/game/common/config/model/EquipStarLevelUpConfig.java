package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.list.JinHaoAndYuHaoListConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020/8/17 19:34
 */
@Getter
@Setter
@ConfigData(file = "cfg_upstar", keys = "level#functionType")
public class EquipStarLevelUpConfig extends AbstractConfigData {

    /**
     * 等级
     */
    private int level;

    /**
     * 装备功能类型
     */
    private int functionType;

    /**
     * 消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> cost;

    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attribute;

    /**
     * 成功概率
     */
    private int chance;

    /**
     * 失败是否锁星
     */
    private int reduce;

    /**
     * 可选消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> unlockstar;

    /**
     * 传承消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoListConverter.class)
    private List<int[]> transfercost;

    /**
     *
     */
    private int bless;
}
