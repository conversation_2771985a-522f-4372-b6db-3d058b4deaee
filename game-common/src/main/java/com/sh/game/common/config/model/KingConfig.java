package com.sh.game.common.config.model;

import com.sh.common.config.AbstractConfigData;
import com.sh.common.config.annotation.ConfigData;
import com.sh.common.config.annotation.ConfigField;
import com.sh.game.common.config.converter.CareerAttributeArrayConverter;
import com.sh.game.common.config.converter.JinHaoAndYuHaoMapIntLongConverter;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * 帝王系统配置
 *
 * <AUTHOR>
 * @date 2022/10/18 16:01
 */
@Getter
@Setter
@ConfigData(file = "cfg_heart")
public class KingConfig extends AbstractConfigData {

    private int id;

    /**
     * 大类
     */
    private int type;

    /**
     * 小类
     */
    private int subType;

    /**
     * 等级
     */
    private int lv;

    /**
     * 升级消耗
     */
    @ConfigField(converter = JinHaoAndYuHaoMapIntLongConverter.class)
    private Map<Integer, Long> cost;

    /**
     * 属性
     */
    @ConfigField(converter = CareerAttributeArrayConverter.class)
    private Map<Integer, Long>[] attribute;

    /**
     * 下一级id
     */
    private int nextId;
}
