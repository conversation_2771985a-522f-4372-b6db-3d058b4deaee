package com.sh.game.common.communication.msg.system.teleport;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>请求传送（通过deliver配置）</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toLogic")
public class ReqTeleportMessage extends ProtobufMessage {

    private com.sh.game.protos.TeleportProtos.ReqTeleport proto;

    private com.sh.game.protos.TeleportProtos.ReqTeleport.Builder builder;

	
	@Override
	public int getId() {
		return 174001;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.TeleportProtos.ReqTeleport.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.TeleportProtos.ReqTeleport.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.TeleportProtos.ReqTeleport.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.TeleportProtos.ReqTeleport getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.TeleportProtos.ReqTeleport proto) {
        this.proto = proto;
    }

}
