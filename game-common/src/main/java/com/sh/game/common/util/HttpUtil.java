package com.sh.game.common.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.sh.commons.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

@Slf4j
public class HttpUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(HttpUtil.class);

    public static String post(String url, Map<String, Object> params, Map<String, String> headers, int timeout) {
        CloseableHttpClient client = null;
        CloseableHttpResponse ret = null;
        try {
            client = HttpClients.createDefault();
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(timeout)
                    .setConnectTimeout(timeout)
                    .setConnectionRequestTimeout(timeout).setRedirectsEnabled(false).build();
            HttpPost post = new HttpPost(url);
            post.setConfig(requestConfig);
            List<NameValuePair> formParams = new ArrayList<>();

            for (String key : params.keySet()) {
                Object value = params.get(key);
                if (value != null) {
                    formParams.add(new BasicNameValuePair(key, String.valueOf(value)));
                }
            }

            if (headers != null && headers.size() > 0) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    post.addHeader(entry.getKey(), entry.getValue());
                }
            }

            UrlEncodedFormEntity entity = new UrlEncodedFormEntity(formParams, Consts.UTF_8);
            post.setEntity(entity);
            ret = client.execute(post);
            HttpEntity response = ret.getEntity();
            String str = EntityUtils.toString(response, Consts.UTF_8);
            return str;
        } catch (Exception e) {
            LOGGER.error("请求失败,url:" + url + ",params:" + params, e);
            return null;
        } finally {

            if (ret != null) {
                try {
                    ret.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 该接口 body为字符串 不适用json
     * body提交的数据会按照 k1=v1&k2=v2 的方式进行编码
     *
     * @param url
     * @param params
     * @return
     */
    public static String postStringBody(String url, Map<String, Object> params, int timeOut) {
        CloseableHttpClient client = null;
        CloseableHttpResponse ret = null;
        try {
            client = HttpClients.createDefault();
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(timeOut).setConnectTimeout(timeOut).setConnectionRequestTimeout(timeOut).setRedirectsEnabled(false).build();
            HttpPost post = new HttpPost(url);
            post.setConfig(requestConfig);
            List<String> kvList = Md5Util.getKVList(params);
            String requestBody = StringUtils.join(kvList, "&");
            StringEntity entity = new StringEntity(requestBody, Consts.UTF_8);
            entity.setContentEncoding("UTF-8");
            post.setEntity(entity);
            ret = client.execute(post);
            HttpEntity response = ret.getEntity();
            return EntityUtils.toString(response, Consts.UTF_8);
        } catch (Exception e) {
            LOGGER.error("请求失败,url:" + url + ",params:" + params, e);
            return null;
        } finally {

            if (ret != null) {
                try {
                    ret.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static String post(String url, Map<String, Object> params, Map<String, String> headers, String contentTypeStr, int timeout) {
        CloseableHttpClient client = null;
        CloseableHttpResponse ret = null;
        try {
            client = HttpClients.createDefault();
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(timeout).setConnectTimeout(timeout).setConnectionRequestTimeout(timeout).setRedirectsEnabled(false).build();
            HttpPost post = new HttpPost(url);
            post.setConfig(requestConfig);

            if (headers != null && headers.size() > 0) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    post.addHeader(entry.getKey(), entry.getValue());
                }
            }
            String jsonStr = JSON.toJSONString(params);
            StringEntity entity = new StringEntity(jsonStr, Consts.UTF_8);
            entity.setContentEncoding("UTF-8");
            entity.setContentType(contentTypeStr);//发送json数据需要设置contentType
            post.setEntity(entity);
            ret = client.execute(post);
            HttpEntity response = ret.getEntity();
            return EntityUtils.toString(response, Consts.UTF_8);
        } catch (Exception e) {
            LOGGER.error("请求失败,url:" + url + ",params:" + params, e);
            return null;
        } finally {

            if (ret != null) {
                try {
                    ret.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static String post(String url, Map<String, Object> params, Map<String, String> headers, String contentTypeStr) {
      return post(url, params, headers, contentTypeStr, 10000);
    }

    public static String post(String url, Map<String, Object> params) {
        return post(url, params, null, 10000);
    }

    public static String post(String url, Map<String, Object> params, int timeout) {
        return post(url, params, null, timeout);
    }

    public static String get(String url, Map<String, Object> params) {
        return get(url, params, 10000);
    }

    public static String get(String url, Map<String, Object> params, int timeout) {
        CloseableHttpClient client = null;
        CloseableHttpResponse ret = null;
        try {
            client = HttpClients.createDefault();
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(timeout)
                    .setConnectTimeout(timeout).setConnectionRequestTimeout(timeout).setRedirectsEnabled(false).build();
            StringBuilder builder = new StringBuilder(url);
            builder.append("?");
            for (String key : params.keySet()) {
                Object value = params.get(key);
                if (value != null) {
                    builder.append(key);
                    builder.append("=");
                    builder.append(value);
                    builder.append("&");
                }
            }
            System.out.println(builder.toString());
            String req = builder.toString();
            String realReq = req.substring(0, req.length() - 1);
            HttpGet get = new HttpGet(realReq);
            get.setConfig(requestConfig);
            ret = client.execute(get);
            HttpEntity response = ret.getEntity();
            String str = EntityUtils.toString(response, Consts.UTF_8);
            return str;
        } catch (Exception e) {
            LOGGER.error("请求失败,url:" + url + ",params:" + params, e);
            return null;
        } finally {


            if (ret != null) {
                try {
                    ret.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (client != null) {
                try {
                    client.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 使用 Map按key进行排序后拼接a=1&b=2
     * @param params
     * @return
     */
    public static String paramStr(Map<String, ? extends Object> params, String flag) {
        return paramStr(params, flag, Sets.newHashSet(), false);
    }

    /**
     * 使用 Map按key进行排序后拼接a=1&b=2
     *
     * @param params
     * @return
     */
    public static String paramStr(Map<String, ? extends Object> params, String flag, boolean urlEncode) {
        return paramStr(params, flag, Sets.newHashSet(), urlEncode);
    }

    public static String paramStr(Map<String, ? extends Object> params, String flag, Set<String> excludeKeys) {
        return paramStr(params, flag, excludeKeys, false);
    }

    public static String paramStr(Map<String, ? extends Object> params, String flag, Set<String> excludeKeys, boolean urlEncode) {
        StringBuilder paramStr = new StringBuilder();

        TreeMap<String, Object> sortMap = new TreeMap<>(Comparator.naturalOrder());
        sortMap.putAll(params);

        try {
            Set<String> keySet = sortMap.keySet();
            Iterator<String> iter = keySet.iterator();
            while (iter.hasNext()) {
                String key = iter.next();
                if (excludeKeys.contains(key)) {
                    continue;
                }
                if ("sign".equals(key)) {
                    continue;
                }
                if ("qudao".equals(key)) {
                    continue;
                }
                if ("sig".equals(key)){
                    continue;
                }
                if ("memo".equals(key)){
                    continue;
                }
                if ("body".equals(key)){
                    if (sortMap.get(key) == null || sortMap.get(key).equals("")){
                        continue;
                    }
                }
                if ("outTradeNo".equals(key)){
                    if (sortMap.get(key) == null || sortMap.get(key).equals("")){
                        continue;
                    }
                }
                if (urlEncode) {
                    paramStr.append(URLEncoder.encode(key, "utf-8")).append("=").append(URLEncoder.encode(String.valueOf(sortMap.get(key)), "utf-8"));
                } else {
                    paramStr.append(key).append("=").append(sortMap.get(key));
                }
                if (!com.sh.commons.util.StringUtil.isEmpty(flag)) {
                    paramStr.append(flag);
                }
            }
            return paramStr.substring(0, StringUtil.isEmpty(flag) ? paramStr.length() : (paramStr.length() - 1));
        } catch (Exception e) {
            log.error("", e);
            return "";
        }
    }

    public static String paramValueStr(Map<String, ? extends Object> params, String flag) {
        StringBuilder paramStr = new StringBuilder();

        TreeMap<String, Object> sortMap = new TreeMap<>(Comparator.naturalOrder());
        sortMap.putAll(params);

        Set<String> keySet = sortMap.keySet();
        Iterator<String> iter = keySet.iterator();
        while (iter.hasNext()) {
            String key = iter.next();
            if ("sign".equals(key)) {
                continue;
            }
            if ("qudao".equals(key)) {
                continue;
            }
            paramStr.append(sortMap.get(key));

            if (!StringUtil.isEmpty(flag)) {
                paramStr.append(flag);
            }
        }

        return paramStr.substring(0, StringUtil.isEmpty(flag) ? paramStr.length() : (paramStr.length() - 1));
    }

    public static String paramValueStr(Map<String, ? extends Object> params) {
      return paramValueStr(params, null);
    }

    public static String paramStr(Map<String, String> params) {
        return paramStr(params, null);
    }

    public static void main(String[] args) throws IOException {
//        System.out.println(TimeUtil.getNowOfMills());
////        String uuid = StringUtils.remove(UUID.randomUUID().toString(), '-');
//        String uuid = "72451fef49f348949072578a1f62d0ee";
//        String url = "https://payment.uc.cn/open/trade/precreate";
////        String notifyUrl = "http://pay.zlzz.app.9125flying.com:9898/pay/998?serverId=" + 1;
//        String notifyUrl = "http://pay.zlzz.app.9125flying.com:9898/pay/998?serverId=1";
//        String uid = "open_id";
//        String orderId = String.valueOf(IDUtil.getId(IDConst.ORDER));
//        //加密参数
//        Map<String, Object> paramsMapTemp = new HashMap<>();
//        paramsMapTemp.put("biz_order_id", "72451fef49f348949072578a1f62d0e1");
//        paramsMapTemp.put("ip", "127.0.0.1");
//        paramsMapTemp.put("pay_type", "101");
//        paramsMapTemp.put("total_amount", 1);
//        paramsMapTemp.put("notify_url", notifyUrl);
//        paramsMapTemp.put("platform", "ANDROID");
//        paramsMapTemp.put("user_id", "open_id");
//        paramsMapTemp.put("title", "1元充值档");
//        paramsMapTemp.put("extra", "1_1");
//
//        //通用参数
//        Map<String, Object> paramsMap = Maps.newHashMap();
//        paramsMap.put("biz_id", "e346fa6dd04a4ff29fd932be326f4835");
//        paramsMap.put("client_id", "e346fa6dd04a4ff29fd932be326f4835");
//        paramsMap.put("nonce_str", "82451fef49f348949072578a1f62d0ee");
//        paramsMap.put("sign_type", "MD5");
//        paramsMap.put("version", "1.0");
//        paramsMap.put("timestamp", 1585817742321L);
////        String encode = "%7B%22total_amount%22%3A1%2C%22user_id%22%3A%22open_id%" +
////                "22%2C%22ip%22%3A%22127.0.0.1%22%2C%22extra%22%3A%221_1%22%2C%22pay_type" +
////                "%22%3A%22101%22%2C%22notify_url%22%3A%22http%3A%2F%2Fpay.zlzz.app.9125flying.com" +
////                "%3A9898%2Fpay%2F998%3FserverId%3D1%22%2C%22title%22%3A%221%E5%85%83%E5%85%85%E5%80" +
////                "%BC%E6%A1%A3%22%2C%22biz_order_id%22%3A%2272451fef49f348949072578a1f62d0ee%22%2C%" +
////                "22platform%22%3A%22ANDROID%22%7D";
//        String encode = URLEncoder.encode(JSON.toJSONString(paramsMapTemp), "utf-8");
//        paramsMap.put("biz_content", encode);
//        String sign = Md5Util.md5("42f8cbae20844bd1a216db11f6bba935", paramsMap);
//        paramsMap.put("sign", sign);
//
//        String ret = HttpUtil.postStringBody(url, paramsMap, 10000);
//        log.info("uc小游戏订单生成结果：{}", ret);
//        JSONObject jsonObject = JSONObject.parseObject(ret);
//        int code = jsonObject.getInteger("code");
//        if (code == 2000000) {
//            String data = jsonObject.getString("data");
//            JSONObject jsonObject1 = JSONObject.parseObject(data);
//            String trade_id = jsonObject1.getString("trade_id");
//        } else {
//            log.error("uc小游戏获取cp方订单号异常,结果码:【{}】", code);
//        }


//        String str = "qudao=998&biz_content=%7B%22third_buyer_id%22%3A%22oc8WyvzufveRO2HVupLBiV4ZE_hA%22%2C%22trade_id%22%3A%22150177176407498289%22%2C%22total_amount%22%3A100%2C%22extra%22%3A%2211_b3842101353a46d1b2fdfce3a2e15f1a_1%22%2C%22trade_pay_time%22%3A%222020-04-08+19%3A41%3A09%22%2C%22trade_status%22%3A%22SUCCESS%22%2C%22pay_type%22%3A%22111%22%2C%22expired_at%22%3A%222020-04-08+21%3A40%3A58%22%2C%22title%22%3A%221%E5%85%83%E6%A1%A3%E9%A6%96%E5%85%85%22%2C%22biz_order_id%22%3A%222702161087177050113%22%7D&nonce_str=53000194371d42da8fce8b757ae60dfe&sign=148af6f10ee714615f9e0bba45b05f03&biz_id=e346fa6dd04a4ff29fd932be326f4835&serverId=1&sign_type=MD5&version=1.0&client_id=d2563f18581046fbbb452dd6af295b79&timestamp=1586346070551&";
//        String[] fields = str.split("&");
//        Map<String, Object> fieldMap = new HashMap<>();
//        for (String field : fields) {
//            String[] pair = field.split("=");
//            if (pair.length != 2) {
//                continue;
//            }
//            // sign 字段不参与校验，空字段不校验
//            if (!"sign".equals(pair[0]) && StringUtils.isNotBlank(pair[1]) &&
//                    !"serverId".equals(pair[0]) && !"998".equals(pair[1])) {
//                fieldMap.put(pair[0], pair[1]);
//            }
//        }
//        System.out.println("111");
//
//        String md5 = Md5Util.md5("42f8cbae20844bd1a216db11f6bba935", fieldMap);
//        System.out.println("md5:" + md5);
//        System.out.println("sign:" + "148af6f10ee714615f9e0bba45b05f03");
//        System.out.println(11);


//        for (int i=0;i<10;i++){
//                Map<String,Object> params = new HashMap<>();
//                long rid = Long.valueOf("23242423"+RandomUtil.random(1,100000));
//                int platformId = 147+RandomUtil.random(1,4);
//                int sid = 1001+ RandomUtil.random(1,4);
//                String name = "adad"+RandomUtil.random(1,6568689);
//                int matchGroup = 1+RandomUtil.random(1,2);
//                int score = 15+RandomUtil.random(1,10);
//                String summary = "1武器eqwde";
//                int nowOfSeconds = TimeUtil.getNowOfSeconds();
//                int random = RandomUtil.random(1, 4);
//                String sign = Md5Util.md5(new StringBuilder("c3d3f3b71ad85d8")
//                        .append(rid)
//                        .append(platformId)
//                        .append(sid)
//                        .append(rid)
//                        .append(name)
//                        .append(matchGroup)
//                        .append(score)
//                        .append(summary)
//                        .append(random)
//                        .append(nowOfSeconds)
//                        .toString());
//                params.put("sign",sign);
//                params.put("group",random);
//                params.put("uid",rid);
//                params.put("name",name);
//                params.put("rid",rid);
//                params.put("pvpGroup",matchGroup);
//                params.put("score",score);
//                params.put("roleSummary", summary);
//                params.put("time",nowOfSeconds);
//                params.put("sid", sid);
//                params.put("pid",platformId);
//                String s = HttpUtil.post("http://10.43.0.82:1227/" + "/kingFight/pull", params);
//                log.info("玩家:【{}】,天梯数据推送结果:【{}】",name,s);
//                try {
//                    Thread.sleep(500L);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
//            }

        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        String dataCenterKey = "c3d3f3b71ad85d8";
        int platformId = 148;
        int matchGroup = 2;
        List<Integer> list = new ArrayList<>();
        list.add(1004);
        list.add(1003);
        StringBuilder serversBuilder = new StringBuilder();
        list.forEach(sid -> {
            serversBuilder.append(sid)
                    .append(",");
        });
        serversBuilder.deleteCharAt(serversBuilder.length() - 1);

        String serverList = serversBuilder.toString();
        String md5Str = new StringBuilder(dataCenterKey)
                .append(serverList)
                .append(platformId)
                .append(matchGroup)
                .append(nowOfSeconds)
                .toString();
        String md5 = Md5Util.md5(md5Str);
        Map<String, Object> params = new HashMap<>();
        params.put("sids", serverList);
        params.put("pid", platformId);
        params.put("pvpGroup", matchGroup);
        params.put("time", nowOfSeconds);
        params.put("sign", md5);
        String post = HttpUtil.post("http://10.43.0.82:1227/" + "kingFight/serversRank", params);
        JSONObject jsonObject = JSONObject.parseObject(post);
        JSONObject rank = jsonObject.getJSONObject("rank");
        for (int i = 0; i < rank.size(); i++) {
            JSONObject data = rank.getJSONObject(String.valueOf(i + 1));
            Map<String, Integer> parse = (Map<String, Integer>) JSONObject.parse(data.toJSONString());
            System.out.println(parse);
        }


//        int nowOfSeconds = TimeUtil.getNowOfSeconds();
//        String dataCenterKey = "c3d3f3b71ad85d8";
//        int serverId = 1004;
//        int platformId = 149;
//        int matchGroup = 2;
//        String md5Str = new StringBuilder(dataCenterKey)
//                .append(serverId)
//                .append(platformId)
//                .append(matchGroup)
//                .append(nowOfSeconds)
//                .toString();
//        String md5 = Md5Util.md5(md5Str);
//        Map<String, Object> params = new HashMap<>();
//        params.put("sid", serverId);
//        params.put("pid", platformId);
//        params.put("pvpGroup", matchGroup);
//        params.put("time", nowOfSeconds);
//        params.put("sign", md5);
//        String s = HttpUtil.post("http://10.43.0.82:1227/" + "kingFight/rank200", params);
//        JSONObject jsonObject = JSONObject.parseObject(s);
//        JSONObject rank = jsonObject.getJSONObject("rank");
//        for (int i=0;i<rank.size();i++){
//            JSONArray jsonArray = rank.getJSONArray(String.valueOf(i + 1));
//            if(jsonArray!=null){
//                System.out.println(jsonArray);
//            }
//        }
//        System.out.println(s);
    }
}
