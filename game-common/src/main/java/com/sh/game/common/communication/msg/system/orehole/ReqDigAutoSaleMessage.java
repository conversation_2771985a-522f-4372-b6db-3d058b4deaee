package com.sh.game.common.communication.msg.system.orehole;

import com.sh.game.common.msg.RPC;
import com.sh.server.ProtobufMessage;
import com.google.protobuf.InvalidProtocolBufferException;


/**
 * <p>自动出售</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@RPC("toServer")
public class ReqDigAutoSaleMessage extends ProtobufMessage {

    private com.sh.game.protos.OreholeProtos.ReqDigAutoSale proto;

    private com.sh.game.protos.OreholeProtos.ReqDigAutoSale.Builder builder;

	
	@Override
	public int getId() {
		return 227018;
	}

    @Override
    public void decode(byte[] bytes) {
        try {
            this.proto = com.sh.game.protos.OreholeProtos.ReqDigAutoSale.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            LOGGER.error("消息解析失败：" + this.getId(), e);
        }

    }

    @Override
    public byte[] encode() {
        return proto.toByteArray();
    }

    public com.sh.game.protos.OreholeProtos.ReqDigAutoSale.Builder newBuilder() {
        if(builder == null)
            builder = com.sh.game.protos.OreholeProtos.ReqDigAutoSale.newBuilder();
        return builder;
    }

    public void build() {
        proto = this.builder.build();
        builder = null;
    }

    public com.sh.game.protos.OreholeProtos.ReqDigAutoSale getProto() {
        return proto;
    }

    public void setProto(com.sh.game.protos.OreholeProtos.ReqDigAutoSale proto) {
        this.proto = proto;
    }

}
