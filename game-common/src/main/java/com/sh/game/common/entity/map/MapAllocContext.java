package com.sh.game.common.entity.map;


import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MapAllocContext {

    private int mapCfgId;

    private long mapId;

    private int x;

    private int y;

    /**
     * 传送范围
     */
    private int range;

    private long unionId;

    private boolean sys;

    private long roleId;

    private int dailyType;

    private int mapCondition;

    private int deliverId;

    /**
     * 地图实例id
     */
    private long mapInstanceId;

    /**
     * 继承的免费时间
     */
    private int inheritFreeTime;
    
}
