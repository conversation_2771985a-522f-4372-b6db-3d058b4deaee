# 游戏实体类使用方法


### demo实体类
```
	public class RoleExample implements Persistable {
	    @Tag(1)
	    private long id;
	    @tag(2)
	    private String name;
	}

```

## 通用性数据

 ### 通用性数据代表只有id和data字段的存储

  
1. 增加注解 PersistDaoTemplate(SimpleGameDao.class)
```
        @PersistDaoTemplate(SimpleGameDao.class)
      	public class RoleExample implements Persistable {
      	    @Tag(1)
      	    private long id;
      	   	 @tag(2)
      	    private String name;
      	}
```
   
2. 使用
```
     DataCenter.get();
     DataCenter.update();
```

## 自定义数据

- 自定义数据，代表非常规id，data字段的表， 并且有特殊的操作查询。

1. 创建自定义dao模板

```
   public class MyDao  extends PersistDao{
       //1 重写自定义查询语句
       //2 自定义操作sql， 使用jdbc自定义
       RoleExample findXXXRole();
       
   }

 ```
2. 增加注解 PersistDaoTemplate(MyDao.class) , 使用自定义操作模板

```
        @PersistDaoTemplate(MyDao.class)
      	public class RoleExample implements Persistable {
      	    @Tag(1)
      	    private long id;
      	   	 @tag(2)
      	    private String name;
     	}
```

3.  使用自定义操作

```
  MyDao mydao= DataCenter.findDao(RoleExample.class);
  mydao.findXXXRole();
```
     
##  扩展操作

   - @PersistName   自定义表名，默认为SimpleClassName
   - @PersistPeriod 指定操作间隔 默认为60*1000 

