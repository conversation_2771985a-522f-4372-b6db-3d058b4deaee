package com.sh.game.script.activity;

import com.sh.game.common.config.model.ActivityHolidayLoginConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.script.activity.abc.AbstractActivityLoginRewardScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.script.annotation.Script;

/**
 * 登录大礼活动（节日登录领奖
 */
@Script
public class ActivityLoginDaLiScript extends AbstractActivityLoginRewardScript {
    @Override
    public int getType() {
        return ActivityConst.LOGIN_DALI;
    }

    /**
     * 发送公告
     *
     * @param role   角色
     * @param config 节日登录配置
     */
    @Override
    protected void sendAnnounce(Role role, ActivityHolidayLoginConfig config) {
        AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
    }
}
