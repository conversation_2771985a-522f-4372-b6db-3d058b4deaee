package com.sh.game.script.activity.giftpack;

import com.sh.common.config.ConfigCacheManager;
import com.sh.game.common.config.cache.RealmCache;
import com.sh.game.common.config.model.RealmSpecialConfig;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.data.DataCenter;
import com.sh.game.script.activity.abc.AbstractGiftPackScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.attr.AttributeManager;
import com.sh.game.system.realm.RealmManager;
import com.sh.game.system.realm.entity.RoleRealm;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

/**
 * 境界-灵宝神通直购
 *
 * <AUTHOR>
 * @date 2022/11/14 14:59
 */
@Slf4j
@Script
public class ActivityGiftPackRealmScript extends AbstractGiftPackScript {

    @Override
    public int getType() {
        return ActivityConst.GIFT_PACK_REALM;
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        super.onRoleRecharged(role, rechargeConfig);
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        RealmCache cache = ConfigCacheManager.getInstance().getCache(RealmCache.class);
        RealmSpecialConfig specialConfig = cache.getSpecialConfig(rechargeConfig.getId());
        if (specialConfig == null) {
            return;
        }
        if (specialConfig.getActivityId() != schedule.getActivityID()) {
            return;
        }
        RoleRealm roleRealm = RealmManager.getInstance().find(role.getRoleId());
        if (roleRealm.getSpecialMap().containsKey(specialConfig.getId())) {
            log.error("境界-直购-重复购买,role:{} {},rechargeId:{},configId:{},list:{}", role.getRoleId(), role.getName(), rechargeConfig.getId(), specialConfig.getId(), roleRealm.getSpecialMap().keySet());
            return;
        }
        roleRealm.getSpecialMap().put(specialConfig.getId(), 0);
        DataCenter.updateData(roleRealm);

        log.info("境界-直购-成功,role:{} {},rechargeId:{},configId:{}", role.getRoleId(), role.getName(), rechargeConfig.getId(), specialConfig.getId());

        AttributeManager.getInstance().attributeCount(role);

        RealmManager.getInstance().reqSpecialInfo(role);
    }
}
