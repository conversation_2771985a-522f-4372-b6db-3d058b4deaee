package com.sh.game.script.activity;

import com.sh.game.common.config.model.ActivitySpecialLoginConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.script.activity.abc.AbstractActivitySpecialLoginRewardScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.script.annotation.Script;

/**
 * 合服登录豪礼
 *
 * <AUTHOR>
 * @date 2022/06/29 22:24
 */
@Script
public class ActivityMergeSpecialLoginRewardScript extends AbstractActivitySpecialLoginRewardScript {

    @Override
    public int getType() {
        return ActivityConst.MERGE_SPECIAL_LOGIN_REWARD;
    }

    /**
     * 发送公告
     *
     * @param role      角色
     * @param config    节日登录配置
     */
    @Override
    protected void sendAnnounce(Role role, ActivitySpecialLoginConfig config) {
        AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
    }
}
