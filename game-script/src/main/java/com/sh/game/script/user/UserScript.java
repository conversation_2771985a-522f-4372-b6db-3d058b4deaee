package com.sh.game.script.user;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigDataManager;
import com.sh.concurrent.AbstractCommand;
import com.sh.game.GameContext;
import com.sh.game.auth.AbstractApiAuth;
import com.sh.game.auth.AuthFactory;
import com.sh.game.auth.IAuthController;
import com.sh.game.common.communication.msg.system.tip.ResAlertMessage;
import com.sh.game.common.communication.msg.system.user.*;
import com.sh.game.common.communication.notice.NpcChangeNotice;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.Ban;
import com.sh.game.common.entity.User;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.sys.*;
import com.sh.game.common.entity.user.LoginContext;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.common.entity.usr.RoleSystem;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.*;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventBeforeEnter;
import com.sh.game.event.IEventOnRoleMergeEnd;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.filter.FilterUtil;
import com.sh.game.filter.NameCheckContext;
import com.sh.game.log.entity.OnlineLog;
import com.sh.game.log.entity.RoleNameChangeLog;
import com.sh.game.log.entity.UserRegisterLog;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.protos.TipProtos;
import com.sh.game.protos.UserProtos;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.server.*;
import com.sh.game.system.activity.impl.ActivityMysteryStoreManager;
import com.sh.game.system.activity.impl.ActivityMysteryStoresManager;
import com.sh.game.system.attr.AttributeManager;
import com.sh.game.system.chat.script.IChatMonitorScript;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.gm.GmManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.invitationCode.InvitationCodeManager;
import com.sh.game.system.invitationCode.entiy.InvitationCode;
import com.sh.game.system.rank.RankManager;
import com.sh.game.system.role.RoleManager;
import com.sh.game.system.role.RoleUtil;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.team.TeamManager;
import com.sh.game.system.user.NameManager;
import com.sh.game.system.user.UserManager;
import com.sh.game.system.user.command.LogoutCommand;
import com.sh.game.system.user.command.PlayerLoginCommand;
import com.sh.game.system.user.entity.LoginToken;
import com.sh.game.system.user.script.IUserScript;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import com.sh.server.Session;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018/3/5 17:46
 */

@Slf4j
@Script
public class UserScript implements IUserScript {

    @Override
    public void clientHeartbeat(Session session) {
        //加速软件后期处理
        /*if (TimeUtil.getNowOfMills() - lastHeart < CLIENT_HEART) {
            return;
        }

        lastHeart = TimeUtil.getNowOfMills();
        */
        SessionValue value = (SessionValue) session.getValue();
        value.setLastHeartTime(TimeUtil.getNowOfMills());
        ResHeartMessage msg = new ResHeartMessage();
        UserProtos.ResHeart.Builder resHeart = UserProtos.ResHeart.newBuilder();
        resHeart.setServerTime(TimeUtil.getNowOfMills());
        msg.setProto(resHeart.build());
        session.sendMessage(msg);
    }

    @Override
    public void login(Session session, LoginContext context) {
        log.info("login >>>> {}", context);
        if (StringUtil.isEmpty(context.getUid())) {
            return;
        }
        if (SessionUtil.isLogin(session)) {
            log.error("重复请求登录: {}", context.getUid());
            return;
        }
        // 服务器状态验证
        if (GameContext.isClosed(context.getUid(), context.getIp())) {
            return;
        }
        // 版本验证
        if (!GameContext.getOption().isDebug() && GameContext.getOption().getVersion() != context.getVersion()) {
            ResVersionErrorMessage msg = new ResVersionErrorMessage();
            UserProtos.ResVersionError.Builder versionError = UserProtos.ResVersionError.newBuilder();
            versionError.setVersion(GameContext.getOption().getVersion());
            msg.setProto(versionError.build());
            session.sendMessage(msg);
            log.error("版本号对不上, uid:{}, 服务端:{}, 客户端:{}", context.getUid(), GameContext.getOption().getVersion(), context.getVersion());
//            if (!GameContext.getOption().isDebug()) {
//                return;
//            }
        }
        //开服不做验证，让php用白名单限制
        // 开服时间验证 (在开服前不在白名单内的ip不能登录游戏)
//        if (LocalDateTime.now().isBefore(GameContext.getOption().getOpenTime())) {
//            if (!UserManager.getInstance().isLocal(context.getIp())) {
//                List<String> whiteList = DataCenter.getWhiteList();
//                List<String> whiteAccountList = DataCenter.getWhiteAccountList();
//                if (!whiteList.contains(context.getIp()) && !whiteAccountList.contains(context.getUid())) {
//                    log.error("开服前禁止登录 uid:{}", context.getUid());
//                    return;
//                }
//            }
//        }


        /**
         * 用户登录
         *  debug模式直接验证过
         *
         * 非debug从php获取登录结果
         */
        IAuthController authController = null;
        if (GameContext.getOption().isDebug()) {
            //本地验证
            authController = AuthFactory.getAuth("1");
        }else {
            //php平台验证
            authController = AuthFactory.getAuth("9");
        }

        authController.auth(context, new AbstractCommand() {
            @Override
            public void doAction() {
                login(context);
            }
        });
    }

    @Override
    public void login(LoginContext context) {
        Session session = context.getSession();
        User user = DataCenter.getUser(context.getSid(), context.getUid());
        if (user == null) {

            // //注册限制
            // BanData banData = SysDataProvider.get(BanData.class);
            // Ban cantCreateUser = banData.getCantCreate();
            // int now = TimeUtil.getNowOfSeconds();
            // if (cantCreateUser != null && (cantCreateUser.getTime() < 0 || cantCreateUser.getTime() > now)) {
            //     ResAlertMessage msg = new ResAlertMessage();
            //     msg.setError(StringUtil.format("服务器已限制注册,\n解封时间为{0}", TimeUtil.timeFormat(cantCreateUser.getTime() * TimeUtil.ONE_MILLS, TimeUtil.DEFAULT_FORMAT)));
            //     ChannelFuture channelFuture = session.getChannel().writeAndFlush(msg);
            //     channelFuture.addListener((ChannelFutureListener) future -> session.close());
            //     return;
            // }

            user = createUser(context);
        }
        if (context.getUsername() != null && !context.getUsername().equals(user.getUsername())) {
            user.setUsername(context.getUsername());
        }
        user.setPlatform(context.getPlatform());
        user.setChannel(context.getChannel());
        user.setIp(context.getIp());

        user.setPkg(context.getPkg());
        user.setDevice(context.getDevice());
        DataCenter.updateData(user);

        //判断ip是否被禁
        if (isBan(session, user, 0)) {
            return;
        }

        //处理顶号逻辑
        Session old = SessionManager.getInstance().getUserSession(user.getId());
        if (old != null) {
            log.info("顶号，立即下线-> {} {}", SessionUtil.getUser(old), SessionUtil.getRole(old));
            logout(old, LogoutCommand.Reason.NET_DISCONNECTED);

            ResAlertMessage msg = new ResAlertMessage();
            msg.setProto(TipProtos.ResAlert.newBuilder()
                    .setError("您的账号已在其它设备上登录.")
                    .build());
            ChannelFuture future = old.getChannel().writeAndFlush(msg);
            try {
                future.get(500, TimeUnit.MILLISECONDS);
            } catch (Exception e) {
                log.error("等待顶号消息发送失败", e);
            }
            old.close();
        }

        // 注册session
        SessionManager.getInstance().login(session, user);
        online(user.getId());

        // 开服天数获取
        int openServerDay = GameContext.getOpenServerDay();
        GlobalConfig globalConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.INVITATION_CODE);
        if (globalConfig == null || StringUtils.isBlank(globalConfig.getValue())) {
            return;
        }
        String[] arr = globalConfig.getValue().split(Symbol.JINHAO);

        ResLoginMessage res = new ResLoginMessage();
        UserProtos.ResLogin.Builder loginInfo = UserProtos.ResLogin.newBuilder();
        loginInfo.setUid(user.getId());
        loginInfo.setLastSel(user.getLastSel());
        //TODO 苏荀，相关代码全部抽取，避免处处字符串解析
//        res.setNeedInvitationCode(openServerDay > Integer.valueOf(arr[0]));

        //不使用邀请码
        //res.setNeedInvitationCode(false);
        loginInfo.setNeedInvitationCode(needShowActiveCode(user));


        // role list

        //玩家登录成功,获取并且设置玩家rid
        user.setRoles(DataCenter.getRoleIdList(user.getId()));

        for (Long roleID : user.getRoles()) {
            RoleSummary summary = SummaryManager.getInstance().getSummary(roleID);
            if (summary.getBanUser() == 1) {
                log.error("账户为 account = {} 的角色 name = {} 被后台封禁", user.getAccount(), summary.getName());
                continue;
            }
            UserProtos.RoleBean.Builder roleBean = UserProtos.RoleBean.newBuilder();
            roleBean.setRid(summary.getId());
            roleBean.setName(summary.getName());
            roleBean.setLevel(summary.getLevel());
            roleBean.setCareer(summary.getCareer());
            roleBean.setSex(summary.getSex());
            roleBean.setHair(summary.getHair());
            loginInfo.addRoleList(roleBean.build());
            log.info("返回角色:{} {}", roleID, summary.getName());
        }
        res.setProto(loginInfo.build());
        session.sendMessage(res);
    }

    /**
     * 断线重连
     *
     * @param session
     * @param tokenString
     */
    @Override
    public void disConnectionLogin(Session session, long rid, String tokenString) {
        log.info("userLogin[retry] >>>> {} {} {}", session.getChannel(), rid, tokenString);
        LoginToken token = SessionManager.getInstance().getLoginToken(rid);
        if (token == null || !token.checkToken(tokenString)) {
            sendReConnectionFailMessage(session);
            return;
        }
        User user = DataCenter.get(User.class,token.getId());
        if (user == null) {
            sendReConnectionFailMessage(session);
            return;
        }
        if (SessionManager.getInstance().isRoleOnline(rid)) {
            Session oSession = SessionManager.getInstance().getRoleSession(rid);
            if (oSession != null) {
                log.error("role is online when reconnect. handle logout. {} {} {}", oSession, user.getAccount(), rid);
                logout(oSession, LogoutCommand.Reason.NET_DISCONNECTED);
            }
        }

        log.info("disConnectionLogin loginName:{} ip:{}", user.getUsername(), session.getIp());

        user.setIp(session.getIp());
        DataCenter.updateData(user);

        // 注册session
        SessionManager.getInstance().login(session, user);
        online(user.getId());
        enterGame(session, rid);
    }

    @Override
    public void enterGame(Session session, long rid) {
        if (SessionUtil.isEnter(session)) {
            log.error("session is already entered. channel:{}", session.getChannel());
            return;
        }
        User user = SessionUtil.getUser(session);
        if (user == null) {
            log.error("user 不存在，sessionValue:{}", JSON.toJSONString(session.getValue()));
            return;
        }
        // ban check
        if (isBan(session, user, rid)) {
            log.error("user baned from login. channel:{},userInfo:{},roleId:{}", session.getChannel(), user, rid);
            return;
        }

        // 服务器状态验证
        if (GameContext.isClosed(user.getAccount(), user.getIp())) {
            log.error("server not ready.");
            return;
        }
        boolean roleIdIllegal = CollectionUtils.isEmpty(user.getRoles()) || user.getRoles().stream().noneMatch(v -> v == rid);
        if (roleIdIllegal) {
            log.error("illegal roleId request to enter game,userId:{}->roleId:{}", user.getId(), rid);
            return;
        }
        Role role = DataCenter.get(Role.class, rid);
        if (role == null) {
            log.error("role illegal for login. channel:{},userInfo:{},roleId:{}", session.getChannel(), user, rid);
            return;
        }
        log.info("enterGame >>>> channel:{},userInfo:{},roleId:{}", session.getChannel(), user, role);

        SummaryManager.getInstance().updateIp(role.getId(), user.getIp());
        LoginToken loginToken = new LoginToken(user);
        SessionManager.getInstance().enter(session, role, loginToken);

        user.setLastSel(role.getId());
        DataCenter.updateData(user);

        role.setOnline(true);
        role.setPlatform(user.getPlatform());
        role.setChannel(user.getChannel());
        role.setPid(user.getPid());
        role.setSid(user.getSid());
        role.setAccount(user.getAccount());
        AttributeManager.getInstance().attributeCount(role);
        Hero hero = role.getHero();
        if (hero != null) {
            AttributeManager.getInstance().attributeCount(hero);
        }
        // role 修改后 -> 公共模块 -> 玩家逻辑模块 -> 更新 DB

        OtherData otherData = DataCenter.getOtherData();
        List<Long> roleLists = otherData.getRoleLists();
        if (roleLists != null && !roleLists.contains(role.getId())) {
            roleLists.add(role.getId());
            DataCenter.updateData(otherData);
        }


        ScriptEngine.get1tn(IEventBeforeEnter.class).forEach(script -> script.beforeEnter(SessionUtil.getRole(session)));

        //发送进入游戏消息
        ResEnterGameMessage res = new ResEnterGameMessage();
        UserProtos.ResEnterGame.Builder enterGame = UserProtos.ResEnterGame.newBuilder();
        enterGame.setServerTime(TimeUtil.getNowOfMills());
        enterGame.setRid(role.getId());
        enterGame.setToken(loginToken.getTokenStr());
        enterGame.setLastLogout(role.getRoleLogin().getLogoutTime());
        enterGame.setCreateTime(role.getRoleLogin().getCreateTime());
        enterGame.setPlatform(GameContext.getOption().getOs());
        res.setProto(enterGame.build());
        session.sendMessage(res);

        //尝试进入地图
        if (role.getCareer() != 3 && role.getServantDTOS() != null) {
            role.getServantDTOS().removeIf(servant -> servant.getMonsterType() == MonsterType.SKILL_CALL);
        }

        //尝试进入地图
        MapProxyManager.getInstance().login(role);

        PlayerLoginCommand command = new PlayerLoginCommand(role);
        GameContext.getGameServer().getRouter().process(ProcessorId.SERVER_PLAYER, command, role.getId());
        log.info("user:【{}】,uid:【{}】-roleName:【{}】进入游戏", user.getAccount(), role.getId(), role.getName());
        //进游戏输出在线人数日志
        roleOnLineLog();
    }

    @Override
    public void updateLoginTime(Role role) {
        long currentTime = TimeUtil.getNowOfMills();

        updateRoleLoginDays(role, currentTime, true);

        // 刷新神秘商店
        ActivityMysteryStoreManager.getInstance().loginFlushMysteryStore(role, role.getRoleLogin().getLoginTime());

        // 刷新神秘商店2
        ActivityMysteryStoresManager.getInstance().loginFlushMysteryStore(role, role.getRoleLogin().getLoginTime());

        role.getRoleLogin().setLoginTime((int) (currentTime / 1000));
        SummaryManager.getInstance().updateLoginTime(role.getId(), (int) (currentTime / 1000));
        //TODO RankManager.getInstance().updateRank(role);

        //判定是否新的一次合服后首次登陆
        OtherData otherData = DataCenter.getOtherData();
        RoleSystem roleSystem = findRoleSystem(role.getId());
        if (roleSystem.getMergeCount() < otherData.getMergeCount()){
            try {
                ScriptEngine.invoke1tn(IEventOnRoleMergeEnd.class, script -> script.onRoleMergeEnd(role));
            } catch (Exception e) {
                log.error(StringUtil.format("【{0}】->【{1}】玩家合服结束事件错误", role.getId(), role.getName()), e);
            }

            roleSystem.setMergeCount(otherData.getMergeCount());
            DataCenter.updateData(roleSystem);
        }

        DataCenter.updateData(role);
    }

    private void updateRoleLoginDays(Role role, long updateTime, boolean login) {
        // 上次更新零点时间戳
        long lastDay0ClockTimestamp = (long) role.getRoleLogin().getResetTime() * 1000;
        // 当天零点时间戳
        long currentDay0ClockTimestamp = TimeUtil.dayZeroMillsFromTime(updateTime);
        // 新的一天
        if (lastDay0ClockTimestamp != currentDay0ClockTimestamp) {
            log.info("玩家登录执行隔天时间:name:{},rid:{} 上次每日重置时间{} ", role.getName(), role.getId(), role.getRoleLogin().getResetTime());
            role.getRoleLogin().setLoginDays(role.getRoleLogin().getLoginDays() + 1);
            try {
                ScriptEngine.invoke1tn(IEventOnRoleMidnightScript.class, script -> script.onRoleMidnight(role));
            } catch (Exception e) {
                log.error(StringUtil.format("【{0}】->【{1}】玩家执行零点事件错误", role.getId(), role.getName()), e);
            }
            role.getRoleLogin().setResetTime((int) (currentDay0ClockTimestamp / 1000));
            DataCenter.updateData(role);


            if (!login) {
                //服务器起零点事件消息
                ResServerMidNightMessage msg = new ResServerMidNightMessage();
                msg.setProto(UserProtos.ResServerMidNight.newBuilder().build());
                MessageUtil.sendMsg(msg, role.getId());
            }
        }
    }

    @Override
    public void updateRoleLoginDays(Role role, long updateTime) {
        updateRoleLoginDays(role, updateTime, false);
    }

    @Override
    public void logout(Session session, LogoutCommand.Reason reason) {
        Boolean logoutHandled = AttributeUtil.get(session.getChannel(), ChannelAttrKey.LOGOUT_HANDLED);
        if (Boolean.TRUE.equals(logoutHandled)) {
            log.error("logout handled. {}", session.getChannel());
            return;
        }
        AttributeUtil.set(session.getChannel(), ChannelAttrKey.LOGOUT_HANDLED, true);
        User user = SessionUtil.getUser(session);
        if (user == null) {
            log.error("user not found when logout. {}", session.getChannel());
            return;
        }
        log.info("userLogout >>>> {} {} {}", session.getChannel(), user.toString(), reason);

        leaveGame(session);
        //保存数据
        DataCenter.updateData(user);
        offline(user.getId());
        if (LogoutCommand.Reason.NET_DISCONNECTED == reason) {
            SessionManager.getInstance().logout(session);
        }
        //退出游戏输出在线人数日志
        roleOnLineLog();
    }

    @Override
    public void leaveGame(Session session) {

        if (!SessionUtil.isEnter(session)) {
            log.error("session not enter when leave game. {}", session.getChannel());
            // return;
        }
        User user = SessionUtil.getUser(session);
        if (user == null) {
            log.error("user not found when leave game. {}", session.getChannel());
            return;
        }
        //获取当前使用的role
        Role role = SessionUtil.getRole(session);
        if (role == null) {
            log.error("session not bind any role to leave game. {} {}", session.getChannel(), user.toString());
            return;
        }
        log.info("leaveGame >>>> {} {} {}", session.getChannel(), user.toString(), role.toString());

        //玩家离线操作
        RoleManager.getInstance().offline(role);
        //清除用户session
        SessionManager.getInstance().quit(session);

        if (AbstractApiAuth.getNoCheckAccount().containsKey(user.getAccount())) {
            AbstractApiAuth.getNoCheckAccount().remove(user.getAccount());
        }

        //删除GM用户
        GmManager.getInstance().removeOpenRoleGm(role.getId());

    }

    @Override
    public void changeRoleNameMessage(Session session, long rid, String newName, int isRole) {
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(rid);
        if (player == null) {
            return;
        }
        MapConfig mapConfig = ConfigDataManager.getInstance().getById(MapConfig.class, player.getMapCfgId());
        if (mapConfig == null || mapConfig.getServerType() == 2) {
            TipUtil.show(session, CommonTips.脚本_当前地图无法使用);
            return;
        }

        //名字合法性判断
        if (NameManager.getInstance().nameIsIllegal(session, newName)) {
            return;
        }
        Role role = SessionUtil.getRole(session);
        if (role == null) {
            return;
        }

        //判断后台是否禁止
        ChatConditionData chatConditionData = SysDataProvider.get(ChatConditionData.class);
        if (chatConditionData.isChangeName()) {
            log.error("后台禁止创角自定义昵称!,角色:{},昵称:{},要修改的昵称:{}", role.getRoleId(), role.getName(), newName);
            return;
        }

        int maxGaiMingCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.GAI_MING);
        int renameCount = CountManager.getInstance().getCount(role, CountConst.CountType.RENAME);
        if (renameCount >= maxGaiMingCount) {
            TipUtil.show(role, CommonTips.服务_改名次数到达每日限制);
            return;
        }

        ChatMonitorConfig monitorConfig = FilterUtil.getCurrentFilterConfig();

        if (monitorConfig != null){
            Class<IChatMonitorScript> classObj = (Class<IChatMonitorScript>)FilterUtil.getScriptExector(monitorConfig.getType());
            if (classObj != null) {
               TipUtil.show(role, CommonTips.服务_新昵称正在审核中_请稍候);
                NameCheckContext nameCheckContext = new NameCheckContext();
                nameCheckContext.setNewName(newName);
                nameCheckContext.setRoleID(rid);
                nameCheckContext.setIsRole(isRole);
                nameCheckContext.setOldName(role.getName());
                ScriptEngine.invoke1t1(classObj, script ->script.checkNameIsIllegal(nameCheckContext, monitorConfig));
                return;
            }
        }

        changeRoleNameExecute(role, newName, isRole);
    }

    /**
     * 执行改名
     * @param role
     * @param newName
     * @param isRole
     */
    @Override
    public void changeRoleNameExecute(Role role, String newName, int isRole) {
        // 扣道具
        List<int[]> gaiMingItem = GlobalUtil.findItemCost(GameConst.GlobalId.GAI_MING_ITEM);
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(gaiMingItem);
        if (!stash.commit(role, LogAction.USE_ROLE_NAME_CHANGE_CARD)) {
            return;
        }

        String oldName;
        // 主角改名
        if (isRole == 1) {
            oldName = role.getName();
            NameManager.getInstance().rename(oldName, newName, role.getId());
            role.setName(newName);
            DataCenter.updateData(role);
            CountManager.getInstance().count(role, CountConst.CountType.UPDATE_ROLE_NAME);
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.UPDATE_ROLE_NAME);

            //更新排行榜玩家名称
            SummaryManager.getInstance().updateName(role.getId(), newName);
            log.info("玩家【{}】使用改名卡改名，old：【{}】，new：【{}】", role.getId(), oldName, newName);
            // 更新排行榜
            RankManager.getInstance().updateRankData();
            TeamManager.getInstance().reqSendTeamInfo(role);
            role.proxyCall(proxy -> proxy.nameUpdateNotice(role.getId(), newName, isRole));

            //角色改名日志添加
            RoleNameChangeLog log = new RoleNameChangeLog(role);
            log.setNewName(newName);
            log.setOldName(oldName);
            log.setTime(TimeUtil.getNowOfSeconds());
            log.setRoleId(role.getId());
            log.submit();

            int renameCount = CountManager.getInstance().getCount(role, CountConst.CountType.RENAME);

            ResNewRoleNameMessage msg = new ResNewRoleNameMessage();
            UserProtos.ResNewRoleName.Builder newRoleName = UserProtos.ResNewRoleName.newBuilder();
            newRoleName.setRoleName(newName).setRenameCount(renameCount);
            msg.setProto(newRoleName.build());
            MessageUtil.sendMsg(msg, role.getRoleId());

            // 更改雕像
            CommonData data = SysDataProvider.get(CommonData.class);
            for (Map.Entry<Integer, Long> entry : data.getTitles().entrySet()) {
                TitleStatueConfig titleStatueConfig = ConfigDataManager.getInstance().getById(TitleStatueConfig.class,
                        entry.getKey());
                if (titleStatueConfig == null) {
                    continue;
                }
                if (entry.getValue() != role.getId()) {
                    continue;
                }

                long mapId = MapProxyManager.getInstance().getMap(titleStatueConfig.getStatuemap()[0]).getId();
                NpcChangeNotice notice = new NpcChangeNotice();
                notice.setMapId(mapId);
                int npcId = role.getSex() == RoleConst.Sex.MALE ? titleStatueConfig.getStatueid()[0] :
                        titleStatueConfig.getStatueid()[1];

                notice.setNpcId(npcId);
                notice.setName(role.getName());
                notice.setUnionName(role.getUnion().getName());

                GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_SCENE, notice, mapId);
            }
            // 计数每日改名
            CountManager.getInstance().count(role, CountConst.CountType.RENAME);
            return;
        }

        // 英雄改名
        Hero hero = role.getHero();
        oldName = hero.getName();
        NameManager.getInstance().rename(oldName, newName, hero.getId());
        hero.setName(newName);
        DataCenter.updateData(role);
        log.info("玩家【{}】英雄使用改名卡改名，old：【{}】，new：【{}】", role.getId(), oldName, newName);
        // 更新排行榜
        RankManager.getInstance().updateRankData();
        role.proxyCall(proxy -> proxy.nameUpdateNotice(role.getId(), newName, isRole));
    }

    @Override
    public void kickBanIp(String timeFormat, String ip) {
        Session[] sessions = SessionManager.getInstance().sessions();
        for (Session targetSession : sessions) {
            if (targetSession == null) {
                continue;
            }
            User user = SessionUtil.getUser(targetSession);
            if (user == null) {
                continue;
            }
            if (SessionUtil.getRole(targetSession) == null) {
                continue;
            }
            if (!ip.equals(user.getIp())) {
                continue;
            }
            TipUtil.alert(targetSession.getId(), "您的IP已被封禁,\n解封时间为{0}", timeFormat);
            UserManager.getInstance().logout(targetSession, LogoutCommand.Reason.BAN_IP_CLOSE);
            targetSession.getChannel().close();

            //TODO 这里需要使用RID

        }
    }

    /**
     * 检测邀请码是否可用
     *
     * @param code 邀请码
     */
    @Override
    public void checkInvitationCode(Session session, String code) {

        int serverMax = GlobalUtil.getGlobalInt(GameConst.GlobalId.INVITATION_CODE_SERVER_REGISTER_MAX);
        int count = DataCenter.getUserSize();
        ResCheckInvitationCodeMessage msg = new ResCheckInvitationCodeMessage();
        UserProtos.ResCheckInvitationCode.Builder resCheckInvitationCode = UserProtos.ResCheckInvitationCode.newBuilder();
        /**
         * 判断是不满足邀请码使用
         */
        if (count > serverMax) {
            resCheckInvitationCode.setValid(false);
            // 服务器爆满
            resCheckInvitationCode.setType(InvitationCodeConst.SERVER_FULL);
            msg.setProto(resCheckInvitationCode.build());
            session.sendMessage(msg);
            return;
        }



        InvitationCodeData invitationCodeData = SysDataProvider.get(InvitationCodeData.class);

        Collection<InvitationCode> invitationCodes = invitationCodeData.getInvitationCodeRecords().values();
        InvitationCode invitationCode = invitationCodes.stream().filter(e -> e.getCode().equals(code)).findFirst().orElse(null);

        /**
         * 判断邀请码是否次数上限
         */
        int useLimit = GlobalUtil.getGlobalInt(GameConst.GlobalId.INVITATION_CODE_USE_LIMIT_MAX);
        if (invitationCode != null && invitationCode.getUserTimes() >= useLimit) {
            resCheckInvitationCode.setType(InvitationCodeConst.USE_LIMIT_EXCEEDED);
            msg.setProto(resCheckInvitationCode.build());
            session.sendMessage(msg);
            return;
        }


        User user = SessionUtil.getUser(session);

        /**
         * 新邀请码，后台传递过来的
         */
        if (invitationCode == null) {

            invitationCode = new InvitationCode();
            invitationCode.setCode(code);
            invitationCode.setUserTimes(0);
            invitationCode.setUid(user.getId());
        }

        invitationCode.setUserTimes(invitationCode.getUserTimes() + 1);
        //更新邀请码使用记录
        invitationCodeData.getUseRecords().put(user.getId(), code);
        DataCenter.updateData(invitationCodeData);

        /**
         *  后台验证邀请码是否正常
         */
        boolean useInvitationCode = InvitationCodeManager.getInstance().useInvitationCode(user.getSid(), user.getId(), user.getUsername(), code);
        resCheckInvitationCode.setValid(useInvitationCode);
        msg.setProto(resCheckInvitationCode.build());
        session.sendMessage(msg);
        log.info("玩家使用邀请码:type:{},valid:{}", resCheckInvitationCode.getType(), resCheckInvitationCode.getValid());
    }

    @Override
    public void randomName(Session session, int sex) {
        if (!RoleUtil.sexCorrect("", sex)) {
            return;
        }

        String name = NameManager.getInstance().rollName(sex);
        //随机昵称重复判断 追加后缀
        int maxNameLength = GlobalUtil.getGlobalInt(GameConst.GlobalId.NAME_LENGTH);
        int num = 10;
        while (NameManager.getInstance().hasName(name) && num > 0) {
            num -= 1;
            int hasLength = maxNameLength - name.length();
            if (hasLength > 0) {
                name = name + RandomUtil.randomDigits(hasLength);
            } else {
                name = NameManager.getInstance().rollName(sex);
            }
        }

        ResRandomNameMessage res = new ResRandomNameMessage();
        UserProtos.ResRandomName.Builder resRandomName = UserProtos.ResRandomName.newBuilder();
        resRandomName.setRoleName(name);
        res.setProto(resRandomName.build());
        session.sendMessage(res);
        log.info("<<<< send random name: {}", name);
    }

    @Override
    public void createRole(Session session, String roleName, int sex, int career, int hair, String activeCode, boolean oldMsg) {
        User user = SessionUtil.getUser(session);
        if (user == null) {
            log.error("创角的时候session.user为空！！！");
            return;
        }
        log.info("userId:{} loginName:{}, 执行创角。", user.getId(), user.getAccount());

        Role oldRole = SessionUtil.getRole(session);
        if (oldRole != null) {
            log.error("userId:{} loginName:{}, 疑似重复请求创角！！！", user.getId(), user.getAccount());
            resCreateRoleSuccessMessage(session, oldRole, hair);
            return;
        }
        CharacterConfig character = ConfigDataManager.getInstance().getById(CharacterConfig.class, career + Symbol.JINHAO + sex);
        if (character == null) {
            log.error("创角参数错误： {} {}", career, sex);
            return;
        }

        //名字合法性判断
        if (NameManager.getInstance().nameIsIllegal(session, roleName)) {
            return;
        }

        //判断后台是否禁止
        ChatConditionData chatConditionData = SysDataProvider.get(ChatConditionData.class);
        if (chatConditionData.isChangeName() && !NameManager.getInstance().checkAutoName(roleName)) {
            log.error("后台禁止创角自定义昵称!");
            return;
        }

        DataCenter.updateData(user);
        // 创角
        Role role = RoleManager.getInstance().createCharacter(session, user, roleName, sex, career, hair, LogAction.CREATE_CHARACTER);

        // 创建简要信息
        SummaryManager.getInstance().createSummary(role);

        // 处理名字
        NameManager.getInstance().use(roleName, role.getId());

        resCreateRoleSuccessMessage(session, role, hair);
        log.info("userId:{} loginName:{} roleName:{}, 完成创角。", user.getId(), user.getAccount(), roleName);
    }

    private void resCreateRoleSuccessMessage(Session session, Role role, int hair) {
        UserProtos.RoleBean.Builder roleBean = UserProtos.RoleBean.newBuilder();
        roleBean.setRid(role.getId());
        roleBean.setName(role.getName());
        roleBean.setLevel(1);
        roleBean.setCareer(role.getCareer());
        roleBean.setSex(role.getSex());
        roleBean.setHair(hair);
        ResCreateRoleSuccessMessage res = new ResCreateRoleSuccessMessage();
        UserProtos.ResCreateRoleSuccess.Builder resCreateRoleSuccess = UserProtos.ResCreateRoleSuccess.newBuilder();
        resCreateRoleSuccess.setRoleInfo(roleBean);
        res.setProto(resCreateRoleSuccess.build());
        session.sendMessage(res);
    }

    @Override
    public void deleteRole(Session session, long rid) {
        User user = SessionUtil.getUser(session);
        if (user == null) {
            log.error("创角的时候session.user为空！！！");
            return;
        }

        boolean available = false;
        for (long roleid : user.getRoles()) {
            if (roleid == rid) {
                available = true;
                break;
            }
        }
        if (!available) {
            log.error("not exist: {}", rid);
            return;
        }
        deleteRole(user.getId(), rid);
        ResDeleteRoleMessage res = new ResDeleteRoleMessage();
        UserProtos.ResDeleteRole.Builder resDeleteRole = UserProtos.ResDeleteRole.newBuilder();
        resDeleteRole.setRid(rid);
        res.setProto(resDeleteRole.build());
        session.sendMessage(res);
    }

    public void deleteRole(long uid, long rid) {
        User user = DataCenter.get(User.class, uid);
        if (user == null) {
            return;
        }
        UserManager.getInstance().onRoleDelete(rid);
        SummaryManager.getInstance().deleteSummary(rid);
        user.getRoles().remove(rid);
        DataCenter.deleteRole(uid, rid);
        DataCenter.updateData(user);

        log.info("delete role success: {}", rid);
    }

    private User createUser(LoginContext context) {
        User user = new User();
        user.setId(IDUtil.getId(IDConst.USER));
        user.setAccount(context.getUid());
        user.setUsername(context.getUsername());
        user.setPid(GameContext.getOption().getPlatformId());
        user.setSid(context.getSid());
        user.setType(getUserType(context.getIp()));
        user.setRegTime(TimeUtil.getNowOfSeconds());
        user.setPlatform(context.getPlatform());
        user.setChannel(context.getChannel());
        user.setIp(context.getIp());

        DataCenter.insertData(user, true);

//        if (DataCenter.insertData(user, true) <= 0) {
//            throw new IllegalStateException("创建用户失败: " + user.toString());
//        }


        user.setRoles(DataCenter.getRoleIdList(user.getId()));
        log.info("创建新用户成功: user[{}]", user.toString());

        UserRegisterLog registerLog = new UserRegisterLog();
        registerLog.setClient(user.getPlatform());
        registerLog.setUid(user.getId());
        registerLog.setLoginName(user.getUsername());
        registerLog.setPid(user.getPid());
        registerLog.setSid(user.getSid());
        registerLog.setChannel(user.getChannel());
        registerLog.setIp(user.getIp());
        registerLog.setAccount(user.getAccount());
        if (context.getPkg() != null) {
            registerLog.setClientPkg(context.getPkg());
        }
        if (context.getDevice() != null) {
            registerLog.setDevice(context.getDevice());
        }
        registerLog.submit();
        return user;
    }


    private int getUserType(String ip) {

        if (!GameContext.getOption().isDebug()) {
            return 0;
        }
        if ("".equals(ip) || ip == null) {
            return 0;
        }
        if (UserManager.getInstance().isLocal(ip)) {
            return 99;
        }
        return 0;
    }

    private int checkCodeEffective(String code) {
        //邀请码上限
        int useLimit = GlobalUtil.getGlobalInt(GameConst.GlobalId.INVITATION_CODE_USE_LIMIT_MAX);
        Collection<InvitationCode> invitationCodes = SysDataProvider.get(InvitationCodeData.class).getInvitationCodeRecords().values();

        InvitationCode invitationCode = invitationCodes.stream().filter(e -> e.getCode().equals(code)).findFirst().orElse(null);


        //邀请码不为空，并且次数大于
        if (invitationCode != null && invitationCode.getUserTimes() >= useLimit) {
            return 2;
        }

        // 有效
        return 1;
    }

    /**
     * 平台验证是否需要验证码
     *
     * @param user
     * @return
     */
    private boolean needShowActiveCode(User user) {
        if (needActiveCode(user)) {
            GlobalConfig platformConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.CREATE_ROLE_CAT_NOT_OPEN_PLATFORM);

            List<Integer> platformIds = StringUtil.intArrToIntList(StringUtil.strToIntArr(platformConfig.getValue(), Symbol.JINHAO));
            boolean contains = platformIds.contains(GameContext.getOption().getPlatformId());
            //如果配置表中包含改服务器，就不需要验证码
            return !contains;
        }
        return false;
    }

    /**
     * 玩家是否需要邀请码（激活码）
     *
     * @param user 返回true需要邀请码，false不需要
     * @return
     */
    private boolean needActiveCode(User user) {
        //服务器注册超过5000，不能注册，此处逻辑为只能使用邀请码
        /**
         * 服务器上限使用邀请码也无法注册
         * 此处代码与检验邀请码是否可用方法重复了
         */
//        int count = DataCenter.getUserSize();
//        if (count > GlobalUtil.getGlobalInt(GameConst.GlobalId.INVITATION_CODE_SERVER_REGISTER_MAX)) {
//            return true;
//        }
        //本地玩家不需要
        /**
         * 暂时debug注释掉，上传需解开
         */
        if (UserManager.getInstance().isLocal(user.getIp())) {
            return false;
        }

        //创建过角色的不需要
        if (user.getRoles() != null && user.getRoles().size() != 0) {
            return false;
        }

        //使用过邀请码的不需要
        Map<Long, String> useRecords = SysDataProvider.get(InvitationCodeData.class).getUseRecords();
        if (useRecords.containsKey(user.getId())) {
            return false;
        }

        //其他用户，开服5天后就需要
        int openServerDay = GameContext.getOpenServerDay();

        //获取配置日期，优先php平台配置日期
        int codeEffectiveDay =InvitationCodeManager.getInstance().getInvitationDay() ;
        if ( codeEffectiveDay > 0) {
            //开服日期大于配置日期，需要邀请码
            return openServerDay >= codeEffectiveDay;
        }

        codeEffectiveDay = GlobalUtil.getGlobalInt(GameConst.GlobalId.INVITATION_CODE_EFFECTIVE_DAY);
        return codeEffectiveDay > 0 && openServerDay >= codeEffectiveDay;


    }

    /**
     * 玩家上线以后将玩家从离线ID列表中移除
     *
     * @param uid
     */
    public void online(long uid) {
        log.info(uid + "|从离线玩家列表中移除");
        UserManager.getInstance().getUserOfflineTimeMap().remove(uid);
    }

    /**
     * 玩家掉线，将玩家加入离线ID列表，启动数据清除任务
     *
     * @param uid
     */
    public void offline(long uid) {
        int count = SessionManager.getInstance().getSessionCount();
        long currentTime = TimeUtil.getNowOfMills();
        log.info(uid + "|放入离线列表中,time=" + currentTime + ",当前在线人数：" + count);
        UserManager.getInstance().getUserOfflineTimeMap().put(uid, currentTime);
    }

    private void sendReConnectionFailMessage(Session session) {
        ResReConnectionResultMessage message = new ResReConnectionResultMessage();
        UserProtos.ResReConnectionResult.Builder resConnect = UserProtos.ResReConnectionResult.newBuilder();
        resConnect.setSuccess(false);
        message.setProto(resConnect.build());
        session.sendMessage(message);
    }

    /**
     * 检测账号或ip是否被封禁
     *
     * @param user
     * @return
     */
    private boolean isBan(Session session, User user, long roleId) {
        if (user == null) {
            return false;
        }

        int now = TimeUtil.getNowOfSeconds();
        BanData banData = SysDataProvider.get(BanData.class);
        // ip
        Ban ipBan = banData.getBanIPs().get(user.getIp());
        if (ipBan != null && (ipBan.getTime() < 0 || ipBan.getTime() > now)) {
            ResAlertMessage msg = new ResAlertMessage();
            msg.setProto(TipProtos.ResAlert.newBuilder()
                    .setError(ipBan.getTime() < 0 ? "您的IP已被永久封禁" : StringUtil.format("您的IP已被封禁,\n解封时间为{0}", TimeUtil.timeFormat(ipBan.getTime() * TimeUtil.ONE_MILLS, TimeUtil.DEFAULT_FORMAT)))
                    .build());
            ChannelFuture channelFuture = session.getChannel().writeAndFlush(msg);
            channelFuture.addListener((ChannelFutureListener) future -> session.close());
            return true;
        }
        // user
        Ban userBan = banData.getBanUsers().get(user.getId());
        if (userBan != null && (userBan.getTime() < 0 || userBan.getTime() > now)) {
            ResAlertMessage msg = new ResAlertMessage();
            msg.setProto(TipProtos.ResAlert.newBuilder()
                    .setError(userBan.getTime() < 0 ? "您的账号已被永久封禁" : StringUtil.format("您的账号已被封禁,\n解封时间为{0}", TimeUtil.timeFormat(userBan.getTime() * TimeUtil.ONE_MILLS, TimeUtil.DEFAULT_FORMAT)))
                    .build());
            ChannelFuture channelFuture = session.getChannel().writeAndFlush(msg);
            channelFuture.addListener((ChannelFutureListener) future -> session.close());
            return true;
        }
        // role
        if (roleId > 0) {
            Ban roleBan = banData.getBanRoles().get(roleId);
            if (roleBan != null && (roleBan.getTime() < 0 || roleBan.getTime() > now)) {
                ResAlertMessage msg = new ResAlertMessage();
                msg.setProto(TipProtos.ResAlert.newBuilder()
                        .setError(roleBan.getTime() < 0 ? "您的角色已被永久封禁" : StringUtil.format("您的角色已被封禁,\n解封时间为{0}", TimeUtil.timeFormat(roleBan.getTime() * TimeUtil.ONE_MILLS, TimeUtil.DEFAULT_FORMAT)))
                        .build());
                ChannelFuture channelFuture = session.getChannel().writeAndFlush(msg);
                channelFuture.addListener((ChannelFutureListener) future -> session.close());
                return true;
            }
        }

        return false;
    }

    @Override
    public void checkName(Session session, String name) {
        boolean canUse = NameManager.getInstance().nameIsIllegal(session, name);
        boolean needSend = true;
        if (!canUse) {
            //当前检验通过了才调用平台的检验服务
            ChatMonitorConfig monitorConfig = FilterUtil.getCurrentFilterConfig();
            if (monitorConfig != null) {
                Class<IChatMonitorScript> classObj = (Class<IChatMonitorScript>) FilterUtil.getScriptExector(monitorConfig.getType());
                if (classObj != null) {
                    NameCheckContext nameCheckContext = new NameCheckContext();
                    nameCheckContext.setNewName(name);
                    nameCheckContext.setRoleID(0);
                    nameCheckContext.setIsRole(1);
                    nameCheckContext.setOldName(name);
                    nameCheckContext.setSession(session);
                    ScriptEngine.invoke1t1(classObj, script -> script.checkNameIsIllegal(nameCheckContext, monitorConfig));
                    needSend = false; //需要根据平台检验才能返回结果
                }
            }
        }

        if (needSend) {
            ResCheckNameMessage msg = new ResCheckNameMessage();
            UserProtos.ResCheckName.Builder resCheckName = UserProtos.ResCheckName.newBuilder();
            resCheckName.setValid(!canUse);
            msg.setProto(resCheckName.build());
            session.sendMessage(msg);
            return;
        }

    }

    //在线人数日志
    private void roleOnLineLog(){
        int count = SessionManager.getInstance().getSessionCount();
        OnlineLog onlineLog = new OnlineLog();
        onlineLog.setOnlineCount(count);
        onlineLog.submit();
        log.info("当前在线人数：{}", count);
    }

    private RoleSystem findRoleSystem(long rid) {
        RoleSystem roleSystem = DataCenter.get(RoleSystem.class, rid);
        if (roleSystem == null) {
            OtherData otherData = DataCenter.getOtherData();
            roleSystem = new RoleSystem();
            roleSystem.setId(rid);
            roleSystem.setMergeCount(otherData.getMergeCount());
            DataCenter.insertData(roleSystem, true);
        }

        return roleSystem;
    }
}
