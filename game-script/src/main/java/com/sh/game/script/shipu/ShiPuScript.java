package com.sh.game.script.shipu;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.util.Symbol;
import com.sh.game.common.communication.msg.system.shipu.ResShiPuInfoMessage;
import com.sh.game.common.config.model.ShiPuLevelConfig;
import com.sh.game.common.config.model.ShiPuTypeConfig;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleExtend;
import com.sh.game.common.entity.usr.RoleNormal;
import com.sh.game.common.util.BackPackStashUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.event.IEventOnRoleLogoutScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.ShiPuProtos;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.render.RenderManager;
import com.sh.game.system.render.entity.RenderData;
import com.sh.game.system.role.RoleExtendManager;
import com.sh.game.system.shipu.entity.ShiPuData;
import com.sh.game.system.shipu.script.IShiPuScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/16 15:04
 */
@Script
@Slf4j
public class ShiPuScript implements IShiPuScript, IEventOnRoleLoginScript, IEventOnRoleLogoutScript {

    @Override
    public void reqShiPuInfo(Role role) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        ShiPuData shiPuData = roleExtend.getShiPuData();
        sendInfo(role, shiPuData.getLevelMap());
    }

    @Override
    public void reqShiPuLevelUp(Role role, int type) {
        reqShiPuLevelUp(role, type, false);
    }

    @Override
    public void reqShiPuLevelUpAdvertise(Role role, int type) {
        setLevelAdvertise(role, type);
    }

    @Override
    public void shiPuLevelUpAdvertiseCallBack(Role role) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        ShiPuData shiPuData = roleExtend.getShiPuData();
        if(shiPuData.getLevelAdvertise() > 0) {
            reqShiPuLevelUp(role, shiPuData.getLevelAdvertise(), true);
        }
        setLevelAdvertise(role, 0);
    }

    private void sendInfo(Role role, Map<Integer, Integer> levelMap) {
        ShiPuProtos.ResShiPuInfoMessage.Builder builder = ShiPuProtos.ResShiPuInfoMessage.newBuilder();
        levelMap.forEach((type,level)-> {
            AbcProtos.CommonKeyValueBean.Builder bean = AbcProtos.CommonKeyValueBean.newBuilder();
            bean.setKey(type);
            bean.setValue(level);
            builder.addInfos(bean);
        });

        ResShiPuInfoMessage message = new ResShiPuInfoMessage();
        message.setProto(builder.build());
        MessageUtil.sendMsg(message, role.getId());
    }


    private void reqShiPuLevelUp(Role role, int type, boolean advertise) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        ShiPuData shiPuData = roleExtend.getShiPuData();
        Map<Integer, Integer> levelMap = shiPuData.getLevelMap();
        int level = levelMap.getOrDefault(type, 0);
        if(level <= 0) {
            ShiPuTypeConfig shiPuTypeConfig = ConfigDataManager.getInstance().getById(ShiPuTypeConfig.class, type);
            if(shiPuTypeConfig == null || !ConditionUtil.validate(role, shiPuTypeConfig.getCondition())) {
                return;
            }
        }

        ShiPuLevelConfig shiPuLevelConfig = ConfigDataManager.getInstance().getById(ShiPuLevelConfig.class, type + Symbol.JINHAO + level);
        if(shiPuLevelConfig == null || shiPuLevelConfig.getNextLevel() <= 0) {
            return;
        }
        if(!advertise) {
            if (!BackPackStashUtil.decrease(role, shiPuLevelConfig.getPrice(), LogAction.SHI_PU_LEVEL_UP)) {
                return;
            }
        }

        levelMap.put(type, shiPuLevelConfig.getNextLevel());
        DataCenter.updateData(roleExtend);
        if(shiPuLevelConfig.getNextLevel() == 1) {
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.SHIPU_BUY_COUNT);
        }

        AtomicInteger totalLevel = calShipuMultiple(role, levelMap);

        log.info("食谱升级,roleId: {}, roleName: {} type: {} newLevel: {} newTotalMultiple: {}", role.getRoleId(), role.getName(), type, shiPuLevelConfig.getNextLevel(), totalLevel.get());
        sendInfo(role, levelMap);
    }

    private AtomicInteger calShipuMultiple(Role role, Map<Integer, Integer> levelMap) {
        RoleNormal normal = role.findNormal();
        RenderData renderData = normal.getRenderData();
        AtomicInteger totalLevel = new AtomicInteger();
        levelMap.forEach((t,l)-> {
            ShiPuLevelConfig config = ConfigDataManager.getInstance().getById(ShiPuLevelConfig.class, t + Symbol.JINHAO + l);
            if(config != null) {
                totalLevel.addAndGet(config.getMultiple());
            }
        });
        renderData.setShipuMultiple(totalLevel.get());
        DataCenter.updateData(normal);

        RenderManager.getInstance().calRenderIncome(role, renderData, true, true, true);
        return totalLevel;
    }

    @Override
    public void onRoleLogin(Role role) {
        setLevelAdvertise(role, 0);
    }

    @Override
    public void onRoleLogout(Role role) {
        setLevelAdvertise(role, 0);
    }

    private void setLevelAdvertise(Role role, int type) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        ShiPuData shiPuData = roleExtend.getShiPuData();
        shiPuData.setLevelAdvertise(type);
    }
}
