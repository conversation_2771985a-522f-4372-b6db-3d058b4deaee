package com.sh.game.script.activity;

import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.script.IActivityZhongQiuBossScript;
import com.sh.game.system.teleport.TeleportManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

@Script
@Slf4j
public class ActivityZhongQiuBossScript extends AbstractActivityScript implements IActivityZhongQiuBossScript {
    @Override
    public int getType() {
        return ActivityConst.ZHONG_QIU_BOSS;
    }


    @Override
    public void reqIntoZhongQiuMap(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        int deliveId = GlobalUtil.getGlobalInt(GameConst.GlobalId.ZHONGQIU_BOSS_DELIVE);
        //传送
        TeleportManager.getInstance().reqTeleport(role, deliveId);
        log.info("中秋boss活动,请求传送,角色:{},昵称:{},挑战表id:{}", role.getRoleId(), role.getName(), deliveId);

    }
}
