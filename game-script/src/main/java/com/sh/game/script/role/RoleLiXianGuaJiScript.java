package com.sh.game.script.role;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.system.role.ResLiXianGuaJiMessage;
import com.sh.game.common.communication.msg.system.role.bean.GuaJiLogBean;
import com.sh.game.common.communication.msg.system.role.bean.RewardBean;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleTask;
import com.sh.game.common.util.*;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.event.IEventOnRoleRechargedScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.GuajisystemProtos;
import com.sh.game.protos.RoleProtos;
import com.sh.game.system.activity.entity.role.LiXianGuaJiData;
import com.sh.game.system.activity.entity.role.LiXianGuaJiLogData;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.game.system.appearance.entity.Appearance;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.goal.entity.Goal;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.role.RoleManager;
import com.sh.game.system.role.script.IRoleLiXianGuaJiScript;
import com.sh.game.system.task.entity.TaskRecord;
import com.sh.game.system.zhanyi.ZhanYiManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 离线挂机系统
 * by zt
 * 2022/10/17
 */
@Slf4j
@Script
public class RoleLiXianGuaJiScript implements IRoleLiXianGuaJiScript,
        IEventOnRoleRechargedScript, IEventOnRoleLoginScript {

    /**
     * 请求获取挂机数据
     */
    @Override
    public void reqLixianGuaJiMsg(Role role) {
        LiXianGuaJiData liXianGuaJiData = loadData(role);
        ResLiXianGuaJiMessage msg = new ResLiXianGuaJiMessage();
        GuajisystemProtos.ResLiXianGuaJi.Builder liXianGuaJi = GuajisystemProtos.ResLiXianGuaJi.newBuilder();
        liXianGuaJi.setOffLineTime(liXianGuaJiData.getLiXianMinute());
        addLogs(msg, liXianGuaJiData.getLogs());
        addLogs(msg, liXianGuaJiData.getOtherLogs());
        liXianGuaJi.setActivited(liXianGuaJiData.getBuyLiXianGuaJi());
        liXianGuaJi.setRewardMultiple(liXianGuaJiData.getRewardMultiple());
        liXianGuaJi.addAllRewardList(tranToRewardBean(liXianGuaJiData.getRewards()));
        liXianGuaJi.addAllTaskRewards(tranToRewardBean(liXianGuaJiData.getTaskRewards()));
        for (Map.Entry<Integer, Boolean> entry : liXianGuaJiData.getLiXianSetting().entrySet()) {
            AbcProtos.CommonKeyValueBean.Builder bean = AbcProtos.CommonKeyValueBean.newBuilder();
            bean.setKey(entry.getKey());
            bean.setValue(entry.getValue() ? 1 : 0);
            liXianGuaJi.addSetting(bean);
        }
        msg.setProto(liXianGuaJi.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    private void addLogs(ResLiXianGuaJiMessage msg, List<LiXianGuaJiLogData> logs) {
        for (LiXianGuaJiLogData liXianGuaJiLogData : logs) {
            GuajisystemProtos.GuaJiLogBean.Builder bean = GuajisystemProtos.GuaJiLogBean.newBuilder();
            bean.setLogLangId(liXianGuaJiLogData.getLangId());
            bean.setLogTime(liXianGuaJiLogData.getLogTime());
            bean.setType(liXianGuaJiLogData.getLogType());
            bean.addAllLogParams(liXianGuaJiLogData.getLogParams());
            msg.setProto(GuajisystemProtos.ResLiXianGuaJi.newBuilder()
                    .addLogs(bean)
                    .build());
        }
    }

    private List<GuajisystemProtos.RewardBean> tranToRewardBean(Map<Integer, Long> items) {
        List<GuajisystemProtos.RewardBean> retList = new ArrayList<>();
        for (Map.Entry<Integer, Long> entry : items.entrySet()) {
            GuajisystemProtos.RewardBean.Builder bean = GuajisystemProtos.RewardBean.newBuilder();
            bean.setItemId(entry.getKey());
            bean.setCount(Math.toIntExact(entry.getValue()));
            retList.add(bean.build());
        }
        return retList;

    }

    /**
     * 请求获取奖励
     */
    @Override
    public void reqGetLixianGuaJiReward(Role role, int getType) {
        LiXianGuaJiData liXianGuaJiData = loadData(role);
        if (liXianGuaJiData.getRewards().size() <= 0) {
            if (!liXianGuaJiData.isGetedReward()) {
                liXianGuaJiData.setGetedReward(true);
                saveData(role);
            }
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.increase(liXianGuaJiData.getRewards());
        if (!stash.commit(role, LogAction.LIXIANGUAJI_GETREWARD)) {
            return;
        }
        liXianGuaJiData.getRewards().clear();
        liXianGuaJiData.setGetedReward(true);
        liXianGuaJiData.setLiXianMinute(0);
        saveData(role);
        reqLixianGuaJiMsg(role);

    }

    @Override
    public int getRewardMultiple(Role role) {
        LiXianGuaJiData liXianGuaJiData = loadData(role);
        return liXianGuaJiData.getRewardMultiple();
    }

    @Override
    public boolean addRewardMultiple(Role role, int addMultiple) {
        LiXianGuaJiData liXianGuaJiData = loadData(role);
        addMultiple = Math.min(liXianGuaJiData.getRewardMultiple() + addMultiple, 30);
        liXianGuaJiData.setRewardMultiple(addMultiple);
        saveData(role);
        reqLixianGuaJiMsg(role);
        return true;
    }

    @Override
    public void liXianGuaJiSetting(Role role, int index, int value) {
        LiXianGuaJiData liXianGuaJiData = loadData(role);
        boolean booleanValue = value == 1;
        liXianGuaJiData.getLiXianSetting().put(index, booleanValue);
        log.info("离线挂机，玩家设置，玩家id:{},name:{},设置类型:{},值:{}", role.getId(), role.getName(), index, value);
        reqLixianGuaJiMsg(role);
    }

    /**
     * 离线数据计算
     */
    private void calLiXianGuaJiData(Role role) {
        LiXianGuaJiData liXianGuaJiData = loadData(role);
        int liXianMaxTimeSet = GlobalUtil.getGlobalInt(GameConst.GlobalId.LIXIANGUAJI_MAXTIME) * 60; //最大可离线时间,转成秒
        if (!isCanCalLiXianReward(role, liXianGuaJiData, liXianMaxTimeSet)) {
            return;
        }
        //最大可挂机时间,减去已挂时间
        liXianMaxTimeSet = liXianMaxTimeSet - liXianGuaJiData.getLiXianMinute() * 60;
        long curSysTime = System.currentTimeMillis();
        long curMaxPower = role.getAtkMax();
        int liXianTimeSet = GlobalUtil.getGlobalInt(GameConst.GlobalId.LIXIANGUAJI_TIMESET) * 60; //转成秒


        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        int offlineTime = role.getRoleLogin().getLogoutTime();
        int calOffLineTime = offlineTime + liXianTimeSet; //离线计算用于循环
        int liXianEndTime = Math.min(nowOfSeconds, calOffLineTime + liXianMaxTimeSet);
        int timeDiff = liXianEndTime - offlineTime;  //(秒)

        liXianGuaJiData.setLiXianTime(offlineTime);
        //已挂时间,累加
        liXianGuaJiData.setLiXianMinute(liXianGuaJiData.getLiXianMinute() + timeDiff / 60);

        if (timeDiff < liXianTimeSet) {
            log.info("离线挂机，玩家:{}->{},当前离线{}秒,未到{},此次不计算离线数据", role.getName(), role.getId(), timeDiff, liXianTimeSet);
            return;
        }


        //先处理掉已完成的任务
        liXianGuaJiData.setGetedReward(false);
        int iCount = 5000;
        int taskStateCheck = 0;
        boolean bNeedRefresh = false;
        int preTaskId = 0;
        int oLevel = role.getLevel();
        int newLevel = oLevel;
        int cfgPerKillWhenUpLv = Math.max(1, GlobalUtil.getGlobalInt(GameConst.GlobalId.LIXIANGUAJI_UPLV_PERKILL));
        int nKillCount = 0;

        //是否自动升级转生
        boolean isAutoUpZS = liXianGuaJiData.getLiXianSetting().getOrDefault(RoleConst.SettingKey.LIXIANGUAJI_AUTOUPZS.getIndex(), false);
        //是否自动升称号
        boolean isAutoUpCH = liXianGuaJiData.getLiXianSetting().getOrDefault(RoleConst.SettingKey.LIXIANGUAJI_AUTOUPCH.getIndex(), false);
        TwoTuple<LiXianGuaJiConfig, Integer> bufferMaxTask = new TwoTuple<>(null, 0);
        log.info("离线挂机，玩家{}->{}离线挂机运算开始, calOffLineTime:{}, liXianEndTime:{}", role.getName(), role.getId(), calOffLineTime, liXianEndTime);
        liXianGuaJiData.setDisabledItemChange(true);
        try {
            while (calOffLineTime < liXianEndTime) {
                int taskid = 0;
                TaskRecord theTaskRec = findRoleTaskID(role);
                if (theTaskRec != null) {
                    taskid = theTaskRec.getTaskId();
                }
                LiXianGuaJiConfig liXianGuaJiConfig = findConfigByTaskID(role, theTaskRec, bufferMaxTask);
                if (liXianGuaJiConfig == null) {
                    log.error("离线挂机，玩家{}->{}离线挂机异常,无法获取taskid ={}", role.getName(), role.getId(), taskid);
                    return;
                }

                //得到间隔时间
                int intervalTime = getLiXianExecuteTime(role, liXianGuaJiConfig, curMaxPower);
                if (intervalTime <= 0) {
                    log.error("离线挂机，玩家{}->{}离线挂机异常,intervalTime ={}", role.getName(), role.getId(), intervalTime);
                    return;
                }
                calOffLineTime += intervalTime;
                taskStateCheck += intervalTime;

                if (taskid != preTaskId) {
                    preTaskId = taskid;
                    //	进行任务{0}正在{1}地图，挑战怪物中	进入新地图时的挂机公告
                    appendLogData(liXianGuaJiData, LiXianGuaJiConst.LIXIANGUAJI_ENTERMAP, calOffLineTime - RandomUtil.random(1, intervalTime / 2), LiXianGuaJiConst.LOGTYPE.RESETLOG,
                            getTaskName(taskid), getMapName(liXianGuaJiConfig.getMapId()));
                }

                //记录一条挂机日志
                if (recordKillMonsterLogs(role, liXianGuaJiData, liXianGuaJiConfig, calOffLineTime + RandomUtil.random(2, intervalTime / 2))) {
                    nKillCount++;
                    //等级处理
                    if (nKillCount >= cfgPerKillWhenUpLv && newLevel < liXianGuaJiConfig.getLevel()) {
                        newLevel++;
                        nKillCount = 0;
                    }
                }


                if (isAutoUpZS) {
                    isAutoUpZS = autoUpDateZS(role, liXianGuaJiData, calOffLineTime + RandomUtil.random(1, intervalTime / 2)); //如果升级失败, 接下来也不需要继续尝试升级
                }

                if (isAutoUpCH) {
                    isAutoUpCH = autoUpDateCH(role, liXianGuaJiData, calOffLineTime + RandomUtil.random(intervalTime / 2, intervalTime)); //如果升级失败, 接下来也不需要继续尝试升级
                }

                iCount--;
                if (iCount <= 0) {
                    log.error("离线挂机，玩家{}->{}离线挂机异常,终止异常循环", role.getName(), role.getId());
                    return;
                }

                //每10分钟检查一次任务状态
                if (taskStateCheck > 600) {
                    taskStateCheck = 0;
                }
                bNeedRefresh = true;

            }
        } finally {
            liXianGuaJiData.setDisabledItemChange(false);
            if (bNeedRefresh) {
                saveData(role);
                if (newLevel > oLevel) {
                    RoleManager.getInstance().levelChange(role, role.getId(), newLevel);
                }
                curSysTime = System.currentTimeMillis() - curSysTime;
                log.info("离线挂机，玩家:{}->{},计算离线数据结束,耗时:{}", role.getName(), role.getId(), curSysTime);
            }
        }

    }

    private boolean autoUpDateCH(Role role, LiXianGuaJiData liXianGuaJiData, int eventTime) {
        List<ChenghaoConfig> list = ConfigDataManager.getInstance().getList(ChenghaoConfig.class);
        Appearance appearance = role.getRoleAdvance().getAppearance();
        ChenghaoConfig unLock = null;
        for (ChenghaoConfig config : list) {
            if (config.getLevel() == 0) {
                continue;
            }
            if (appearance.getStatus().containsKey(config.getFashionid())) {
                continue;
            }
            if (!ConditionUtil.validate(role, config.getCondition(), false)) {
                continue;
            }
            if (unLock == null || unLock.getLevel() > config.getLevel()) {
                unLock = config;
            }

        }
        if (unLock == null) {
            return false;
        }
        // 自动升级只扣元宝，不动灵符
        AppearanceManager.getInstance().reqSelfAppearanceUnlock(role, unLock.getId() - 1, 0);
        if (!appearance.getStatus().containsKey(unLock.getFashionid())) {
            return false;
        }
        ChenghaoConfig config = ConfigDataManager.getInstance().getById(ChenghaoConfig.class, unLock.getId() - 1);
        String sCost = "";
        if (!config.getCostitem().isEmpty()) {
            int[] cost = config.getCostitem().get(0);
            sCost = GlobalUtil.tranItemIntArrToString(cost);
        }
        // 花费{0}称号升至{1}	升级称号时的挂机公告
        appendLogData(liXianGuaJiData, LiXianGuaJiConst.LIXIANGUAJI_UPCH, eventTime, LiXianGuaJiConst.LOGTYPE.RESETLOG,
                sCost, unLock.getLevelinfo());
        log.info("离线挂机，自动升级称号，玩家id:{},name:{},info:{}", role.getId(), role.getName(), unLock.getLevelinfo());
        return true;
    }

    private boolean autoUpDateZS(Role role, LiXianGuaJiData liXianGuaJiData, int eventTime) {
        int cfgId = role.getZhanyi().getCfgId();
        ZhanyiConfig preConfig = ConfigDataManager.getInstance().getById(ZhanyiConfig.class, cfgId);
        ZhanyiConfig nextConfig = ConfigDataManager.getInstance().getById(ZhanyiConfig.class, preConfig.getNextId());
        while (nextConfig != null) {

            if (!ConditionUtil.validate(role, preConfig.getCondition(), false)) {
                return false;
            }
            int[] cost = nextConfig.getCostitem().get(0);
            if (nextConfig.getCostitem().isEmpty() || cost.length < 2) {
                return false;
            }
            ZhanYiManager.getInstance().levelUp(role, 0, cost[0]);
            if (role.getZhanyi().getCfgId() != nextConfig.getId()) {
                return false;
            }
            String sCost;
            sCost = GlobalUtil.tranItemIntArrToString(cost);
            // 花费{0}转生升至{1}	升级转生时的挂机公告
            appendLogData(liXianGuaJiData, LiXianGuaJiConst.LIXIANGUAJI_UPZS, eventTime, LiXianGuaJiConst.LOGTYPE.RESETLOG,
                    sCost, nextConfig.getLevelinfo());
            log.info("离线挂机，自动转生，转生成功，玩家id:{},name:{},info:{}", role.getId(), role.getName(), nextConfig.getLevelinfo());
            preConfig = nextConfig;
            nextConfig = ConfigDataManager.getInstance().getById(ZhanyiConfig.class, nextConfig.getNextId());
        }
        return true;
    }

    @Override
    public void ReqLiXianGuaJiGetTaskReward(Role role) {

        LiXianGuaJiData liXianGuaJiData = loadData(role);
        Map<Integer, Long> taskRewards = liXianGuaJiData.getTaskRewards();
        if (taskRewards.size() <= 0) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(taskRewards);
        if (!stash.commit(role, LogAction.LIXIANGUAJI_GETTASKREWARD)) {
            List<Item> reward = taskRewards.entrySet().stream().map(item -> ItemUtil.create(item.getKey(), item.getValue(), LogAction.LIXIANGUAJI_GETTASKREWARD)).collect(Collectors.toList());
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, reward);
        }

    }

    /**
     * 记录一条日志并增加挂机奖励
     */
    private boolean recordKillMonsterLogs(Role role, LiXianGuaJiData liXianGuaJiData, LiXianGuaJiConfig liXianGuaJiConfig, int eventTime) {
        appendReward(role, liXianGuaJiData, liXianGuaJiData.getRewards(), liXianGuaJiConfig.getReward());
        if (liXianGuaJiConfig.getMonsterId().size() <= 0) {
            return false;
        }

        int rndMonsterID = liXianGuaJiConfig.getMonsterId().get(RandomUtil.random(0, liXianGuaJiConfig.getMonsterId().size() - 1));
        if (rndMonsterID > 0) {
            appendLogData(liXianGuaJiData, LiXianGuaJiConst.LIXIANGUAJI_FIGHTMONSTER, eventTime, LiXianGuaJiConst.LOGTYPE.KILLMONSTER,
                    getMapName(liXianGuaJiConfig.getMapId()), getMonsterName(rndMonsterID));
            //任务goal更新
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.KILL_MONSTER, rndMonsterID);
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.KILL_MONSTER_SHARE, rndMonsterID);
            return true;
        }
        return false;

    }

    private String getTaskName(int taskId) {
        TaskConfig taskConfig = ConfigDataManager.getInstance().getById(TaskConfig.class, taskId);
        if (taskConfig == null) {
            return "未知任务";
        }
        return taskConfig.getName();
    }

    private String getMonsterName(int cfgId) {
        MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, cfgId);
        if (monsterConfig == null) {
            return "神秘BOSS";
        }
        return monsterConfig.getName();
    }

    private String getMapName(int mapid) {
        MapConfig mapConfig = ConfigDataManager.getInstance().getById(MapConfig.class, mapid);
        if (mapConfig == null) {
            return "神秘领域";
        }
        return mapConfig.getName();
    }

    private void appendLogData(LiXianGuaJiData liXianGuaJiData, int langId, int logTime, int LogType, String... params) {
        LiXianGuaJiLogData logData = new LiXianGuaJiLogData();
        logData.setLangId(langId);
        logData.setLogTime(logTime);
        logData.setLogType(LogType);
        for (String param : params) {
            logData.getLogParams().add(param);
        }
        if (LogType == LiXianGuaJiConst.LOGTYPE.RESETLOG) {
            liXianGuaJiData.getLogs().add(logData);
            if (liXianGuaJiData.getLogs().size() > LiXianGuaJiConst.LIXIANGUAJI_MAXLOGCOUNT) {
                liXianGuaJiData.getLogs().remove(0);
            }
        } else {
            liXianGuaJiData.getOtherLogs().add(logData);
            if (liXianGuaJiData.getOtherLogs().size() > LiXianGuaJiConst.LIXIANGUAJI_MAXLOGCOUNT) {
                liXianGuaJiData.getOtherLogs().remove(0);
            }
        }
    }

    /**
     * 追求奖励
     */
    private void appendReward(Role role, LiXianGuaJiData liXianGuaJiData, Map<Integer, Long> storeReward, Map<Integer, Integer> reward) {
        if (reward == null) {
            return;
        }
        List<Item> itemList = new ArrayList<>();
        int mutil = Math.max(1, liXianGuaJiData.getRewardMultiple());
        for (Map.Entry<Integer, Integer> entry : reward.entrySet()) {
            Integer itemId = entry.getKey();
            ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
            Integer count = entry.getValue();
            if (count > 0 && config.getAutouse() == 1 && config.getUseType() == 200) {
                // 获取宝箱信息
                BoxConfig boxConfig = ConfigDataManager.getInstance().getById(BoxConfig.class, config.getUseParam()[0][0]);
                if (boxConfig != null) {
                    // 打开宝箱，将道具加到集合中
                    List<Item> box = BoxUtil.openBox(role, boxConfig.getId(), count);
                    for (Item item : box) {
                        if (GlobalConfig.liXianGuaJiMutilItems != null && GlobalConfig.liXianGuaJiMutilItems.contains(item.getCfgId())) {
                            long itemCount = item.getCount();
                            itemCount = Math.max(0,itemCount * mutil);
                            item.setCount(itemCount);
                        }
                    }
                    itemList.addAll(box);
                } else {
                    itemList.add(ItemUtil.create(itemId, count, LogAction.LIXIANGUAJI_GETREWARD));
                }
            } else {
                itemList.add(ItemUtil.create(itemId, count, LogAction.LIXIANGUAJI_GETREWARD));
            }

        }
        for (Item item : itemList) {
            storeReward.put(item.getCfgId(), storeReward.getOrDefault(item.getCfgId(), 0L) + item.getCount());
        }
    }

    /**
     * 取玩家当前最新的杀怪主线任务
     */
    private TaskRecord findRoleTaskID(Role role) {
        RoleTask roleTask = role.getRoleTask();
        TaskRecord retTask = null;
        for (TaskRecord record : roleTask.getAllTaskByID().values()) {
            TaskConfig config = ConfigDataManager.getInstance().getById(TaskConfig.class, record.getTaskId());
            if (!config.getTypeEnum().equals(TaskTypeEnum.MAIN)) {
                continue;
            }
            if (!ConditionUtil.validate(role,config.getGuajiCondition())) {
                continue;
            }
            // if (config.getMonsterId() == null || config.getMonsterId().isEmpty()) {
            //     continue;
            // }
            if (retTask == null || retTask.getTaskId() < record.getTaskId()) {
                retTask = record;
            }

        }

        return retTask;
    }

    public LiXianGuaJiConfig findMaxTaskConfig(Role role, int taskID) {
        LiXianGuaJiConfig lastConfig = null;
        int maxTaskID = 0;
        List<LiXianGuaJiConfig> listConfigs = ConfigDataManager.getInstance().getList(LiXianGuaJiConfig.class);
        for (LiXianGuaJiConfig curConfig : listConfigs) {
            if (ConditionUtil.validate(role, curConfig.getConditions())) {
                if (taskID == 0) {
                    //如果玩家身上没有任务时,返回最大满足任务
                    if (curConfig.getTaskId() > maxTaskID) {
                        maxTaskID = curConfig.getTaskId();
                        lastConfig = curConfig;
                    }
                } else {
                    //找到离当前任务最近的那个任务
                    if (curConfig.getTaskId() <= taskID && curConfig.getTaskId() > maxTaskID) {
                        maxTaskID = curConfig.getTaskId();
                        lastConfig = curConfig;
                    }
                }
            }
        }
        return lastConfig;
    }

    private LiXianGuaJiConfig findFirstDefTask(Role role, TwoTuple<LiXianGuaJiConfig, Integer> bufferMaxTask, int taskID) {
        //没有任务了, 从小到大取
        if (bufferMaxTask.getSecond() <= 0) {
            //没缓存过，先缓存一下，防止 每次循环都去查
            bufferMaxTask.setFirst(findMaxTaskConfig(role, taskID));
            if (bufferMaxTask.getFirst() != null) {
                bufferMaxTask.setSecond(bufferMaxTask.getFirst().getTaskId());
            }
        }
        return bufferMaxTask.getFirst();
    }

    /**
     * 根据任务id找到符合条件的挂机配置
     */
    public LiXianGuaJiConfig findConfigByTaskID(Role role, TaskRecord taskRec, TwoTuple<LiXianGuaJiConfig, Integer> bufferMaxTask) {
        int taskID = 0;
        if (taskRec != null) {
            taskID = taskRec.getTaskId();
        }
        List<LiXianGuaJiConfig> lstConfigs = LiXianGuaJiConfig.cacheByTaskId.get(taskID);
        if (lstConfigs == null) {
            return findFirstDefTask(role, bufferMaxTask, taskID);
        }
        LiXianGuaJiConfig firstTask = null;
        Collection<Goal> taskGoalRecords = null;
        if (taskRec != null) {
            taskGoalRecords = taskRec.getGoalList();
        }
        for (LiXianGuaJiConfig cfg : lstConfigs) {
            //找到满足条件的
            if (ConditionUtil.validate(role, cfg.getConditions())) {
                if (firstTask == null) {
                    firstTask = cfg;
                }

                //找到需要去打怪的任务task
                if (taskGoalRecords != null && checkIsMatched(taskGoalRecords, cfg)) {
                    return cfg;
                }
            }
        }
        if (firstTask != null) {
            return firstTask;
        }
        return findFirstDefTask(role, bufferMaxTask, taskID);
    }

    /**
     * 检查当前配置是否符合打怪要求
     */
    private boolean checkIsMatched(Collection<Goal> taskGoalRecords, LiXianGuaJiConfig cfg) {
        if (taskGoalRecords == null) {
            return true;
        }


        for (Goal goal : taskGoalRecords) {
            GoalsConfig goalsConfig = ConfigDataManager.getInstance().getById(GoalsConfig.class, goal.getGoalId());
            //当前任务已经完成了的
            if (goal.getCount() >= goalsConfig.getCount()) {
                continue;
            }
            for (int monsterid : cfg.getMonsterId()) {
                if (StringUtil.intArrToIntList(goalsConfig.getParam()).contains(monsterid)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 根据任务获取当前任务执行间隔时间
     */
    private int getLiXianExecuteTime(Role role, LiXianGuaJiConfig cfg, long maxPower) {
        if (cfg == null || cfg.getPower().size() <= 0 || cfg.getTime().size() <= 0) {
            return 100;
        }

        int foundIdx = -1;
        int[] lastRange = cfg.getPower().get(cfg.getPower().size() - 1);
        if (maxPower >= lastRange[0]) {
            foundIdx = cfg.getPower().size() - 1;
        } else {
            for (int i = 0; i <= cfg.getPower().size() - 2; i++) {
                int[] range = cfg.getPower().get(i);
                if (range.length < 2) {
                    continue;
                }
                if (maxPower >= range[0] && maxPower < range[1]) {
                    foundIdx = i;
                    break;
                }
            }
        }

        if (foundIdx < 0) {
            log.error("离线挂机，玩家:{}->{},未找到挂机配置{},的攻击力范围{}", role.getName(), role.getId(), cfg.getId(), maxPower);
            return 100;
        }

        if (foundIdx >= cfg.getTime().size()) {
            foundIdx = cfg.getTime().size() - 1;
        }

        return cfg.getTime().get(foundIdx);
    }

    /**
     * 判断是否允许离线挂机
     */
    private boolean isCanCalLiXianReward(Role role, LiXianGuaJiData liXianGuaJiData, int liXianMaxTimeSet) {
        if (liXianGuaJiData.getBuyLiXianGuaJi() <= 0) {
            return false;
        }

        if (liXianGuaJiData.getLiXianMinute() * 60 >= liXianMaxTimeSet) {
            log.info("离线挂机，玩家{},已达最大挂机时间:{}", role.getName(), liXianMaxTimeSet);
            return false;
        }
        return true;
    }

    private LiXianGuaJiData loadData(Role role) {
        return role.findNormal().getLiXianGuaJiData();
    }

    private void saveData(Role role) {
        DataCenter.updateData(role.findNormal());
    }


    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        int rechargeID = GlobalUtil.getGlobalInt(GameConst.GlobalId.LIXIANGUAJI_RECHARGEID);
        if (rechargeConfig.getId() != rechargeID) {
            return;
        }
        LiXianGuaJiData liXianGuaJiData = loadData(role);
        liXianGuaJiData.setBuyLiXianGuaJi(1);
        saveData(role);
        reqLixianGuaJiMsg(role);
        log.info("离线挂机，玩家激活离线挂机 玩家id:{},name:{}", role.getId(), role.getName());
    }

    /**
     * 登录结束
     */
    @Override
    public void onRoleLogin(Role role) {
        calLiXianGuaJiData(role);
    }
}
