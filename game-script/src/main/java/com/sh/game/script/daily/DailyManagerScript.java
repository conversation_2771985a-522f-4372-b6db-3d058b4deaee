package com.sh.game.script.daily;

import com.sh.common.config.ConfigCacheManager;
import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.system.daily.*;
import com.sh.game.common.config.cache.ScuffleCache;
import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.MapConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.map.MonsterHpDto;
import com.sh.game.common.entity.sys.CommonData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleDaily;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnRoleExitScript;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.event.IEventScheduleUpdateOnMinuteScript;
import com.sh.game.protos.DailyProtos;
import com.sh.game.system.daily.DailyManager;
import com.sh.game.system.daily.entity.DailySchedule;
import com.sh.game.system.daily.entity.RoleDailyTimes;
import com.sh.game.system.daily.script.IDailyManagerScript;
import com.sh.script.annotation.Script;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Script
public class DailyManagerScript implements IDailyManagerScript, IEventScheduleUpdateOnMinuteScript,
        IEventOnRoleMidnightScript, IEventOnRoleLoginScript, IEventOnRoleExitScript {

    @Override
    public void reqDailyTimedSchedules(Role role) {
        ResDailyTimedScheduleMessage msg = new ResDailyTimedScheduleMessage();
        DailyProtos.ResDailyTimedSchedule.Builder dailyTimedSchedule = DailyProtos.ResDailyTimedSchedule.newBuilder();
        for (int weekday = 1; weekday <= 7; weekday++) {
            DailyProtos.DailyTimedOpensOnWeekday.Builder bean = DailyProtos.DailyTimedOpensOnWeekday.newBuilder();
            bean.setWeekday(weekday);
            for (DailySchedule schedule : DailyManager.schedule.getSchedules(weekday)) {
                bean.addOpens(schedule.getId());
            }
            dailyTimedSchedule.addSchedule(bean);
        }
        msg.setProto(dailyTimedSchedule.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqOpenServerDaily(Role role) {
        int openServerDay = GameContext.getOpenServerDay();
        if (openServerDay > 7) {
            return;
        }
        int weekDay = LocalDate.now().getDayOfWeek().getValue() - openServerDay;
        ResOpenServerDailyMessage msg = new ResOpenServerDailyMessage();
        DailyProtos.ResOpenServerDaily.Builder openServerDaily = DailyProtos.ResOpenServerDaily.newBuilder();
        List<DailySchedule> list;
        DailyProtos.DailyTimedOpensOnWeekday.Builder bean;
        for (int i = 0; i < 7; i++) {
            weekDay++;
            list = DailyManager.schedule.getSchedules(weekDay);
            if (list == null) {
                continue;
            }
            bean = DailyProtos.DailyTimedOpensOnWeekday.newBuilder();
            bean.setWeekday(weekDay);
            for (DailySchedule schedule : list) {
                bean.addOpens(schedule.getId());
            }
            openServerDaily.addSchedule(bean);
        }
        msg.setProto(openServerDaily.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqDailyTimedOpens(Role role) {
        ResDailyTimedOpensMessage msg = new ResDailyTimedOpensMessage();
        DailyProtos.ResDailyTimedOpens.Builder dailyTimedOpens = DailyProtos.ResDailyTimedOpens.newBuilder();
        for (DailySchedule schedule : DailyManager.schedule.getSchedules()) {
            DailyProtos.DailyTimedStatusBean.Builder bean = DailyProtos.DailyTimedStatusBean.newBuilder();
            bean.setScheduleID(schedule.getId());
            bean.setDailyID(schedule.getConf().getType());
            bean.setBeginTime(schedule.getPeriodBegin());
            bean.setEndTime(schedule.getPeriodEnd());
            bean.setStatus(schedule.getStatus());
            dailyTimedOpens.addOpens(bean);
        }
        msg.setProto(dailyTimedOpens.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqDailyTimedNext(Role role, int daily) {
        DailySchedule schedule = DailyManager.schedule.getNext(daily);

        ResDailyTimedNextTimeMessage msg = new ResDailyTimedNextTimeMessage();
        DailyProtos.ResDailyTimedNextTime.Builder dailyTimedNextTime = DailyProtos.ResDailyTimedNextTime.newBuilder();
        dailyTimedNextTime.setDailyType(daily);
        dailyTimedNextTime.setOpenTime(schedule != null ? schedule.getPeriodBegin() : 0);
        msg.setProto(dailyTimedNextTime.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void brawlingEnd(long rid, int rank, int score) {
        Role role = DataCenter.getRole(rid);
        if (role == null) {
            return;
        }

        ScuffleCache cache = ConfigCacheManager.getInstance().getCache(ScuffleCache.class);
        List<int[]> rankReward = cache.getRankMap().get(rank);
        if (rankReward == null) {
            rankReward = cache.getRankMap().get(0);
        }

        if (rankReward != null) {
            BackpackStash stash = new BackpackStash();
            stash.increase(rankReward);
            stash.commitToMail(rid, EmailConst.MailId.BRAWLING_RANK, rank);
        }

        Map.Entry<Integer, List<int[]>> entry = cache.getScoreMap().floorEntry(score);
        if (entry != null) {
            List<int[]> scoreReward = entry.getValue();
            BackpackStash stash = new BackpackStash();
            stash.increase(scoreReward);
            stash.commitToMail(rid, EmailConst.MailId.BRAWLING_SCORE, score);
        }
    }

    @Override
    public void reqDailyTimesInfo(Role role) {
        ResDailyTimesInfoMessage msg = new ResDailyTimesInfoMessage();
        DailyProtos.ResDailyTimesInfo.Builder dailyTimesInfo = DailyProtos.ResDailyTimesInfo.newBuilder();
        RoleDaily roleDaily = DataCenter.get(RoleDaily.class,role.getId());
        for (Map.Entry<Integer, RoleDailyTimes> dailyTimes : roleDaily.getDailyTimes().entrySet()) {
            DailyProtos.DailyTimesBean.Builder bean = DailyProtos.DailyTimesBean.newBuilder();
            bean.setDailyID(dailyTimes.getKey());
            bean.setUtimes(dailyTimes.getValue().getUTimes());
            bean.setEtimes(dailyTimes.getValue().getETimes());
            dailyTimesInfo.addTimes(bean);
        }
        msg.setProto(dailyTimesInfo.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void onDailyComplete(Role role, int daily, int times) {
        times += role.getRoleDaily().getDailyTimes().computeIfAbsent(daily, k -> new RoleDailyTimes()).getUTimes();
        onDailyCompleteUpdate(role, daily, times);
    }

    @Override
    public void onDailyCompleteUpdate(Role role, int daily, int times) {
        RoleDaily roleDaily = DataCenter.get(RoleDaily.class,role.getId());
        RoleDailyTimes dailyTimes = roleDaily.getDailyTimes().computeIfAbsent(daily, k -> new RoleDailyTimes());
        dailyTimes.setUTimes(times);
        DataCenter.updateData(roleDaily);

        ResDailyTimesUpdateMessage msg = new ResDailyTimesUpdateMessage();
        DailyProtos.ResDailyTimesUpdate.Builder dailyTimesUpdate = DailyProtos.ResDailyTimesUpdate.newBuilder();
        DailyProtos.DailyTimesBean.Builder bean = DailyProtos.DailyTimesBean.newBuilder();
        bean.setDailyID(daily);
        bean.setUtimes(dailyTimes.getUTimes());
        bean.setEtimes(dailyTimes.getETimes());
        dailyTimesUpdate.addTimes(bean);
        msg.setProto(dailyTimesUpdate.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void onRoleMidnight(Role role) {
        RoleDaily roleDaily = DataCenter.get(RoleDaily.class,role.getId());
        roleDaily.getDailyTimes().clear();

        roleDaily.getMonsterHpRecord().reset();

        DataCenter.updateData(roleDaily);

        reqDailyTimedSchedules(role);
        reqDailyTimedOpens(role);
        reqDailyTimesInfo(role);
    }

    @Override
    public void scheduleUpdateOnMinute() {
        DailyManager.getInstance().scheduleUpdate();
    }

    @Override
    public void onRoleLogin(Role role) {
        CommonData data = SysDataProvider.get(CommonData.class);
        ResArenaResultMessage message = DailyManager.getInstance().getCrossArenaResult(data);
        if (message == null) {
            return;
        }
        MessageUtil.sendMsg(message, role.getId());
    }

    @Override
    public void onRoleExit(Role role, DuplicateConfig config, List<MonsterHpDto> hps) {
        if (config.getDuplicateCate() == MapConst.DUPLICATE_TYPE.CLIMB_TOWER_1 ||
                config.getDuplicateCate() == MapConst.DUPLICATE_TYPE.CLIMB_TOWER_2 ||
                config.getDuplicateCate() == MapConst.DUPLICATE_TYPE.PERSONAL_BOSS) {
            RoleDaily roleDaily = DataCenter.get(RoleDaily.class, role.getId());
            roleDaily.getMonsterHpRecord().record(config.getId(), hps);
            DataCenter.updateData(roleDaily);
        }
    }
}
