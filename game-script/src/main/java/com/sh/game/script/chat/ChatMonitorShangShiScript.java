package com.sh.game.script.chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sh.game.GameContext;
import com.sh.game.common.config.model.ChatMonitorConfig;
import com.sh.game.common.constant.ChatConst;
import com.sh.game.common.entity.User;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.HttpUtil;
import com.sh.game.common.util.Md5Util;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.server.SessionManager;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.chat.script.IChatMonitorShangShiScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;


@Slf4j
@Script
public class ChatMonitorShangShiScript extends ChatMonitorBaseImpScript  implements IChatMonitorShangShiScript {
    @Override
    String chatApi(Role role, int channel, long target, String content, ChatMonitorConfig monitorConfig) {
        Map<String, Object> params = new HashMap<>();
        String signKey = "6Q1AIO3IV4JGIVCC";

        int ts =  TimeUtil.getNowOfSeconds();
        params.put("app_id", monitorConfig.getParams().get("app_id")); //	游戏 ID	是	上士平台的游戏 ID
        params.put("time", ts); //	时间	是	聊天信息发送的时间
        params.put("msg", content); //	内容	是	聊天信息发送的内容
        params.put("chat_channel", getChannelId(channel)); //	频道	是	1-私聊，2-世界，3-公会，4-跨服
        params.put("platform", "android"); //	平台	是	android 或者 ios，默认 android
        params.put("source", 0); //	渠道	是	渠道，默认值 0
        params.put("from_role_id", role.getId()); //	角色 ID	是	发起会话人
        params.put("from_role_name", role.getName()); //	角色名称	是	发起会话人
        params.put("from_server_id", GameContext.getServerId()); //	区服标识	是	发起会话人
        params.put("from_server_name", GameContext.getServerId()+"区服"); //	区服名称	否	发起会话人
        params.put("from_level", role.getLevel()); //	等级	否	发起会话人
        params.put("from_other_level", role.getLevel()); //	其他等级	否	发起会话人
        User user = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(role.getId()));
        if (user != null) {
            params.put("from_user_id", user.getAccount()); //	发起会话人	是	上士用户唯一标识，SDK 登录后返回
            params.put("ip", user.getIp());//	IP	否	发起会话人
        }

        if (channel == ChatConst.ChatType.PERSON) {
            Role receiver = SessionManager.getInstance().getRole(target);
            if (receiver != null) {
                User receiverUser = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(receiver.getId()));
                if (receiverUser != null) {
                    params.put("to_user_id", receiverUser.getAccount());//	接收会话人	否	上士用户唯一标识，SDK 登录后返回
                }
                params.put("to_role_id", receiver.getId()); //	角色 ID	否	发起会话人
                params.put("to_role_name", receiver.getName()); //	角色名称	否	发起会话人
                params.put("to_server_id", GameContext.getServerId()); //	区服标识	否	发起会话人
                params.put("to_server_name", GameContext.getServerId()+"区服"); //	区服名称	否	发起会话人
                params.put("to_level", receiver.getLevel()); //	等级	否	发起会话人
                params.put("to_other_level", receiver.getLevel()); //	其他等级	否	发起会话人
            }
        }

        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder builder = new StringBuilder();
        for (String key : keys) {
            builder.append(key);
            builder.append("=");
            String sData = String.valueOf(params.get(key));
            builder.append(sData);
            builder.append("&");
        }
        String sign = Md5Util.md5(builder.toString().substring(0, builder.length() - 1) +  signKey);
        params.put("sign", sign);

        String ret = HttpUtil.post(monitorConfig.getUrl(), params, 3000);
        if (ret != null) {
            try {
                JSONObject retJson = JSON.parseObject(ret);
                if (retJson.getInteger("state") != 1) {
                    log.error("XY聊天发送失败：{}", ret);
                }
            } catch (Exception e) {
                log.error("访问运营方聊天接口失败: {}", e.getMessage());
            }
        }

        return content;
    }

    private int getChannelId(int channel){
        switch (channel){
            case ChatConst.ChatType.PERSON: // "私聊";
                channel = 1;
                break;
            case ChatConst.ChatType.WORLD: // "世界";
                channel = 2;
                break;
            case ChatConst.ChatType.UNION: // "行会";
                channel = 3;
                break;
             //其它都为2， 因为文档里没写
            case ChatConst.ChatType.SYSTEM: // "系统";
                channel = 2;
                break;
            case ChatConst.ChatType.YELL: // "喊话";
                channel = 2;
                break;
            case ChatConst.ChatType.NEAR: // "附近";
                channel = 2;
                break;

            case ChatConst.ChatType.GROUP: // "队伍";
                channel = 2;
                break;
            case ChatConst.ChatType.REMOTE: // "跨服";
                channel = 2;
                break;
            case ChatConst.ChatType.MAP: // "本地(场景)";
                channel = 2;
                break;
        }
        return channel;
    }
}
