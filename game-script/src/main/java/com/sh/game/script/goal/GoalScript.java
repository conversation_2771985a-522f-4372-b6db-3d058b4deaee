package com.sh.game.script.goal;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.config.model.GoalsConfig;
import com.sh.game.common.config.model.MonsterConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.constant.GoalConst.GoalType;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.*;
import com.sh.game.system.auction.script.IAuctionScript;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.goal.entity.Goal;
import com.sh.game.system.goal.handler.GoalHandleFactory;
import com.sh.game.system.goal.handler.IGoalHanlder;
import com.sh.game.system.goal.script.IGoalScript;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.task.entity.TaskRecord;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;


@Slf4j
@Script
public class GoalScript implements IGoalScript,
        IEventOnRoleLevelUpScript,
        IEventOnRoleZhuanShengUpScript,
        IEventOnMonsterDie,
        IEventOnRoleAttendDailyScript,
        IEventOnRoleSkillLevelUpScript,
        IEventOnRoleMidnightScript,
        IEventOnEquipChangedScript,
        IEventOnDuplicateCompleteScript,
        IEventOnRoleEquipRecycleScript,
        IEventOnRoleStoreBuyScript,
        IEventOnRoleOrHeroAtkChanged,
        IEventOnRoleEquipStarUp,
        IEventOnZhanyiLvUp,
        IEventOnFengHaoLvUp,
        IEventOnRoleCoinChangedScript,
        IEventOnMgBattleEnd
{

    @Override
    public void onRoleAtkMaxChanged(Role role) {
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_ATK_OR_TOTAL);
        GoalManager.getInstance().updateGoal(role, GoalType.UPGRADE_ATTACKVAL);
    }

    @Override
    public void onHeroAtkMaxChanged(Hero hero) {
        GoalManager.getInstance().updateGoal(hero.getRole(), GoalType.HERO_ATK);
        GoalManager.getInstance().updateGoal(hero.getRole(), GoalType.ROLE_ATK_OR_TOTAL);
        GoalManager.getInstance().updateGoal(hero.getRole(), GoalType.UPGRADE_ATTACKVAL);
    }

    @Override
    public void onRoleEquipStarUp(Role role) {
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_EQUIP_WEAR_STAR);
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_EQUIP_WEAR_STAR_TOTAL);
    }

    @Override
    public void onRoleStoreBuy(Role role, int id, int count) {
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_STORE_BUY, id);
    }

    @Override
    public void onRoleEquipRecycle(Role role) {
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_EQUIP_RECYCLE);
    }

    @Override
    public int onChallengeDuplicate(Role role, DuplicateConfig config) {
        return 0;
    }

    @Override
    public void onCompleteDuplicate(Role role, DuplicateConfig config, boolean success) {
        if (success) {
            GoalManager.getInstance().updateGoal(role, GoalType.ROLE_FINISH_DUPLICATE, config.getId());
        }
    }

    @Override
    public void onRoleEquipChanged(Role role, List<ItemChange> changes) {
        boolean gem = false;
        for (ItemChange change : changes) {
            if (change.getIndex() == EquipConst.EquipIndex.POS_JEWEL.getCls()) {
                gem = true;
                break;
            }
        }
        if (gem) {
            GoalManager.getInstance().updateGoal(role, GoalType.ROLE_GEM);
            return;
        }

        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_EQUIP_WEAR);
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_EQUIP_WEAR_STAR);
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_EQUIP_WEAR_STAR_TOTAL);
        GoalManager.getInstance().updateGoal(role, GoalType.WEAR_EQUIP_RANK);
        GoalManager.getInstance().updateGoal(role, GoalType.WEAR_EQUIP_QUALITY);
        GoalManager.getInstance().updateGoal(role, GoalType.WEAR_SHEN_BING_EQUIP_LEVEL);
    }

    @Override
    public void onHeroEquipChanged(Hero hero, List<ItemChange> changes) {
        GoalManager.getInstance().updateGoal(hero.getRole(), GoalType.WEAR_EQUIP_RANK);
    }

    @Override
    public void onRoleShouJueEquipChanged(Role role, List<ItemChange> changes) {

    }

    @Override
    public void onRoleMidnight(Role role) {
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_LOGINS);
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_LOGINS_TOTAL);
        GoalManager.getInstance().updateGoal(role, GoalType.FIRST_RECHARGE_OR_LOGIN_DAYS);
    }

    @Override
    public void onRoleSkillLevelUp(Role role, long actorId, int skillId, int skillLevel) {
        if (role.getId() != actorId) {
            return;
        }
        GoalManager.getInstance().updateGoal(role, GoalType.ROLE_SKILL, skillId, skillLevel);
    }

    @Override
    public void zhanyiUp(Role role, int lv) {
        GoalManager.getInstance().updateGoal(role, GoalType.ZHANYI_LEVEL, lv);
    }

    @Override
    public void fenghaoUp(Role role, int fenghaoId) {
        GoalManager.getInstance().updateGoal(role, GoalType.FENGHAO, fenghaoId);
    }

    @Override
    public void onRoleAttendDaily(Role role, int dailyType) {
        GoalManager.getInstance().updateGoal(role, GoalType.ATTEND_DAILY, dailyType);
    }

    @Override
    public void onMonsterDie(int mapID, int monsterID, int diePointX, int diePointY, long killerID, long ownerID, Set<Long> allThreat, Set<Long> threatAllSet, Set<Long> hurtMap) {
        if (allThreat == null || allThreat.isEmpty()) {
            return;
        }
        MonsterConfig config = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterID);
        if (config == null) {
            return;
        }
        Role role;
        for (long rid : allThreat) {
            role = DataCenter.getRole(rid);
            if (role == null) {
                continue;
            }
            GoalManager.getInstance().updateGoal(role, GoalType.KILL_MAP_MONSTER, mapID);
            GoalManager.getInstance().updateGoal(role, GoalType.KILL_MONSTER, monsterID);
            GoalManager.getInstance().updateGoal(role, GoalType.KILL_TYPE_MONSTER, config.getType());
            GoalManager.getInstance().updateGoal(role, GoalType.KILL_ANYWHERE_MONSTER);
            GoalManager.getInstance().updateGoal(role, GoalType.KILL_MONSTER_SHARE, monsterID);
        }

        if (config.getPaimaiid() != null && config.getPaimaiid().size() > 0){
            ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.scheduleOnItems(monsterID, config.getPaimaiid()));
        }
    }

    @Override
    public void onRoleZhuanShengUp(Role role, long actorId) {
        if (role.getId() == actorId) {
            GoalManager.getInstance().updateGoal(role, GoalType.ROLE_REIN);
        } else {
            GoalManager.getInstance().updateGoal(role, GoalType.HERO_REIN);
        }
    }

    @Override
    public void onRoleLevelUp(Role role, long actorId, int oLv) {
        if (role.getId() != actorId) {
            GoalManager.getInstance().updateGoal(role, GoalType.HERO_LEVEL);
        } else {
            GoalManager.getInstance().updateGoal(role, GoalType.ROLE_LEVEL);
        }
    }

    @Override
    public TaskRecord createTaskRecord(Role role, int taskID, int[] goals, boolean allCheck, int state) {
        return createTaskRecord(role, taskID, goals, allCheck, state, 0);
    }

    @Override
    public TaskRecord createTaskRecord(Role role, int taskID, int[] goals, boolean allCheck, int state, int activityType) {
        int count = 0;
        List<Integer> actTypes = GlobalUtil.findJingHaoList(GameConst.GlobalId.TASK_ACTIVITY_TYPE);
        if (actTypes.contains(activityType)) {
            // 任务多余进度溢出重新赋值
            count = CountManager.getInstance().getCount(role, CountConst.CountType.TASK_PROGRESS_COUNT, activityType);
        }
        TaskRecord record = new TaskRecord();
        record.setTaskId(taskID);
        record.setAllCheck(allCheck);
        record.setStartTime(TimeUtil.getNowOfSeconds());
        if (goals != null && goals.length > 0) {
            record.setState(state);
            for (int goalID : goals) {
                Goal goal = new Goal();
                goal.setGoalId(goalID);
                if (count > 0) {
                    GoalsConfig goalsConfig = ConfigDataManager.getInstance().getById(GoalsConfig.class, goal.getGoalId());
                    if (count > goalsConfig.getCount()) {
                        goal.setCount(goalsConfig.getCount());
                        count = count - goalsConfig.getCount();
                    } else {
                        goal.setCount(count);
                    }
                }
                record.getGoalList().add(goal);
            }
            if (actTypes.contains(activityType)) {
                CountManager.getInstance().setCount(role, CountConst.CountType.TASK_PROGRESS_COUNT, activityType, count);
            }
            checkTaskSchedule(role, record);
        } else {
            record.setState(TaskConst.State.HAS_COMPLETE);
        }

        return record;
    }

    /**
     * 检查任务进度
     *
     * @param role
     * @param record
     */
    public boolean checkTaskSchedule(Role role, TaskRecord record) {
        if (record.getGoalList() == null || record.getGoalList().isEmpty()) {
            record.setState(TaskConst.State.HAS_COMPLETE);
            return true;
        }
        if (record.getState() != TaskConst.State.HAS_ACCEPT) {
            return false;
        }
        boolean change = false;
        boolean finished = false;
        GoalsConfig config;
        int old;
        for (Goal goal : record.getGoalList()) {
            config = ConfigDataManager.getInstance().getById(GoalsConfig.class, goal.getGoalId());
            old = goal.getCount();
            if (doCheck(role, goal, config)) {
                finished = true;
            }
            if (old != goal.getCount()) {
                change = true;
            }
        }
        if (finished) {
            checkCompleted(record);
        }
        return change;
    }

    /**
     * 检查任务目标完成
     *
     * @param role
     * @param goal
     * @param config
     * @return
     */
    private boolean doCheck(Role role, Goal goal, GoalsConfig config) {
        if (config == null || config.getCount() < 1) {
            goal.setFinished(true);
            return true;
        }
        IGoalHanlder handle = GoalHandleFactory.findHandle(config.getType());

        if (handle!=null){
            return  handle.receive(role, goal, config);
        }

        goal.setFinished(true);
        return true;
    }

    @Override
    public boolean checkCompleted(TaskRecord record) {
        if (record.getState() == TaskConst.State.HAS_COMPLETE) {
            return true;
        }
        if (record.getState() != TaskConst.State.HAS_ACCEPT) {
            return false;
        }
        if (record.getGoalList().size() <= 0) {
            record.setState(TaskConst.State.HAS_COMPLETE);
            return true;
        }

        int total = 0;
        int finishCount = 0;
        for (Goal goal : record.getGoalList()) {
            total++;
            if (goal.isFinished()) {
                finishCount++;
            }
        }
        boolean completed = false;
        if ((record.isAllCheck() && finishCount == total) || (!record.isAllCheck() && finishCount > 0)) {
            record.setState(TaskConst.State.HAS_COMPLETE);
            completed = true;
        }
        return completed;
    }

    @Override
    public boolean doupdate(Role role, TaskRecord record, int goalType, int... params) {
        if (record.getState() != TaskConst.State.HAS_ACCEPT) {
            return false;
        }
        boolean changeFinish = false;
        boolean isUpdate = false;
        for (Goal goal : record.getGoalList()) {
            if (goal.isFinished()) {
                continue;
            }
            GoalsConfig config = ConfigDataManager.getInstance().getById(GoalsConfig.class, goal.getGoalId());
            if (config == null || config.getType() != goalType) {
                continue;
            }

            IGoalHanlder goalHanlder = GoalHandleFactory.findHandle(goalType);
            if (goalHanlder == null) {
                continue;
            }

            int progress = goalHanlder.doUpdate(role, config, goal, params);
            if(GoalConst.GoalCoinGroup.CURRENT_OBTAIN.contains(goalType)
                    || GoalConst.GoalCoinGroup.RECEIVE_OBTAIN.contains(goalType)
                    || GoalConst.GoalCoinGroup.ROLE_HISTORY_OBTAIN.contains(goalType)
                    || GoalConst.GoalCoinGroup.RANK.contains(goalType)) {
                if(progress == -1) {
                    isUpdate = true;
                    continue;
                }
            }

            if (progress >= config.getCount()) {
                if (params.length >= 3) {
                    // 传了活动类型的需要计数任务溢出数量
                    List<Integer> actTypes = GlobalUtil.findJingHaoList(GameConst.GlobalId.TASK_ACTIVITY_TYPE);
                    int activityType = params[2];
                    if (actTypes.contains(activityType)) {
                        CountManager.getInstance().setCount(role, CountConst.CountType.TASK_PROGRESS_COUNT, activityType, progress - config.getCount());
                    }
                }
                progress = config.getCount();
            }
            if (goal.isFinished()) {
                continue;
            }

            goal.setCount(progress);
            if (progress >= config.getCount()) {
                goal.setCount(config.getCount());
                goal.setFinished(true);
                changeFinish = true;
            }
            // CampManager.getInstance().updateCount(role, goal.getGoalId());
            isUpdate = true;
        }
        if (changeFinish) {
            GoalManager.getInstance().checkCompleted(record);
        }
        return isUpdate;
    }


    @Override
    public void onRoleCoinChanged(Role role, Map<Integer, Long> changes, int action) {
        //不算消费的行为
        if (ActionConst.CONSUME_IGNORE_ACTIONS.contains(action)) {
            return;
        }
        long lingfuCost = changes.getOrDefault(BagConst.ItemId.MONEY, 0L);
        long bindLingfuCost = changes.getOrDefault(BagConst.ItemId.MONEY_BIND, 0L);
        int xiaofeiNum = 0;
        int bindXiaofeiNum = 0;
        //lingfuCost<0为消耗灵符
        if (lingfuCost < 0) {
            xiaofeiNum = (int) -lingfuCost;
        }
        //bindLingfuCost<0为消耗绑定灵符
        if (bindLingfuCost < 0) {
            bindXiaofeiNum = (int) -bindLingfuCost;
        }

        //本次消费的灵符数量
        int lingfuNum = xiaofeiNum + bindXiaofeiNum;
        if (lingfuNum <= 0) {
            return;
        }

        GoalManager.getInstance().updateGoal(role, GoalType.LINGFU_COST, lingfuNum);
    }

    @Override
    public void onBattleEnd(Role role, int battleType, long targetId, int targetCfgId, boolean success, long totalHurt) {
        GoalManager.getInstance().updateGoal(role, GoalType.BATTLE_COUNT, battleType);
    }
}
