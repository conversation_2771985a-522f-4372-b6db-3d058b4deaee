package com.sh.game.script.activity;

import com.google.common.primitives.Ints;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.activity.ResTouZiHaoLiInfoMessage;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.config.model.TouZiHaoLiConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.script.activity.abc.AbstractActivityJieRiTouZiScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.ActivityTouZiHaoLiData;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Script
@Slf4j
public class ActivityWorldCupTouZiScript extends AbstractActivityJieRiTouZiScript {
    @Override
    public int getType() {
        return ActivityConst.WORLDCUP_TOUZI;
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        //32天后无法投资
        int limit = GlobalUtil.getGlobalInt(GameConst.GlobalId.WORLDCUP_TOUZI_ACTIVITY_LIMIT);
        int now = TimeUtil.getNowOfSeconds();
        if ((schedule.getBeginAt() + TimeUtil.ONE_DAY_IN_SECONDS * limit) < now) {
            log.error("投资豪礼活动id:{},超过投资时间,依然购买投资,请联系客户端,角色:{},昵称:{},活动开启时间:{},现在时间:{}",
                    schedule.getActivityID(), role.getRoleId(), role.getName(), schedule.getBeginAt(), now);
            return;
        }

        //不需要第一天才能投资
        RoleActivity roleActivity = role.getRoleActivity();
        ActivityTouZiHaoLiData activityTouZiHaoLiData = roleActivity.getTouZiHaoLiDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivityTouZiHaoLiData());
        List<TouZiHaoLiConfig> configList = ConfigDataManager.getInstance().getList(TouZiHaoLiConfig.class);
        TouZiHaoLiConfig touZiHaoLiConfig = null;
        for (TouZiHaoLiConfig config : configList) {
            if (config.getActivityId() != schedule.getActivityID()
                    || !ConditionUtil.validate(role, config.getCondition())
                    || config.getRechargeId() != rechargeConfig.getId()
                    || activityTouZiHaoLiData.getGiftIdMap().containsKey(config.getGroup())
                    || activityTouZiHaoLiData.getGiftCircleMap().getOrDefault(config.getGroup(), 1) != config.getCircle()
                    || config.getLimit() != 1) {
                continue;
            }
            touZiHaoLiConfig = config;
            break;
        }
        if (touZiHaoLiConfig == null) {
            return;
        }
        if (!activityTouZiHaoLiData.getGiftCircleMap().containsKey(touZiHaoLiConfig.getGroup())) {
            activityTouZiHaoLiData.getGiftCircleMap().put(touZiHaoLiConfig.getGroup(), 1);
        }
        activityTouZiHaoLiData.getGiftIdMap().put(touZiHaoLiConfig.getGroup(), new ArrayList<>());
        activityTouZiHaoLiData.getBuyGiftTime().put(touZiHaoLiConfig.getGroup(), TimeUtil.getNowOfMills());
        DataCenter.updateData(roleActivity);

        AnnounceManager.getInstance().post(touZiHaoLiConfig.getAnnounce(), 0L, role);
        sendMsg(role, schedule.getActivityID());
        log.info("投资豪礼活动id:{},成功购买直购礼包,角色:{},昵称:{},直购礼包grop:{}", schedule.getActivityID(), role.getRoleId(), role.getName(), touZiHaoLiConfig.getGroup());
    }


    @Override
    protected void sendMsg(Role role, int activityId) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        if (schedule.getActivityID() != activityId) {
            return;
        }
        RoleActivity roleActivity = role.getRoleActivity();
        ActivityTouZiHaoLiData activityTouZiHaoLiData = roleActivity.getTouZiHaoLiDataMap().getOrDefault(schedule.getActivityID(), new ActivityTouZiHaoLiData());

        ResTouZiHaoLiInfoMessage msg = new ResTouZiHaoLiInfoMessage();
        ActivityProtos.ResTouZiHaoLiInfo.Builder protoBuilder = ActivityProtos.ResTouZiHaoLiInfo.newBuilder();
        protoBuilder.setActivityId(schedule.getActivityID());
        List<ActivityProtos.TouZiHaoLiBean> beanList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : activityTouZiHaoLiData.getGiftCircleMap().entrySet()) {
            Integer giftGroup = entry.getKey();
            Integer circle = entry.getValue();
            if (giftGroup == null || circle == null) {
                continue;
            }
            ActivityProtos.TouZiHaoLiBean.Builder bean = ActivityProtos.TouZiHaoLiBean.newBuilder();
            List<Integer> rerwardList = activityTouZiHaoLiData.getGiftIdMap().get(giftGroup);
            bean.setCircle(circle);
            bean.addAllRewardList(rerwardList == null ? new ArrayList<>() : rerwardList);
            bean.setGiftGroup(giftGroup);
            Long time = activityTouZiHaoLiData.getBuyGiftTime().get(giftGroup);
            if (time != null) {
                int timeSeconds = Ints.checkedCast(time / 1000);
                bean.setBuyGiftTime(timeSeconds);
            }
            beanList.add(bean.build());
        }
        protoBuilder.addAllTouZiHaoLiBeanList(beanList);
        msg.setProto(protoBuilder.build());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void reqReceiveReWard(Role role, int cfgId, int activityId) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        if (schedule.getActivityID() != activityId) {
            return;
        }
        TouZiHaoLiConfig config = ConfigDataManager.getInstance().getById(TouZiHaoLiConfig.class, cfgId);
        if (config == null) {
            log.error("投资豪礼活动id:{},领取奖励,找不到配置表,角色:{},昵称:{},配置id:{}", schedule.getActivityID(), role.getRoleId(), role.getName(), cfgId);
            return;
        }
        if (!ConditionUtil.validate(role, config.getCondition())) {
            return;
        }
        //校验是否购买该直购礼包
        RoleActivity roleActivity = role.getRoleActivity();
        ActivityTouZiHaoLiData activityTouZiHaoLiData = roleActivity.getTouZiHaoLiDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivityTouZiHaoLiData());
        if (!activityTouZiHaoLiData.getGiftIdMap().containsKey(config.getGroup())) {
            log.error("投资豪礼活动id:{},未购买该直购礼包,角色:{},昵称:{},配置id:{},已购买的直购礼包grop:{}",
                    schedule.getActivityID(), role.getRoleId(), role.getName(), cfgId, activityTouZiHaoLiData.getGiftIdMap().keySet());
            return;
        }
        Integer circle = activityTouZiHaoLiData.getGiftCircleMap().computeIfAbsent(config.getGroup(), k -> 1);

        if (config.getCircle() != circle) {
            log.error("投资豪礼活动id:{},领取奖励,参数轮次异常,角色:{},昵称:{},配置id:{},玩家轮次:{}",
                    schedule.getActivityID(), role.getRoleId(), role.getName(), cfgId, circle);
            return;
        }

        //校验是否领取过
        List<Integer> rewardList = activityTouZiHaoLiData.getGiftIdMap().computeIfAbsent(config.getGroup(), k -> new ArrayList<>());
        if (rewardList.contains(cfgId)) {
            log.error("投资豪礼活动id:{},不可重复领取,角色:{},昵称:{},配置id:{},已领取的奖励:{}", schedule.getActivityID(), role.getRoleId(), role.getName(), cfgId, rewardList);
            return;
        }

        if (!activityTouZiHaoLiData.getBuyGiftTime().containsKey(config.getGroup())) {
            return;
        }
        Long time = activityTouZiHaoLiData.getBuyGiftTime().get(config.getGroup());
        int day = TimeUtil.getNaturalDayFromTime(time);


        //判断是否达到领取日期
        if (config.getLimit() > day + 1) {
            log.error("投资豪礼活动id:{},未达到领取日期,角色:{},昵称:{},配置id:{}", schedule.getActivityID(), role.getRoleId(), role.getName(), cfgId);
            return;
        }

        rewardList.add(config.getId());
        DataCenter.updateData(roleActivity);

        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.TOUZI_HAOLI_REWARD)) {
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(config.getReward(), LogAction.TOUZI_HAOLI_REWARD));
        }

        log.info("投资豪礼活动id:{},成功领取奖励,角色:{},昵称:{},配置id:{},礼包组:{},已领取的奖励:{}",
                schedule.getActivityID(), role.getRoleId(), role.getName(), cfgId, config.getGroup(), rewardList);
        //是否进入下一轮
        verifNextRound(role, config.getGroup());

        sendMsg(role, activityId);
    }

    private void verifNextRound(Role role, int group) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        RoleActivity roleActivity = role.getRoleActivity();
        ActivityTouZiHaoLiData activityTouZiHaoLiData = roleActivity.getTouZiHaoLiDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivityTouZiHaoLiData());

        List<TouZiHaoLiConfig> configList = ConfigDataManager.getInstance().getList(TouZiHaoLiConfig.class);

        List<TouZiHaoLiConfig> fuheConfigList = new ArrayList<>();
        for (TouZiHaoLiConfig config : configList) {
            //这里不能校验condition，不然玩家没达到条件就到下一轮了
            if (schedule.getActivityID() != config.getActivityId()) {
                continue;
            }
            if (group != config.getGroup()) {
                continue;
            }

            Integer circle = activityTouZiHaoLiData.getGiftCircleMap().getOrDefault(config.getGroup(), 1);
            if (circle != config.getCircle()) {
                continue;
            }
            fuheConfigList.add(config);
        }
        List<Integer> rewardList = activityTouZiHaoLiData.getGiftIdMap().getOrDefault(group, new ArrayList<>());
        if (rewardList.size() != fuheConfigList.size()) {
            return;
        }
        //开启下一轮
        activityTouZiHaoLiData.getBuyGiftTime().remove(group);
        activityTouZiHaoLiData.getGiftIdMap().remove(group);
        Integer circle = activityTouZiHaoLiData.getGiftCircleMap().getOrDefault(group, 1);
        activityTouZiHaoLiData.getGiftCircleMap().put(group, circle + 1);
        DataCenter.updateData(roleActivity);
        log.info("投资特殊活动:{},开启下一轮,角色:{},昵称:{},礼包组:{},轮次:{}", schedule.getActivityID(), role.getRoleId(), role.getName(), group, activityTouZiHaoLiData.getGiftCircleMap().get(group));

    }





}
