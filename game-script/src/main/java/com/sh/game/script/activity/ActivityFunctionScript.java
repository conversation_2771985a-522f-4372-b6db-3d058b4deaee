package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.function.ResFunctionInfoMessage;
import com.sh.game.common.config.model.FunctionTotalConfig;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.sys.OtherData;
import com.sh.game.common.util.BoxUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.protos.FunctionProtos;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.function.entity.Function;
import com.sh.game.system.function.script.IFunctionScript;
import com.sh.script.annotation.Script;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @data 2020/10/27 14:14
 */
@Script
public class ActivityFunctionScript extends AbstractActivityScript implements IFunctionScript {
    @Override
    public int getType() {
        return 1024;
    }


    @Override
    public void getReward(Role role, int type, int count) {
        if (count <= 0) {
            return;
        }
        Map<Integer, Function> functionCount = role.getRoleActivity().getFunctionCount();
        List<FunctionTotalConfig> list = ConfigDataManager.getInstance().getList(FunctionTotalConfig.class);
        FunctionTotalConfig config = null;
        for (FunctionTotalConfig rewardConfig : list) {
            if (rewardConfig.getType().contains(type) && rewardConfig.getCount() == count) {
                config = rewardConfig;
                break;
            }
        }
        if (config == null) return;
        Function function = functionCount.get(type < 2 ? 1 : 2);
        if (function == null) return;
        if (function.getCount() < config.getCount()) return;
        if (function.getList().contains(count)) return;

        List<Item> items = BoxUtil.openBox(role, config.getReward());
        BackpackStash stash = new BackpackStash(role);
        stash.increase(items);
        if (!stash.commit(role, LogAction.FUNCTION, false)) {
            return;
        }

        function.getList().add(count);
        DataCenter.updateData(role.getRoleActivity());
        sendInfo(role);
    }

    @Override
    public void sendInfo(Role role) {
        OtherData otherData = DataCenter.getOtherData();
        Map<Integer, List<Long>> function = otherData.getFunction();
        List<FunctionProtos.FunctionBean> beanList = new ArrayList<>();
        Map<Integer, Function> functionCount = role.getRoleActivity().getFunctionCount();
        if (functionCount == null || functionCount.isEmpty()) {
            functionCount = initFunction(role);
        }
        for (Map.Entry<Integer, Function> entry : functionCount.entrySet()) {
            FunctionProtos.FunctionBean.Builder bean = FunctionProtos.FunctionBean.newBuilder();
            bean.setType(entry.getKey());
            bean.setCount(entry.getValue().getCount());
            List<FunctionProtos.FunctionRewardBean> beans = new ArrayList<>();
            for (Integer count : entry.getValue().getList()) {
                FunctionProtos.FunctionRewardBean.Builder rewardBean = FunctionProtos.FunctionRewardBean.newBuilder();
                rewardBean.setNeedCount(count);
                rewardBean.setState(true);
                beans.add(rewardBean.build());
            }
            bean.addAllBeans(beans);
            List<String> names = new ArrayList<>();
            if (entry.getKey() == 1) {
                //
                for (int i = 0; i < 3; i++) {
                    List<Long> longs = function.getOrDefault(2, new ArrayList<>());
                    if (i < longs.size()) {
                        String name = DataCenter.getRole(longs.get(i)).getName();
                        names.add(name);
                    } else {
                        names.add(null);
                    }
                }
                for (int i = 0; i < 3; i++) {
                    List<Long> longs = function.getOrDefault(3, new ArrayList<>());
                    if (i < longs.size()) {
                        String name = DataCenter.getRole(longs.get(i)).getName();
                        names.add(name);
                    } else {
                        names.add(null);
                    }
                }
            } else {
                for (Map.Entry<Integer, List<Long>> listEntry : function.entrySet()) {
                    if (listEntry.getKey() == 2 || listEntry.getKey() == 3) {
                        continue;
                    }
                    if (listEntry.getValue().size() > 0) {
                        String name = DataCenter.getRole(listEntry.getValue().get(0)).getName();
                        names.add(name);
                    }
                }
            }
            bean.addAllNames(names);
            beanList.add(bean.build());
        }
        ResFunctionInfoMessage msg = new ResFunctionInfoMessage();
        msg.setProto(FunctionProtos.ResFunctionInfo.newBuilder()
                .addAllBeanList(beanList)
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    private Map<Integer, Function> initFunction(Role role) {
        Map<Integer, Function> map = new HashMap<>();
        for (int i = 0; i < 2; i++) {
            map.put(i + 1, new Function());
        }

        role.getRoleActivity().setFunctionCount(map);
        DataCenter.updateData(role.getRoleActivity());
        return map;
    }

    @Override
    public void count(Role role, int itemId) {
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
        if (itemConfig == null) return;
        int type;
        if (itemConfig.getGodEquip() == 2 || itemConfig.getGodEquip() == 3) {
            type = 1;
        } else if (itemConfig.getGodEquip() == 4) {
            type = 2;
        } else {
            return;
        }
        Map<Integer, Function> functionCount = role.getRoleActivity().getFunctionCount();
        Function function = functionCount.getOrDefault(type, new Function(0, new ArrayList<>()));
        function.setCount(function.getCount() + 1);

        role.proxyCall(proxy -> proxy.functionCount(itemId));
    }
}
