package com.sh.game.script.activity;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.system.activity.ResWorldCupDianQiuInfoMessage;
import com.sh.game.common.config.cache.WorldCupDianQiuCache;
import com.sh.game.common.config.model.WorldCupDianQiuConfig;
import com.sh.game.common.config.model.WorldCupDianQiuCountConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.sys.CommonData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleNormal;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.ActivityWorldCupDianQiuData;
import com.sh.game.system.activity.script.IActivityWorldCupDianQiuScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/2 14:48
 */
@Script
@Slf4j
public class ActivityWorldCupDianQiuScript extends AbstractActivityScript implements IActivityWorldCupDianQiuScript, IEventOnRoleMidnightScript {

    @Override
    public int getType() {
        return ActivityConst.WORLD_CUP_DIAN_QIU;
    }

    @Override
    public void onRoleMidnight(Role role) {
        RoleNormal normal = role.findNormal();
        for (Map.Entry<Integer, ActivityWorldCupDianQiuData> entry : normal.getDianQiuDataMap().entrySet()) {
            ActivityWorldCupDianQiuData data = entry.getValue();
            Map<Integer, Integer> dianQiuNum = data.getDianQiuNum();
            if (dianQiuNum.isEmpty()) {
                continue;
            }

            int linFuCount = 0;
            for (Map.Entry<Integer, Integer> e : dianQiuNum.entrySet()) {
                Integer count = e.getKey();
                Integer num = e.getValue();
                linFuCount = (int) (linFuCount + Math.pow(10, count - 1) * num);
            }

            Integer activityId = entry.getKey();
            data.setDianQiuCount(0);
            log.info("点球大作战，发送玩家奖励并充值每日点球次数，活动id {} 玩家id {} name {} 发送灵符数量 {} 玩家点球数字详情 {}", activityId, role.getId(), role.getName(), linFuCount, JSON.toJSONString(dianQiuNum));
            dianQiuNum.clear();
            DataCenter.updateData(normal);

            Item item = ItemUtil.create(BagConst.ItemId.MONEY_BIND, linFuCount, LogAction.WORLD_CUP_DIAN_QIU_LING_FU);
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.WORLD_CUP_DIAN_QIU_MAIL, Collections.singletonList(item),linFuCount + "");
            info(role);
        }
    }

    @Override
    public void info(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        int activityID = schedule.getActivityID();
        RoleNormal normal = role.findNormal();
        ActivityWorldCupDianQiuData data = normal.getDianQiuDataMap().computeIfAbsent(activityID, k -> new ActivityWorldCupDianQiuData());
        CommonData commonData = SysDataProvider.get(CommonData.class);

        ResWorldCupDianQiuInfoMessage msg = new ResWorldCupDianQiuInfoMessage();
        ActivityProtos.ResWorldCupDianQiuInfo.Builder protoBuilder = ActivityProtos.ResWorldCupDianQiuInfo.newBuilder();
        protoBuilder.setDianQiuCount(data.getDianQiuCount());
        protoBuilder.addAllKuangHuanReward(data.getKuangHuanReward());
        protoBuilder.setTotalDianQiu(data.getTotalDianQiu());
        protoBuilder.setServerTotalDianQiu(commonData.getServerDianQiu().getOrDefault(activityID, 0));
        for (Map.Entry<Integer, Integer> entry : data.getDianQiuNum().entrySet()) {
            protoBuilder.addDianQiuNum(AbcProtos.CommonKeyValueBean.newBuilder()
                    .setKey(entry.getKey())
                    .setValue(entry.getValue())
                    .build());
        }

        msg.setProto(protoBuilder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqDianQiu(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        int activityID = schedule.getActivityID();
        int daily = role.getRechargeDaily();

        WorldCupDianQiuCache cache = ConfigCacheManager.getInstance().getCache(WorldCupDianQiuCache.class);
        int canUseCount = 0;
        for (WorldCupDianQiuConfig config : cache.findConfigList(activityID)) {
            if (!ConditionUtil.validate(role, config.getCondition())) {
                continue;
            }

            if (config.getRecharge() > daily) {
                continue;
            }
            canUseCount = config.getCount();
        }

        RoleNormal normal = role.findNormal();
        ActivityWorldCupDianQiuData data = normal.getDianQiuDataMap().computeIfAbsent(activityID, k -> new ActivityWorldCupDianQiuData());
        int useCount = data.getDianQiuCount();
        if (useCount >= canUseCount) {
            log.error("点球大作战，玩家请求点球，玩家点球次数不足，活动id {} 玩家id {} name {} 已使用次数 {} 可使用次数 {} 玩家今日充值 {}", activityID, role.getId(), role.getName(), useCount, canUseCount, daily);
            return;
        }
        useCount++;

        WorldCupDianQiuConfig config = cache.findConfig(activityID, useCount);

        List<int[]> dianqiu = config.getDianqiu();
        List<Integer> numList = new ArrayList<>(dianqiu.size());
        List<Integer> weightList = new ArrayList<>(dianqiu.size());
        for (int[] param : dianqiu) {
            if (2 > param.length) {
                log.error("点球大作战，玩家请求点球，点球数据配置错误，活动id {} 玩家id {} name {} 配置id {}", activityID, role.getId(), role.getName(), config.getDianqiu());
                return;
            }
            numList.add(param[0]);
            weightList.add(param[1]);
        }

        int index = RandomUtil.randomIndexByProb(weightList);
        if (0 > index) {
            log.error("点球大作战，玩家请求点球，点球数据配置错误，活动id {} 玩家id {} name {} 配置id {}", activityID, role.getId(), role.getName(), config.getDianqiu());
            return;
        }
        Integer num = numList.get(index);
        data.setDianQiuCount(useCount);
        Map<Integer, Integer> dianQiuNum = data.getDianQiuNum();
        dianQiuNum.put(useCount, num);

        data.setTotalDianQiu(data.getTotalDianQiu() + 1);
        DataCenter.updateData(normal);

        CommonData commonData = SysDataProvider.get(CommonData.class);
        Integer serverCount = commonData.getServerDianQiu().compute(activityID, (k, v) -> {
            if (v == null) {
                return 1;
            }
            v++;
            return v;
        });
        DataCenter.updateData(commonData);

        log.info("点球大作战，玩家请求点球，玩家点球成功，活动id {} 玩家id {} name {} 已使用点球次数 {} 可使用点球次数 {} 玩家总点球次数 {} 全服总点球次数 {} 玩家今日充值 {} 玩家点球数字详情 {}",
                activityID, role.getId(), role.getName(), useCount, canUseCount, data.getTotalDianQiu(), serverCount, daily, JSON.toJSONString(dianQiuNum));
        AnnounceManager.getInstance().post(config.getAnnounce(), 0L, role);
        info(role);
    }

    @Override
    public void reqKuangHuanReward(Role role, List<Integer> idList) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        int activityID = schedule.getActivityID();
        int announceId = 0;
        for (Integer cid : idList) {

            WorldCupDianQiuCountConfig config = ConfigDataManager.getInstance().getById(WorldCupDianQiuCountConfig.class, cid);
            if (config == null) {
                log.error("点球大作战，玩家请求领取点球狂欢奖励，配置找不到，活动id {} 玩家id {} name {} 配置id {}", activityID, role.getId(), role.getName(), cid);
                continue;
            }

            if (config.getActivityId() != activityID) {
                log.error("点球大作战，玩家请求领取点球狂欢奖励，活动id不对，活动id {} 玩家id {} name {} 配置id {}", activityID, role.getId(), role.getName(), cid);
                continue;
            }

            RoleNormal normal = role.findNormal();
            ActivityWorldCupDianQiuData data = normal.getDianQiuDataMap().computeIfAbsent(activityID, k -> new ActivityWorldCupDianQiuData());
            List<Integer> kuangHuanReward = data.getKuangHuanReward();
            if (kuangHuanReward.contains(cid)) {
                log.error("点球大作战，玩家请求领取点球狂欢奖励，玩家已领取过当前配置，活动id {} 玩家id {} name {} 配置id {} 玩家领取详情 {}", activityID, role.getId(), role.getName(), cid, JSON.toJSONString(kuangHuanReward));
                continue;
            }

            int count = 0;

            switch (config.getType()) {
                case 1:
                    count = SysDataProvider.get(CommonData.class).getServerDianQiu().getOrDefault(activityID, 0);
                    break;
                case 2:
                    count = data.getTotalDianQiu();
                    break;
                default:
                    continue;
            }

            if (config.getCount() > count) {
                log.error("点球大作战，玩家请求领取点球狂欢奖励，条件未达成，活动id {} 玩家id {} name {} 配置id {} 类型 {} 当前达成情况 {}", activityID, role.getId(), role.getName(), cid, config.getType(), count);
                continue;
            }

            kuangHuanReward.add(cid);

            BackpackStash stash = new BackpackStash(role);
            stash.increase(config.getReward());
            if (!stash.commit(role, LogAction.WORLD_CUP_DIAN_QIU_KUANG_HUAN)) {
                MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, EmailConst.toMailAttach(config.getReward(), LogAction.WORLD_CUP_DIAN_QIU_KUANG_HUAN));
            }
            if (config.getAnnounce() > 0) {
                announceId = config.getAnnounce();
            }
            log.info("点球大作战，玩家请求领取点球狂欢奖励，领取成功，活动id {} 玩家id {} name {} 配置id {} 类型 {} 当前达成情况 {} 玩家领取情况 {}", activityID, role.getId(), role.getName(), cid, config.getType(), count, JSON.toJSONString(kuangHuanReward));
        }
        AnnounceManager.getInstance().post(announceId, 0L, role);
        info(role);
    }
}
