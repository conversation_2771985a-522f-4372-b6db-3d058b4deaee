package com.sh.game.script.militaryRank;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.militaryRank.ResMilitaryRankInfoMessage;
import com.sh.game.common.communication.notice.MilitaryRankLevelChangeNotice;
import com.sh.game.common.config.cache.TaskCache;
import com.sh.game.common.config.model.GoalsConfig;
import com.sh.game.common.config.model.MilitaryRankConfig;
import com.sh.game.common.config.model.TaskConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.TaskConst;
import com.sh.game.common.constant.TaskTypeEnum;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnGoalUpdateScript;
import com.sh.game.event.IEventOnRoleConditionChangedScript;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.protos.MilitaryRankProtos;
import com.sh.game.protos.TaskProtos;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.goal.entity.Goal;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.militaryRank.entity.RoleMilitaryRank;
import com.sh.game.system.militaryRank.script.IMilitaryRankScript;
import com.sh.game.system.task.entity.TaskRecord;
import com.sh.game.system.unionCamp.UnionCampManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 军衔系统
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2022-05-09
 **/
@Slf4j
@Script(order = 2)
public class MilitaryRankScript implements IMilitaryRankScript, IEventOnRoleMidnightScript,
        IEventOnGoalUpdateScript, IEventOnRoleConditionChangedScript {

    @Override
    public RoleMilitaryRank find(long rid) {
        RoleMilitaryRank roleMilitaryRank = DataCenter.get(RoleMilitaryRank.class, rid);
        if (roleMilitaryRank == null) {
            roleMilitaryRank = new RoleMilitaryRank();
            roleMilitaryRank.setId(rid);
            roleMilitaryRank.setMilitaryRankCfgId(1000);
            DataCenter.insertData(roleMilitaryRank, true);
        }

        return roleMilitaryRank;
    }

    @Override
    public void sendMilitaryRankInfo(Role role) {
        acceptMilitaryRankTask(role);
        sendMilitaryRankInfo(role, false);
    }

    private void sendMilitaryRankInfo(Role role, boolean isLevelUp) {
        RoleMilitaryRank roleMilitaryRank = find(role.getRoleId());
        ResMilitaryRankInfoMessage msg = new ResMilitaryRankInfoMessage();
        MilitaryRankProtos.ResMilitaryRankInfo.Builder militaryRankInfo = MilitaryRankProtos.ResMilitaryRankInfo.newBuilder();
        militaryRankInfo.setLevel(roleMilitaryRank.getMilitaryRankCfgId());
        List<TaskProtos.TaskDataBean> beans = new ArrayList<>();
        for (TaskRecord taskRecord : roleMilitaryRank.getTaskRecordList()) {
            if (taskRecord == null) {
                continue;
            }
            beans.add(taskRecord.toDataBean());
        }
        militaryRankInfo.addAllTaskBean(beans);
        long lastStamp = roleMilitaryRank.getRequiredDailyStamp() * TimeUtil.ONE_MILLS;
        militaryRankInfo.setIsRequired(TimeUtil.isSameDay(TimeUtil.getNowOfMills(), lastStamp));
        militaryRankInfo.setIsLevelUp(isLevelUp);
        msg.setProto(militaryRankInfo.build());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void militaryRankUp(Role role) {
        RoleMilitaryRank roleMilitaryRank = find(role.getId());
        MilitaryRankConfig config = ConfigDataManager.getInstance().getById(MilitaryRankConfig.class, roleMilitaryRank.getMilitaryRankCfgId());
        if (config == null || !ConditionUtil.validate(role, config.getCondition())) {
            return;
        }

        MilitaryRankConfig nextConfig = ConfigDataManager.getInstance().getById(MilitaryRankConfig.class, config.getNextId());
        if (nextConfig == null) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getCost());
        if (!stash.commit(role, LogAction.MILITARY_RANK_UP_COST)) {
            return;
        }
        roleMilitaryRank.setMilitaryRankCfgId(nextConfig.getId());
        DataCenter.updateData(roleMilitaryRank);
        log.info("军衔-等级提升成功#玩家：{}，昵称：{}，军衔等级提升：{} -> {}",
                role.getRoleId(), role.getName(), config.getId(), nextConfig.getId());

        int unionCampType = UnionCampManager.getInstance().findUnionCampType(role);
        AnnounceManager.getInstance().post(nextConfig.getAnnounce(), 0, role,
                unionCampType == 1 ? nextConfig.getName() : nextConfig.getName2());

        sendMilitaryRankInfo(role, true);

        sendMilitaryRankLevelChangeNotice(role);
    }

    @Override
    public void gainTaskReward(Role role, int taskId) {
        RoleMilitaryRank roleMilitaryRank = find(role.getId());
        Optional<TaskRecord> taskRecordOptional = roleMilitaryRank.getTaskRecordList().stream()
                .filter(e -> e.getTaskId() == taskId).findFirst();
        if (!taskRecordOptional.isPresent()) {
            return;
        }
        TaskRecord taskRecord = taskRecordOptional.get();
        if (!taskRecord.checkState(TaskConst.State.HAS_COMPLETE)) {
            return;
        }
        TaskConfig taskConfig = ConfigDataManager.getInstance().getById(TaskConfig.class, taskId);
        if (taskConfig == null) {
            return;
        }
        taskRecord.setState(TaskConst.State.HAS_SUBMIT);
        BackpackStash stash = new BackpackStash(role);
        stash.increase(taskConfig.getRewards());
        if (!stash.commit(role, LogAction.MILITARY_RANK_TASK_REWARD)) {
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(taskConfig.getRewards(), LogAction.MILITARY_RANK_TASK_REWARD));
        }
        DataCenter.updateData(roleMilitaryRank);
        log.info("军衔-领取任务奖励#玩家：{}，昵称：{}，领取军衔任务：{}，奖励", role.getRoleId(), role.getName(), taskId);

        sendMilitaryRankInfo(role);
    }

    @Override
    public void gainDailyReward(Role role) {
        RoleMilitaryRank roleMilitaryRank = find(role.getId());
        long requiredDailyStamp = roleMilitaryRank.getRequiredDailyStamp() * TimeUtil.ONE_MILLS;
        if (TimeUtil.isSameDay(TimeUtil.getNowOfMills(), requiredDailyStamp)) {
            return;
        }
        MilitaryRankConfig config = ConfigDataManager.getInstance().getById(MilitaryRankConfig.class, roleMilitaryRank.getMilitaryRankCfgId());
        if (config == null) {
            return;
        }
        roleMilitaryRank.setRequiredDailyStamp(TimeUtil.getNowOfSeconds());
        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.MILITARY_RANK_DAILY_REWARD)) {
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(config.getReward(), LogAction.MILITARY_RANK_DAILY_REWARD));
        }
        DataCenter.updateData(roleMilitaryRank);

        sendMilitaryRankInfo(role);

        log.info("军衔-领取日常俸禄#玩家：{}，昵称：{}，军衔：{}，领取日常俸禄",
                role.getRoleId(), role.getName(), config.getId());
    }

    @Override
    public void onGoalUpdate(Role role, int goalType, int... params) {
        boolean isUpdateTask = false;
        RoleMilitaryRank roleMilitaryRank = find(role.getRoleId());
        List<TaskRecord> taskRecordList = roleMilitaryRank.getTaskRecordList();
        for (TaskRecord taskRecord : taskRecordList) {
            if (taskRecord == null) {
                continue;
            }
            for (Goal goal : taskRecord.getGoalList()) {
                GoalsConfig goalsConfig = ConfigDataManager.getInstance().getById(GoalsConfig.class, goal.getGoalId());
                if (goalsConfig == null || goalsConfig.getType() != goalType) {
                    continue;
                }
                if (!GoalManager.getInstance().doupdate(role, taskRecord, goalType, params)) {
                    continue;
                }
                isUpdateTask = true;
            }
        }

        if (!isUpdateTask) {
            return;
        }
        DataCenter.updateData(roleMilitaryRank);
        sendMilitaryRankInfo(role);
    }

    @Override
    public void onRoleMidnight(Role role) {
        RoleMilitaryRank roleMilitaryRank = find(role.getRoleId());
        roleMilitaryRank.getTaskRecordList().clear();
        DataCenter.updateData(roleMilitaryRank);
        acceptMilitaryRankTask(role);
        sendMilitaryRankInfo(role);
    }

    /**
     * 接受军衔任务
     *
     * @param role 角色
     */
    private void acceptMilitaryRankTask(Role role) {
        List<TaskRecord> acceptTaskList = findAcceptTaskList(role);
        if (acceptTaskList.isEmpty()) {
            return;
        }
        RoleMilitaryRank roleMilitaryRank = find(role.getRoleId());
        List<TaskRecord> taskRecordList = roleMilitaryRank.getTaskRecordList();
        taskRecordList.addAll(acceptTaskList);
        DataCenter.updateData(roleMilitaryRank);
        log.info("军衔-任务接取#玩家：{}，昵称：{}，接取军衔任务：{}", role.getRoleId(), role.getName(),
                acceptTaskList.stream().map(TaskRecord::getTaskId).collect(Collectors.toList()));
        sendMilitaryRankInfo(role);
    }

    @Override
    public void onRoleConditionChanged(Role role, int conditionType, Object... params) {
        // acceptMilitaryRankTask(role);
    }

    private List<TaskRecord> findAcceptTaskList(Role role) {
        RoleMilitaryRank roleMilitaryRank = find(role.getRoleId());
        List<TaskRecord> taskRecordList = new ArrayList<>();
        List<Integer> taskCfgIdList = roleMilitaryRank.getTaskRecordList().stream()
                .map(TaskRecord::getTaskId).collect(Collectors.toList());
        TaskCache cache = ConfigCacheManager.getInstance().getCache(TaskCache.class);
        List<TaskConfig> taskConfigs = cache.getAllTaskByType(TaskTypeEnum.MILITARY_RANK.getType());
        for (TaskConfig taskConfig : taskConfigs) {
            if (!ConditionUtil.validate(role, taskConfig.getCondition())) {
                continue;
            }
            if (taskCfgIdList.contains(taskConfig.getId())) {
                continue;
            }
            TaskRecord record = GoalManager.getInstance().createTaskRecord(role, taskConfig, TaskConst.State.HAS_ACCEPT);
            taskRecordList.add(record);
        }
        return taskRecordList;
    }

    /**
     * 同步场景对象军衔等级
     *
     * @param role 角色
     */
    private void sendMilitaryRankLevelChangeNotice(Role role) {
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getRoleId());
        if (player == null) {
            return;
        }
        RoleMilitaryRank roleMilitaryRank = find(role.getRoleId());
        MilitaryRankLevelChangeNotice notice = new MilitaryRankLevelChangeNotice(role.getRoleId(),
                roleMilitaryRank.getMilitaryRankCfgId());
        player.sendNotice(notice);
    }
}
