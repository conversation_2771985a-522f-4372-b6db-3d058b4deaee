package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.abc.bean.ItemBriefBean;
import com.sh.game.common.communication.msg.abc.bean.LogBean;
import com.sh.game.common.communication.msg.abc.bean.RoleBriefBean;
import com.sh.game.common.communication.msg.system.activity.ResActivityRechargeDrawInfoMessage;
import com.sh.game.common.communication.msg.system.activity.ResActivityRechargeDrawLogMessage;
import com.sh.game.common.communication.msg.system.activity.bean.ActivityStatusBean;
import com.sh.game.common.config.model.ActivityRechargeDrawConfig;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.log.CommonLog;
import com.sh.game.common.entity.sys.OtherData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleRechargedScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.script.IActivityRechargeDrawScript;
import com.sh.game.system.summary.SummaryManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Script
public class ActivityRechargeDrawScript extends AbstractActivityScript implements
        IActivityRechargeDrawScript,
        IEventOnRoleRechargedScript
{

    @Override
    public int getType() {
        return 1021;
    }

    @Override
    public void reqInfo(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        RoleActivity activity = findRoleActitiy(role);
        ResActivityRechargeDrawInfoMessage msg = new ResActivityRechargeDrawInfoMessage();
        ActivityProtos.ResActivityRechargeDrawInfo.Builder protoBuilder = ActivityProtos.ResActivityRechargeDrawInfo.newBuilder();
        protoBuilder.setRecharged(activity.getRechargeDrawRecharged());
        for (ActivityRechargeDrawConfig config: ConfigDataManager.getInstance().getList(ActivityRechargeDrawConfig.class)) {
            if (config.getActivityID() != schedule.getActivityID()) {
                continue;
            }
            if (!ConditionUtil.validate(role, config.getCondition())) {
                continue;
            }

            ActivityProtos.ActivityStatusBean.Builder bean = ActivityProtos.ActivityStatusBean.newBuilder();
            bean.setCid(config.getId());
            bean.setStatus(activity.getRechargeDrawAcquired().getOrDefault(config.getId(), 0));
            protoBuilder.addStatus(bean);
        }

        OtherData data = DataCenter.getOtherData();
        for (CommonLog commonLog: data.getRechargeDrawLog()) {
            protoBuilder.addLogs(packLogBean(commonLog));
        }

        msg.setProto(protoBuilder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqAcquire(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        RoleActivity activity = findRoleActitiy(role);

        List<TwoTuple<Integer, Integer>> pool = new ArrayList<>();
        int weight = 0;
        for (ActivityRechargeDrawConfig config: ConfigDataManager.getInstance().getList(ActivityRechargeDrawConfig.class)) {
            if (config.getActivityID() != schedule.getActivityID()) {
                continue;
            }
            if (!ConditionUtil.validate(role, config.getCondition())) {
                continue;
            }

            if (activity.getRechargeDrawRecharged() >= config.getAmount() && activity.getRechargeDrawAcquired().getOrDefault(config.getId(), 0) == 0) {
                weight += config.getWeight();
                pool.add(new TwoTuple<>(config.getId(), weight));
            }
        }
        if (weight <= 0) {
            return;
        }

        int rand = RandomUtil.random(1, weight);
        Integer draw = null;
        for (TwoTuple<Integer, Integer> ele: pool) {
            if (rand <= ele.getSecond()) {
                draw = ele.getFirst();
                break;
            }
        }
        if (draw == null) {
            return;
        }

        ActivityRechargeDrawConfig config = ConfigDataManager.getInstance().getById(ActivityRechargeDrawConfig.class, draw);
        if (config == null) {
            log.error("config not found: {}", draw);
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getItem());
        if (!stash.commit(role, LogAction.ACTIVITY_RECHARGE_DRAW)) {
            return;
        }
        activity.getRechargeDrawAcquired().put(config.getId(), 1);
        DataCenter.updateData(role);
        DataCenter.updateData(activity);

        ActivityProtos.ActivityStatusBean.Builder bean = ActivityProtos.ActivityStatusBean.newBuilder();
        bean.setCid(config.getId());
        bean.setStatus(activity.getRechargeDrawAcquired().getOrDefault(config.getId(), 0));

        ResActivityRechargeDrawInfoMessage msg = new ResActivityRechargeDrawInfoMessage();
        ActivityProtos.ResActivityRechargeDrawInfo proto = ActivityProtos.ResActivityRechargeDrawInfo.newBuilder()
                .setRecharged(activity.getRechargeDrawRecharged())
                .addStatus(bean)
                .build();
        msg.setProto(proto);
        MessageUtil.sendMsg(msg, role.getId());

        if (config.getFirstPrize() == 1) {
            // announce

            OtherData data = DataCenter.getOtherData();
            CommonLog commonLog = new CommonLog();
            commonLog.setTime(TimeUtil.getNowOfSeconds());
            commonLog.setRoleId(role.getId());
            commonLog.setAcquireId(config.getItem().get(0)[0]);
            commonLog.setAcquireCn(config.getItem().get(0)[1]);
            data.getRechargeDrawLog().add(commonLog);
            DataCenter.updateData(data);

            ResActivityRechargeDrawLogMessage logMessage = new ResActivityRechargeDrawLogMessage();
            ActivityProtos.ResActivityRechargeDrawLog logProto = ActivityProtos.ResActivityRechargeDrawLog.newBuilder()
                    .setLog(packLogBean(commonLog))
                    .build();
            logMessage.setProto(logProto);
            MessageUtil.sendToWorld(msg);
        }
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        RoleActivity activity = findRoleActitiy(role);
        int recharged = activity.getRechargeDrawRecharged() + rechargeConfig.getCount();
        activity.setRechargeDrawRecharged(recharged);
        DataCenter.updateData(role);

        ResActivityRechargeDrawInfoMessage msg = new ResActivityRechargeDrawInfoMessage();
        ActivityProtos.ResActivityRechargeDrawInfo proto = ActivityProtos.ResActivityRechargeDrawInfo.newBuilder()
                .setRecharged(activity.getRechargeDrawRecharged())
                .build();
        msg.setProto(proto);
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {
        super.onScheduleBeginPrivate(schedule, role);

        RoleActivity activity = findRoleActitiy(role);
        activity.setRechargeDrawRecharged(0);
        activity.getRechargeDrawAcquired().clear();
        DataCenter.updateData(activity);
    }

    @Override
    protected void onScheduleBegin(ActivitySchedule schedule) {
        OtherData data = DataCenter.getOtherData();
        data.getRechargeDrawLog().clear();
        DataCenter.updateData(data);
    }

    private AbcProtos.LogBean packLogBean(CommonLog commonLog) {
        AbcProtos.RoleBriefBean.Builder role = AbcProtos.RoleBriefBean.newBuilder();
        role.setUid(commonLog.getRoleId());
        RoleSummary summary = SummaryManager.getInstance().getSummary(commonLog.getRoleId());
        if (summary != null) {
            role.setName(summary.getName());
        }

        AbcProtos.ItemBriefBean item = AbcProtos.ItemBriefBean.newBuilder()
                .setItemId(commonLog.getAcquireId())
                .setItemCount(commonLog.getAcquireCn())
                .build();

        return AbcProtos.LogBean.newBuilder()
                .setRole(role)
                .setItem(item)
                .build();
    }
}
