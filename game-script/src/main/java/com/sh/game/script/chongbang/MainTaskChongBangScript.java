package com.sh.game.script.chongbang;

import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.RankConst;
import com.sh.game.script.activity.abc.AbstractChongBangActivity;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

/**
 * 主线任务冲榜
 *
 * <AUTHOR>
 * @since 2024-12-16
 */
@Script
@Slf4j
public class MainTaskChongBangScript extends AbstractChongBangActivity {
    @Override
    public int getType() {
        return ActivityConst.MAIN_TASK_RANK;
    }

    @Override
    protected int getGoalType() {
        return RankConst.RankScoreType.MAIN_TASK;
    }
}
