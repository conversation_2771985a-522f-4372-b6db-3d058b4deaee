package com.sh.game.script.zongmen;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.system.zongmen.*;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleExtend;
import com.sh.game.common.util.*;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleZhuanShengUpScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.ZongMenProtos;
import com.sh.game.system.activity.impl.ActivityValueCardManager;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.jieyuan.entity.JieYuanData;
import com.sh.game.system.render.entity.RenderData;
import com.sh.game.system.role.RoleExtendManager;
import com.sh.game.system.role.script.IRoleOnSecondScript;
import com.sh.game.system.secretary.SecretaryManager;
import com.sh.game.system.secretary.entity.SecretaryData;
import com.sh.game.system.user.script.IRoleFirstLoginScript;
import com.sh.game.system.zongmen.entity.RoleXuanBaData;
import com.sh.game.system.zongmen.entity.RoleZongMenData;
import com.sh.game.system.zongmen.entity.XuanBaType;
import com.sh.game.system.zongmen.script.IZongMenScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @Description: 宗门
 * <AUTHOR>
 * @Date 2025/2/14 10:36
 */
@Slf4j
@Script
public class ZongMenScript implements IZongMenScript, IRoleOnSecondScript, IRoleFirstLoginScript, IEventOnRoleZhuanShengUpScript {

    @Override
    public void reqZongMenInfoList(Role role) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        ZongMenProtos.ResZongMenInfoList.Builder builder = ZongMenProtos.ResZongMenInfoList.newBuilder();
        zongMenData.values().forEach(zongMen-> {
            builder.addZongMens(toZongMenBean(zongMen));
        });
        ResZongMenInfoListMessage message = new ResZongMenInfoListMessage();
        message.setProto(builder.build());
        MessageUtil.sendMsg(message, role);

        resZongMenMotivateInfo(role, roleExtend);
    }

    private void sendInfo(Role role, RoleZongMenData zongMen) {
        ResZongMenInfoMessage message = new ResZongMenInfoMessage();
        message.setProto(ZongMenProtos.ResZongMenInfo.newBuilder().setZongMens(toZongMenBean(zongMen)).build());
        MessageUtil.sendMsg(message, role);
    }

    private void resZongMenMotivateInfo(Role role, RoleExtend roleExtend) {
        Map<Integer, Integer> zongMenMotivateCoinData = roleExtend.getZongMenMotivateCoinData();
        Map<Integer, Integer> zongMenMotivateItemData = roleExtend.getZongMenMotivateItemData();
        ZongMenProtos.ResZongMenMotivateInfo.Builder builder = ZongMenProtos.ResZongMenMotivateInfo.newBuilder();
        zongMenMotivateCoinData.forEach((id,count)-> {
            builder.addMotivateCoinCount(AbcProtos.CommonKeyValueBean.newBuilder().setKey(id).setValue(count));
        });
        zongMenMotivateItemData.forEach((id,count)-> {
            builder.addMotivateItemCount(AbcProtos.CommonKeyValueBean.newBuilder().setKey(id).setValue(count));
        });

        ResZongMenMotivateInfoMessage message = new ResZongMenMotivateInfoMessage();
        message.setProto(builder.build());
        MessageUtil.sendMsg(message, role);
    }

    private ZongMenProtos.ZongMenBean.Builder toZongMenBean(RoleZongMenData zongMen) {
        ZongMenProtos.ZongMenBean.Builder zongMenBean = ZongMenProtos.ZongMenBean.newBuilder();
        if(zongMen != null) {
            zongMenBean.setId(zongMen.getId());
            zongMenBean.setLevel(zongMen.getLevel());
            zongMenBean.setExtraDizi(zongMen.getExtraDizi());
            zongMen.getSeatSecretaryMap().forEach((seat, id)-> {
                zongMenBean.addSeatSecretarys(AbcProtos.CommonKeyValueBean.newBuilder().setKey(seat).setValue(id));
            });
            zongMen.getSecondIncome().forEach((id, count)-> {
                AbcProtos.CommonKeyValueLongBean.Builder bean = AbcProtos.CommonKeyValueLongBean.newBuilder();
                bean.setKey(id);
                bean.setValue(count);
                zongMenBean.addSecondIncome(bean);
            });
        }
        return zongMenBean;
    }

    @Override
    public void reqZongMenFight(Role role, int zongMenCfgId) {
        ZongMenMainConfig zongMenMainConfig = ConfigDataManager.getInstance().getById(ZongMenMainConfig.class, zongMenCfgId);
        if(zongMenMainConfig == null || !ConditionUtil.validate(role, zongMenMainConfig.getTriggerCondition())) {
            return;
        }
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        if(zongMenData.containsKey(zongMenCfgId)) {
            return;
        }
        Map<Integer, Integer> zongMenMotivateCoinData = roleExtend.getZongMenMotivateCoinData();
        Map<Integer, Integer> zongMenMotivateItemData = roleExtend.getZongMenMotivateItemData();
        ZongMenProtos.ResZongMenFight.Builder builder = ZongMenProtos.ResZongMenFight.newBuilder();
        BigInteger atkTotalClick = BigInteger.ZERO;
        BigInteger monsterTotalClick = BigInteger.ZERO;
        if(zongMenMainConfig.getFightMonster() > 0) {
            ZongMenFightConfig zongMenFightConfig = ConfigDataManager.getInstance().getById(ZongMenFightConfig.class, zongMenMainConfig.getFightMonster());
            monsterTotalClick = ItemCoinUtil.getCoinBigInteger(zongMenFightConfig.getNum());
            int[] motivateAddition = GlobalUtil.findJinghaoIntArray(GameConst.GlobalId.ZONGMEN_MOTIVATE_ADDITION);
            BigInteger addition = BigInteger.valueOf((long) zongMenMotivateCoinData.getOrDefault(zongMenCfgId, 0) * motivateAddition[0] + (long) zongMenMotivateItemData.getOrDefault(zongMenCfgId, 0) * motivateAddition[1]);
            BigInteger motivateBfb = BigInteger.valueOf(100);
            Map<Integer, String> secretaryIncomeMap = roleExtend.getSecretaryData().getSecretaryIncomeMap();
            for(Map.Entry<Integer, String> entry : secretaryIncomeMap.entrySet()) {
                BigInteger secretaryBase = new BigInteger(entry.getValue());
                if(!addition.equals(BigInteger.ZERO)) {
                    BigInteger temp = secretaryBase.multiply(addition).divide(motivateBfb);
                    secretaryBase = secretaryBase.add(temp);
                }
                builder.addSecretaryPowers(AbcProtos.CommonKeyValueStringBean.newBuilder().setKey(entry.getKey()).setValue(secretaryBase.toString()));

                Pair<BigInteger, BigInteger> xiuWeiPair = SecretaryManager.getInstance().calSecretaryRestrainRelation(entry.getKey(), secretaryBase, zongMenFightConfig.getRestrainType(), BigInteger.ZERO);
                secretaryBase = xiuWeiPair.getKey();

                atkTotalClick = atkTotalClick.add(secretaryBase);
                //TODO
                //怪物 = xiuWeiPair.getValue();
            }
        }

        boolean win = monsterTotalClick.equals(BigInteger.ZERO) || atkTotalClick.compareTo(monsterTotalClick) >= 0;
        builder.setAttWin(win);
        if(win) {
            RoleZongMenData roleZongMenData = RoleZongMenData.valueOf(zongMenCfgId, 1);
            zongMenData.put(zongMenCfgId, roleZongMenData);
            zongMenMotivateCoinData.remove(zongMenCfgId);
            zongMenMotivateItemData.remove(zongMenCfgId);
            DataCenter.updateData(roleExtend);
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZONGMEN_CID_SHOUBIAN);
            BackPackStashUtil.increase(role, zongMenMainConfig.getFightReward(), LogAction.ZONGMEN_FIGHT_REWARD, false);

            calZongMenIncome(role, roleZongMenData);
            log.info("宗门-收编-成功，玩家id {} name {} zongMenCfgId {}", role.getId(), role.getName(), zongMenCfgId);
        }

        ResZongMenFightMessage message = new ResZongMenFightMessage();
        message.setProto(builder.build());
        MessageUtil.sendMsg(message, role);
    }

    @Override
    public void reqZongMenFightMotivate(Role role, int zongMenCfgId, boolean costItem) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        if(zongMenData.containsKey(zongMenCfgId)) {
            return;
        }
        Map<Integer, Integer> zongMenMotivateItemData = roleExtend.getZongMenMotivateItemData();
        Map<Integer, Integer> zongMenMotivateCoinData = roleExtend.getZongMenMotivateCoinData();
        if(costItem) {
            int motivateCount = zongMenMotivateItemData.getOrDefault(zongMenCfgId, 0);
            List<List<int[]>> baseCost = GlobalUtil.findJinHaoShuXianAndYuHaoList(GameConst.GlobalId.ZONGMEN_MOTIVATE_ITEM_COST);
            if(motivateCount >= baseCost.size()) {
                return;
            }
            List<int[]> motivateCost = baseCost.get(motivateCount);
            if(!BackPackStashUtil.decrease(role, motivateCost, LogAction.ZONGMEN_FIGHT_MOTIVATE)) {
                return;
            }
            zongMenMotivateItemData.put(zongMenCfgId, motivateCount + 1);
        } else {
            int motivateCount = zongMenMotivateCoinData.getOrDefault(zongMenCfgId, 0);
            List<List<int[]>> baseCost = GlobalUtil.findJinHaoShuXianAndYuHaoList(GameConst.GlobalId.ZONGMEN_MOTIVATE_COIN_COST);
            if(motivateCount >= baseCost.size()) {
                return;
            }
            List<int[]> tempCost = baseCost.get(motivateCount);
            int multip = tempCost.get(0)[0];
            Map<Integer, Long> motivateCost = new HashMap<>();
            RenderData renderData = role.findNormal().getRenderData();
            renderData.getClickIncome().forEach((id,count)-> {
                motivateCost.merge(id, count * multip, Long::sum);
            });
            ItemCoinUtil.refreshCoinBigNumCarry(motivateCost);
            if(!BackPackStashUtil.decrease(role, motivateCost, 1, LogAction.ZONGMEN_FIGHT_MOTIVATE)) {
                return;
            }
            zongMenMotivateCoinData.put(zongMenCfgId, motivateCount + 1);
        }
        DataCenter.updateData(roleExtend);
        log.info("宗门-激励-成功，玩家id {} name {} zongMenCfgId {}, costItem {}, motivateItemCount {}, motivateCoinCount {}", role.getId(), role.getName(),
                zongMenCfgId, costItem, zongMenMotivateItemData.getOrDefault(zongMenCfgId, 0), zongMenMotivateCoinData.getOrDefault(zongMenCfgId, 0));
        resZongMenMotivateInfo(role, roleExtend);
    }

    @Override
    public void reqZongMenLevel(Role role, int zongMenCfgId) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        RoleZongMenData roleZongMenData = zongMenData.get(zongMenCfgId);
        if(roleZongMenData == null) {
            return;
        }
        ZongMenLevelConfig zongMenLevelConfig = ConfigDataManager.getInstance().getById(ZongMenLevelConfig.class, roleZongMenData.getLevel());
        if(zongMenLevelConfig == null || MapUtils.isEmpty(zongMenLevelConfig.getCost())) {
            return;
        }
        if(!BackPackStashUtil.decrease(role, zongMenLevelConfig.getCost(), 1, LogAction.ZONG_MEN_LEVEL_COST)) {
            return;
        }
        roleZongMenData.setLevel(roleZongMenData.getLevel() + 1);
        DataCenter.updateData(roleExtend);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZONG_MEN_LEVEL_UP);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZONGMEN_CID_LEVEL);

        calZongMenIncome(role, roleZongMenData);
        log.info("宗门-升级-成功，玩家id {} name {} newLevel {}", role.getId(), role.getName(), roleZongMenData.getLevel());
    }

    @Override
    public void reqZongMenSeatSecretaryBind(Role role, int zongMenCfgId, int seat, int secretaryCfgId) {
        ZongMenJingYingConfig zongMenJingYingConfig = ConfigDataManager.getInstance().getById(ZongMenJingYingConfig.class, zongMenCfgId);
        if(zongMenJingYingConfig == null) {
            return;
        }
        int[] seatOpen = zongMenJingYingConfig.getSeatOpen();
        if(seat < 1 || seat > seatOpen.length) {
            return;
        }
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        RoleZongMenData roleZongMenData = zongMenData.get(zongMenCfgId);
        if(roleZongMenData == null) {
            return;
        }

        Map<Integer, Integer> seatSecretaryMap = roleZongMenData.getSeatSecretaryMap();
        if(secretaryCfgId > 0) {
            if(seatOpen[seat - 1] > roleZongMenData.getLevel()) {
                return;
            }
            if(seatSecretaryMap.getOrDefault(seat, 0) > 0) {
                return;
            }
            if(zongMenData.values().stream().anyMatch(p -> p.getSeatSecretaryMap().values().stream().anyMatch(k -> k == secretaryCfgId))) {
                return;
            }
        }
        seatSecretaryMap.put(seat, secretaryCfgId);
        DataCenter.updateData(roleExtend);
        if(secretaryCfgId > 0) {
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZONGMEN_CID_BIND_SECRETARY_COUNT);
        }

        calZongMenIncome(role, roleZongMenData);
        log.info("宗门-主持插卡-成功，玩家id {} name {} seat {} secretaryCfgId {}", role.getId(), role.getName(), seat, secretaryCfgId);
    }

    @Override
    public void reqZongMenDiZiBuy(Role role, int zongMenCfgId, boolean ten) {
        ZongMenJingYingConfig zongMenJingYingConfig = ConfigDataManager.getInstance().getById(ZongMenJingYingConfig.class, zongMenCfgId);
        if(zongMenJingYingConfig == null) {
            return;
        }
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        RoleZongMenData roleZongMenData = zongMenData.get(zongMenCfgId);
        if(roleZongMenData == null) {
            return;
        }

        int buySuccCount = 0;
        ZongMenLevelConfig zongMenLevelConfig = ConfigDataManager.getInstance().getById(ZongMenLevelConfig.class, roleZongMenData.getLevel());
        int num = ten ? 10 : 1;
        double[] formulaNum = zongMenJingYingConfig.getFormulaNum();
        for (int i = 0; i < num; i++) {
            int curDiZi = roleZongMenData.getExtraDizi() + zongMenJingYingConfig.getDiziNum();
            if(curDiZi >= zongMenLevelConfig.getDiziMax()) {
                break;
            }
            //ROUND(当前子弟数^系数1*系数2+系数3)
            long costCount = Math.round(Math.pow(curDiZi, formulaNum[0]) * formulaNum[1] + formulaNum[2]);
            Map<Integer, Long> cost = new HashMap<>(zongMenJingYingConfig.getCost());
            for(Map.Entry<Integer, Long> entry : cost.entrySet()) {
                entry.setValue(entry.getValue() * costCount);
            }
            if(!BackPackStashUtil.decrease(role, cost, 1, LogAction.ZONG_MEN_DIZI_BUY)) {
                break;
            }
            roleZongMenData.setExtraDizi(roleZongMenData.getExtraDizi() + 1);
            buySuccCount += 1;
        }

        if(buySuccCount > 0) {
            DataCenter.updateData(roleExtend);
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZONG_MEN_DIZI_BUY, roleZongMenData.getId(), buySuccCount);
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZONGMEN_CID_DIZI_COUNT);

            calZongMenIncome(role, roleZongMenData);
            log.info("宗门-弟子购买-成功，玩家id {} name {} buySuccCount {} curDiZi {}", role.getId(), role.getName(), buySuccCount, roleZongMenData.getExtraDizi() + zongMenJingYingConfig.getDiziNum());
        }
    }

    @Override
    public void onRoleSecond(Role role) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        if(MapUtils.isEmpty(zongMenData) || zongMenData.values().stream().anyMatch(mod-> MapUtils.isEmpty(mod.getSecondIncome()))) {
            return;
        }
        boolean hasTeQuanCard = ActivityValueCardManager.getInstance().hasTeQuanCard(role, ValueCardConst.ValueCardEquity.ZONG_MEN_INCOME);
        boolean change = zongMenData.values().stream().anyMatch(mod-> mod.isTeQuanCard() != hasTeQuanCard);
        if(change){
            if(calZongMenIncome(role, null)) {
                reqZongMenInfoList(role);
            }
        }

        zongMenIncomeReward(role, 1);

        resetXuanBaData(role);
    }

    @Override
    public Map<Integer, Long> calZongMenLiXianTotalIncome(Role role, int liXianSecond) {
        calZongMenIncome(role, null);
        return zongMenIncomeReward(role, liXianSecond);
    }

    @Override
    public void calZongMenTotalIncome(Role role, int secretaryCfgId) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        if(MapUtils.isEmpty(zongMenData)) {
            reqZongMenInfoList(role);//初始化
            return;
        }

        RoleZongMenData bindZongMenData = findZongMenBind(role, zongMenData, secretaryCfgId);
        boolean calSucc = calZongMenIncome(role, bindZongMenData);
        if(bindZongMenData == null && calSucc) {
            reqZongMenInfoList(role);
        }
    }

    private RoleZongMenData findZongMenBind(Role role, Map<Integer, RoleZongMenData> zongMenData, int secretaryCfgId) {
        if(secretaryCfgId <= 0) {
            return null;
        }
        return zongMenData.values().stream().filter(p -> p.getSeatSecretaryMap().values().stream().anyMatch(s-> s == secretaryCfgId)).findFirst().orElse(null);
    }

    private Map<Integer, Long> zongMenIncomeReward(Role role, int second) {
        if(!role.isOnline()) {
            return Collections.emptyMap();
        }
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        if(MapUtils.isEmpty(zongMenData)) {
            return Collections.emptyMap();
        }
        Map<Integer, Long> totalIncome = new HashMap<>();
        zongMenData.values().forEach(zongMen-> {
            zongMen.getSecondIncome().forEach((k,v)-> totalIncome.merge(k,v,Long::sum));
        });
        ItemCoinUtil.refreshCoinBigNumCarry(totalIncome);
        BackPackStashUtil.increase(role, totalIncome, second, LogAction.ZONG_MEN_ZHUAN_QIAN, false);
        return totalIncome;
    }

    @Override
    public boolean calZongMenIncome(Role role, RoleZongMenData zongMen) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        if(MapUtils.isEmpty(zongMenData)) {
            return false;
        }

        SecretaryData secretaryData = roleExtend.getSecretaryData();
        boolean hasTeQuanCard = ActivityValueCardManager.getInstance().hasTeQuanCard(role, ValueCardConst.ValueCardEquity.ZONG_MEN_INCOME);
        int valueCardZhongMen = GlobalUtil.getGlobalInt(GameConst.GlobalId.VALUECARDEQUITY_ZONGMEN_ZHUANQIAN);

        BigDecimal bigDecimal1000 = new BigDecimal(GlobalUtil.getGlobalInt(GameConst.GlobalId.ZONGMEN_ZHUAN_QIAN_SECRETARY_BASE));
        if(zongMen == null) {
            zongMenData.values().forEach(mod-> {
                calZongMenIncome(role, roleExtend, mod, secretaryData, hasTeQuanCard, valueCardZhongMen, false, bigDecimal1000);
            });
        } else {
            calZongMenIncome(role, roleExtend, zongMen, secretaryData, hasTeQuanCard, valueCardZhongMen, true, bigDecimal1000);
        }

        return true;
    }

    private void calZongMenIncome(Role role, RoleExtend roleExtend, RoleZongMenData zongMen, SecretaryData secretaryData, boolean hasTeQuanCard, int valueCardZhongMen, boolean notice, BigDecimal bigDecimal1000) {
        //基本赚速：弟子数量*弟子转速+仙友加成
        //仙友加成：主持仙友总修为/1000（具体见仙友插卡单子）
        //宗门赚速：ROUND(基本赚速*（等级加成+其他加成(仙友技能加成)）,0)
        //总赚速=所有已收编宗门赚速之合
        ZongMenJingYingConfig zongMenJingYingConfig = ConfigDataManager.getInstance().getById(ZongMenJingYingConfig.class, zongMen.getId());
        ZongMenLevelConfig zongMenLevelConfig = ConfigDataManager.getInstance().getById(ZongMenLevelConfig.class, zongMen.getLevel());
        int bindSecretaryTypeAdd = GlobalUtil.getGlobalInt(GameConst.GlobalId.ZONGMEN_BIND_SECRETARY_TYPE_ADD);
        Map<Integer, Integer> seatSecretaryMap = zongMen.getSeatSecretaryMap();
        AtomicLong secretaryBaseTotal = new AtomicLong();
        AtomicInteger secretarySkillAdd = new AtomicInteger();
        seatSecretaryMap.values().forEach(secretaryCfgId-> {
            if(secretaryCfgId > 0) {
                SecretaryConfig secretaryConfig = ConfigDataManager.getInstance().getById(SecretaryConfig.class, secretaryCfgId);
                String secretaryIncome = secretaryData.getSecretaryIncomeMap().getOrDefault(secretaryCfgId, "0");
                BigDecimal bigDecimal = new BigDecimal(secretaryIncome);
                long secretaryBase = bigDecimal.divide(bigDecimal1000, 1, RoundingMode.HALF_UP).longValue();
                secretaryBaseTotal.addAndGet(secretaryBase > 1 ? secretaryBase:1);
                //仙友技能加成：由仙友的技能提升的百分比，累加到其他加成内
                Map<Integer, Integer> skillMap = secretaryData.getSkillLevelMap().get(secretaryCfgId);
                if(skillMap != null) {
                    skillMap.forEach((id,lv)-> {
                        SecretarySkillConfig skillConfig = ConfigDataManager.getInstance().getById(SecretarySkillConfig.class, id);
                        if(skillConfig != null && lv > 0 && skillConfig.getEffectTarget() == SecretarySkillConfig.EffectTargetType.ZONG_MEN && skillConfig.getEffect() > 0) {
                            int baseAdd = lv * skillConfig.getEffect();
                            if(secretaryConfig.getType() == zongMenJingYingConfig.getType()) {
                                baseAdd = baseAdd * bindSecretaryTypeAdd;
                            }
                            secretarySkillAdd.addAndGet(baseAdd);
                        }
                    });
                }
            }
        });
        int totalDiZi = zongMen.getExtraDizi() + zongMenJingYingConfig.getDiziNum();
        Map<Integer, Long> zhuanqian = new HashMap<>(zongMenJingYingConfig.getZhuanQian());
        for(Map.Entry<Integer, Long> entry : zhuanqian.entrySet()) {
            long base = entry.getValue() * totalDiZi + secretaryBaseTotal.get();
            long zongmenZs = Math.round(base * (zongMenLevelConfig.getType() + secretarySkillAdd.get()) / 100D);
            //特权卡
            if(hasTeQuanCard) {
                zongmenZs += Math.round(zongmenZs * valueCardZhongMen / 100D);
            }
            entry.setValue(zongmenZs);
        }
        // 添加结缘秒赚
        JieYuanData jieYuanData = role.findNormal().getJieYuanData();
        List<JieYuanConfig> jieYuanConfigs = ConfigDataManager.getInstance().getList(JieYuanConfig.class);
        for (JieYuanConfig jieYuanConfig : jieYuanConfigs) {
            if (jieYuanConfig.getZongmenId().contains(zongMen.getId())) {
                TwoTuple<Integer, Integer> tuple = jieYuanData.getJieYuanMap().getOrDefault(jieYuanConfig.getType(), new TwoTuple<>(0, 0));
                if (tuple.getFirst() <= 0) {
                    continue;
                }
                JieYuanLevelConfig jieYuanLevelConfig = ConfigDataManager.getInstance().getById(JieYuanLevelConfig.class, tuple.getFirst());
                if (jieYuanLevelConfig.getType1() == 1) {
                    MapUtil.merge(jieYuanLevelConfig.getLevel_attr(), zhuanqian);
                }
                if (jieYuanLevelConfig.getType1() == 2) {
                    for(Map.Entry<Integer, Long> entry : zhuanqian.entrySet()) {
                        entry.setValue(entry.getValue() + Math.round((double) (entry.getValue() * jieYuanLevelConfig.getPercent()) / 10000));
                    }
                }
            }
        }

        ItemCoinUtil.refreshCoinBigNumCarry(zhuanqian);
        zongMen.setSecondIncome(zhuanqian);
        zongMen.setTeQuanCard(hasTeQuanCard);

        if(notice) {
            sendInfo(role, zongMen);
        }
    }

    @Override
    public void reqXuanBaInfo(Role role) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleXuanBaData> xuanBaData = roleExtend.getXuanBaData();
        List<ZongMenProtos.ZongMenXuanBaBean> tupleBeans = xuanBaData.entrySet().stream().map(entry ->
                        ZongMenProtos.ZongMenXuanBaBean.newBuilder().setType(entry.getKey())
                        .setCount(entry.getValue().getCount())
                        .setLastExpired(entry.getValue().getLastExpired())
                        .setTime(entry.getValue().getTime()).build())
                .collect(Collectors.toList());
        ResZongMenXuanBaInfoMessage msg = new ResZongMenXuanBaInfoMessage();
        msg.setProto(ZongMenProtos.ResZongMenXuanBaInfo.newBuilder().addAllData(tupleBeans).build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqXuanBa(Role role, boolean isAdvert, int type) {
        int now = TimeUtil.getNowOfSeconds();
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleXuanBaData> xuanBaData = roleExtend.getXuanBaData();
        if (!xuanBaData.containsKey(type)) {
            log.error("宗门选拔-请求选拔-类型错误#玩家:{}-{}", role.getId(), role.getName());
            return;
        }
        RoleXuanBaData roleXuanBaData = xuanBaData.get(type);
        if (isAdvert) {
            // 避免是0的时候扣成负数
            roleXuanBaData.setCount(roleXuanBaData.getCount() + 1);
        }
        if (roleXuanBaData.getCount() <= 0) {
            log.error("宗门选拔-请求选拔-次数不足#玩家:{}-{}", role.getId(), role.getName());
            return;
        }
        Map<Integer, Long> rewardMap = new HashMap<>();
        Map<Integer, RoleZongMenData> zongMenData = roleExtend.getZongMenData();
        LogAction logAction = null;
        if (XuanBaType.ZONG_MEN.getType() == type) {
            long result = 0;
            for (Map.Entry<Integer, RoleZongMenData> entry : zongMenData.entrySet()) {
                ZongMenMainConfig mainConfig = ConfigDataManager.getInstance().getById(ZongMenMainConfig.class, entry.getKey());
                if (mainConfig == null) {
                    continue;
                }
                ZongMenJingYingConfig zongMenJingYingConfig = ConfigDataManager.getInstance().getById(ZongMenJingYingConfig.class, entry.getKey());
                if(zongMenJingYingConfig == null) {
                    continue;
                }
                int curDiZi = entry.getValue().getExtraDizi() + zongMenJingYingConfig.getDiziNum();
                double[] xuanba = mainConfig.getXuanba();
                result += Math.round(Math.pow(curDiZi, xuanba[0]) * xuanba[1] + xuanba[2]);
            }
            if (result == 0) {
                log.error("宗门选拔-请求选拔-无宗门#玩家:{}-{}", role.getId(), role.getName());
                return;
            }
            double[] costForMula = GlobalUtil.findJinghaoDoubleArray(GameConst.GlobalId.XUAN_BA_COST_ITEM_FORMULA);
            long costNum = Math.round(Math.pow(result, costForMula[0]) * costForMula[1] + costForMula[2]);
            if (!isAdvert && !BackPackStashUtil.decrease(role, GlobalUtil.getGlobalInt(GameConst.GlobalId.XUAN_BA_COST_ITEM), costNum, LogAction.XUAN_BA_COST)) {
                log.error("宗门选拔-请求选拔-门人消耗道具不足#玩家:{}-{}", role.getId(), role.getName());
                return;
            }
            rewardMap.put(GlobalUtil.getGlobalInt(GameConst.GlobalId.XUAN_BA_ITEM_ID), result);
            logAction = LogAction.XUAN_BA_REWARD;
        }
        if (XuanBaType.ZHENG_SHOU.getType() == type) {
            int levelSum = zongMenData.values().stream().mapToInt(RoleZongMenData::getLevel).sum();
            double[] formula = GlobalUtil.findJinghaoDoubleArray(GameConst.GlobalId.ZHENG_SHOU_FORMULA);
            long result = Math.round(Math.pow(levelSum, formula[0]) * formula[1] + formula[2]);
            rewardMap.merge(GlobalUtil.getGlobalInt(GameConst.GlobalId.ZHENG_SHOU_ITEM_ID), result, Long::sum);
            logAction = LogAction.ZHENG_SHOU_REWARD;
        }
        BackPackStashUtil.increase(role, rewardMap, logAction, true);
        roleXuanBaData.setCount(roleXuanBaData.getCount() - 1);
        roleXuanBaData.setLastExpired(now);
        DataCenter.updateData(roleExtend);

        CountManager.getInstance().count(role, CountConst.CountType.ZONG_MEN_XUAN_BA_TOTAL_COUNT, type);
        CountManager.getInstance().count(role, CountConst.CountType.ZONG_MEN_XUAN_BA_DAY_COUNT, type);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZONG_MEN_XUAN_BA_COUNT, type);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZONG_MEN_XUAN_BA_TOTAL_COUNT, type);

        reqXuanBaInfo(role);
    }

    @Override
    public void reqModifyName(Role role, String name) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        if (StringUtil.isBlank(name)) {
            log.error("宗门修改名-请求修改名-名字为空#玩家:{}-{}", role.getId(), role.getName());
            return;
        }
        TwoTuple<String, Boolean> firstZongMen = roleExtend.getFirstZongMen();
        if (firstZongMen.getSecond()) {
            log.error("宗门修改名-请求修改名-已经修改过#玩家:{}-{}", role.getId(), role.getName());
            return;
        }
        firstZongMen.setFirst(name);
        firstZongMen.setSecond(true);
        DataCenter.updateData(roleExtend);
        reqFirstZongMen(role);
    }

    @Override
    public void reqFirstZongMen(Role role) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        ResFirstZongMenNameMessage msg = new ResFirstZongMenNameMessage();
        msg.setProto(ZongMenProtos.ResFirstZongMenName.newBuilder().setName(roleExtend.getFirstZongMen().getFirst())
                .setIsModifyName(roleExtend.getFirstZongMen().getSecond()).build());

        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqXuanBaCountForItem(Role role, int type) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleXuanBaData> xuanBaData = roleExtend.getXuanBaData();
        RoleXuanBaData roleXuanBaData = xuanBaData.computeIfAbsent(type, k -> new RoleXuanBaData());
        ZhuanShengConfig zhuanShengConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, role.getZhuanShengId());
        int countLimit = zhuanShengConfig.getZongmenShiwu().getOrDefault(type, 0);
        roleXuanBaData.setCount(countLimit);
        roleXuanBaData.setLastExpired(TimeUtil.getNowOfSeconds());
        DataCenter.updateData(roleExtend);

        reqXuanBaInfo(role);
    }

    @Override
    public void onFirstLogin(Role role) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        roleExtend.getFirstZongMen().setFirst(GlobalUtil.getGlobalValue(GameConst.GlobalId.FIRST_ZONG_MEN_NAME));
        DataCenter.updateData(roleExtend);
    }

    private void resetXuanBaData(Role role) {
        int now = TimeUtil.getNowOfSeconds();
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleXuanBaData> xuanBaData = roleExtend.getXuanBaData();

        boolean update = false;
        for (XuanBaType xuanBaType : XuanBaType.values()) {
            RoleXuanBaData value = xuanBaData.computeIfAbsent(xuanBaType.getType(), k -> new RoleXuanBaData());
            ZhuanShengConfig zhuanShengConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, role.getZhuanShengId());
            int countLimit = zhuanShengConfig.getZongmenShiwu().getOrDefault(xuanBaType.getType(), 0);
            if (value.getCount() >= countLimit) {
                continue;
            }
            int dayCount = CountManager.getInstance().getCount(role, CountConst.CountType.ZONG_MEN_XUAN_BA_DAY_COUNT, xuanBaType.getType());
            List<ZongMenZhengShouConfig> zhengShouConfigs = ConfigDataManager.getInstance().getList(ZongMenZhengShouConfig.class);
            ZongMenZhengShouConfig zhengShouConfig = zhengShouConfigs
                    .stream().filter(e -> e.getType() == xuanBaType.getType()
                            && (dayCount >= e.getNum()[0] && dayCount <= e.getNum()[1]))
                    .findFirst().orElse(null);
            if (zhengShouConfig == null) {
                log.error("宗门选拔-请求选拔-获取记时配置错误#玩家:{}-{}, type:{}", role.getId(), role.getName(), xuanBaType.getType());
                continue;
            }
            if (value.getLastExpired() == 0) {
                value.setCount(countLimit);
                value.setLastExpired(now);
                value.setTime(zhengShouConfig.getTime());
                update = true;
                continue;
            }
            int time = now - value.getLastExpired();
            if (time < value.getTime()) {
                continue;
            }
            value.setCount(Math.min(value.getCount() + time / value.getTime(), countLimit));
            if (value.getCount() == 0) {
                log.info("宗门选拔-请求选拔-刷新#玩家:{}-{}, type:{},当前时间戳:{},当前time:{},当前valueTime:{},计算增加的次数:{}", role.getId(), role.getName(), xuanBaType.getType()
                , now, time, value.getTime(), time / value.getTime());
            }
            value.setLastExpired(now);
            value.setTime(zhengShouConfig.getTime());
            update = true;
        }
        if (update) {
            DataCenter.updateData(roleExtend);
            reqXuanBaInfo(role);
        }
    }

    @Override
    public void onRoleZhuanShengUp(Role role, long actorId) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        Map<Integer, RoleXuanBaData> xuanBaData = roleExtend.getXuanBaData();
        ZhuanShengConfig zhuanShengConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, role.getZhuanShengId());
        for (Map.Entry<Integer, Integer> entry : zhuanShengConfig.getZongmenShiwu().entrySet()) {
            RoleXuanBaData roleXuanBaData = xuanBaData.computeIfAbsent(entry.getKey(), k -> new RoleXuanBaData());
            roleXuanBaData.setCount(roleXuanBaData.getCount() + 1);
        }
        DataCenter.updateData(roleExtend);
        reqXuanBaInfo(role);
    }
}
