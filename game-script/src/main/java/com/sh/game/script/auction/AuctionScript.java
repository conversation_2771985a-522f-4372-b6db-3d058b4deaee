package com.sh.game.script.auction;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.system.auction.*;
import com.sh.game.common.communication.msg.system.count.ResCountTypeMessage;
import com.sh.game.common.communication.notice.BackpackStashCommitRetNotice;
import com.sh.game.common.communication.notice.DealAuctionTimeOutNotice;
import com.sh.game.common.config.model.AuctionAutoConfig;
import com.sh.game.common.config.model.AuctionConfig;
import com.sh.game.common.config.model.GlobalConfig;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.EquipData;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.sys.AuctionData;
import com.sh.game.common.entity.sys.OtherData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.*;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnRoleLogoutScript;
import com.sh.game.event.IEventOnServerStartUpScript;
import com.sh.game.event.IEventScheduleUpdateOnMidnightScript;
import com.sh.game.event.IEventScheduleUpdateOnMinuteScript;
import com.sh.game.log.entity.AuctionLog;
import com.sh.game.notice.NoticeCallback;
import com.sh.game.protos.PaimaiProtos;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.auction.AuctionManager;
import com.sh.game.system.auction.constant.AuctionConst;
import com.sh.game.system.auction.entity.AuctionStore;
import com.sh.game.system.auction.entity.BidInfo;
import com.sh.game.system.auction.script.IAuctionScript;
import com.sh.game.system.chat.ChatManager;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.identify.IdentifyManager;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.role.entity.RoleAuctionSoldRecord;
import com.sh.game.system.secondaryPassword.SecondaryPasswordManager;
import com.sh.game.system.union.UnionManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

import static com.sh.game.common.entity.backpack.constant.BackpackConst.Browse.BACKPACK_AND_HERO;

@Script
@Slf4j
public class AuctionScript implements IAuctionScript, IEventScheduleUpdateOnMinuteScript, IEventOnRoleLogoutScript, IEventScheduleUpdateOnMidnightScript, IEventOnServerStartUpScript {

    /**
     * 检查商品是否自动下架
     */
    @Override
    public void scheduleUpdateOnMinute() {
        //TODO 临时注释
//        // 处理已到期且有人已经竞拍的道具分发给竞拍者,同时处理行会竞拍分红
//        scheduleAuction();
//        // 系统自动上架
//        scheduleOnSystemAuto();
//        // 对行会相关的自动上架的已过期物品自动下架
//        removeUnionExpireStore();
    }

    void removeUnionExpireStore(){
        AuctionData data = SysDataProvider.get(AuctionData.class);

        boolean bNeedSave = false;
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        bNeedSave = removeExpireStoreByMap(data, data.getUnionStoreMap(), nowOfSeconds, "UnionStoreMap");

        Map<Long, Map<Long, AuctionStore>> unionBossStoreMap = data.getUnionBossStoreMap();
        for (Map.Entry<Long, Map<Long, AuctionStore>> longMapEntry : unionBossStoreMap.entrySet()) {
            if (removeExpireStoreByMap(data, longMapEntry.getValue(), nowOfSeconds, "unionBossStoreMap")){
                bNeedSave = true;
            }
        }
        if (bNeedSave){
            DataCenter.updateData(data);
        }
    }

    boolean removeExpireStoreByMap(AuctionData data, Map<Long, AuctionStore> mapItems, int nowTime, String optName){
        boolean retFlag = false;
        List<Long> lstStoreIds =  new ArrayList<>();
        for (Map.Entry<Long, AuctionStore> item: mapItems.entrySet()){
            AuctionStore store = item.getValue();
            //只处理过期,无人竞拍,且非玩家商品的
            if ( store.getOffShelTime() < nowTime  && store.getBId() == 0 && store.getRId() < Integer.MAX_VALUE){
                lstStoreIds.add(store.getStoreId());
                log.info("拍卖行自动下架{}的已过期物品 storeID{} itemId {} count {}", optName, store.getStoreId(), store.getItemId(), store.getItemCount());
            }
        }
        //开始移除过期商品
        for (Long storeID: lstStoreIds){
            if (mapItems.remove(storeID) != null){
                log.info("拍卖行自动下架{}的已过期物品 storeID{} 移除成功", optName, storeID);
                retFlag = true;
            }
        }
        return retFlag;
    }

    void scheduleAuction() {
        AuctionData data = SysDataProvider.get(AuctionData.class);
        OtherData otherData = SysDataProvider.get(OtherData.class);
        Map<Long, AuctionStore> diamondMap = data.getDiamondStoreMap();
        Map<Long, AuctionStore> ingotsMap = data.getIngotsStoreMap();
        Map<Long, AuctionStore> worldMap = data.getWorldStoreMap();
        Map<Long, AuctionStore> unionMap = data.getUnionStoreMap();
        Map<Long, Map<Long, AuctionStore>> unionBossStoreMap = data.getUnionBossStoreMap();

        // 拍卖行数据为空
        if (diamondMap.isEmpty() && ingotsMap.isEmpty() && worldMap.isEmpty() && unionMap.isEmpty() && unionBossStoreMap.isEmpty()) {
            return;
        }
        GlobalConfig globalConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.AUCTION_HAND_FEE);
        if (globalConfig == null) {
            return;
        }
        // 到期商品列表
        List<AuctionStore> list = new ArrayList<>();
        Map<Long, List<AuctionStore>> otherList = new HashMap<>();
        int nowOfSeconds = TimeUtil.getNowOfSeconds();

        list.addAll(getExpiredList(diamondMap, nowOfSeconds, AuctionConst.NORMAL_DEAL));
        list.addAll(getExpiredList(ingotsMap, nowOfSeconds, AuctionConst.NORMAL_DEAL));
        list.addAll(getExpiredList(worldMap, nowOfSeconds, AuctionConst.SYSTEM_DEAL));
        list.addAll(getExpiredList(unionMap, nowOfSeconds, AuctionConst.SYSTEM_DEAL));

        for (Map.Entry<Long, Map<Long, AuctionStore>> longMapEntry : unionBossStoreMap.entrySet()) {
            List<AuctionStore> expiredList = getExpiredList(longMapEntry.getValue(), nowOfSeconds, AuctionConst.SYSTEM_DEAL);
            otherList.put(longMapEntry.getKey(), expiredList);
        }
        if (list.isEmpty() && otherList.isEmpty()) {
            return;
        }

        for (AuctionStore store : list) {
            AuctionStore remove = getPutOffStoreInfo(null, data, store.getStoreId(), store.getPutOnType(), store.getMoneyType());
            if (remove == null) {
                continue;
            }
            if (remove.getBId() == 0) {
                continue;
            }
            log.info("拍卖行-删除过期商品信息:{}", JSON.toJSONString(remove));
            // 到期商品有人竞价
            if (store.getPutOnType() == 0) {
                dealStore(data, remove, store.getBidMoney(), remove.getBId(), Integer.valueOf(globalConfig.getValue()), AuctionConst.NORMAL_DEAL);
            } else {
                dealStore(data, remove, store.getBidMoney(), remove.getBId(), Integer.valueOf(globalConfig.getValue()), AuctionConst.SYSTEM_DEAL);
                // 行会拍卖,累计交易金额
                if (store.getPutOnType() == AuctionConst.UNION_AUCTION) {
                    if (store.getMoneyType() == AuctionConst.MONEY_INGOTS) {
                        otherData.setUnionTotalIngots(otherData.getUnionTotalIngots() + store.getBidMoney());
                    }
                    if (store.getMoneyType() == AuctionConst.MONEY_DIAMOND) {
                        otherData.setUnionTotalDiamond(otherData.getUnionTotalDiamond() + store.getBidMoney());
                    }
                    DataCenter.updateData(otherData);
                }
            }
            auctionLog(store, AuctionConst.OperateType.OFF_SHELVES, store.getBidMoney(), store.getBId());
        }

        for (Map.Entry<Long, List<AuctionStore>> longListEntry : otherList.entrySet()) {
            List<AuctionStore> value = longListEntry.getValue();
            for (AuctionStore store : value) {
                AuctionStore remove = getPutOffStoreInfo(null, data, store.getStoreId(), store.getPutOnType(), store.getMoneyType());
                if (remove == null) {
                    continue;
                }
                if (remove.getBId() == 0) {
                    continue;
                }
                dealStore(data, remove, store.getBidMoney(), remove.getBId(), Integer.valueOf(globalConfig.getValue()), AuctionConst.SYSTEM_DEAL);
                // 行会拍卖,累计交易金额
                if (store.getMoneyType() == AuctionConst.MONEY_INGOTS) {
                    otherData.getUnionMoneyMap().merge(longListEntry.getKey(), store.getBidMoney(), Integer::sum);
                }
                if (store.getMoneyType() == AuctionConst.MONEY_DIAMOND) {
                    otherData.getUnionDiamondMap().merge(longListEntry.getKey(), store.getBidMoney(), Integer::sum);
                }
                DataCenter.updateData(otherData);
            }
        }

        //其他拍卖分红
        for (Map.Entry<Long, Map<Long, AuctionStore>> longMapEntry : unionBossStoreMap.entrySet()) {
            long unionId = longMapEntry.getKey();
            Union union = DataCenter.get(Union.class,unionId);
            if (union == null) {
                continue;
            }
            Map<Long, AuctionStore> value = longMapEntry.getValue();
            if (!value.isEmpty()) {
                continue;
            }
            int totalDiamond = otherData.getUnionDiamondMap().getOrDefault(unionId, 0);
            int totalMoney = otherData.getUnionMoneyMap().getOrDefault(unionId, 0);
            if (totalDiamond <= 0 && totalMoney <= 0) {
                continue;
            }
            List<Long> users = new ArrayList<>();
            for (Map.Entry<Long, Long> entry : union.getAuctionScoreMap().entrySet()) {
                if (entry.getKey() != 0 && entry.getValue() != 0 && union.getMemberInfos().containsKey(entry.getKey())) {
                    users.add(entry.getKey());
                }
            }
            if (!users.isEmpty()) {
                int diamond = totalDiamond / users.size();
                int money = totalMoney / users.size();
                List<Item> itemList = new ArrayList<>();
                Item ingotsItem = ItemUtil.create(AuctionConst.BIND_INGOTS, money, LogAction.AUCTION);
                Item diamondItem = ItemUtil.create(AuctionConst.BIND_DIAMOND, diamond, LogAction.AUCTION);
                itemList.add(ingotsItem);
                itemList.add(diamondItem);
                for (Long rid : users) {
                    MailManager.getInstance().sendMail(rid, AuctionConst.UNION_HAND_OUT, itemList);
                }
                otherData.getUnionDiamondMap().put(unionId, 0);
                otherData.getUnionMoneyMap().put(unionId, 0);
                DataCenter.updateData(otherData);
            }
        }
    }

    /**
     * 系统自动上架检测
     */
    void scheduleOnSystemAuto() {
        AuctionData data = SysDataProvider.get(AuctionData.class);
        List<AuctionAutoConfig> autoList = ConfigDataManager.getInstance().getList(AuctionAutoConfig.class);
        for (AuctionAutoConfig autoConfig : autoList) {
            if (GameContext.getOpenServerDay() < autoConfig.getStarttime()) {
                continue;
            }
            if (GameContext.getOpenServerDay() > autoConfig.getOvertime()) {
                continue;
            }
            boolean check = false;
            if (autoConfig.getGroundingtime().size() <= 0){
                //没有配置时间的,不自动上架,如击杀Boss时上架则没有时间配置
                continue;
            }
            // 时间检测
            for (int[] times : autoConfig.getGroundingtime()) {
                if (times[0] == TimeUtil.getNowHour() && times[1] == TimeUtil.getNowMinutes()) {
                    check = true;
                    break;
                }
            }
            if (!check) {
                continue;
            }

            for (int i = 0; i < autoConfig.getOncenumber(); i++) {
                // 概率检测
                check = RandomUtil.isGenerate(10000, autoConfig.getRate());
                if (!check) {
                    log.info("拍卖行，系统自动上架，上架失败，上架概率检查失败 配置id {}", autoConfig.getId());
                    continue;
                }
                // 上架数量检测
                List<AuctionStore> storeList = new ArrayList<>();
                switch (autoConfig.getType()) {
                    case AuctionConst.MONEY_DIAMOND:
                        storeList.addAll(data.getDiamondStoreMap().values());
                        break;
                    case AuctionConst.MONEY_INGOTS:
                        storeList.addAll(data.getIngotsStoreMap().values());
                        break;
                    default:
                        log.error("拍卖行，系统自动上架，上架失败，没有当前上架类型 配置id {} 类型 {}", autoConfig.getId(), autoConfig.getType());
                        return;
                }
                // 上架总数量检测
                int storeCount = 0;
                for (AuctionStore store : storeList) {
                    if (store.getRId() == autoConfig.getId() ) {
                        if (store.getOffShelTime() > TimeUtil.getNowOfSeconds()){
                            //没有过期的,已上架数量+1
                            storeCount++;
                        }else{
                            removeExpireStore(data, store, autoConfig);
                        }
                    }
                }
                if(storeCount >= autoConfig.getLimitnumber()){
                    log.error("拍卖行，系统自动上架，上架失败，上架数量超过限制 配置id {}", autoConfig.getId());
                    break;
                }
                // 上架
                int itemId = autoConfig.getItemnumber()[0];
                int count = autoConfig.getItemnumber().length > 1 ? autoConfig.getItemnumber()[1] : 0;
                reqSystemPutOn(autoConfig.getId(), itemId, count, autoConfig.getType(), autoConfig.getBidPrice(),
                        autoConfig.getBuyPrice(), autoConfig.getShelfTime());
                log.info("拍卖行，系统自动上架，上架成功， 配置id {} itemId {} count {} 竞拍价格 {} 一口价 {} type {}", autoConfig.getId(), itemId, count, autoConfig.getBidPrice(),
                        autoConfig.getBuyPrice(), autoConfig.getType());
            }
        }
        DataCenter.updateData(data);
    }

    /**
     * 移除已过期的自动上架商品
     */
    boolean removeExpireStore( AuctionData data, AuctionStore store, AuctionAutoConfig autoConfig){
        if (store.getBId() > 0){
            //加上限制,如果此物品有人正在竞拍,不能删除,实际上在scheduleAuction 中已经处理了竞拍了的玩家并在到期时将物品给了对应的玩家,但为了保险起见,这里还是需要判断一下
            return false;
        }
        switch (autoConfig.getType()) {
            case AuctionConst.MONEY_DIAMOND:
                data.getDiamondStoreMap().remove(store.getStoreId());
                log.info("拍卖行自动下架系统的已过期物品 配置id {} 类型 {}", autoConfig.getId(), autoConfig.getType());
                return true;
            case AuctionConst.MONEY_INGOTS:
                data.getIngotsStoreMap().remove(store.getStoreId());
                log.info("拍卖行自动下架系统的已过期物品 配置id {} 类型 {}", autoConfig.getId(), autoConfig.getType());
                return true;
            default:
                log.error("拍卖行，removeExpireStore没有当前上架类型 配置id {} 类型 {}", autoConfig.getId(), autoConfig.getType());
                return false;
        }
    }
    /**
     * 获取已上架数量
     */
    int getPutedOnCount(AuctionData data , AuctionAutoConfig autoConfig ){

        List<AuctionStore> storeList = new ArrayList<>();
        switch (autoConfig.getType()) {
            case AuctionConst.MONEY_DIAMOND:
                storeList.addAll(data.getDiamondStoreMap().values());
                break;
            case AuctionConst.MONEY_INGOTS:
                storeList.addAll(data.getIngotsStoreMap().values());
                break;
            default:
                log.error("获取拍卖行上架数量失败，没有当前上架类型 配置id {} 类型 {}", autoConfig.getId(), autoConfig.getType());
                return Integer.MAX_VALUE;
        }

        int storeCount = 0;
        for (AuctionStore store : storeList) {
            if (store.getRId() == autoConfig.getId()) {
                if (store.getOffShelTime() > TimeUtil.getNowOfSeconds()){
                    //没有过期的,已上架数量+1
                    storeCount++;
                }else{
                    removeExpireStore(data, store, autoConfig);
                }
            }
        }
        return storeCount;
    }
    /**
     * 判断是否可上架
     */
    boolean checkIsCanPutOn(AuctionData data , AuctionAutoConfig autoConfig ){
        if (GameContext.getOpenServerDay() < autoConfig.getStarttime()) {
            return false;
        }
        if (GameContext.getOpenServerDay() > autoConfig.getOvertime()) {
            return false;
        }
        // 上架数量检测
        int storeCount = getPutedOnCount(data, autoConfig);
        if (storeCount == Integer.MAX_VALUE) {
            return false;
        }

        if (storeCount >= autoConfig.getLimitnumber()) {
            log.error("拍卖行，上架失败，上架数量超过限制 配置id {}", autoConfig.getId());
            return false;
        }

        return true;
    }

    /**
     * 上架一批物品
     * @param paimaiid
     * @return
     */
    @Override
    public boolean scheduleOnItems(int nMonsterID, List<Integer> paimaiid){
        if (paimaiid == null){
            return false;
        }
        boolean bRet = false;
        List<Integer> lstAnnounce = new ArrayList<>();
        AuctionData data = SysDataProvider.get(AuctionData.class);
        for (Integer id : paimaiid){
            AuctionAutoConfig autoConfig = ConfigDataManager.getInstance().getById(AuctionAutoConfig.class, id);
            if (autoConfig == null){
                continue;
            }

            boolean check = false;
            for (int i = 0; i < autoConfig.getOncenumber(); i++) {
                // 概率检测
                check = RandomUtil.isGenerate(10000, autoConfig.getRate());
                if (!check) {
                    log.info("拍卖行，scheduleOnItems 自动上架，上架概率检查失败 配置id {}", autoConfig.getId());
                    continue;
                }

                if (! checkIsCanPutOn(data, autoConfig)){
                    log.info("拍卖行，scheduleOnItems 自动上架结束,已不可继续上架: 配置id {}", autoConfig.getId());
                    break;
                }

                // 上架
                int itemId = autoConfig.getItemnumber()[0];
                int count = autoConfig.getItemnumber().length > 1 ? autoConfig.getItemnumber()[1] : 0;
                reqSystemPutOn(autoConfig.getId(), itemId, count, autoConfig.getType(), autoConfig.getBidPrice(),
                        autoConfig.getBuyPrice(), autoConfig.getShelfTime());
                log.info("拍卖行，scheduleOnItems 上架，上架成功， 配置id {} itemId {} count {} 竞拍价格 {} 一口价 {} type {}", autoConfig.getId(), itemId, count, autoConfig.getBidPrice(),
                        autoConfig.getBuyPrice(), autoConfig.getType());
                bRet = true;
                if (autoConfig.getAnnounce() > 0 && ! lstAnnounce.contains(autoConfig.getAnnounce())){
                        lstAnnounce.add(autoConfig.getAnnounce());
                }
            }

        }
        for (Integer id: lstAnnounce){
            AnnounceManager.getInstance().post(id , 0, nMonsterID);
        }
        //由于reqSystemPutOn中已经写了 DataCenter.updateData(data); 这里就不再重复执行了
        return bRet;
    }

    /**
     * 请求拍卖行商品列表
     *
     * @param role role
     * @param type 拍卖行类别1钻石拍卖2元宝拍卖3世界拍卖4行会拍卖
     */
    @Override
    public void reqAuctionListInfo(Role role, int type) {
        // 获取拍卖行数据
        AuctionData data = SysDataProvider.get(AuctionData.class);
        ResAuctionListMessage msg = new ResAuctionListMessage();
        PaimaiProtos.ResAuctionList.Builder auctionList = PaimaiProtos.ResAuctionList.newBuilder();
        // 个人参与竞价物品信息
        Map<Long, BidInfo> map = role.getAuctionInfo().getBidStore();
        int now = TimeUtil.getNowOfSeconds();
        switch (type) {
            case AuctionConst.DIAMOND_AUCTION:
                for (AuctionStore store : data.getDiamondStoreMap().values()) {
                    if (now >= store.getOffShelTime()) {
                        continue;
                    }
                    auctionList.addStoreList(getBean(store, map));
                }
                auctionList.setType(AuctionConst.DIAMOND_AUCTION);
                break;
            case AuctionConst.INGOTS_AUCTION:
                for (AuctionStore store : data.getIngotsStoreMap().values()) {
                    if (now >= store.getOffShelTime()) {
                        continue;
                    }
                    auctionList.addStoreList(getBean(store, map));
                }
                auctionList.setType(AuctionConst.INGOTS_AUCTION);
                break;
            case AuctionConst.WORLD_AUCTION:
                for (AuctionStore store : data.getWorldStoreMap().values()) {
                    if (now >= store.getOffShelTime()) {
                        continue;
                    }
                    auctionList.addStoreList(getBean(store, map));
                }
                auctionList.setType(AuctionConst.WORLD_AUCTION);
                break;
            case AuctionConst.UNION_AUCTION:
                auctionList.setType(AuctionConst.UNION_AUCTION);
                Map<Long, AuctionStore> allUnionStoreMap = data.getAllUnionStoreMap(role);
                for (AuctionStore store : allUnionStoreMap.values()) {
                    if (now >= store.getOffShelTime()) {
                        continue;
                    }
                    auctionList.addStoreList(getBean(store, map));
                }
                break;
            default:
                break;
        }
        msg.setProto(auctionList.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求上架商品列表
     *
     * @param role role
     */
    @Override
    public void reqAuctionPutOnList(Role role) {
        AuctionData data = SysDataProvider.get(AuctionData.class);
        // 返回上架列表信息
        ResAuctionPutOnMessage msg = getPutOnResponse(data, role);
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求上架商品
     *
     * @param role      role
     * @param uniqueId  道具唯一id
     * @param itemCount 道具数量
     * @param moneyType 商品价格类型
     * @param bidMoney  竞拍价
     * @param money     一口价
     */
    @Override
    public void reqAuctionPutOn(Role role, long uniqueId, int itemId, int itemCount, int moneyType, int bidMoney, int money) {
        if (itemCount <= 0) {
            return;
        }

        if (bidMoney <= 0 || money <= 0) {
            log.error("拍卖行，上架商品，玩家上架商品传入价格参数异常，玩家id {} name {} monetType {} bidMoney {} money {} itemId {} count {}", role.getId(), role.getName(), moneyType, bidMoney, money, itemId, itemCount);
            return;
        }

		if (SecondaryPasswordManager.getInstance().secondPasswordCheck(role)) {
            TipUtil.show(role.getId(), CommonTips.脚本_二级密码已锁定);
            return;
        }
        AuctionData data = SysDataProvider.get(AuctionData.class);
        //获取已上架次数，检查剩余上架数量rCount
        int aCount = data.getAuctionCount().getOrDefault(role.getId(), 0);
        int rCount = CountManager.getInstance().verifyRemainderCount(role, aCount, ActionCountLimitUtil.Action.AUCTION);
        if (rCount <= 0) {
            ResCountTypeMessage resCountTypeMessage = new ResCountTypeMessage();
            resCountTypeMessage.setHelpId(AcutionHelpId);
            MessageUtil.sendMsg(resCountTypeMessage, role.getId());
            return;
        }
        // 1、背包扣除玩家道具
        Item item = role.getBackpack().findItemByItemId(uniqueId, BACKPACK_AND_HERO);
        if (item == null) {
            return;
        }
        // 2、获取拍卖道具配置信息
        AuctionConfig config = ConfigDataManager.getInstance().getById(AuctionConfig.class, item.getCfgId());
        if (config == null) {
            return;
        }
        // 3、检查道具出售价格类型是否正确
        if (!checkMoneyType(config.getMoneyType(), moneyType)) {
            TipUtil.show(role, CommonTips.脚本_道具价格类型错误);
            return;
        }
        // 4、判断玩家上架物品数量是否已到上限
        GlobalConfig globalConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.AUCTION_PUT_ON_LIMIT);
        if (globalConfig == null) {
            TipUtil.show(role, CommonTips.脚本_错误的全局ID);
            return;
        }
        if (role.getAuctionInfo().getPutOnStoreId().size() >= Integer.valueOf(globalConfig.getValue())) {
            TipUtil.show(role, CommonTips.脚本_拍卖行上架商品数量已达上限);
            return;
        }

        EquipData equipData = item.getEquipData();
        // 投保判断
        if (equipData != null && equipData.getToubao() == 1) {
            TipUtil.show(role.getId(), CommonTips.投保_弃保后才可交易);
            return;
        }
        if (!IdentifyManager.getInstance().checkTrade(item)) {
            return;
        }

        //附魂判断(已附魂的道具无法上架)
        if (equipData != null && equipData.getGemId() > 0) {
            TipUtil.show(role.getId(), CommonTips.已附魂的道具无法交易);
            return;
        }

        //混沌 附魂判断(已附魂的道具无法上架)
        if (equipData != null && equipData.getChaoticGemId() > 0) {
            TipUtil.show(role.getId(), CommonTips.已附魂的道具无法交易);
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(item, itemCount);
        if (!stash.commit(role, LogAction.BOURSE_PUT_ON)) {
            log.error("背包道具扣除错误，玩家id = {} name = {} itemId = {} itemCfgId = {} itemCount = {}"
                    , role.getId(), role.getName(), item.getId(), item.getCfgId(), itemCount);
            return;
        }

        // 6、拍卖行新增商品
        AuctionStore store = new AuctionStore(role, item, moneyType, bidMoney, money, config.getShelfTime(), 0);

        log.info("拍卖行-上架商品,name:{},rid:{},type:{},bindMoney:{},oneMoney:{} storeInfo {}", role.getName(), role.getId(), moneyType, bidMoney, money, JSON.toJSONString(store));

        store.setItemCount(itemCount);
        switch (moneyType) {
            case AuctionConst.MONEY_DIAMOND:
                data.getDiamondStoreMap().put(store.getStoreId(), store);
                break;
            case AuctionConst.MONEY_INGOTS:
                data.getIngotsStoreMap().put(store.getStoreId(), store);
                break;
            default:
                break;
        }
        // 7、个人上架商品数量+1
        role.getAuctionInfo().getPutOnStoreId().add(store.getStoreId());

        Map<Long, Integer> auctionCount = data.getAuctionCount();
        auctionCount.computeIfPresent(role.getId(), (k, v) -> v + 1);
        auctionCount.putIfAbsent(role.getId(), 1);
		DataCenter.updateData(data);
        DataCenter.updateData(role);

        int count = data.getAuctionCount().getOrDefault(role.getId(), 0);
        // 返回上架剩余次数
        int remainderCount = CountManager.getInstance().verifyRemainderCount(role, count, ActionCountLimitUtil.Action.AUCTION);
        ResAuctionPutDiscardMessage message = new ResAuctionPutDiscardMessage();
        message.setProto(PaimaiProtos.ResAuctionPutDiscard.newBuilder()
                .setRemainderCount(remainderCount)
                .build());
        MessageUtil.sendMsg(message, role.getId());
        // 返回上架列表信息
        ResAuctionPutOnMessage msg = getPutOnResponse(data, role);
        MessageUtil.sendMsg(msg, role.getId());
        auctionLog(store, AuctionConst.OperateType.RE_PUTAWAY, store.getBidMoney(), store.getBId());
    }

    void reqSystemPutOn(int sysId, int itemId, int itemCount, int moneyType, int bidMoney, int money, int shelfTime) {
        AuctionData data = SysDataProvider.get(AuctionData.class);
        // 6、拍卖行新增商品
        // 鉴定属性
        EquipData equipData = new EquipData();
//        equipData.setIdentifyAttribute(IdentifyScript.randomAttribute(itemId, Collections.EMPTY_LIST, null));
//        SpecialXiLianConfig config = ConfigDataManager.getInstance().getById(SpecialXiLianConfig.class, itemId + "#1");
//        if (config != null && config.getType() == 1) {
//            itemId = config.getIdentifyget();
//        }

        AuctionStore store = new AuctionStore(sysId, itemId, itemCount, moneyType, bidMoney, money, shelfTime, 0, equipData);
        store.setItemCount(itemCount);
        switch (moneyType) {
            case AuctionConst.MONEY_DIAMOND:
                data.getDiamondStoreMap().put(store.getStoreId(), store);
                break;
            case AuctionConst.MONEY_INGOTS:
                data.getIngotsStoreMap().put(store.getStoreId(), store);
                break;
            default:
                break;
        }
        DataCenter.updateData(data);
        auctionLog(store, AuctionConst.OperateType.SYS_AUCTION, store.getBidMoney(), store.getBId());
    }

    /**
     * 请求下架商品
     *
     * @param role    role
     * @param storeId 商品唯一id
     */
    @Override
    public void reqAuctionPutOff(Role role, long storeId) {
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        AuctionData data = SysDataProvider.get(AuctionData.class);
        // 个人上架商品信息
        Set<Long> putOnSet = role.getAuctionInfo().getPutOnStoreId();
        if (!putOnSet.contains(storeId)) {
            putOnSet.remove(storeId);
            DataCenter.updateData(role);
            reqAuctionPutOnList(role);
            return;
        }
        AuctionStore store = getStoreByIdPuton(role, data, storeId, 0);
        // 道具不在拍卖行或已进入自动交易流程
        if (store == null || (nowOfSeconds > store.getOffShelTime() && store.getBId() != 0)) {
            TipUtil.show(role, CommonTips.脚本_该商品不存在);
            reqAuctionPutOnList(role);
            return;
        }

        // 拍卖行删除商品
        AuctionStore remove = getPutOffStoreInfo(role, data, store.getStoreId(), 0, store.getMoneyType());
        if (remove == null) {
            TipUtil.show(role, CommonTips.脚本_该商品已售出);
            reqAuctionPutOnList(role);
            return;
        }

        // 下架已过期的商品
        if (nowOfSeconds > store.getOffShelTime()) {
            // 背包获得下架商品,个人上架列表移除商品
            BackpackStash toBack = new BackpackStash(role);
            Item item = ItemUtil.create(store.getItemId(), store.getItemCount(), LogAction.AUCTION);
            item.setEquipData(store.getEquipData());
            toBack.increase(item);
            toBack.commit(role, LogAction.BOURSE_PUT_DOWN, true, new NoticeCallback<BackpackStashCommitRetNotice>() {
                @Override
                public void callback(BackpackStashCommitRetNotice notice) {
                    if (!notice.isSuccess()) {
                        return;
                    }
                    putOnSet.remove(storeId);
                    DataCenter.updateData(role);
                    DataCenter.updateData(data);
                    ResAuctionPutOnMessage msg = getPutOnResponse(data, role);
                    MessageUtil.sendMsg(msg, role.getId());
                }
            });
        } else {
            // 背包新增下架商品
            BackpackStash stash = new BackpackStash(role);
            Item item = ItemUtil.create(remove.getItemId(), remove.getItemCount(), LogAction.AUCTION);
            item.setEquipData(remove.getEquipData());
            stash.increase(item);
            stash.commit(role, LogAction.BOURSE_PUT_DOWN, true, new NoticeCallback<BackpackStashCommitRetNotice>() {
                @Override
                public void callback(BackpackStashCommitRetNotice notice) {
                    if (!notice.isSuccess()) {
                        if (remove.getMoneyType() == AuctionConst.MONEY_INGOTS) {
                            data.getIngotsStoreMap().put(remove.getStoreId(), remove);
                        } else {
                            data.getDiamondStoreMap().put(remove.getStoreId(), remove);
                        }
                        return;
                    }
                    // 返还其他竞价者货币
                    if (remove.getBId() != 0) {
                        refund(remove);
                    }
                    // 个人上架商品列表更新
                    putOnSet.remove(storeId);
                    DataCenter.updateData(data);
                    DataCenter.updateData(role);
                    // 返回上架列表信息
                    ResAuctionPutOnMessage msg = getPutOnResponse(data, role);
                    MessageUtil.sendMsg(msg, role.getId());
                }
            });
        }
        auctionLog(store, AuctionConst.OperateType.OFF_SHELVES, store.getBidMoney(), store.getBId());
    }

    /**
     * 请求竞价
     *
     * @param role      role
     * @param storeId   商品id
     * @param bidPrice  竞价价格
     * @param type      1钻石2元宝3世界4行会5我的竞价列表
     * @param moneyType 价格类型
     */
    @Override
    public void reqAuctionBid(Role role, long storeId, int bidPrice, int type, int moneyType) {
        if (SecondaryPasswordManager.getInstance().secondPasswordCheck(role)) {
            TipUtil.show(role.getId(), CommonTips.脚本_二级密码已锁定);
            return;
        }
        AuctionData data = SysDataProvider.get(AuctionData.class);
        // 获取上架商品信息
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        AuctionStore store = getStoreInfoById(role, data, storeId, type);
        if (store == null || nowOfSeconds >= store.getOffShelTime()) {
            TipUtil.show(role, CommonTips.脚本_该商品已被购买);
            returnAuctionInfo(role, type);
            return;
        }

        if (bidPrice <= 0) {
            log.error("拍卖行，玩家竞价，传入竞拍价格小于0，玩家id {} name {} bidPrice {} monetType {} storeInfo {}", role.getId(), role.getName(), bidPrice, moneyType, JSON.toJSONString(store));
            return;
        }
        if (store.getRId() == role.getId()) {
            TipUtil.show(role, CommonTips.脚本_不能购买自己的商品);
            return;
        }

        // 判断newPrice 是否小于当前最新的竞价
        if (bidPrice <= store.getBidMoney()) {
            return;
        }
        // 竞价价格是否大于一口价
        if (bidPrice >= store.getMoney()) {
            log.error("玩家竞价价格超过一口价，竞价价格：{}，一口价：{}", bidPrice, store.getMoney());
            return;
        }

        /**
         * 判断最小加价是否满足
         */
        if (moneyType == BagConst.ItemId.GOLD || moneyType == BagConst.ItemId.MONEY) {

            AuctionConfig auctionConfig = ConfigDataManager.getInstance().getById(AuctionConfig.class, store.getItemId());
            if (auctionConfig == null) {
                log.error("玩家拍卖不存在的物品RoleId:{},RoleName:{},StoreId:{},bidPrice:{},type:{},MoneyType:{},Item:{}", role.getId(), role.getName(), storeId, bidPrice, type, moneyType, store.getItemId());
                return;
            }

            int minPrice = moneyType == BagConst.ItemId.GOLD ? auctionConfig.getMinGold() : auctionConfig.getMinMoney();
            //不符合最低加价格策略
            if (bidPrice - store.getBidMoney() < minPrice) {
                return;
            }
        }



        // 扣除竞价玩家货币
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(store.getMoneyType(), bidPrice);
        stash.commit(role, LogAction.BOURSE_BIDDING, true, new NoticeCallback<BackpackStashCommitRetNotice>() {
            @Override
            public void callback(BackpackStashCommitRetNotice notice) {
                if (!notice.isSuccess()) {
                    return;
                }
                // 当前商品存在其他竞价者
                if (store.getBId() != 0) {
                    ResBidderChangeMessage msg = new ResBidderChangeMessage();
                    msg.setProto(PaimaiProtos.ResBidderChange.newBuilder().build());
                    MessageUtil.sendMsg(msg, store.getBId());
                    refund(store);
                }
                BidInfo info = new BidInfo();
                info.setBidMoney(bidPrice);
                info.setPutOnType(store.getPutOnType());
                // 添加竞价记录
                role.getAuctionInfo().getBidStore().put(storeId, info);

                // 更新竞价和竞价者信息
                store.setBId(role.getId());
                store.setBidMoney(bidPrice);
                DataCenter.updateData(data);
                DataCenter.updateData(role);
                // 返回信息
                returnAuctionInfo(role, type);
                returnAuctionChangeInfo(store);
                auctionLog(store, AuctionConst.OperateType.AUCTION, bidPrice, role.getId());
            }
        });
    }

    /**
     * 请求购买商品
     *
     * @param role      role
     * @param storeId   商品唯一id
     * @param type      1钻石2元宝3世界4行会5我的竞价列表
     * @param moneyType 价格类型
     */
    @Override
    public void reqAuctionBuy(Role role, long storeId, int type, int moneyType) {
        if (SecondaryPasswordManager.getInstance().secondPasswordCheck(role)) {
            TipUtil.show(role.getId(), CommonTips.脚本_二级密码已锁定);
            return;
        }
        AuctionData data = SysDataProvider.get(AuctionData.class);
        // 获取商品信息
        AuctionStore store = getStoreInfoById(role, data, storeId, type);
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        if (store == null || nowOfSeconds >= store.getOffShelTime()) {
            TipUtil.show(role, CommonTips.脚本_该商品已被购买);
            // 返回信息
            returnAuctionInfo(role, type);
            return;
        }
        GlobalConfig globalConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.AUCTION_HAND_FEE);
        if (globalConfig == null) {
            return;
        }
        if (store.getRId() == role.getId()) {
            TipUtil.show(role, CommonTips.脚本_不能购买自己的商品);
            return;
        }
        int money = 0;
        // 所买商品的当前竞价者是自己
        if (store.getBId() == role.getId()) {
            money = store.getMoney() - store.getBidMoney();
        } else {
            money = store.getMoney();
        }
        // 买家扣钱
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(store.getMoneyType(), money);
        stash.commit(role, LogAction.AUCTION_BUY, true, new NoticeCallback<BackpackStashCommitRetNotice>() {
            @Override
            public void callback(BackpackStashCommitRetNotice notice) {
                if (!notice.isSuccess()) {
                    return;
                }
                //删除商品
                AuctionStore remove = getPutOffStoreInfo(role, data, store.getStoreId(), store.getPutOnType(), store.getMoneyType());
                if (remove == null) {
                    BackpackStash rollbackStash = new BackpackStash(role);
                    rollbackStash.increase(store.getMoneyType(), store.getMoney());
                    rollbackStash.commit(role, LogAction.BOURSE_BIDDING_COST_BACK, true, new NoticeCallback<BackpackStashCommitRetNotice>() {
                        @Override
                        public void callback(BackpackStashCommitRetNotice notice) {
                            if (!notice.isSuccess()) {
                                log.error("【{}】->【{}】玩家拍卖行购买失败退款未成功,货币类型：{},价格：{}", role.getId(), role.getName(), store.getMoneyType(), store.getBidMoney());
                            }
                        }
                    });
                    return;
                }
                // 返还其他竞价者货币
                if (remove.getBId() != 0 && remove.getBId() != role.getId()) {
                    refund(remove);
                }
                dealStore(data, remove, remove.getMoney(), role.getId(), Integer.valueOf(globalConfig.getValue()), AuctionConst.NORMAL_DEAL);
                // 返回信息
                returnAuctionInfo(role, type);
                auctionLog(store, AuctionConst.OperateType.BUY, store.getMoney(), role.getId());
            }
        });
    }

    /**
     * 请求拍卖行个人成交记录
     *
     * @param role role
     */
    @Override
    public void reqAuctionDealList(Role role) {
        // 获取个人成交记录
        List<RoleAuctionSoldRecord> soldStore = role.getAuctionInfo().getSoldStore();
        ResAuctionDealListMessage msg = new ResAuctionDealListMessage();
        if (soldStore.isEmpty()) {
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        for (RoleAuctionSoldRecord store : soldStore) {
            msg.setProto(PaimaiProtos.ResAuctionDealList.newBuilder()
                    .addStoreList(soldRecordToBean(store))
                    .build());
        }
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求拍卖行个人竞价列表
     *
     * @param role role
     */
    @Override
    public void reqAuctionBidList(Role role) {
        AuctionData data = SysDataProvider.get(AuctionData.class);
        ResAuctionBidListMessage msg = new ResAuctionBidListMessage();
        PaimaiProtos.ResAuctionBidList.Builder resAuctionBidList = PaimaiProtos.ResAuctionBidList.newBuilder();
        // 个人参与竞价物品信息
        Map<Long, BidInfo> bidStore = role.getAuctionInfo().getBidStore();
        if (bidStore.isEmpty()) {
            msg.setProto(resAuctionBidList.build());
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        Iterator<Map.Entry<Long, BidInfo>> iterator = bidStore.entrySet().iterator();
        // 移除不存在商品的记录
        while (iterator.hasNext()) {
            Map.Entry<Long, BidInfo> entry = iterator.next();
            AuctionStore s = getStoreByIdPuton(role, data, entry.getKey(), entry.getValue().getPutOnType());
            if (s == null) {
                iterator.remove();
            } else {
                resAuctionBidList.addStoreList(getBidBean(s, entry.getValue()));
            }
        }
        msg.setProto(resAuctionBidList.build());
        DataCenter.updateData(role);
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 行会拍卖上架道具
     *
     * @param playerScores 沙巴克积分
     */
    @Override
    public void reqUnionAuction(Union union, Map<Long, Long> playerScores, int type) {
        if (playerScores == null || playerScores.isEmpty()) return;
        // 行会拍卖道具获取
        if (union == null) {
            return;
        }
        List<Item> list = new ArrayList<>(union.getAuctionItems());
        if (list.isEmpty()) {
            return;
        }
        // 上架行会拍卖道具
        AuctionData data = SysDataProvider.get(AuctionData.class);
        for (Item item : list) {
            AuctionConfig config = ConfigDataManager.getInstance().getById(AuctionConfig.class, item.getCfgId());
            if (config == null) {
                continue;
            }
            AuctionStore as;
            if (config.getGuildBidPrice().isEmpty()) {
                log.error("cfg_auction_item配置表id：{}未配置指导价格", config.getItemId());
                continue;
            }

            if (config.getGuildBidPrice().get(0).length < 2) {
                log.error("cfg_auction_item配置表id：{}指导价格配置出错", config.getItemId());
                continue;
            }

            if (config.getGuildBidPrice().get(0)[0] == AuctionConst.MONEY_INGOTS) {
                as = new AuctionStore(null, item, AuctionConst.MONEY_INGOTS, config.getGuildBidPrice().get(0)[1], 0, config.getGuildShelfTime(), AuctionConst.UNION_AUCTION);
            } else {
                as = new AuctionStore(null, item, AuctionConst.MONEY_DIAMOND, config.getGuildBidPrice().get(0)[1], 0, config.getGuildShelfTime(), AuctionConst.UNION_AUCTION);
            }
            as.setItemCount(item.findCount());
            if (type == 1) {
                data.getUnionStoreMap().put(as.getStoreId(), as);
            } else {
                Map<Long, Map<Long, AuctionStore>> unionBossStoreMap = data.getUnionBossStoreMap();
                Map<Long, AuctionStore> longAuctionStoreMap = unionBossStoreMap.computeIfAbsent(union.getId(), k -> new HashMap<>());
                longAuctionStoreMap.put(as.getStoreId(), as);
            }
        }
        DataCenter.updateData(data);
        union.getAuctionItems().clear();
        //添加上架提醒
        ChatManager.getInstance().chat(null, ChatConst.ChatType.UNION, union.getId(), "已有新道具上架行会拍卖中", null, null);
    }

    @Override
    public void reqOpenAuction(Role role) {
        Set<Long> auctionSet = AuctionManager.getSet();
        auctionSet.add(role.getId());
    }

    @Override
    public void reqCloseAuction(Role role) {
        Set<Long> auctionSet = AuctionManager.getSet();
        auctionSet.remove(role.getId());
    }

    /**
     * 返回拍卖行页面信息
     *
     * @param role role
     * @param type 1钻石2元宝3世界4行会5我的竞价列表
     */
    private void returnAuctionInfo(Role role, int type) {
        if (type == AuctionConst.PERSONAL_BID_AUCTION) {
            reqAuctionBidList(role);
        } else {
            reqAuctionListInfo(role, type);
        }
    }

    /**
     * 返回拍卖行价格数据变换信息
     */
    private void returnAuctionChangeInfo(AuctionStore store) {
        Set<Long> auctionSet = AuctionManager.getSet();
        if (auctionSet.isEmpty()) {
            return;
        }
        ResAuctionChangeMessage msg = new ResAuctionChangeMessage();
        msg.setProto(PaimaiProtos.ResAuctionChange.newBuilder()
                .setStore(getBean(store, new HashMap<>()))
                .build());
        for (Long sid : auctionSet) {
            MessageUtil.sendMsg(msg, sid);
        }
    }

    /**
     * 获取过期商品
     *
     * @param map  商品列表
     * @param now  当前时间
     * @param type 上架类型
     * @return
     */
    private List<AuctionStore> getExpiredList(Map<Long, AuctionStore> map, int now, int type) {
        if (!map.isEmpty()) {
            if (type == AuctionConst.NORMAL_DEAL) {
                return map.values().stream()
                        .filter(store -> store.getOffShelTime() < now && store.getBId() != 0)
                        .collect(Collectors.toList());
            }
            return map.values().stream()
                    .filter(store -> store.getOffShelTime() < now)
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 检查道具出售价格类型是否正确
     *
     * @param moneyType 配置的价格类型
     * @param type      实际价格类型
     * @return
     */
    private boolean checkMoneyType(int moneyType, int type) {
        if (moneyType == AuctionConst.MONEY_COMMON) {
            return type == AuctionConst.MONEY_INGOTS || type == AuctionConst.MONEY_DIAMOND;
        }
        return type == moneyType;
    }

    /**
     * 拍卖行下架商品
     *
     * @param data
     * @param storeId
     * @param putOnType
     * @param moneyType
     * @return
     */
    private AuctionStore getPutOffStoreInfo(Role role, AuctionData data, long storeId, int putOnType, int moneyType) {
        // 拍卖行删除商品
        switch (putOnType) {
            case AuctionConst.WORLD_AUCTION:
                return data.getWorldStoreMap().remove(storeId);
            case AuctionConst.UNION_AUCTION:
                AuctionStore auctionStore = data.getUnionStoreMap().remove(storeId);
                if (auctionStore == null) {
                    Union union = UnionManager.getInstance().getUnion(role);
                    //行会不为空直接取 为空遍历取
                    if (union != null) {
                        Map<Long, AuctionStore> orDefault = data.getUnionBossStoreMap().getOrDefault(union.getId(), new HashMap<>());
                        auctionStore = orDefault.remove(storeId);
                    } else {
                        Iterator<Map.Entry<Long, Map<Long, AuctionStore>>> iterator = data.getUnionBossStoreMap().entrySet().iterator();
                        while (iterator.hasNext()) {
                            Map.Entry<Long, Map<Long, AuctionStore>> next = iterator.next();
                            Iterator<Map.Entry<Long, AuctionStore>> it = next.getValue().entrySet().iterator();
                            while (it.hasNext()) {
                                Map.Entry<Long, AuctionStore> nextEle = it.next();
                                if (nextEle.getKey() == storeId) {
                                    auctionStore = nextEle.getValue();
                                    it.remove();
                                }
                            }
                        }
                    }
                }
                return auctionStore;
            default:
                if (moneyType == AuctionConst.MONEY_DIAMOND) {
                    return data.getDiamondStoreMap().remove(storeId);
                }
                if (moneyType == AuctionConst.MONEY_INGOTS) {
                    return data.getIngotsStoreMap().remove(storeId);
                }
        }
        return null;
    }

    /**
     * 组装上架列表信息
     *
     * @param role role
     * @return
     */
    private ResAuctionPutOnMessage getPutOnResponse(AuctionData data, Role role) {
        ResAuctionPutOnMessage msg = new ResAuctionPutOnMessage();
        PaimaiProtos.ResAuctionPutOn.Builder resAuctionPutOn = PaimaiProtos.ResAuctionPutOn.newBuilder();
        Set<Long> putOnSet = role.getAuctionInfo().getPutOnStoreId();
        if (putOnSet.isEmpty()) {
            msg.setProto(resAuctionPutOn.build());
            return msg;
        }
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        Iterator<Long> iterator = putOnSet.iterator();
        long storeId;
        while (iterator.hasNext()) {
            storeId = iterator.next();
            // 清除拍卖行不存在的商品
            AuctionStore as = getStoreByIdPuton(role, data, storeId, 0);
            if (as == null || (nowOfSeconds >= as.getOffShelTime() && as.getBId() != 0)) {
                iterator.remove();
            } else {
                resAuctionPutOn.addStoreList(storeToBean(as));
            }
        }
        msg.setProto(resAuctionPutOn.build());
        DataCenter.updateData(role);
        return msg;
    }

    /**
     * 根据商品id获取商品信息
     *
     * @param storeId 商品id
     * @return
     */
    private AuctionStore getStoreInfoById(Role role, AuctionData data, long storeId, int type) {
        switch (type) {
            case AuctionConst.DIAMOND_AUCTION:
                return data.getDiamondStoreMap().get(storeId);
            case AuctionConst.INGOTS_AUCTION:
                return data.getIngotsStoreMap().get(storeId);
            case AuctionConst.WORLD_AUCTION:
                return data.getWorldStoreMap().get(storeId);
            case AuctionConst.UNION_AUCTION:
                return data.getAllUnionStoreMap(role).get(storeId);
            case AuctionConst.ALL_AUCTION:
                if (data.getDiamondStoreMap().get(storeId) != null) {
                    return data.getDiamondStoreMap().get(storeId);
                }
                if (data.getIngotsStoreMap().get(storeId) != null) {
                    return data.getIngotsStoreMap().get(storeId);
                }
                if (data.getWorldStoreMap().get(storeId) != null) {
                    return data.getWorldStoreMap().get(storeId);
                }
                return data.getAllUnionStoreMap(role).get(storeId);
        }
        return null;
    }

    /**
     * 获取商品信息
     *
     * @param data
     * @param storeId
     * @param putontype
     * @return
     */
    private AuctionStore getStoreByIdPuton(Role role, AuctionData data, long storeId, int putontype) {
        switch (putontype) {
            case AuctionConst.WORLD_AUCTION:
                return data.getWorldStoreMap().get(storeId);
            case AuctionConst.UNION_AUCTION:
                return data.getAllUnionStoreMap(role).get(storeId);
            default:
                AuctionStore store = data.getDiamondStoreMap().get(storeId);
                if (store != null) {
                    return store;
                }
                return data.getIngotsStoreMap().get(storeId);
        }
    }


    /**
     * 交易商品
     *
     * @param data        data
     * @param store       商品信息
     * @param dealPrice   交易价格
     * @param bid         买家
     * @param handlingFee 手续费百分百
     * @param type        1普通交易 2系统交易
     */
    private void dealStore(AuctionData data, AuctionStore store, int dealPrice, long bid, int handlingFee, int type) {
        if (type == AuctionConst.NORMAL_DEAL) {
            Role seller = DataCenter.getRole(store.getRId());
            Role buyer = DataCenter.getRole(bid);
            if (buyer == null) {
                return;
            }
            int money = (int) (dealPrice - Math.ceil(dealPrice * handlingFee / 100.0));
            // 给卖家发获得货币邮件
            sendItemMail(store, store.getRId(), AuctionConst.AUCTION_SELL_SUC, AuctionConst.RETURN_MONEY, money);
            // 卖家交易记录更新
            store.setMoney(dealPrice); // 交易价格
            if (seller != null) {
                List<RoleAuctionSoldRecord> soldList = seller.getAuctionInfo().getSoldStore();
                if (soldList.size() >= AuctionConst.MAX_SOLD_LOG_SIZE) {
                    soldList.remove(0);
                }
                soldList.add(storeToSoldRecord(store));
                DataCenter.updateData(seller);
            }
        }
        // 给买家发获得道具邮件
        sendItemMail(store, bid, AuctionConst.AUCTION_BUY_SUC, AuctionConst.RETURN_ITEM, 0);
        DataCenter.updateData(data);
        auctionLog(store, AuctionConst.OperateType.AUCTION_SUCCESS, dealPrice, bid);
    }

    /**
     * 发送商品邮件
     *
     * @param store  商品信息
     * @param rid    收件人id
     * @param mailId 邮件id
     * @param type   发送类型
     * @param money  价格
     */
    private void sendItemMail(AuctionStore store, long rid, int mailId, int type, int money) {
        Item item = null;
        List<Item> list = new ArrayList<>();
        ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, store.getItemId());
        if (config == null) {
            return;
        }
        switch (type) {
            case AuctionConst.RETURN_ITEM:
                item = ItemUtil.create(store.getItemId(), store.getItemCount(), LogAction.AUCTION);
                item.setEquipData(store.getEquipData());
                break;
            case AuctionConst.RETURN_MONEY:
                item = ItemUtil.create(store.getMoneyType(), money, LogAction.AUCTION);
                break;
            default:
                break;
        }
        list.add(item);
        MailManager.getInstance().sendMail(rid, mailId, list, config.getName());
    }

    /**
     * 退钱
     *
     * @param store 商品信息
     * @return
     */
    private void refund(AuctionStore store) {
        sendItemMail(store, store.getBId(), AuctionConst.AUCTION_BID_BACK, AuctionConst.RETURN_MONEY, store.getBidMoney());
    }

    @Override
    public void onRoleLogout(Role role) {
        Set<Long> auctionSet = AuctionManager.getSet();
        auctionSet.remove(role.getId());
    }

    /******************************************拍卖行数据封装*************************************************************/

    /**
     * 组装竞价列表
     *
     * @param a     当前商品信息
     * @param store 玩家商品的竞价信息
     * @return
     */
    private PaimaiProtos.AuctionStoreBean getBidBean(AuctionStore a, BidInfo store) {
        PaimaiProtos.AuctionStoreBean.Builder bean = PaimaiProtos.AuctionStoreBean.newBuilder();
        bean.setRId(a.getRId());
        bean.setStoreId(a.getStoreId());
        bean.setBid(a.getBId());
        bean.setItemId(a.getItemId());
        bean.setItemCount(a.getItemCount());
        bean.setMoneyType(a.getMoneyType());
        bean.setBidMoney(a.getBidMoney());
        bean.setMyBidMoney(store.getBidMoney());
        bean.setMoney(a.getMoney());
        bean.setPutOnTime(a.getPutOnTime());
        bean.setOffShelTime(a.getOffShelTime());
        bean.setEquipData(ItemUtil.packEquipDataBean(a.getEquipData()));
        return bean.build();
    }

    /**
     * 组装列表信息
     *
     * @param store
     * @param map
     * @return
     */
    private PaimaiProtos.AuctionStoreBean getBean(AuctionStore store, Map<Long, BidInfo> map) {
        PaimaiProtos.AuctionStoreBean.Builder a = PaimaiProtos.AuctionStoreBean.newBuilder();
        a.setStoreId(store.getStoreId());
        a.setRId(store.getRId());
        a.setBid(store.getBId());
        a.setItemCount(store.getItemCount());
        a.setItemId(store.getItemId());
        a.setMoneyType(store.getMoneyType());
        a.setPutOnType(store.getPutOnType());
        a.setBidMoney(store.getBidMoney());

        BidInfo bidInfo = map.get(store.getStoreId());
        if (bidInfo != null) {
            a.setMyBidMoney(bidInfo.getBidMoney());
        }
        a.setMoney(store.getMoney());
        a.setPutOnTime(store.getPutOnTime());
        a.setOffShelTime(store.getOffShelTime());
        a.setEquipData(ItemUtil.packEquipDataBean(store.getEquipData()));
        return a.build();
    }

    /**
     * 商品信息转bean
     *
     * @param store
     * @return
     */
    private PaimaiProtos.AuctionStoreBean storeToBean(AuctionStore store) {
        PaimaiProtos.AuctionStoreBean.Builder bean = PaimaiProtos.AuctionStoreBean.newBuilder();
        bean.setRId(store.getRId());
        bean.setBid(store.getBId());
        bean.setStoreId(store.getStoreId());
        bean.setItemId(store.getItemId());
        bean.setItemCount(store.getItemCount());
        bean.setMoneyType(store.getMoneyType());
        bean.setPutOnType(store.getPutOnType());
        bean.setBidMoney(store.getBidMoney());
        bean.setMyBidMoney(0);
        bean.setMoney(store.getMoney());
        bean.setPutOnTime(store.getPutOnTime());
        bean.setOffShelTime(store.getOffShelTime());
        bean.setEquipData(ItemUtil.packEquipDataBean(store.getEquipData()));
        return bean.build();
    }

    /**
     * 出售记录转bean
     *
     * @param soldRecord
     * @return
     */
    private PaimaiProtos.AuctionStoreBean soldRecordToBean(RoleAuctionSoldRecord soldRecord) {
        PaimaiProtos.AuctionStoreBean.Builder bean = PaimaiProtos.AuctionStoreBean.newBuilder();
        bean.setStoreId(soldRecord.getStoreId());
        bean.setItemId(soldRecord.getItemId());
        bean.setItemCount(soldRecord.getItemCount());
        bean.setMoneyType(soldRecord.getMoneyType());
        bean.setMoney(soldRecord.getMoney());
        bean.setSoldTime(soldRecord.getSoldTime());
        bean.setEquipData(ItemUtil.packEquipDataBean(soldRecord.getEquipData()));
        return bean.build();
    }

    /**
     * 商品出售记录
     *
     * @param store
     * @return
     */
    private RoleAuctionSoldRecord storeToSoldRecord(AuctionStore store) {
        RoleAuctionSoldRecord record = new RoleAuctionSoldRecord();
        record.setStoreId(store.getStoreId());
        record.setItemId(store.getItemId());
        record.setItemCount(store.getItemCount());
        record.setMoneyType(store.getMoneyType());
        record.setMoney(store.getMoney());
        record.setSoldTime(TimeUtil.getNowOfSeconds());
        record.setEquipData(store.getEquipData());
        return record;
    }

    @Override
    public void scheduleUpdateOnMidnight() {
        DealAuctionTimeOutNotice notice = new DealAuctionTimeOutNotice();
        AuctionData data = SysDataProvider.get(AuctionData.class);
        data.getAuctionCount().clear();
        DataCenter.updateData(data);
        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.SERVER_SOCIAL, notice, 0L);
    }

    @Override
    public void dealTimeOutAuction() {
        AuctionData data = SysDataProvider.get(AuctionData.class);
        Map<Long, AuctionStore> diamondStoreMap = data.getDiamondStoreMap();
        Map<Long, AuctionStore> ingotsStoreMap = data.getIngotsStoreMap();
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        long limitTime = TimeUtil.ONE_DAY_IN_SECONDS * 3;
        Iterator<Map.Entry<Long, AuctionStore>> iterator = diamondStoreMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, AuctionStore> next = iterator.next();
            AuctionStore auctionStore = next.getValue();
            if (auctionStore == null) {
                iterator.remove();
                continue;
            }
            long rId = auctionStore.getRId();
            Role role = DataCenter.getRole(rId);
            if (role == null) {
                continue;
            }
            Set<Long> putOnSet = role.getAuctionInfo().getPutOnStoreId();
            int offShelTime = auctionStore.getOffShelTime();
            if (offShelTime > 0 && nowOfSeconds - offShelTime > limitTime && putOnSet.contains(auctionStore.getStoreId())) {
                dealAuctionBack(auctionStore);
                iterator.remove();
            }
        }
        Iterator<Map.Entry<Long, AuctionStore>> it = ingotsStoreMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<Long, AuctionStore> next = it.next();
            AuctionStore auctionStore = next.getValue();
            if (auctionStore == null) {
                it.remove();
                continue;
            }
            long rId = auctionStore.getRId();
            Role role = DataCenter.getRole(rId);
            if (role == null) {
                continue;
            }
            Set<Long> putOnSet = role.getAuctionInfo().getPutOnStoreId();
            int offShelTime = auctionStore.getOffShelTime();
            if (offShelTime > 0 && nowOfSeconds - offShelTime > limitTime && putOnSet.contains(auctionStore.getStoreId())) {
                dealAuctionBack(auctionStore);
                it.remove();
            }
        }
        DataCenter.updateData(data);
    }

    /**
     * 处理竞拍过期返还发邮件
     *
     * @param auctionStore
     */
    private void dealAuctionBack(AuctionStore auctionStore) {
        Item item = ItemUtil.create(auctionStore.getItemId(), auctionStore.getItemCount(), LogAction.AUCTION_TIME_OUT);
        item.setEquipData(auctionStore.getEquipData());
        List<Item> itemList = new ArrayList<>();
        itemList.add(item);
        MailManager.getInstance().sendMail(auctionStore.getRId(), EmailConst.MailId.AUCTION_BACK, itemList);
    }

    @Override
    public void onSeverStartUp() {
        AuctionData data = SysDataProvider.get(AuctionData.class);
        Map<Long, AuctionStore> diamondStoreMap = data.getDiamondStoreMap();
        Map<Long, AuctionStore> ingotsStoreMap = data.getIngotsStoreMap();
        Iterator<Map.Entry<Long, AuctionStore>> iterator = diamondStoreMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, AuctionStore> next = iterator.next();
            AuctionStore auctionStore = next.getValue();
            if (auctionStore == null) {
                iterator.remove();
                continue;
            }
            long rId = auctionStore.getRId();
            Role role = DataCenter.getRole(rId);
            if (role == null) {
                AuctionAutoConfig autoConfig = ConfigDataManager.getInstance().getById(AuctionAutoConfig.class,
                        (int)rId);
                if (autoConfig == null) {
                    iterator.remove();
                    continue;
                }
            } else {
                Set<Long> putOnSet = role.getAuctionInfo().getPutOnStoreId();
                if (!putOnSet.contains(auctionStore.getStoreId())) {
                    iterator.remove();
                }
            }
        }
        Iterator<Map.Entry<Long, AuctionStore>> it = ingotsStoreMap.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<Long, AuctionStore> next = it.next();
            AuctionStore auctionStore = next.getValue();
            if (auctionStore == null) {
                it.remove();
                continue;
            }
            long rId = auctionStore.getRId();
            Role role = DataCenter.getRole(rId);
            if (role == null) {
                AuctionAutoConfig autoConfig = ConfigDataManager.getInstance().getById(AuctionAutoConfig.class,
                        (int)rId);
                if (autoConfig == null) {
                    it.remove();
                    continue;
                }
            } else {
                Set<Long> putOnSet = role.getAuctionInfo().getPutOnStoreId();
                if (!putOnSet.contains(auctionStore.getStoreId())) {
                    it.remove();
                }
            }
        }
        DataCenter.updateData(data);
    }

    /**
     * 拍卖行记录日志
     *
     * @param store 拍卖商品信息
     * @param type 操作类型
     * @param dealPrice 商品竞价价格
     */
    private void auctionLog(AuctionStore store, AuctionConst.OperateType type, int dealPrice, long bidderId) {
        if (store == null) {
            return;
        }
        AuctionLog log = new AuctionLog();
        log.setAuctioneerId(store.getRId());
        log.setBidderId(bidderId);
        log.setCoinType(store.getMoneyType());
        log.setItemId(store.getItemId());
        log.setCount(store.getItemCount());
        log.setPrice(store.getMoney());
        log.setNowPrice(dealPrice);
        log.setOperateType(type.getOperateType());
        log.setTime(TimeUtil.getNowOfSeconds());
        log.submit();
    }

}