package com.sh.game.script.chat;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sh.game.GameContext;
import com.sh.game.common.config.model.ChatMonitorConfig;
import com.sh.game.common.constant.ChatConst;
import com.sh.game.common.constant.RoleConst;
import com.sh.game.common.entity.User;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.HttpUtil;
import com.sh.game.common.util.Md5Util;
import com.sh.game.common.util.StringUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.server.SessionManager;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.chat.ChatManager;
import com.sh.game.system.chat.script.IChatMonitorTanWanScript;
import com.sh.game.system.chat.script.IChatMonitorWanXingJuHeScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.time.ZoneOffset;
import java.util.*;

/**
 * 玩心聚合
 *由ZT改造到SCRIPT,方便热加载
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/10/12.
 */

@Slf4j
@Script
public class ChatMonitorWanXingJuHeScript extends ChatMonitorBaseImpScript implements IChatMonitorWanXingJuHeScript {
    /**
     * 与平台对接的具体实现,
     * @param role
     * @param channel
     * @param target
     * @param content
     * @return
     */
    @Override
    String chatApi(Role role, int channel, long target, String content, ChatMonitorConfig monitorConfig) {

        String ChatMonitorURL= monitorConfig.getUrl();

        Map<String, Object> params = new HashMap<>();
        params.put("j_game_id", monitorConfig.getParams().get("gameID"));
        params.put("channel", monitorConfig.getParams().get("channelID") );
        params.put("server", GameContext.getServerId());


        User user = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(role.getId()));
        if (user != null) {
            params.put("channel_user_id", user.getAccount());
            params.put("user_ip", user.getIp());
        }
        params.put("role_id", role.getId());
        params.put("role_level", role.getLevel());
        params.put("role_name", role.getName());
        params.put("vip_level", 0);


        Map<String, Object> toChatParams = new HashMap<>();
        if (channel == ChatConst.ChatType.PERSON) {
            Role receiver = SessionManager.getInstance().getRole(target);
            if (receiver != null) {
                User receiverUser = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(receiver.getId()));
                if (receiverUser != null) {
                    toChatParams.put("channel_user_id", receiverUser.getAccount());
                }
                toChatParams.put("server",  GameContext.getServerId());
                toChatParams.put("role_id", receiver.getId());
                toChatParams.put("role_name", receiver.getName());
                toChatParams.put("role_level",receiver.getLevel());
                toChatParams.put("vip_level",receiver.getLevel());
                params.put("chat", toChatParams);
            }
        }
        int content_type = 9;
        //1 私聊 2 世界 3 公会 4 公会公告 5 邮件 6队伍 7 同屏 8 喇叭 9 其他 10 跨服
        switch (channel) {
            case ChatConst.ChatType.PERSON:
                content_type = 1;
                break;
            case ChatConst.ChatType.WORLD:
                content_type = 2;
                break;
            case ChatConst.ChatType.UNION:
                content_type = 3;
                break;
            case ChatConst.ChatType.GROUP:
                content_type = 6;
                break;
            case ChatConst.ChatType.NEAR:
                content_type = 7;
                break;
            case ChatConst.ChatType.YELL:
                content_type = 8;
                break;
            case ChatConst.ChatType.REMOTE:
                content_type = 10;
                break;
        }
        params.put("chat_type", content_type);
        params.put("contents", content);
        int now = TimeUtil.getNowOfSeconds();
        params.put("chat_at", now);
        params.put("money", role.getRecharge().getRechargedTotal() );

        params.put("sign", buildSigns(params, toChatParams, monitorConfig.getParams().get("appKey")));


        Map<String, String> header = new HashMap<>();
        header.put("content-type", "application/json;charset=utf8");
        String ret = HttpUtil.post(ChatMonitorURL,  params, header, "application/json;charset=utf8", 3000);
        if (ret != null) {
            try {
                JSONObject retJson = JSON.parseObject(ret);
                if (retJson.getInteger("ret") != 1) {
                    log.error("聊天监控记录失败：{}", ret);
                    //只是记录失败,所以需要返回
                }
            } catch (Exception e) {
                log.error("访问运营方聊天接口失败: {}", e.getMessage());
            }
        }

        content = callWanXinJuHeCheckService(role, content, monitorConfig, "chat");
        if (content == null){
            return null;
        }
        return content;
    }

    private String buildSigns(Map<String, Object> params, Map<String, Object> toChatParams, String appKey){
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        List<String> toChatKeys = new ArrayList<>(toChatParams.keySet());
        Collections.sort(toChatKeys);

        StringBuilder builder = new StringBuilder();
        for (String key : keys) {
            if (key.equals("chat")){
                for (String toChatKey: toChatKeys){
                    Object oValue = toChatParams.get(toChatKey);
                    if (oValue == null || StringUtil.isEmpty(oValue.toString())){
                        continue;
                    }
                    builder.append(UrlEncodeEx("chat["+toChatKey+"]"));
                    builder.append("=");
                    builder.append(UrlEncodeEx(String.valueOf(oValue)));
                    builder.append("&");
                }
                continue;
            }
            Object oValue = params.get(key);
            if (oValue == null || StringUtil.isEmpty(oValue.toString())){
                continue;
            }
            builder.append(key);
            builder.append("=");
            builder.append(UrlEncodeEx(String.valueOf(oValue)));
            builder.append("&");

        }
        String secKey =  "02451a79839a3c39a46c617f18a40c0f";
        if ("2".equals(appKey)){
            secKey =  "aa2ae3fa26b4d4687217c9b392a34e35";
        }
        String sign = Md5Util.md5(builder.toString().substring(0, builder.length() - 1)  + secKey);

        return sign;
    }


    @Override
    String playerNameCheckApi(Role role, String sNewName, ChatMonitorConfig monitorConfig){
        return callWanXinJuHeCheckService(role, sNewName, monitorConfig, "nick");
    }

    @Override
    String unionNameCheckApi(Role role, String sNewName, ChatMonitorConfig monitorConfig){
        return callWanXinJuHeCheckService(role, sNewName, monitorConfig, "union");
    }

    private  String callWanXinJuHeCheckService(Role role, String sText, ChatMonitorConfig monitorConfig, String context_type){
        Map<String, Object> params = new HashMap<>();
        params.put("product", "大侠传奇_6485b4da");
        params.put("user_id", "0");
        params.put("message_type", context_type);
        params.put("text", sText);

        //创建角色时是没有role的,这里需要区别处理
        if (role != null) {
            User user = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(role.getId()));
            if (user != null) {
                params.put("user_id", user.getAccount());
            }
            params.put("nickname", role.getName());
        }

        Map<String, String> header = new HashMap<>();
        header.put("content-type", "application/json;charset=utf8");
        String ret = HttpUtil.post("https://dispelspam.com/message",  params, header, "application/json;charset=utf8", 3000);
        if (ret != null) {
            try {
                JSONObject retJson = JSONObject.parseObject(ret);
                if ("ERROR".equals(retJson.getString("status"))){
                    log.error("玩心不止检验服务异常：{}", ret);
                    return sText;
                }

                double adValue = retJson.getDouble("ad");  //⼴告类垃圾信息.
                double external_snsValue = retJson.getDouble("external_sns");  //诱导玩家加入外部聊天⼯具的消息.
                double BBIWYValue = retJson.getDouble("BBIWY");  //涉政消息
                double pornValue = retJson.getDouble("porn"); //⾊情消息
                double checkSet = 0.5;
                if (adValue >= checkSet ||
                        external_snsValue >= checkSet ||
                        BBIWYValue >= checkSet ||
                        pornValue >= checkSet ) {
                    log.error("玩心不止敏感字检验失败：{}", ret);
                    return null;
                }

            } catch (Exception e) {
                log.error("访问运营方聊天接口失败: {}", e.getMessage());
            }
        }

        return sText;
    }


}
