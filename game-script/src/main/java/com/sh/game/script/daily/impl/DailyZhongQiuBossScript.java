package com.sh.game.script.daily.impl;

import com.sh.game.common.config.model.DailyScheduleConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.ChatConst;
import com.sh.game.common.constant.DailyConst;
import com.sh.game.scene.MapProxy;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.script.daily.abc.AbstractDailyTimedScript;
import com.sh.game.system.activity.ActivityManager;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.daily.entity.DailySchedule;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

/**
 * 中秋boss
 */
@Script
@Slf4j
public class DailyZhongQiuBossScript extends AbstractDailyTimedScript {

    @Override
    public int getType() {
        return DailyConst.DailyType.ZHONGQIU_BOSS;
    }

    @Override
    protected void onScheduleBegin(DailySchedule schedule) {
        ActivitySchedule activitySchedule = ActivityManager.getInstance().getAvailable(ActivityConst.ZHONG_QIU_BOSS);
        if (activitySchedule == null) {
            return;
        }
        DailyScheduleConfig conf = schedule.getConf();
        if (conf == null) {
            return;
        }
        //重新开启地图
        for (int mapConfigId : conf.getUseMap()) {
            MapProxyManager.getInstance().alloc(mapConfigId);
        }
        AnnounceManager.getInstance().post(ChatConst.AnnounceId.ZHONGQIU_BOSS_ANNOUNCE, 0L);
    }

    @Override
    protected void onScheduleEnd(DailySchedule schedule) {
        DailyScheduleConfig conf = schedule.getConf();
        if (conf == null) {
            return;
        }
        for (int mapConfigId : conf.getUseMap()) {
            MapProxy mapProxy = MapProxyManager.getInstance().getMap(mapConfigId);
            if (mapProxy != null) {
                mapProxy.kickPlayer();
                mapProxy.destroy();
            }
        }

    }
}
