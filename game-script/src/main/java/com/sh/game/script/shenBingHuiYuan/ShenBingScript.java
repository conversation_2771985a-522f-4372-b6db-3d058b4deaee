package com.sh.game.script.shenBingHuiYuan;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.monster.ResMonsterLocationMessage;
import com.sh.game.common.communication.msg.system.shenBingHuiYuan.ResLuckDropPanelMessage;
import com.sh.game.common.communication.msg.system.shenBingHuiYuan.ResShenBingReliveFreeTimeMessage;
import com.sh.game.common.communication.msg.system.shenBingHuiYuan.ResShenBingStatusMessage;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.*;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnMonsterDie;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.event.IEventOnRoleRechargedScript;
import com.sh.game.log.entity.RoleShenbingVipLog;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.MonsterProtos;
import com.sh.game.scene.MapProxy;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.role.RoleManager;
import com.sh.game.system.secondaryPassword.SecondaryPasswordManager;
import com.sh.game.system.shenBingHuiYuan.ShenBingManager;
import com.sh.game.system.shenBingHuiYuan.entity.RoleLuckDrop;
import com.sh.game.system.shenBingHuiYuan.entity.RoleShenbingVip;
import com.sh.game.system.shenBingHuiYuan.entity.ShenBingProxy;
import com.sh.game.system.shenBingHuiYuan.script.IShenBingScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.sh.game.common.constant.GameConst.GlobalId.SHENBING_DAILY_REWARD;

/**
 * 神兵会员特权
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-07-23
 **/
@Slf4j
@Script
public class ShenBingScript implements IShenBingScript,
        IEventOnRoleMidnightScript, IEventOnRoleLoginScript, IEventOnRoleRechargedScript, IEventOnMonsterDie {

    /**
     * 幸运货币大
     */
    private static final int LUCK_COIN_BIG = 72;

    /**
     * 幸运货币小
     */
    private static final int LUCK_COIN_SMALL = 71;

    @Override
    public RoleShenbingVip find(long rid) {
        RoleShenbingVip roleShenBingVip = DataCenter.get(RoleShenbingVip.class, rid);
        if (roleShenBingVip == null) {
            roleShenBingVip = new RoleShenbingVip();
            roleShenBingVip.setId(rid);
            DataCenter.insertData(roleShenBingVip, true);
        }

        return roleShenBingVip;
    }

    @Override
    public void reqShenBingStatus(Role role) {
        RoleShenbingVip roleShenBingVip = find(role.getId());
        //发送消息
        ResShenBingStatusMessage msg = new ResShenBingStatusMessage();
        msg.setCurShenBingCfgId(roleShenBingVip.getShengBingVipLevel());
        msg.setCurlockShenBingCfgId(roleShenBingVip.getCurlockShenBingCfgId());
        msg.setPurchasedGifts(roleShenBingVip.getPurchasedGifts());
        msg.setReceivedRewards(roleShenBingVip.getRequiredShenBingIds());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void unlockShenBing(Role role, int shenBingCfgId, boolean isSendMail) {
        RoleShenbingVip roleShenBingVip = find(role.getId());

        // 防止重复领取
        List<Integer> shenBingStates = roleShenBingVip.getRequiredShenBingIds();
        if (shenBingStates.contains(shenBingCfgId)) {
            log.info("玩家：{}的神兵特权{}，重复领取", role.getId(), shenBingCfgId);
            TipUtil.show(role, CommonTips.脚本_已经获取过了);
            return;
        }

        ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, shenBingCfgId);
        if (config == null) {
            return;
        }

        // 更新免费复活次数
        int curShenBingLevelCfgId = roleShenBingVip.getShengBingVipLevel();
        ShenBingHuiYuanConfig curConfig = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, curShenBingLevelCfgId);
        if (curConfig == null && curShenBingLevelCfgId != 0) {
            return;
        }
        if (curConfig != null && curConfig.getNext() != shenBingCfgId) {
            return;
        }
        int increaseFreeTime;
        if (curShenBingLevelCfgId == 0) {
            increaseFreeTime = config.getRevive();
        } else {
            increaseFreeTime = config.getRevive() - curConfig.getRevive();
        }
        roleShenBingVip.setReliveFreeTime(roleShenBingVip.getReliveFreeTime() + increaseFreeTime);

        //通知复活次数变更
        reqShenBingReliveFreeTime(role);

        // 添加神兵会员特权id
        roleShenBingVip.setShengBingVipLevel(shenBingCfgId);

        // 补充神兵经验
        boolean isFillExp = GlobalUtil.findSwitches(GameConst.GlobalId.SHEN_BING_EXP_FILL);
        if (isFillExp) {
            roleShenBingVip.setExtraExp(config.getExperience());
            // 通知客户端经验变化
            RoleManager.getInstance().reqShenBingExtraDailyExp(role);
        }

        DataCenter.updateData(roleShenBingVip);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.SHENG_BING_UNLOCK);

        //通知场景神兵会员等级变化
        ShenBingProxy proxy = ShenBingManager.getInstance().getProxy(role.getId());
        proxy.shenBingVipLevelUpdate(role.findCurShenBingLevel(), roleShenBingVip.getRequiredShenBingIds());

        // 开启下一阶段神兵特权
        ShenBingHuiYuanConfig nextConf = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, config.getNext());

        if (nextConf != null && ConditionUtil.validate(role, nextConf.getShowcondition())) {
            roleShenBingVip.setCurlockShenBingCfgId(nextConf.getId());
        }

        if (nextConf != null) {
            // 解锁侠侣皮肤
            AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, nextConf.getXialvFashion());
            if (appearanceConfig != null) {
                AppearanceManager.getInstance().onUnlock(role, appearanceConfig, null);
            }
        }

        DataCenter.updateData(roleShenBingVip);

        // 通知客户端
        reqShenBingStatus(role);

        String hoistType = "";

        RoleShenbingVipLog log = new RoleShenbingVipLog(role);
        log.setUserId(role.getUid());
        log.setRoleName(role.getName());
        log.setNowLevel(config.getId());
        log.setZhanYiDengJi(role.getZhanYiLevel() + "+" + role.getLevel());
        log.setTime(TimeUtil.getNowOfSeconds());
        log.setHoistType(hoistType);
        log.setAfterLevel(config.getNext());
        log.setAccount(role.getAccount());
        log.submit();

    }

    /**
     * 请求领取神兵解锁奖励
     *
     * @param role
     * @param shenBingCfgId
     */
    @Override
    public void reqShenBingReward(Role role, int shenBingCfgId) {
        RoleShenbingVip shenbingVip = find(role.getRoleId());
        ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, shenBingCfgId);
        if (config == null) {
            return;
        }
        // 防止重复领取
        if (shenbingVip.getRequiredShenBingIds().contains(shenBingCfgId)) {
            return;
        }
        // 领取神兵特权奖励
        List<int[]> rewards = config.getRewards();
        BackpackStash stash = new BackpackStash(role);
        stash.increase(rewards);
        if (!stash.commit(role, LogAction.SHENBING_REWARD, false)) {
            TipUtil.show(role, CommonTips.脚本_背包空间不足发邮件);
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.SHEN_BING_REWARD,
                    EmailConst.toMailAttach(rewards, LogAction.SHENBING_REWARD));
        }
        // 神兵获取公告
        AnnounceManager.getInstance().post(AnnounceConst.TYPE.SEHNBING, 0, role, config.getName());
        shenbingVip.getRequiredShenBingIds().add(shenBingCfgId);
        DataCenter.updateData(shenbingVip);

        //通知场景神兵会员等级变化
        ShenBingProxy proxy = ShenBingManager.getInstance().getProxy(role.getId());
        proxy.shenBingVipLevelUpdate(role.findCurShenBingLevel(), shenbingVip.getRequiredShenBingIds());

        reqShenBingStatus(role);
    }

    @Override
    public void reqShenBingDailyReward(Role role) {
        RoleShenbingVip roleShenbingVip = find(role.getId());
        List<Integer> requiredShenBingIds = roleShenbingVip.getRequiredShenBingIds();
        if (requiredShenBingIds.isEmpty()) {
            TipUtil.show(role, CommonTips.脚本_神兵等级不足);
            return;
        }
        Map<Integer, Integer> dailyRewardCount = roleShenbingVip.getDailyRewardCount();

        // 验证当日领取次数和上限
        GlobalConfig globalConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, SHENBING_DAILY_REWARD);
        if (globalConfig == null) {
            return;
        }
        int limitNum = Integer.parseInt(globalConfig.getValue());

        // 累计奖励
        List<int[]> dailyTotalReward = new ArrayList<>();

        for (Integer requiredShenBingId : requiredShenBingIds) {
            int useCount = dailyRewardCount.getOrDefault(requiredShenBingId, 0);
            if (useCount >= limitNum) {
                continue;
            }

            ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, requiredShenBingId);
            if (config == null) {
                return;
            }
            // 领取奖励
            List<int[]> dailyReward = config.getDayreward();
            dailyTotalReward.addAll(dailyReward);
            dailyRewardCount.computeIfPresent(requiredShenBingId, (k, v) -> v + 1);
            dailyRewardCount.putIfAbsent(requiredShenBingId, 1);
            log.info("玩家：{}，领取每日神兵：{}的会员工资，次数：{}", role.getId(), requiredShenBingId, dailyRewardCount.get(requiredShenBingId));
        }

        if (dailyTotalReward.isEmpty()) {
            TipUtil.show(role, CommonTips.脚本_已经获取过了);
            return;
        }
        DataCenter.updateData(roleShenbingVip);

        BackpackStash stash = new BackpackStash(role);
        stash.increase(dailyTotalReward);
        if (!stash.commit(role, LogAction.SHENBING_DAILY_REWARD, false)) {
            TipUtil.show(role, CommonTips.脚本_背包空间不足发邮件);
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(dailyTotalReward, LogAction.SHENBING_DAILY_REWARD));
        }

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.SHEN_BING_DAILY_REWARD,
                dailyRewardCount.get(requiredShenBingIds.get(0)));
    }

    /**
     * 请求购买神兵
     *
     * @param role
     * @param buyShenBingCfgId
     */
    @Override
    public void reqBuyShenBing(Role role, int buyShenBingCfgId) {
        RoleShenbingVip roleShenbingVip = find(role.getId());
        // 验证购买条件
        int curShenBingLevel = roleShenbingVip.getShengBingVipLevel();
        int nextShenBingLevel;
        switch (curShenBingLevel) {
            case 0:
                ShenBingHuiYuanConfig shenBingHuiYuanConfig = ConfigDataManager.getInstance()
                        .getList(ShenBingHuiYuanConfig.class).stream().findFirst().orElse(null);
                if (shenBingHuiYuanConfig == null) {
                    return;
                }
                nextShenBingLevel = shenBingHuiYuanConfig.getId();
                break;
            default:
                ShenBingHuiYuanConfig curConfig = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, curShenBingLevel);
                if (curConfig == null) {
                    return;
                }
                nextShenBingLevel = curConfig.getNext();
                break;
        }

        // 已解锁所有神兵
        List<Integer> cfgList = ConfigDataManager.getInstance().getList(ShenBingHuiYuanConfig.class).stream()
                .map(ShenBingHuiYuanConfig::getId).collect(Collectors.toList());
        if (roleShenbingVip.getRequiredShenBingIds().containsAll(cfgList)) {
            log.info("玩家:{},已解锁所有神兵", role.getId());
            return;
        }

        // 验证购买条件，仅能购买下一等级神兵
        if (nextShenBingLevel != buyShenBingCfgId) {
            log.info("玩家:{},当前神兵会员等级:{}，只能购买下一级:{}", role.getId(), curShenBingLevel, nextShenBingLevel);
            return;
        }
        if (SecondaryPasswordManager.getInstance().secondPasswordCheck(role)) {
            TipUtil.show(role.getId(), CommonTips.脚本_二级密码已锁定);
            return;
        }

        // 当前购买的神兵会员
        ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, buyShenBingCfgId);
        if (config == null || !ConditionUtil.validate(role, config.getShowcondition())) {
            return;
        }

        // 充值额度是否达标
        if (role.getRecharge().getRechargedTotal() < config.getRecharge()) {
            log.info("玩家:{},充值金额没有达到神兵解锁要求", role.getId());
            return;
        }

        // 领奖
        unlockShenBing(role, buyShenBingCfgId, true);

    }

    @Override
    public void completeShenBingTask(Role role) {
        // TODO 神兵令暂时去除 用于实现直升神兵等级
    }

    @Override
    public void onRoleMidnight(Role role) {
        RoleShenbingVip roleShenbingVip = find(role.getRoleId());
        // 重置每日可免费复活次数
        ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, roleShenbingVip.getShengBingVipLevel());
        if (config != null) {
            roleShenbingVip.setReliveFreeTime(config.getRevive());
        } else {
            roleShenbingVip.setReliveFreeTime(0);
        }
        // 清除神兵每日福利奖励领取次数
        roleShenbingVip.getDailyRewardCount().clear();
        DataCenter.updateData(roleShenbingVip);
        log.info("玩家：{}的每日神兵会员工资领取状态重置，当前状态；{}", role.getId(), roleShenbingVip.getDailyRewardCount());

        // 通知更新免费复活次数
        reqShenBingReliveFreeTime(role);

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.OPEN_SERVER_DAY);
    }


    /**
     * 神兵经验处理
     *
     * @param role       角色
     * @param sumExp     累加经验
     * @param monsterCid 怪物id
     * @param pointX     怪物死亡点坐标x
     * @param pointY     怪物死亡点坐标y
     */
    @Override
    public void shenBingExpDeal(Role role, long sumExp, int monsterCid, int pointX, int pointY) {
        int shenBingVipLevel = role.findCurShenBingLevel();
        if (shenBingVipLevel == 0) {
            return;
        }
        ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, shenBingVipLevel);
        if (config == null) {
            log.info("玩家：{}，神兵等级：{}，不存在。", role.getRoleId(), shenBingVipLevel);
            return;
        }

        RoleShenbingVip roleShenBingVip = find(role.getRoleId());
        // 今天已累计的经验
        long addDailyExtraExp = roleShenBingVip.getExtraExp();

        long dailyExpLimit = config.getExperience();
        if (addDailyExtraExp >= dailyExpLimit) {
            // 到达上限
            return;
        }

        // 经验加成倍率
        float pickExperience = config.getPickexperience();

        // 精度计算并向上取整
        BigDecimal sumExpDecimal = BigDecimal.valueOf(sumExp);
        BigDecimal expMul = BigDecimal.valueOf(pickExperience);
        long addExp = sumExpDecimal.multiply(expMul).setScale(0, BigDecimal.ROUND_UP).longValue();

        // 经验限制
        if ((addDailyExtraExp + addExp) >= dailyExpLimit) {
            addExp = dailyExpLimit - addDailyExtraExp;
        }
        // 更新每日神兵经验
        roleShenBingVip.countExtraExp(addExp);
        DataCenter.updateData(roleShenBingVip);

        // 增加经验发送怪物死亡位置
        ResMonsterLocationMessage msg = new ResMonsterLocationMessage();
        MonsterProtos.ResMonsterLocation.Builder monsterLocation = MonsterProtos.ResMonsterLocation.newBuilder();
        monsterLocation.setLocationX(pointX);
        monsterLocation.setLocationY(pointY);
        monsterLocation.setMonsterCid(monsterCid);
        monsterLocation.setExp(addExp);
        msg.setProto(monsterLocation.build());
        MessageUtil.sendMsg(msg, role.getRoleId());


        // 通知客户端
        RoleManager.getInstance().reqShenBingExtraDailyExp(role);
    }

    /**
     * 通知客户端复活次数
     *
     * @param role
     */
    @Override
    public void reqShenBingReliveFreeTime(Role role) {
        RoleShenbingVip shenBingVip = find(role.getRoleId());

        // 通知场景 更新复活次数
        ShenBingProxy proxy = ShenBingManager.getInstance().getProxy(role.getId());
        proxy.shenBingFreeReliveUpdate(shenBingVip.getReliveFreeTime());

        // 通知客户端
        ResShenBingReliveFreeTimeMessage msg = new ResShenBingReliveFreeTimeMessage();
        msg.setFreeTime(shenBingVip.getReliveFreeTime());
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 购买神兵礼包
     *
     * @param role
     * @param ShenBingCfgId
     */
    @Override
    public void reqBuyShenBingGift(Role role, int ShenBingCfgId) {
        //当前神兵等级 礼包
        RoleShenbingVip shenBingVip = find(role.getRoleId());
        ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, ShenBingCfgId);
        if (config == null) {
            return;
        }

        //不可重复购买
        if (shenBingVip.getPurchasedGifts().contains(ShenBingCfgId)) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        // 礼包消耗
        stash.decrease(config.getCost());
        if (!stash.commit(role, LogAction.SHENBING_GIFT_REWARD)) {
            return;
        }

        //礼包内容
        List<int[]> giftList = config.getGift();
        stash.increase(giftList);
        if (!stash.commit(role, LogAction.SHENBING_GIFT_REWARD, false)) {
            TipUtil.show(role, CommonTips.脚本_背包空间不足发邮件);
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(giftList, LogAction.SHENBING_REWARD));
        }

        shenBingVip.getPurchasedGifts().add(ShenBingCfgId);
        DataCenter.updateData(shenBingVip);
        //通知客户端
        reqShenBingStatus(role);
    }

    /**
     * 处理对应神兵任务配置表变更
     *
     * @param role
     */
    @Override
    public void onRoleLogin(Role role) {

        // TODO 临时触发
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.OPEN_SERVER_DAY);

        RoleShenbingVip roleShenbingVip = find(role.getId());

        //通知客户端 复活次数
        reqShenBingReliveFreeTime(role);

        if (roleShenbingVip.getShengBingVipLevel() != 0) {
            return;
        }

        ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getList(ShenBingHuiYuanConfig.class).stream().findFirst().orElse(null);
        if (config == null) {
            return;
        }

        // 符合第一挡充值条件自动解锁神兵vip等级
        if (config.getRecharge() <= role.getRechargeTotal()) {
            unlockShenBing(role, config.getId(), false);
        }
        DataCenter.updateData(roleShenbingVip);
        //通知客户端 复活次数
        reqShenBingReliveFreeTime(role);
    }

    @Override
    public void onMonsterDie(int mapID, int monsterID, int diePointX, int diePointY, long killerID, long ownerID, Set<Long> allThreat, Set<Long> threatAllSet, Set<Long> hurtMap) {
        Role role = DataCenter.get(Role.class, killerID);
        if (role == null) {
            return;
        }

        RoleShenbingVip roleShenbingVip = find(killerID);
        RoleLuckDrop roleLuckDrop = roleShenbingVip.getRoleLuckDrop();
        ShenBingHuiYuanConfig config = ConfigDataManager.getInstance().getById(ShenBingHuiYuanConfig.class, role.findCurShenBingLevel());
        if (config == null) {
            return;
        }

        MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterID);
        if (monsterConfig == null) {
            return;
        }
        int monsterLevel = monsterConfig.getLevel();

        Backpack backpack = DataCenter.get(Backpack.class, role.getId());
        long bigCoinCount = backpack.fetchCountLByCfgId(LUCK_COIN_BIG);
        long smallCoinCount = backpack.fetchCountLByCfgId(LUCK_COIN_SMALL);

        BackpackStash stash = new BackpackStash(role);
        // 向地图中掉落道具
        MapProxy mapProxy = MapProxyManager.getInstance().getMap(role.getMapId());
        if (mapProxy == null) {
            return;
        }

        // 掉落类型
        int type = 0;

        ResLuckDropPanelMessage msg = new ResLuckDropPanelMessage();

        int smallMonsterLvLimit = GlobalUtil.getGlobalInt(GameConst.GlobalId.LUCK_DROP_SMALL_LEVEL);
        //幸运掉落小
        if (monsterLevel >= smallMonsterLvLimit && smallCoinCount > 0) {
            roleLuckDrop.setSmallKillCount(roleLuckDrop.getSmallKillCount() + 1);
            if (roleLuckDrop.getSmallKillCount() >= GlobalUtil.getGlobalInt(GameConst.GlobalId.LUCK_DROP_SMALL_KILL)) {
                // 消耗幸运币
                stash.decrease(LUCK_COIN_SMALL, 1);
                stash.commit(role, LogAction.LUCK_DROP_SMALL);

                // 向场景中掉落
                int box = GlobalUtil.getGlobalInt(GameConst.GlobalId.LUCK_DROP_SMALL_REWARD);
                List<Item> items = BoxUtil.openBox(role, box);
                mapProxy.dropItem(diePointX, diePointY, items, 0L, killerID);
                roleLuckDrop.setSmallKillCount(0);
                type = 2;
                List<AbcProtos.CommonItemBean> beanList = items.stream().map(ItemUtil::packCommonItemBean).collect(Collectors.toList());
                //todo 协议更换
//                msg.getItems().addAll(beanList);
            }
        }

        int bigMonsterLvLimit = GlobalUtil.getGlobalInt(GameConst.GlobalId.LUCK_DROP_BIG_LEVEL);
        // 幸运掉落大
        if (monsterLevel >= bigMonsterLvLimit && bigCoinCount > 0) {
            roleLuckDrop.setBigKillCount(roleLuckDrop.getBigKillCount() + 1);
            if (roleLuckDrop.getBigKillCount() >= GlobalUtil.getGlobalInt(GameConst.GlobalId.LUCK_DROP_BIG_KILL)) {
                // 消耗幸运币
                stash.decrease(LUCK_COIN_BIG, 1);
                stash.commit(role, LogAction.LUCK_DROP_BIG);

                // 向场景中掉落
                int box = GlobalUtil.getGlobalInt(GameConst.GlobalId.LUCK_DROP_BIG_REWARD);
                List<Item> items = BoxUtil.openBox(role, box);
                mapProxy.dropItem(diePointX, diePointY, items, 0L, killerID);
                roleLuckDrop.setBigKillCount(0);
                type = 1;

                List<AbcProtos.CommonItemBean> beanList = items.stream().map(ItemUtil::packCommonItemBean).collect(Collectors.toList());
                //todo 协议更换
//                msg.getItems().addAll(beanList);
            }
        }
        DataCenter.updateData(roleShenbingVip);

        if (type != 0) {
            msg.setType(type);
            MessageUtil.sendMsg(msg, killerID);
        }

    }

    /**
     * 充值自动解锁神兵
     *
     * @param role
     * @param rechargeConfig
     */
    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
//        RoleShenbingVip shenBingVip = find(role.getRoleId());
//        int rechargeTotal = role.getRechargeTotal();
//        // 可解锁神兵
//        List<Integer> needToUnlock = ConfigDataManager.getInstance().getList(ShenBingHuiYuanConfig.class).stream()
//                .filter(e -> e.getRecharge() <= rechargeTotal && !shenBingVip.getRequiredShenBingIds().contains(e.getId()))
//                .map(ShenBingHuiYuanConfig::getId).collect(Collectors.toList());
//        for (Integer integer : needToUnlock) {
//            reqBuyShenBing(role, integer);
//        }
    }
}
