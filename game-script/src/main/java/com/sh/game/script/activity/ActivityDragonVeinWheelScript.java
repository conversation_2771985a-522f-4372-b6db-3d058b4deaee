package com.sh.game.script.activity;

import com.sh.game.common.config.model.ActivityWheelRewardConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.script.activity.abc.AbstractWheelScript;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.script.annotation.Script;

/**
 * 龙脉转盘
 *
 * <AUTHOR>
 * @date 2022/07/25 15:25
 */
@Script
public class ActivityDragonVeinWheelScript extends AbstractWheelScript {

    @Override
    public int getType() {
        return ActivityConst.DRAGON_VEIN_WHEEL;
    }

    /**
     * 发送公告
     *
     * @param role              角色
     * @param wheelRewardConfig 转盘奖励配置
     * @param item              道具
     */
    @Override
    protected void sendAnnounce(Role role, ActivityWheelRewardConfig wheelRewardConfig, Item item) {
        AnnounceManager.getInstance().post(wheelRewardConfig.getAnnounce(), 0L, role, item);
    }

}
