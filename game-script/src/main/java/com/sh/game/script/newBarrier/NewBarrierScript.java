package com.sh.game.script.newBarrier;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.newbarrier.ResNewBarrierBattleMessage;
import com.sh.game.common.config.model.WestBoundConfig;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleNewBarrier;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnMgBattleEnd;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.protos.NewBarrierProtos;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.newbarrier.script.INewBarrierScript;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/19
 */
@Slf4j
@Script
public class NewBarrierScript implements INewBarrierScript, IEventOnMgBattleEnd {
    @Override
    public void reqBarrierBattle(Role role) {
//        RoleNewBarrier roleBarrier = role.findNewBarrier();
//        int curBarrierId = roleBarrier.getCurBarrierId();
//        WestBoundConfig config = ConfigDataManager.getInstance().getById(WestBoundConfig.class, curBarrierId);
//        if (config == null) {
//            log.warn("主线推图-已达到最大关卡,roleId:{},roleName:{},curBarrierId:{}", role.getId(), role.getName(), curBarrierId);
//            return;
//        }
//        int process = roleBarrier.getProcess();
//        if (process >= config.getMonsterId().size() - 1) {
//            log.warn("主线推图-进度超过当前配置的怪物数量,roleId:{},roleName:{},curBarrierId:{}", role.getId(), role.getName(), curBarrierId);
//            return;
//        }
//        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
//        if (player == null) {
//            log.error("主线推图-找不到玩家代理对象,roleId:{},roleName:{}", role.getId(), role.getName());
//            return;
//        }
//
//        // 进行战斗
//        int monsterId = config.getMonsterId().get(process + 1);
//        BattleManager.getInstance().reqTurnBasedBattle(role, monsterId, TurnBasedConst.BattleType.NEW_BARRIER);
//        log.info("主线推图-玩家请求挑战boss,roleId:{},roleName:{},当前关卡:{},怪物id:{}", role.getId(), role.getName(), curBarrierId, monsterId);
    }

    @Override
    public void reqBarrierBattleInfo(Role role) {
        sendBarrierInfo(role.findNewBarrier());
    }

    @Override
    public void onBattleEnd(Role role, int battleType, long targetId, int targetCfgId, boolean success, long totalHurt) {
//        if (battleType != TurnBasedConst.BattleType.NEW_BARRIER) {
//            return;
//        }
//
//        RoleNewBarrier roleBarrier = role.findNewBarrier();
//        int curBarrierId = roleBarrier.getCurBarrierId();
//        WestBoundConfig curBarrierConfig = ConfigDataManager.getInstance().getById(WestBoundConfig.class, curBarrierId);
//        int nextBarrierId = curBarrierConfig.getNextId();
//        WestBoundConfig nextBarrierConfig = ConfigDataManager.getInstance().getById(WestBoundConfig.class, nextBarrierId);
//        if (nextBarrierConfig == null) {
//            log.warn("主线推图-已达到最大关卡,roleId:{},roleName:{},curBarrierId:{}", role.getId(), role.getName(), curBarrierId);
//            return;
//        }
//        if (targetCfgId != curBarrierConfig.getMonsterId().get(roleBarrier.getProcess() + 1)) {
//            log.warn("主线推图-怪物配置id和关卡不符,roleId:{},roleName:{},curBarrierId:{},nextBarrierId:{}", role.getId(), role.getName(), curBarrierId, nextBarrierId);
//            return;
//        }
//        if (!success) { //战斗失败，重置进度
////            roleBarrier.setProcess(-1);
////            DataCenter.updateData(roleBarrier);
//            sendBarrierInfo(roleBarrier);
//            log.info("主线推图-战斗失败,roleId:{},roleName:{},curBarrierId:{},nextBarrierId:{}", role.getId(), role.getName(), curBarrierId, nextBarrierId);
//            return;
//        }
//
//        if (roleBarrier.getProcess() + 1 == curBarrierConfig.getMonsterId().size() - 1) { //进度完成
//            // 更新关卡
//            roleBarrier.setCurBarrierId(nextBarrierId);
//            roleBarrier.setProcess(-1);
//            DataCenter.updateData(roleBarrier);
//
//            // 发放奖励
//            List<int[]> reward = curBarrierConfig.getReward();
//            BackpackStash stash = new BackpackStash(role);
//            stash.increase(reward);
//            stash.commitOrToMail(role, LogAction.NEW_BARRIER_REWARD, false);
//            // 给前端发送奖励消息
//            RewardInfoManager.getInstance().resRewardInfo(role, battleType, reward);
//            // 更新任务
//            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.NEW_BARRIER_ID);
//            log.info("主线推图-战斗成功,roleId:{},roleName:{},curBarrierId:{},nextBarrierId:{}", role.getId(), role.getName(), curBarrierId, nextBarrierId);
//        } else {
//            roleBarrier.setProcess(roleBarrier.getProcess() + 1);
//            DataCenter.updateData(roleBarrier);
//            log.info("主线推图-战斗成功,roleId:{},roleName:{},curBarrierId:{},curProcess:{}", role.getId(), role.getName(), curBarrierId, roleBarrier.getProcess());
//        }
//        sendBarrierInfo(roleBarrier);
    }

    private void sendBarrierInfo(RoleNewBarrier roleBarrier) {
        ResNewBarrierBattleMessage msg = new ResNewBarrierBattleMessage();
        msg.setProto(NewBarrierProtos.ResNewBarrierBattleMessage.newBuilder()
                .setBarrierId(roleBarrier.getCurBarrierId())
                .setProcess(roleBarrier.getProcess())
                .build());

        MessageUtil.sendMsg(msg, roleBarrier.getId());
    }
}
