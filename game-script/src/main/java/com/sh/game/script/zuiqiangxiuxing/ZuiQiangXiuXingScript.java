package com.sh.game.script.zuiqiangxiuxing;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.ThreeTuple;
import com.sh.game.common.communication.msg.system.zuiqiangxiuxing.ResHisRankMessage;
import com.sh.game.common.communication.msg.system.zuiqiangxiuxing.ResMyInfoMessage;
import com.sh.game.common.communication.msg.system.zuiqiangxiuxing.ResRankMessage;
import com.sh.game.common.config.cache.PracticeChoseConfigCache;
import com.sh.game.common.config.model.PracticeActionConfig;
import com.sh.game.common.config.model.PracticeChoseConfig;
import com.sh.game.common.config.model.PracticeScoreConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.ProtoUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.ZuiQiangXiuXingProtos;
import com.sh.game.script.activity.abc.AbstractRankingActivity;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.zuiqiangxiuxing.entity.RoleZuiQiangXiuXing;
import com.sh.game.system.zuiqiangxiuxing.entity.ZuiQiangXiuXingHistoryData;
import com.sh.game.system.zuiqiangxiuxing.entity.ZuiQiangXiuXingHistoryRoleData;
import com.sh.game.system.zuiqiangxiuxing.entity.ZuiQiangXiuXingSummaryData;
import com.sh.game.system.zuiqiangxiuxing.script.IZuiQiangXiuXingScript;
import com.sh.game.system.zuiqiangxiuxing.script.IZuiQiangXiuXingStageScript;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Script
@Slf4j
public class ZuiQiangXiuXingScript extends AbstractRankingActivity implements IZuiQiangXiuXingScript {
    @Override
    public int getType() {
        return ActivityConst.ZUI_QIANG_XIU_XING;
    }

    @Override
    protected int getGoalType() {
        return RankConst.RankScoreType.ZUI_QIANG_XIU_XING;
    }

    /**
     * 更新阶段
     *
     * @param role  role
     * @param type  1、砍树 2、雷达消耗 3、神魔召唤次数 4、神识抽取
     * @param param type=2，param需要传两个参数 消耗数量和道具类型
     */
    @Override
    public void updateStageProcess(Role role, int type, int... param) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        PracticeActionConfig config = ConfigDataManager.getInstance().getById(PracticeActionConfig.class, type);
        if (config == null) {
            log.warn("最强修行者-更新积分#玩家:{}-{},积分类型:{},参数:{},未找到该积分类型对应的配置", role.getId(), role.getName(), type, param);
            return;
        }
        ZuiQiangXiuXingSummaryData summaryData = DataCenter.getZuiQiangXiuXingSummaryData();
        PracticeScoreConfig scoreConfig = ConfigDataManager.getInstance().getById(PracticeScoreConfig.class, summaryData.getStage());
        if (scoreConfig == null) {
            log.warn("最强修行者-更新积分#玩家:{}-{},积分类型:{},参数:{},当前阶段未配置:{}", role.getId(), role.getName(), type, param, summaryData.getStage());
            return;
        }
        if (!scoreConfig.getActionType().contains(type)) {
            log.warn("最强修行者-更新积分#玩家:{}-{},积分类型:{},参数:{},当前阶段:{}不包含该积分类型", role.getId(), role.getName(), type, param, summaryData.getStage());
            return;
        }
        int scoreMax = scoreConfig.getScoreMax();
        RoleZuiQiangXiuXing roleXX = role.findZuiQiangXiuXing();
        if (scoreMax > 0 && roleXX.getStageScore() >= scoreMax) {
            log.warn("最强修行者-更新积分#玩家:{}-{},积分类型:{},参数:{},当前阶段:{}，当前积分:{},积分已达最大积分，无法再增加积分", role.getId(), role.getName(), type, param, summaryData.getStage(), roleXX.getStageScore());
            return;
        }

        int count = param[0];
        if (type == ZuiQiangXiuXingConst.StateType.ITEM_COST) {//该类型，增加的进度使用第二个参数，第一个参数用于区分道具类型
            if (param.length < 2) {
                log.warn("最强修行者-更新积分#玩家:{}-{},积分类型:{},参数:{},当前阶段:{},该积分类型传入参数不符合规范", role.getId(), role.getName(), type, param, summaryData.getStage());
                return;
            }
            if (!config.getParam().contains(param[1])) {
                log.warn("最强修行者-更新积分#玩家:{}-{},积分类型:{},参数:{},当前阶段:{},该积分类型传入参数未配置，不能增加积分", role.getId(), role.getName(), type, param, summaryData.getStage());
                return;
            }
        }

        int processTmp = roleXX.getStageProcess().getOrDefault(type, 0) + count;
        int multi = processTmp / config.getCount(); //倍数
        int process = processTmp % config.getCount(); //进度
        if (multi > 0) { //进度可加积分
            int score = config.getScore();
            //折扣
            int discount = scoreConfig.getScoreDiscount();
            if (discount > 0) {
                score = score * discount / 100;
            }
            int realScore = score * multi;
            //阶段最高积分限制
            if (scoreMax > 0 && roleXX.getStageScore() + realScore >= scoreMax) {
                score = scoreMax - roleXX.getStageScore();
            }
            roleXX.getStageProcess().put(type, process);
            roleXX.setStageScore(roleXX.getStageScore() + realScore);
            roleXX.setScore(roleXX.getScore() + realScore);

            //更新排行榜
            ScriptEngine.invoke1t1(IZuiQiangXiuXingStageScript.class, script -> script.updateStageScoreRank(role,
                    roleXX.getStageScore()));//更新阶段积分
            updateScore(role, getGoalType(), BigInteger.valueOf(roleXX.getScore())); //更新总积分

            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ZUI_QIANG_XIU_XING_SCORE, score);
            log.info("最强修行者-更新积分#玩家:{}->{}，更新积分:{},当前阶段:{},当前阶段进度：{}, 当前阶段积分:{},当前总积分:{}", role.getId(), role.getName(), score, summaryData.getStage(), roleXX.getStageProcess(), roleXX.getStageScore(), roleXX.getScore());
            sendRankMessage(role, 2);
            sendRankMessage(role, 1);
        } else {
            roleXX.getStageProcess().put(type, processTmp);
            log.info("最强修行者-更新积分#玩家:{}->{}，更新阶段进度,当前阶段:{},当前阶段进度：{}, 当前阶段积分:{},当前总积分:{}", role.getId(), role.getName(), summaryData.getStage(), roleXX.getStageProcess(), roleXX.getStageScore(), roleXX.getScore());
        }
        DataCenter.updateData(roleXX);
        sendMsg(role);
    }

    /**
     * 自选奖励，奖励索引
     *
     * @param role  role
     * @param index 索引
     */
    @Override
    public void selectionChest(Role role, int index) {
        if (index < 0) {
            log.warn("最强修行者-自选奖励#玩家:{}-{},所选奖励索引:{},传入参数有误", role.getId(), role.getName(), index);
            return;
        }
        PracticeChoseConfigCache cache = ConfigCacheManager.getInstance().getCache(PracticeChoseConfigCache.class);
        ZuiQiangXiuXingSummaryData data = DataCenter.getZuiQiangXiuXingSummaryData();
        Set<PracticeChoseConfig> configs = cache.getCfgSetBySeason(data.getSeason());
        if (CollectionUtils.isEmpty(configs)) {
            log.warn("最强修行者-自选奖励#玩家:{}-{},当前赛季:{},所选奖励索引:{},当前赛季未配置自选奖励", role.getId(), role.getName(), data.getSeason(), index);
            return;
        }
        PracticeChoseConfig config = configs.stream().findFirst().orElse(null);
        if (config == null) {
            log.warn("最强修行者-自选奖励#玩家:{}-{},当前赛季:{},所选奖励索引:{},未找到赛季自选奖励", role.getId(), role.getName(), data.getSeason(), index);
            return;
        }
        if (index >= config.getReward().size()) {
            log.warn("最强修行者-自选奖励#玩家:{}-{},当前赛季:{},所选奖励索引:{},当前配置奖励数量:{},索引越界", role.getId(), role.getName(), data.getSeason(), index, config.getReward().size());
            return;
        }
        RoleZuiQiangXiuXing roleXX = role.findZuiQiangXiuXing();
        roleXX.setSelectionChest(index);
        DataCenter.updateData(roleXX);
        log.info("最强修行者-自选奖励#玩家:{}->{}，当前赛季:{},玩家选择奖励索引:{},奖励信息:{}", role.getId(), role.getName(), data.getSeason(), index, config.getReward().get(index));

        sendMsg(role);
    }

    /**
     * 排行信息
     *
     * @param role role
     * @param type 1:总积分排行 2：阶段积分排行
     */
    @Override
    public void sendRankMessage(Role role, int type) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        ZuiQiangXiuXingSummaryData data = DataCenter.getZuiQiangXiuXingSummaryData();
        ThreeTuple<BigInteger, Integer, List<AbcProtos.CommonRankingBean>> query;
        if (type == 2) {
            query = queryRanking(role, 3, schedule);
        } else {
            query = queryRanking(role, 1, schedule);
        }

        ResRankMessage msg = new ResRankMessage();
        ZuiQiangXiuXingProtos.ResRankMessage proto = ZuiQiangXiuXingProtos.ResRankMessage.newBuilder()
                .setIndex(data.getSeason())
                .setType(type)
                .setMyScore(query.first.longValue())
                .setMyRank(query.second)
                .addAllRankings(query.third)
                .build();
        msg.setProto(proto);
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 查询历史赛季排名信息
     *
     * @param role       role
     * @param startIndex 查询起始赛季
     * @param endIndex   查询结束赛季
     */
    @Override
    public void sendHistoryMsg(Role role, int startIndex, int endIndex) {
        if (startIndex < 0 || endIndex < 0) {
            return;
        }
//        ActivitySchedule schedule = getAvailableSchedule(role);
//        if (schedule == null) {
//            return;
//        }
        int realStartIndex = startIndex - 1;
        int realEndIndex = endIndex;
        if (startIndex > endIndex) {
            realStartIndex = endIndex - 1;
            realEndIndex = startIndex;
        }
        ResHisRankMessage msg = new ResHisRankMessage();
        ZuiQiangXiuXingProtos.ResHisRankMessage.Builder builder = ZuiQiangXiuXingProtos.ResHisRankMessage.newBuilder();
        ZuiQiangXiuXingSummaryData data = DataCenter.getZuiQiangXiuXingSummaryData();
        Map<Integer, ZuiQiangXiuXingHistoryData> historyRankMap = data.getHistoryRankMap();
        if (MapUtils.isNotEmpty(historyRankMap)) {
            realEndIndex = Math.min(realEndIndex, historyRankMap.size());
            for (int i = realStartIndex; i < realEndIndex; i++) {
                ZuiQiangXiuXingHistoryData historyData = historyRankMap.getOrDefault(i, null);
                if (historyData == null) {
                    continue;
                }
                ZuiQiangXiuXingProtos.HistoryRankInfo.Builder rankBuilder = ZuiQiangXiuXingProtos
                        .HistoryRankInfo.newBuilder();
                rankBuilder.setIndex(i);
                rankBuilder.setStartTime(historyData.getStartTime());
                rankBuilder.setEndTime(historyData.getEndTime());

                for (ZuiQiangXiuXingHistoryRoleData roleData : historyData.getRankData()) {
                    AbcProtos.CommonRankingBean bean = toRankingBean(roleData);
                    if (bean == null) {
                        continue;
                    }
                    rankBuilder.getRankInfoList().add(bean);
                }

                builder.getRankInfoList().add(rankBuilder.build());
            }
        }
        msg.setProto(builder.build());
        MessageUtil.sendMsg(msg, role);
    }

    private AbcProtos.CommonRankingBean toRankingBean(ZuiQiangXiuXingHistoryRoleData roleData) {
        RoleSummary summary = SummaryManager.getInstance().getSummary(roleData.getRid());
        if (summary == null) {
            return null;
        }
        AbcProtos.CommonRankingBean.Builder beanBuilder = AbcProtos.CommonRankingBean.newBuilder();
        beanBuilder.setRanking(roleData.getRank());
        beanBuilder.setUid(summary.getId());
        beanBuilder.setName(roleData.getName());
        beanBuilder.setScore(roleData.getScore() + "");
        if (MapUtils.isNotEmpty(roleData.getFashions())) {
            for (Map.Entry<Integer, Integer> entry : roleData.getFashions().entrySet()) {
                AbcProtos.CommonSlotBean slotBean = AbcProtos.CommonSlotBean.newBuilder()
                        .setIndex(entry.getKey())
                        .setId(entry.getValue())
                        .build();
                beanBuilder.addFashions(slotBean);
            }
        }
        return beanBuilder.build();
    }

    public void sendMsg(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        ZuiQiangXiuXingSummaryData data = DataCenter.getZuiQiangXiuXingSummaryData();
        RoleZuiQiangXiuXing roleXX = role.findZuiQiangXiuXing();
        ResMyInfoMessage msg = new ResMyInfoMessage();
        ZuiQiangXiuXingProtos.ResMyInfoMessage proto = ZuiQiangXiuXingProtos.ResMyInfoMessage.newBuilder()
                .setSeason(data.getSeason())
                .setStage(data.getStage())
                .setStageStartTime(data.getStageStartTime())
                .setSelectionChest(roleXX.getSelectionChest())
                .setMyStageScore(roleXX.getStageScore())
                .addAllMyStageProcess(ProtoUtil.coverCommonKeyValueList(roleXX.getStageProcess()))
                .setMyScore(roleXX.getScore())
                .build();
        msg.setProto(proto);
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void onScheduleBegin(ActivitySchedule schedule) {
        ZuiQiangXiuXingSummaryData data = DataCenter.getZuiQiangXiuXingSummaryData();
        data.setSeason(data.getSeason() + 1);
        int day = TimeUtil.betweenDay(TimeUtil.getNowOfMills(), schedule.getBeginAt() * 1000L);
        List<PracticeScoreConfig> list = ConfigDataManager.getInstance().getList(PracticeScoreConfig.class);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        int stage = 1;
        while (day > 0) {
            PracticeScoreConfig scoreConfig = ConfigDataManager.getInstance().getById(PracticeScoreConfig.class, stage);
            if (scoreConfig == null) {
                continue;
            }
            day -= scoreConfig.getDay();
            stage++;
        }

        data.setStage(stage);
        data.setStageStartTime(TimeUtil.dayZeroSecondsFromNow() + (int) (day * TimeUtil.ONE_DAY_IN_SECONDS));
        DataCenter.updateData(data);
    }

    @Override
    public void afterRankReward(ActivitySchedule schedule, long rid, int rank, BigInteger score, boolean isUnion) {
        RoleSummary summary = SummaryManager.getInstance().getSummary(rid);
        if (summary == null) {
            return;
        }
        int hisRank = GlobalUtil.getGlobalInt(GameConst.GlobalId.ZUI_QIANG_XIU_XING_HIS_SIZE);
        if (hisRank == 0) {
            hisRank = 10;
        }
        if (rank > hisRank) {
            return;
        }
        //记录历史数据
        ZuiQiangXiuXingSummaryData data = DataCenter.getZuiQiangXiuXingSummaryData();
        ZuiQiangXiuXingHistoryData historyData = data.getHistoryRankMap().computeIfAbsent(data.getSeason(),
                v -> new ZuiQiangXiuXingHistoryData());
        historyData.setActId(schedule.getActivityID());
        historyData.setStartTime(schedule.getBeginAt());
        historyData.setEndTime(schedule.getEndAt());
        ZuiQiangXiuXingHistoryRoleData roleData = new ZuiQiangXiuXingHistoryRoleData();
        roleData.setRid(rid);
        roleData.setName(summary.getName());
        roleData.setScore(score.longValue());
        roleData.setRid(rank);
        roleData.setFashions(summary.getData().getFashions());
        historyData.getRankData().add(roleData);
        DataCenter.updateData(data);
    }

    private void selectionChestReward(long rid, int rank, BackpackStash stash) {
        if (!DataCenter.hasRole(rid)) {
            return;
        }
        Role role = DataCenter.get(Role.class, rid);
        if (role == null) {
            return;
        }
        RoleZuiQiangXiuXing roleXX = role.findZuiQiangXiuXing();
        PracticeChoseConfigCache cache = ConfigCacheManager.getInstance().getCache(PracticeChoseConfigCache.class);
        ZuiQiangXiuXingSummaryData data = DataCenter.getZuiQiangXiuXingSummaryData();
        Set<PracticeChoseConfig> configs = cache.getCfgSetBySeason(data.getSeason());
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }
        for (PracticeChoseConfig config : configs) {
            if (config.getRankMin() > rank || config.getRankMax() < rank) {
                continue;
            }
            List<List<int[]>> reward = config.getReward();
            List<int[]> choseReward;
            if (reward.size() <= roleXX.getSelectionChest()) { //如果索引选择异常，默认发第一个奖励
                choseReward = reward.get(0);
            } else {
                choseReward = reward.get(roleXX.getSelectionChest());
            }
            stash.increase(choseReward);
            log.info("最强修行者-自选奖励发奖#赛季:{},排行:{},获奖rid:{},奖励:{}", data.getSeason(), rank, rid, choseReward);
            break;
        }
    }

    @Override
    public void duringRankStashIncrease(long rid, int rank, BackpackStash stash) {
        selectionChestReward(rid, rank, stash);
    }

    public void onScheduleEndPrivate(ActivitySchedule schedule, Role role) {
        RoleZuiQiangXiuXing roleXX = role.findZuiQiangXiuXing();
        roleXX.setScore(0);
        roleXX.setSelectionChest(0);
        roleXX.setStageScore(0);
        roleXX.getStageProcess().clear();
        DataCenter.updateData(roleXX);
    }
}
