package com.sh.game.script.activity.abc;

import com.alibaba.fastjson.JSONObject;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.ThreeTuple;
import com.sh.game.common.config.model.ActivityRankingConfig;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.ActionConst;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.containers.Ranking;
import com.sh.game.common.entity.containers.RankingData;
import com.sh.game.common.entity.sys.ActivityRankingData;
import com.sh.game.common.entity.sys.CommonData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.*;
import com.sh.game.protos.AbcProtos;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.chongbang.ChongBangSummaryData;
import com.sh.game.system.query.QueryManager;
import com.sh.game.system.rank.RankManager;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.union.UnionManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractRankingActivity extends AbstractActivityScript implements
        IEventOnActivityStatistic,
        IEventOnRoleLevelUpScript,
        IEventOnRoleOrHeroAtkChanged,
        IEventOnRoleRechargedScript,
        IEventOnRoleCoinChangedScript,
        IEventOnZhanyiLvUp {

    /**
     * 1:等级排行 2：攻击力排行 3：累充 4：累消 5：boos积分 6：特戒等级, 7挖宝排行
     * @return
     */
    protected int getGoalType() {return 0;}
    /**
     * 只显示前三的具体信息
     */
    private static final int  TOP_LENGTH = 3;
    /**
     * 前10表示入了排行榜
     */
    private static final int ON_LENGTH = 500;

    private ActivityRankingConfig getDesc(ActivitySchedule schedule) {
        ActivityRankingConfig config = null;

        List<ActivityRankingConfig> rankingConfigs = ConfigDataManager.getInstance().getList(ActivityRankingConfig.class);
        for (ActivityRankingConfig rankingConfig : rankingConfigs) {
            if (rankingConfig == null) {
                continue;
            }

            if (rankingConfig.getActivityID() != schedule.getActivityID()) {
                continue;
            }
            if (!ConditionUtil.validate(rankingConfig.getCondition())) {
                continue;
            }

            config = rankingConfig;
            if (rankingConfig.getRank() == 0) {
                break;
            }

        }

        return config;
    }

    protected Ranking createRanking(ActivitySchedule schedule) {
        ActivityRankingConfig config = getDesc(schedule);
        Ranking ranking = new Ranking(config != null ? config.getVolume() : 0);
        ranking.setParam(config != null ? config.getType() : 0);
        return ranking;
    }

    protected ThreeTuple<BigInteger, Integer, List<AbcProtos.CommonRankingBean>> queryRanking(Role role, int type, ActivitySchedule schedule) {
        return queryRanking(role, type, ON_LENGTH);
    }

    protected ThreeTuple<BigInteger, Integer, List<AbcProtos.CommonRankingBean>> queryRanking(Role role, int type, int length) {
        BigInteger selfScore = new BigInteger("0");
        int selfRanking = 0;
        List<AbcProtos.CommonRankingBean> beans = new ArrayList<>();

        ActivityRankingData data = SysDataProvider.get(ActivityRankingData.class);
        Map<Integer, Ranking> rankings;
        if (type == 1) {
            rankings = data.getRoleRankings();
        } else if (type == 2) {
            rankings = data.getHeroRankings();
        } else if (type == 3) {
            rankings = data.getStageRankings();
        } else {
            rankings = data.getUnionRankings();
        }
        Ranking ranking = rankings.get(getType());
        if (ranking == null) {
            return new ThreeTuple<>(selfScore, selfRanking, beans);
        }
        if (type == 4) {
            selfScore = ranking.queryValue(role.getUnionId());
        } else {
            selfScore = ranking.queryValue(role.getId());
        }

        List<RankingData> sort = ranking.getSort();

        int tmpRank = 0;
        for (int i = 0; i < sort.size() && i < 500; i++) {
            RankingData rankingData = sort.get(i);
            tmpRank++;

            if (tmpRank <= length) {
                if (type == 4) {
                    if (rankingData.getKey() == role.getUnionId()) {
                        selfRanking = tmpRank;
                    }
                } else {
                    if (rankingData.getKey() == role.getId()) {
                        selfRanking = tmpRank;
                    }
                }
            }
            if (type == 4) {
                toUnionRankingBean(beans, tmpRank, rankingData.getValue().toString(), rankingData.getKey());
            } else {
                toRankingBean(beans, tmpRank, rankingData);
            }
        }

        return new ThreeTuple<>(selfScore, selfRanking, beans);
    }

    private List<ActivityRankingConfig> getActivityRankingConfigList(ActivitySchedule schedule) {
        List<ActivityRankingConfig> list = ConfigDataManager.getInstance().getList(ActivityRankingConfig.class);
        return list.stream()
                .filter(config -> config.getActivityID() == schedule.getActivityID())
                .filter(config -> ConditionUtil.validate(config.getCondition()))
                .collect(Collectors.toList());
    }

    private ActivityRankingConfig getActivityRankingConfig(List<ActivityRankingConfig> activityRankingConfigs, int rank) {
        for (ActivityRankingConfig config : activityRankingConfigs) {
            if (rank >= config.getRank() && rank <= config.getEndRank()) {
                return config;
            }
        }
        return null;
    }

    private void toRankingBean(List<AbcProtos.CommonRankingBean> beans, int tmpRank, RankingData rankingData) {
        toRankingBean(beans, tmpRank, rankingData.getValue().toString(), rankingData.getKey());
    }

    protected void toUnionRankingBean(List<AbcProtos.CommonRankingBean> beans, int rank, String score, long unionId) {
        Union union = UnionManager.getInstance().getUnion(unionId);
        if (union == null) {
            return;
        }
        AbcProtos.CommonRankingBean bean = AbcProtos.CommonRankingBean.newBuilder()
                .setRanking(rank)
                .setUid(union.getId())
                .setName(union.getName())
                .setScore(score)
                .setLevel(union.getUnionLevel())
                .addAllFashions(Collections.singleton(AbcProtos.CommonSlotBean.newBuilder().setId(union.getFlag()).build()))
                .build();
        beans.add(bean);
    }

    protected void toRankingBean(List<AbcProtos.CommonRankingBean> beans, int rank, String score, long rid) {
        RoleSummary summary = SummaryManager.getInstance().getSummary(rid);
        if (summary == null) {
            return;
        }
        Role role = DataCenter.get(Role.class, rid);

        AbcProtos.CommonRankingBean bean = AbcProtos.CommonRankingBean.newBuilder()
                .setRanking(rank)
                .setUid(summary.getId())
                .setName(summary.getName())
                .setScore(score)
                .addAllFashions(QueryManager.getInstance().fashionsToBean(summary.getData().getFashions()))
                .setLevel(summary.getLevel())
                .setZhuanSheng(role.getZhuanShengId())
                .build();

        beans.add(bean);
    }

    /**
     * 只取前三名的玩家 是带RoleSimpleBean的
     * @param role
     * @return
     */
    protected ThreeTuple<BigInteger, Integer, List<AbcProtos.CommonRankingWithRoleSimpleBean>> queryRankingTop(Role role, int type) {
        BigInteger selfScore = new BigInteger("0");
        int selfRanking = 0;
        List<AbcProtos.CommonRankingWithRoleSimpleBean> beans = new ArrayList<>();

        ActivityRankingData data = SysDataProvider.get(ActivityRankingData.class);
        Map<Integer, Ranking> rankings;
        if (type == 1) {
            rankings = data.getRoleRankings();
        } else if (type == 2) {
            rankings = data.getHeroRankings();
        } else if (type == 3) {
            rankings = data.getStageRankings();
        } else {
            rankings = data.getUnionRankings();
        }
        Ranking ranking = rankings.get(getType());

        if (ranking == null) {
            return new ThreeTuple<>(selfScore, selfRanking, beans);
        }

        selfScore = ranking.queryValue(role.getId());
        List<RankingData> sort = ranking.getSort();

        int tmpRank = 0;
        for (int i = 0; i < sort.size() && i < 50; i++) {
            RankingData rankingData = sort.get(i);
            tmpRank++;

            if (tmpRank <= ON_LENGTH) {
                if (type == 4) {
                    if (rankingData.getKey() == role.getUnionId()) {
                        selfRanking = tmpRank;
                    }
                } else {
                    if (rankingData.getKey() == role.getId()) {
                        selfRanking = tmpRank;
                    }
                }
            }
            if (type == 4) {
                toUnionSimpleBean(beans, tmpRank, rankingData);
            } else {
                toRoleSimpleBean(beans, tmpRank, rankingData);
            }

            //只取前三和自己就够了
            if ((tmpRank >= TOP_LENGTH) && (selfRanking > 0)){
                break;
            }
        }

        return new ThreeTuple<>(selfScore, selfRanking, beans);
    }

    protected ThreeTuple<BigInteger, Integer, List<AbcProtos.CommonRankingWithRoleSimpleBean>> queryRankingTop(Role role, ActivitySchedule schedule) {
        return queryRankingTop(role, 1);
    }

    private void toRoleSimpleBean(List<AbcProtos.CommonRankingWithRoleSimpleBean> beans, int tmpRank, RankingData rankingData) {
        if (tmpRank > TOP_LENGTH) {
            return;
        }

        RoleSummary summary = SummaryManager.getInstance().getSummary(rankingData.getKey());
        if (summary == null) {
            return;
        }

        AbcProtos.RoleSimpleBean roleSimpleBean = RankManager.getInstance().roleSummaryToSimpleBean(summary);

        AbcProtos.CommonRankingWithRoleSimpleBean bean = AbcProtos.CommonRankingWithRoleSimpleBean.newBuilder()
                .setRanking(tmpRank)
                .setUid(summary.getId())
                .setName(summary.getName())
                .setScore(rankingData.getValue().toString())
                .setPlayer(roleSimpleBean)
                .build();

        beans.add(bean);
    }

    private void toUnionSimpleBean(List<AbcProtos.CommonRankingWithRoleSimpleBean> beans, int tmpRank, RankingData rankingData) {
        if (tmpRank > TOP_LENGTH) {
            return;
        }
        Union union = UnionManager.getInstance().getUnion(rankingData.getKey());
        if (union == null) {
            return;
        }
        AbcProtos.CommonRankingWithRoleSimpleBean bean = AbcProtos.CommonRankingWithRoleSimpleBean.newBuilder()
                .setRanking(tmpRank)
                .setUid(union.getId())
                .setName(union.getName())
                .setScore(rankingData.getValue().toString())
                .setPlayer(AbcProtos.RoleSimpleBean.newBuilder().addAllFashions(Collections.singleton(AbcProtos.CommonSlotBean.newBuilder().setId(union.getFlag()).build())))
                .build();

        beans.add(bean);
    }



    @Override
    protected void onScheduleBegin(ActivitySchedule schedule) {
        super.onScheduleBegin(schedule);

//        ActivityRankingData data = SysDataProvider.get(ActivityRankingData.class);
//        data.getRoleRankings().put(getType(), createRanking(schedule));
//        data.getHeroRankings().put(getType(), createRanking(schedule));
//        DataCenter.updateData(data);
    }

    @Override
    protected void onScheduleEnd(ActivitySchedule schedule) {
        super.onScheduleEnd(schedule);

        // TODO deliver ranking rewards
        ActivityRankingData data = SysDataProvider.get(ActivityRankingData.class);
        Ranking roleRanking = data.getRoleRankings().get(schedule.getActivityType());
        if (roleRanking != null) {
            sendRankingAward(roleRanking, schedule, false);
        }

        Ranking heroRanking = data.getHeroRankings().get(schedule.getActivityType());
        if (heroRanking != null) {
            sendRankingAward(heroRanking, schedule, true);
        }

        Ranking unionRanking = data.getUnionRankings().get(schedule.getActivityType());
        if (unionRanking != null) {
            sendUnionRankingAward(unionRanking, schedule);
        }

        data.getRoleRankings().remove(getType());
        data.getHeroRankings().remove(getType());
        data.getStageRankings().remove(getType());
        data.getUnionRankings().remove(getType());
        DataCenter.updateData(data);
    }

    public void sendUnionRankingAward(Ranking ranking, ActivitySchedule schedule) {
        List<ActivityRankingConfig> activityRankingConfigs = getActivityRankingConfigList(schedule);
        int rank = 0;
        if (MapUtils.isEmpty(ranking.getData()) || CollectionUtils.isEmpty(ranking.getSort())) {
            return;
        }
        CommonData commonData = SysDataProvider.get(CommonData.class);
        ChongBangSummaryData summaryData = commonData.getChongBangHistoryDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ChongBangSummaryData());
        Set<Long> rewarded = new HashSet<>(ranking.getSort().size());
        for (RankingData data : ranking.getSort()) {
            if (rewarded.contains(data.getKey())) {
                continue;
            }
            if (data.getValue().equals(BigInteger.ZERO)) {
                continue;
            }
            rewarded.add(data.getKey());
            rank++;
            ActivityRankingConfig config = getActivityRankingConfig(activityRankingConfigs, rank);
            if (config == null) {
                continue;
            }
            Union union = UnionManager.getInstance().getUnion(data.getKey());
            if (union == null) {
                continue;
            }
            for (Long rid : union.getMemberInfos().keySet()) {
                BackpackStash stash = new BackpackStash();
                if (checkOpenTime(config.getRewardOpenTime(), schedule)) {
                    stash.increase(config.getReward());
                } else {
                    stash.increase(config.getReward2());
                }
                JSONObject extra = new JSONObject();
                extra.put("rank", rank);
                extra.put("roleName", union.getName());
                extra.put("score", data.getValue().toString());
                extra.put("actId", schedule.getActivityID());
                extra.put("stage", summaryData.getStage());
                stash.commitToMail2(rid, config.getMail(), true, extra.toJSONString(), rank);
            }
            afterRankReward(schedule, data.getKey(), rank, data.getValue(), true);
            log.info("活动排行邮件发奖#邮件id:{},排行:{},获奖联盟:{},奖励id:{}", config.getMail(), rank, union.getName(), config.getId());
        }
    }

    public void sendRankingAward(Ranking ranking, ActivitySchedule schedule, boolean isHero) {
        List<ActivityRankingConfig> activityRankingConfigs = getActivityRankingConfigList(schedule);
        int rank = 0;
        if (MapUtils.isEmpty(ranking.getData()) || CollectionUtils.isEmpty(ranking.getSort())) {
            return;
        }
        CommonData commonData = SysDataProvider.get(CommonData.class);
        ChongBangSummaryData summaryData = commonData.getChongBangHistoryDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ChongBangSummaryData());
        Set<Long> rewarded = new HashSet<>(ranking.getSort().size());
        for (RankingData data : ranking.getSort()) {
            if (rewarded.contains(data.getKey())) {
                continue;
            }
            if (data.getValue().equals(BigInteger.ZERO)) {
                continue;
            }
            rewarded.add(data.getKey());
            rank++;
            ActivityRankingConfig config = getActivityRankingConfig(activityRankingConfigs, rank);
            if (config == null) {
                continue;
            }

            BackpackStash stash = new BackpackStash();
            if (checkOpenTime(config.getRewardOpenTime(), schedule)) {
                stash.increase(isHero ? config.getHeroReward() : config.getReward());
            } else {
                stash.increase(config.getReward2());
            }
            duringRankStashIncrease(data.getKey(), rank, stash);
            RoleSummary summary = SummaryManager.getInstance().getSummary(data.getKey());
            JSONObject extra = new JSONObject();
            extra.put("rank", rank);
            extra.put("roleName", summary.getName());
            extra.put("score", data.getValue().toString());
            extra.put("actId", schedule.getActivityID());
            extra.put("stage", summaryData.getStage());
            stash.commitToMail2(data.getKey(), config.getMail(), true, extra.toJSONString(), rank);

            afterRankReward(schedule, data.getKey(), rank, data.getValue(), false);
            log.info("活动排行邮件发奖#邮件id:{},排行:{},获奖rid:{},是否是玩家:{},奖励id:{}", config.getMail(), rank, data.getKey(), isHero, config.getId());
        }
    }

    protected void duringRankStashIncrease(long rid, int rank, BackpackStash stash) {

    }

    protected void afterRankReward(ActivitySchedule schedule, long rid, int rank, BigInteger score, boolean isUnion) {

    }


    @Override
    public void onRoleLevelUp(Role role, long actorId, int oLv) {
        if (role.getId() == actorId) {
            int zhanYiLevel = role.getZhanYiLevel();
            int level = role.getLevel();
            int score = zhanYiLevel * 1000 + level;
            // type 1 分数为 战意等级 * 1000 + 玩家等级
            updateScore(role, 1, BigInteger.valueOf(score));
        } else {
            Hero hero = role.getHero();
            updateScore(hero, 1, BigInteger.valueOf(hero.getLevel()));
        }
    }

    @Override
    public void zhanyiUp(Role role, int lv) {
        int level = role.getLevel();
        int score = lv * 1000 + level;
        updateScore(role, 1, BigInteger.valueOf(score));

    }

    @Override
    public void onRoleAtkMaxChanged(Role role) {
        updateScore(role, 2, BigInteger.valueOf(role.getAtkMax()));
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig){
        updateScore(role, 3, BigInteger.valueOf(rechargeConfig.getCount()), false);
    }

    @Override
    public void onHeroAtkMaxChanged(Hero hero) {
        updateScore(hero, 2, java.math.BigInteger.valueOf(hero.getAtkMax()));
    }

    /**
     * 针对货币ItemId.MONEY,,BagConst.ItemId.MONEY_BIND的累计消耗排行
     * @param role
     * @param changes
     * @param action
     */
    @Override
    public void onRoleCoinChanged(Role role, Map<Integer, Long> changes , int action){
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        if (ActionConst.CONSUME_IGNORE_ACTIONS.contains(action)) {
            return;
        }
        long lingfuCost = changes.getOrDefault(BagConst.ItemId.MONEY, 0L);
        long bindLingfuCost = changes.getOrDefault(BagConst.ItemId.MONEY_BIND, 0L);
        int xiaofeiNum = 0;
        int bindXiaofeiNum = 0;

        //lingfuCost<0为消耗灵符
        if (lingfuCost < 0) {
            xiaofeiNum = (int) -lingfuCost;
        }
        //bindLingfuCost<0为消耗绑定灵符
        if (bindLingfuCost < 0) {
            bindXiaofeiNum = (int) -bindLingfuCost;
        }

        //本次消费的灵符数量
        int lingfuNum = xiaofeiNum + bindXiaofeiNum;
        if (lingfuNum <= 0) {
            return;
        }
        updateScore(role, 4, BigInteger.valueOf(lingfuNum), false);
    }

    protected void updateScore(Role role, int goalType, BigInteger score){
        this.updateScore(role, goalType, score, true);
    }

    /**
     *
     * @param role      role
     * @param goalType  排行类型
     * @param score     排行分数
     * @param update true时是更新为score的值， false时是加上score的值
     */
    protected void updateScore(Role role, int goalType, BigInteger score, boolean update) {
        updateScore(role, goalType, score, update, false);
    }

    protected void updateScore(Role role, int goalType, BigInteger score, boolean update, boolean stage) {
        if (getGoalType() != goalType) {
            return;
        }

        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        ActivityRankingConfig config = getDesc(schedule);
        if (config == null) {
            return;
        }
        if (!ConditionUtil.validate(role, config.getUpdateCondition())) {
            return;
        }
        ActivityRankingData data = SysDataProvider.get(ActivityRankingData.class);
        Ranking ranking;
        if (!stage) {
            ranking = data.getRoleRankings().get(getType());
            if (ranking == null) {
                ranking = createRanking(schedule);
                data.getRoleRankings().put(getType(), ranking);
                DataCenter.updateData(data);
            }
        } else {
            ranking = data.getStageRankings().get(getType());
            if (ranking == null) {
                ranking = createRanking(schedule);
                ranking.setParam(getGoalType());
                data.getStageRankings().put(getType(), ranking);
                DataCenter.updateData(data);
            }
        }
        if (ranking.getParam() != goalType) {
            return;
        }
        if (update) {
            //=score
            ranking.update(role.getId(), score);
        } else {
            //加score
            ranking.change(role.getId(), score);
        }
        DataCenter.updateData(data);
    }

    protected void updateScore(Hero hero, int goalType, BigInteger score) {
        if (hero == null) {
            return;
        }
        if (getGoalType() != goalType){
            return;
        }
        Role role = hero.getRole();
        if (role == null) {
            return;
        }
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        ActivityRankingConfig config = getDesc(schedule);
        if (config == null) {
            return;
        }
        if (!ConditionUtil.validate(role, config.getUpdateCondition())) {
            return;
        }
        ActivityRankingData data = SysDataProvider.get(ActivityRankingData.class);
        Ranking ranking = data.getHeroRankings().get(getType());
        if (ranking == null) {
            ranking = createRanking(schedule);
            data.getHeroRankings().put(getType(), ranking);
            DataCenter.updateData(data);
        }
        if (ranking.getParam() != goalType) {
            return;
        }

        ranking.update(role.getId(), score);
        DataCenter.updateData(data);
    }

    protected void updateScore(Union union, int goalType, BigInteger score) {
        ActivitySchedule schedule = getOpen();
        if (schedule == null) {
            return;
        }
        if (getGoalType() != goalType){
            return;
        }
        if (union == null) {
            return;
        }
        ActivityRankingConfig config = getDesc(schedule);
        if (config == null) {
            return;
        }
        ActivityRankingData data = SysDataProvider.get(ActivityRankingData.class);
        Ranking ranking = data.getUnionRankings().get(getType());
        if (ranking == null) {
            ranking = createRanking(schedule);
            data.getUnionRankings().put(getType(), ranking);
            DataCenter.updateData(data);
        }
        if (ranking.getParam() != goalType) {
            return;
        }

        ranking.update(union.getId(), score);
        DataCenter.updateData(data);
    }

    protected void decScore(Role role, int goalType, BigInteger score) {
        if (getGoalType() != goalType){
            return;
        }
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        ActivityRankingData data = SysDataProvider.get(ActivityRankingData.class);
        Ranking ranking = data.getRoleRankings().get(getType());
        if (ranking == null) {
            return;
        }
        if (ranking.getParam() != goalType) {
            return;
        }

        BigInteger value = ranking.queryValue(role.getId());
        if (value.compareTo(score) >= 0) {
            ranking.update(role.getId(), value.subtract(score));
        } else {
            ranking.remove(role.getId());
        }
        DataCenter.updateData(data);
    }

    protected String queryScore(Role role, int goalType) {
        if (getGoalType() != goalType){
            return "0";
        }
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return "0";
        }
        ActivityRankingData data = SysDataProvider.get(ActivityRankingData.class);
        Ranking ranking = data.getRoleRankings().get(getType());
        if (ranking == null) {
            return "0";
        }
        if (ranking.getParam() != goalType) {
            return "0";
        }
        return ranking.queryValue(role.getId()).toString();
    }

}
