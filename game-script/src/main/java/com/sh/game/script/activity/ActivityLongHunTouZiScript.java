package com.sh.game.script.activity;

import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.data.DataCenter;
import com.sh.game.script.activity.abc.AbstractActivityJieRiTouZiScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.ActivityTouZiHaoLiData;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

@Script
@Slf4j
public class ActivityLongHunTouZiScript extends AbstractActivityJieRiTouZiScript {
    @Override
    public int getType() {
        return ActivityConst.LONGHUN_TOUZI_FRIDAY;
    }


    /**
     * 查询活动开启天数
     * @param schedule
     * @return
     */
    @Override
    protected int findScheduleDay(ActivitySchedule schedule){
        //活动开启时间从0开始取余
        return ((schedule.findProceedDays() - 1) % 7) + 1;
    }

    @Override
    public void onRoleMidnight(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        if (schedule.getActivityType() != getType()) {
            return;
        }
        //判断是否是周五
        if (findScheduleDay(schedule) != 1) {
            return;
        }

        RoleActivity roleActivity = role.getRoleActivity();
        roleActivity.getTouZiHaoLiDataMap().compute(schedule.getActivityID(), (k, v) -> v = new ActivityTouZiHaoLiData());
        DataCenter.updateData(roleActivity);

        sendMsg(role, schedule.getActivityID());
        log.info("疯狂星期五投资豪礼活动id:{},活动重开清除数据,角色:{},昵称:{}", schedule.getActivityID(), role.getRoleId(), role.getName());
    }

    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {

    }
}
