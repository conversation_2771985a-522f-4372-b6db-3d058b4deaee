package com.sh.game.script.merchantship;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.merchantship.ResMerchantShipItemInfoMessage;
import com.sh.game.common.communication.msg.system.merchantship.ResMerchantShipRewardMessage;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleSmallGame;
import com.sh.game.common.util.*;
import com.sh.game.data.DataCenter;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.MerchantShipProtos;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.merchantship.entity.MerchantShipData;
import com.sh.game.system.merchantship.script.IMerchantShipScript;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import com.sh.game.system.smallgame.SmallGameManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 商船 鲤跃龙门
 * <AUTHOR>
 * @Date 2024/9/30 19:15
 */
@Script
@Slf4j
public class MerchantShipScript implements IMerchantShipScript {

    @Override
    public void reqMerchantShipBuy(Role role) {
        List<int[]> itemCost = GlobalUtil.findItemCost(GameConst.GlobalId.MERCHANT_SHIP_ORDINARY_BUY_COST);
        if (!BackPackStashUtil.decrease(role, itemCost, LogAction.MERCHANT_SHIP_ORDINARY_BUY_COST)) {
            return;
        }
        int[] itemArray = GlobalUtil.findJinghaoIntArray(GameConst.GlobalId.MERCHANT_SHIP_ITEM);
        int itemCfgId = itemArray[0];

        RoleSmallGame roleSmallGame = SmallGameManager.getInstance().getRoleSmallGame(role);
        MerchantShipData merchantShipData = roleSmallGame.getMerchantShipData();
        merchantShipData.setItemCfgId(itemCfgId);
        merchantShipData.setRewardCount(0);
        merchantShipData.setAdvertiseBuy(0);
        DataCenter.updateData(roleSmallGame);

        resMerchantShipItemInfo(role, itemCfgId);
    }

    @Override
    public void reqMerchantShipAdvertiseBuy(Role role) {
        RoleSmallGame roleSmallGame = SmallGameManager.getInstance().getRoleSmallGame(role);
        MerchantShipData merchantShipData = roleSmallGame.getMerchantShipData();
        merchantShipData.setItemCfgId(0);
        merchantShipData.setRewardCount(0);
        merchantShipData.setAdvertiseBuy(1);
        DataCenter.updateData(roleSmallGame);
    }

    @Override
    public void reqMerchantShipReward(Role role) {
        RoleSmallGame roleSmallGame = SmallGameManager.getInstance().getRoleSmallGame(role);
        MerchantShipData merchantShipData = roleSmallGame.getMerchantShipData();
        if(merchantShipData.getItemCfgId() <= 0) {
            return;
        }
        if(merchantShipData.getRewardCount() >= GlobalUtil.getGlobalInt(GameConst.GlobalId.MERCHANT_SHIP_MAX_BUY)) {
            return;
        }
        List<Item> items = BoxUtil.openBox(role, merchantShipData.getItemCfgId());
        if(CollectionUtils.isEmpty(items)) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(items);
        if (!stash.commit(role, LogAction.MERCHANT_SHIP_REWARD, true)) {
            return;
        }

        merchantShipData.setRewardCount(merchantShipData.getRewardCount() + 1);
        DataCenter.updateData(roleSmallGame);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.LONGMEN_FISH_COUNT);

        MerchantShipProtos.ResMerchantShipRewardMessage.Builder builder = MerchantShipProtos.ResMerchantShipRewardMessage.newBuilder();
        builder.addAllRewards(items.stream().map(ItemUtil::packCommonItemBean).collect(Collectors.toList()));
        ResMerchantShipRewardMessage message = new ResMerchantShipRewardMessage();
        message.setProto(builder.build());
        MessageUtil.sendMsg(message, role.getId());
    }

    @Override
    public void reqMerchantShipItemSell(Role role, List<AbcProtos.CommonKeyValueBean> itemsList) {
        if(CollectionUtils.isEmpty(itemsList)) {
            return;
        }
        Map<Integer, Long> sellItemMap = new HashMap<>();
        Map<Integer, Long> rewardItemMap = new HashMap<>();
        for (AbcProtos.CommonKeyValueBean item : itemsList) {
            ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, item.getKey());
            if(itemConfig == null || CollectionUtils.isEmpty(itemConfig.getEqual())) {
                return;
            }
            long oldCount = sellItemMap.getOrDefault(item.getKey(), 0L);
            sellItemMap.put(item.getKey(), oldCount + item.getValue());

            itemConfig.getEqual().forEach(array-> {
                long count = rewardItemMap.getOrDefault(array[0], 0L);
                rewardItemMap.put(array[0], count + ((long) array[1] * item.getValue()));
            });
        }

        if (!BackPackStashUtil.decrease(role, sellItemMap, 1, LogAction.MERCHANT_SHIP_RECYCLE)) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(rewardItemMap);
        if (!stash.commit(role, LogAction.MERCHANT_SHIP_RECYCLE, true)) {
            stash.commitToMail2(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }

        // 推送前端
        RewardInfoManager.getInstance().resRewardInfoByItemMap(role, LogAction.MERCHANT_SHIP_RECYCLE.getCode(), rewardItemMap);
    }

    @Override
    public void merchantShipAdvertiseBuyCallBack(Role role) {
        RoleSmallGame roleSmallGame = SmallGameManager.getInstance().getRoleSmallGame(role);
        MerchantShipData merchantShipData = roleSmallGame.getMerchantShipData();
        if(merchantShipData.getAdvertiseBuy() <= 0) {
            return;
        }
        int[] itemArray = GlobalUtil.findJinghaoIntArray(GameConst.GlobalId.MERCHANT_SHIP_ITEM);
        int itemCfgId = itemArray[1];
        merchantShipData.setAdvertiseBuy(0);
        merchantShipData.setItemCfgId(itemCfgId);
        DataCenter.updateData(roleSmallGame);

        //resMerchantShipItemInfo(role, itemCfgId);
    }


    private void resMerchantShipItemInfo(Role role, int itemCfgId){
        MerchantShipProtos.ResMerchantShipItemInfoMessage.Builder builder = MerchantShipProtos.ResMerchantShipItemInfoMessage.newBuilder();
        builder.setItemCfgId(itemCfgId);

        ResMerchantShipItemInfoMessage message = new ResMerchantShipItemInfoMessage();
        message.setProto(builder.build());
        MessageUtil.sendMsg(message, role.getId());
    }
}
