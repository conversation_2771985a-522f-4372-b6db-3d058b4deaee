package com.sh.game.script.advance;


import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.EquipAttribute;
import com.sh.game.common.entity.backpack.item.EquipData;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.advance.equipInherit.script.IEquipInheritScript;
import com.sh.script.annotation.Script;

@Script
public class EquipInheritScript implements IEquipInheritScript {

    @Override
    public void reqInherit(Role role, long source, long target) {
        Backpack backpack = role.getBackpack();

        Item sourceItem = backpack.findItemByUniqueId(source);
        if (sourceItem == null) {
            return;
        }
        Item targetItem = backpack.findItemByUniqueId(target);
        if (targetItem == null) {
            return;
        }

        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, sourceItem.getCfgId());
        if (itemConfig == null) return;
        ItemConfig targetItemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, targetItem.getCfgId());
        if (targetItemConfig == null) return;
        //只能低往高传
        if (targetItemConfig.getRank() < itemConfig.getRank()) return;

        //装备位配置
        int type = itemConfig.getType();
        EquipLocationConfig locationConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, type);
        if (locationConfig == null) return;


        BackpackStash stash = new BackpackStash(role);
        //觉醒
        EquipData sourceEquip = sourceItem.eData();
        // star
        EquipStarLevelUpConfig starConfig = ConfigDataManager.getInstance().getById(EquipStarLevelUpConfig.class,
                sourceEquip.getStarLevel() + Symbol.JINHAO + locationConfig.getFunctionType());
        if (starConfig != null) {
            stash.decrease(starConfig.getTransfercost());
        }


        EquipData targetEquip = targetItem.eData();

        int maxZhufu;
        EquipAttribute maxAttr = null;
        if (sourceEquip.getZhufuAttribute() == null) {
            maxAttr = targetEquip.getZhufuAttribute();
            maxZhufu = targetEquip.getZhufu();
        } else if (targetEquip.getZhufuAttribute() == null) {
            maxAttr = sourceEquip.getZhufuAttribute();
            maxZhufu = sourceEquip.getZhufu();
        } else {
            if (sourceEquip.getZhufuAttribute().compare(targetEquip.getZhufuAttribute()) == -1) {
                maxAttr = targetEquip.getZhufuAttribute();
                maxZhufu = targetEquip.getZhufu();
            } else {
                maxAttr = sourceEquip.getZhufuAttribute();
                maxZhufu = sourceEquip.getZhufu();
            }
        }
        EquipAttribute attr = maxAttr;
        stash.update(targetItem, item -> {
            item.eData().setStarLevel(sourceEquip.getStarLevel());
            item.eData().setStarLocked(sourceEquip.getStarLocked());
            item.eData().setStarExp(sourceEquip.getStarExp());
            item.eData().setZhufu(maxZhufu);
            item.eData().setZhufuAttribute(attr);
            item.eData().setXilianAttr(sourceEquip.getXilianAttr());
        });
        stash.update(sourceItem, item -> {
            item.eData().setStarLevel(0);
            item.eData().setStarLocked(0);
            item.eData().setStarExp(0);
            item.eData().setZhufu(0);
            item.eData().setZhufuAttribute(null);
            item.eData().setXilianAttr(null);
        });
        stash.commit(role, LogAction.EQUIP_INHERIT, true, null);
    }
}
