package com.sh.game.script.daily.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.daily.ResDailyHiddenRoomInfoMessage;
import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleDaily;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.protos.DailyProtos;
import com.sh.game.system.daily.script.IDailyHiddenRoomScript;
import com.sh.script.annotation.Script;

import java.util.Map;

@Script
public class DailyHiddenRoomScript implements IDailyHiddenRoomScript, IEventOnRoleMidnightScript, IEventOnRoleLoginScript {

    private int getDuplicateCate() {
        return 118;
    }

    /**
     * 隐藏房间信息
     *
     * @param role
     */
    @Override
    public void reqInfo(Role role) {
        int duplicateCate = getDuplicateCate();
        RoleDaily roleDaily = DataCenter.get(RoleDaily.class,role.getId());
        Map<Integer, Integer> uTimes = roleDaily.getDailyHiddenRoomUTimes();

        ResDailyHiddenRoomInfoMessage msg = new ResDailyHiddenRoomInfoMessage();
        DailyProtos.ResDailyHiddenRoomInfo.Builder hiddenRoomInfo = DailyProtos.ResDailyHiddenRoomInfo.newBuilder();
        for (DuplicateConfig duplicateConfig : ConfigDataManager.getInstance().getList(DuplicateConfig.class)) {
            if (duplicateConfig.getDuplicateCate() != duplicateCate) {
                continue;
            }
            if (duplicateConfig.getLimitTimes() == 0) {
                continue;
            }
            DailyProtos.DailyHiddenRoomTimes.Builder bean = DailyProtos.DailyHiddenRoomTimes.newBuilder();
            bean.setDuplicateID(duplicateConfig.getId());
            bean.setUTimes(uTimes.getOrDefault(duplicateConfig.getId(), 0));
            hiddenRoomInfo.addTimes(bean);
        }
        msg.setProto(hiddenRoomInfo.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void updateInfo(Role role, int mapId) {
        int duplicateCate = getDuplicateCate();
        DuplicateConfig duplicateConfig = ConfigDataManager.getInstance().getById(DuplicateConfig.class, mapId);
        if (duplicateConfig == null || duplicateConfig.getLimitTimes() == 0 || duplicateConfig.getDuplicateCate() != duplicateCate) {
            return;
        }
        RoleDaily roleDaily = DataCenter.get(RoleDaily.class,role.getId());
        Map<Integer, Integer> uTimes = roleDaily.getDailyHiddenRoomUTimes();
        uTimes.merge(mapId, 1, Integer::sum);
        DataCenter.updateData(role);
        reqInfo(role);
    }

    @Override
    public void onRoleLogin(Role role) {
        reqInfo(role);
    }

    @Override
    public void onRoleMidnight(Role role) {
        RoleDaily roleDaily = DataCenter.get(RoleDaily.class,role.getId());
        roleDaily.getDailyHiddenRoomUTimes().clear();
        DataCenter.updateData(roleDaily);
        reqInfo(role);
    }
}
