package com.sh.game.script.role;

import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.event.IEventOnRoleLogoutScript;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.role.script.IRoleFakeAttributeScript;
import com.sh.script.annotation.Script;

import java.util.Map;

@Script
public class RoleFakeAttributeScript implements IRoleFakeAttributeScript, IEventOnRoleAttributeCountScript, IEventOnRoleLogoutScript {

    @Override
    public void onFakeAttribute(Role role) {
        onRoleAttributeChange(role);
        if (role.getHero() != null) {
            onHeroAttributeChange(role.getHero());
        }
    }

    @Override
    public AttributeConst.AttributeType getAttributeType() {
        return AttributeConst.AttributeType.Role.GM;
    }

    @Override
    public void onRoleAttributeCount(Role role) {
        Attribute attribute = new Attribute();
        Map<Integer, Long> attributeTemp = role.getMemory().getGMAttributeTemp();
        attribute.fixAdd(attributeTemp);
        role.getAttributes().put(getAttributeType(), attribute);
    }

    @Override
    public void onHeroAttributeCount(Hero hero) {
        Attribute attribute = new Attribute();
        Role role = hero.getRole();
        Map<Integer, Long> attributeTemp = role.getMemory().getGMAttributeTemp();
        attribute.fixAdd(attributeTemp);
        hero.getAttributes().put(getAttributeType(), attribute);
    }

    @Override
    public void onRoleLogout(Role role) {
        if (role.getMemory().getGMAttributeTemp().size() > 0) {
            role.getMemory().getGMAttributeTemp().clear();
        }
    }
}
