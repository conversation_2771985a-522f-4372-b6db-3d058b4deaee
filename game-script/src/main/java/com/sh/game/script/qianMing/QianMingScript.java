package com.sh.game.script.qianMing;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.qianMing.ResQianMingInfoMessage;
import com.sh.game.common.config.model.AppearanceConfig;
import com.sh.game.common.config.model.QianMingConfig;
import com.sh.game.common.constant.FashionConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.QianMingProtos;
import com.sh.game.system.qianMing.script.IQianMingScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Script
@Slf4j
public class QianMingScript implements IQianMingScript {

    private void sendMsg(Role role) {
        ResQianMingInfoMessage msg = new ResQianMingInfoMessage();
        List<AbcProtos.QianMingBean> beanList = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : role.getRoleQianMing().entrySet()) {
            Integer index = entry.getKey();
            Integer fashionId = entry.getValue();
            beanList.add(AbcProtos.QianMingBean.newBuilder()
                    .setIndex(index)
                    .setFashionId(fashionId)
                    .build());
        }
        msg.setProto(QianMingProtos.ResQianMingInfo.newBuilder()
                .addAllBeanList(beanList)
                .build());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }


    @Override
    public void reqInlayQianMing(Role role, int index, int fashionId) {
        QianMingConfig qianMingConfig = ConfigDataManager.getInstance().getById(QianMingConfig.class, index);
        if (qianMingConfig == null) {
            log.error("签名系统,请求镶嵌,部位参数异常,角色:{},昵称:{},装备位:{},时装id:{}", role.getRoleId(), role.getName(), index, fashionId);
            return;
        }
        AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashionId);
        if (appearanceConfig == null) {
            log.error("签名系统,请求镶嵌,时装参数异常,角色:{},昵称:{},装备位:{},时装id:{}", role.getRoleId(), role.getName(), index, fashionId);
            return;
        }
        if (appearanceConfig.getType() != FashionConst.AppearanceType.QIANMING) {
            log.error("签名系统,请求镶嵌,时装参数异常,角色:{},昵称:{},装备位:{},时装id:{}", role.getRoleId(), role.getName(), index, fashionId);
            return;
        }
        if (!role.getRoleAdvance().getAppearance().getStatus().containsKey(fashionId)) {
            log.error("签名系统,请求镶嵌,未解锁该时装,角色:{},昵称:{},装备位:{},时装id:{}", role.getRoleId(), role.getName(), index, fashionId);
            return;
        }
        //如果已经在镶嵌需要把那个卸掉
        List<Integer> indexList = new ArrayList<>();
        Map<Integer, Integer> roleQianMing = role.getRoleQianMing();
        if (roleQianMing.containsValue(fashionId)) {
            for (Map.Entry<Integer, Integer> entry : roleQianMing.entrySet()) {
                Integer roleIndex = entry.getKey();
                Integer roleFashionId = entry.getValue();
                if (roleFashionId != fashionId) {
                    continue;
                }
                indexList.add(roleIndex);
            }
        }
        //删除被替换的签名
        for (Integer indx : indexList) {
            roleQianMing.remove(indx);
        }
        roleQianMing.put(index, fashionId);
        DataCenter.updateData(role);

        sendMsg(role);
        log.info("签名系统,请求镶嵌,镶嵌成功,角色:{},昵称:{},装备位:{},时装id:{}", role.getRoleId(), role.getName(), index, fashionId);
    }

    @Override
    public void reqDismantleQianMing(Role role, int index) {
        QianMingConfig qianMingConfig = ConfigDataManager.getInstance().getById(QianMingConfig.class, index);
        if (qianMingConfig == null) {
            log.error("签名系统,请求拆除,参数异常,角色:{},昵称:{},装备位:{}", role.getRoleId(), role.getName(), index);
            return;
        }
        Map<Integer, Integer> roleQianMing = role.getRoleQianMing();
        if (!roleQianMing.containsKey(index)) {
            log.error("签名系统,请求拆除,该装备位未镶嵌,角色:{},昵称:{},装备位:{}", role.getRoleId(), role.getName(), index);
            return;
        }

        //删除被替换的签名
        roleQianMing.remove(index);
        DataCenter.updateData(role);

        sendMsg(role);
        log.info("签名系统,请求拆除,镶嵌成功,角色:{},昵称:{},装备位:{}", role.getRoleId(), role.getName(), index);
    }


    @Override
    public void reqQianMingInfo(Role role) {
        sendMsg(role);
    }

}
