package com.sh.game.script.xinghun;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.xinghun.ResXingHunInfoMessage;
import com.sh.game.common.communication.msg.system.xinghun.ResXingHunUpLevelMessage;
import com.sh.game.common.config.cache.XingHunCache;
import com.sh.game.common.config.model.XingHunConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleNormal;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.XinghunProtos;
import com.sh.game.system.xinghun.script.IXingHunScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/12/27 19:24
 */
@Slf4j
@Script
public class XingHunScript implements IXingHunScript {

    @Override
    public void info(Role role) {
        RoleNormal normal = role.findNormal();
        Map<Integer, Integer> xingHunMap = normal.getXingHunMap();
        ResXingHunInfoMessage msg = new ResXingHunInfoMessage();
        XinghunProtos.ResXingHunInfo.Builder protoBuilder = XinghunProtos.ResXingHunInfo.newBuilder();
        for (Map.Entry<Integer, Integer> entry : xingHunMap.entrySet()) {
            protoBuilder.addInfoList(AbcProtos.CommonKeyValueBean.newBuilder()
                    .setKey(entry.getKey())
                    .setValue(entry.getValue())
                    .build());
        }
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void upLevel(Role role, int type) {
        RoleNormal normal = role.findNormal();
        Map<Integer, Integer> xingHunMap = normal.getXingHunMap();
        Integer cid = xingHunMap.get(type);
        XingHunConfig config;
        if (cid == null) {
            XingHunCache cache = ConfigCacheManager.getInstance().getCache(XingHunCache.class);
            if (cache == null) {
                log.error("星魂系统，找不到星魂配置缓存");
                return;
            }
            config = cache.findConfig(type, 0);
        } else {
            config = ConfigDataManager.getInstance().getById(XingHunConfig.class, cid);
        }

        if (config == null) {
            log.error("星魂系统，玩家请求升级，找不到玩家星魂配置，玩家id {} name {} type {} 玩家星魂详情 {}", role.getId(), role.getName(), type, JSON.toJSONString(xingHunMap));
            return;
        }

        XingHunConfig next = ConfigDataManager.getInstance().getById(XingHunConfig.class, config.getNextId());
        if (next == null) {
            log.error("星魂系统，玩家请求升级，找不到下级配置，玩家id {} name {} type {} 配置id {}", role.getId(), role.getName(), type, cid);
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getCost());
        if (!stash.commit(role, LogAction.XING_HUN_COST)) {
            return;
        }

        xingHunMap.put(type, next.getId());
        DataCenter.updateData(normal);

        log.info("星魂系统，玩家请求升级，玩家升级成功，玩家id {} name {} type {} old {} new {}", role.getId(), role.getName(), type, cid, next.getId());

        ResXingHunUpLevelMessage msg = new ResXingHunUpLevelMessage();
        msg.setProto(XinghunProtos.ResXingHunUpLevel.newBuilder()
                .setType(type)
                .setCid(next.getId())
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

}
