package com.sh.game.script.xianyou;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.system.xianyou.ResGroupInfoMessage;
import com.sh.game.common.communication.msg.system.xianyou.ResXianYouMessage;
import com.sh.game.common.config.model.XianYouConfig;
import com.sh.game.common.config.model.XianYouShiJianConfig;
import com.sh.game.common.constant.CountConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleExtend;
import com.sh.game.common.util.*;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.XianYouProtos;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.role.RoleExtendManager;
import com.sh.game.system.role.script.IRoleOnMinuteScript;
import com.sh.game.system.secretary.SecretaryManager;
import com.sh.game.system.secretary.entity.SecretaryData;
import com.sh.game.system.xianyou.entity.RoleXianYou;
import com.sh.game.system.xianyou.script.IXianYouScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-04-01
 */
@Slf4j
@Script
public class XianYouScript implements IXianYouScript, IEventOnRoleLoginScript, IRoleOnMinuteScript {

    @Override
    public void reqGroupInfo(Role role, int groupType) {
        RoleXianYou roleXianYou = role.findXianYou();
        Map<Integer, Map<Integer, TwoTuple<Integer, Integer>>> groupInfo = roleXianYou.getGroupInfo();

        List<XianYouProtos.GroupInfoData> groupInfos = groupInfo.entrySet().stream()
                .map(entry -> {
                    XianYouProtos.GroupInfoData.Builder builder = XianYouProtos.GroupInfoData.newBuilder().setGroup(entry.getKey());
                    entry.getValue().forEach((k, v) -> builder.addInfos(
                            AbcProtos.ThreeTupleBean.newBuilder().setFirst(k).setSecond(v.first).setThree(v.second).build()));
                    return builder.build();
                })
                .collect(Collectors.toList());

        ResGroupInfoMessage msg = new ResGroupInfoMessage();
        msg.setProto(XianYouProtos.ResGroupInfo.newBuilder().addAllDatas(groupInfos).build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqXianYou(Role role, int xianYouCount, boolean isAdvert) {
        RoleXianYou roleXianYou = role.findXianYou();
        if (!isAdvert && (roleXianYou.getXianYouCount() <= 0 || roleXianYou.getXianYouCount() - xianYouCount < 0)) {
            log.error("仙游-请求仙游-玩家:{}-{},仙游次数不足", role.getRoleId(), role.getName());
            return;
        }

        List<XianYouProtos.XianYouData> dataList = new ArrayList<>();
        Map<Integer, Long> reward = new HashMap<>();

        List<XianYouShiJianConfig> xianYouShiJianConfigs = ConfigDataManager.getInstance().getList(XianYouShiJianConfig.class);
        List<Integer> shiJianWeights = xianYouShiJianConfigs.stream().map(XianYouShiJianConfig::getWeight).collect(Collectors.toList());

        for (int i = 0; i < xianYouCount; i++) {
            XianYouProtos.XianYouData.Builder builder = XianYouProtos.XianYouData.newBuilder();
            int shiJianIndex = RandomUtil.randomIndexByProb(shiJianWeights);
            XianYouShiJianConfig xianYouShiJianConfig = xianYouShiJianConfigs.get(shiJianIndex);
            builder.setShiJianId(xianYouShiJianConfig.getId());
            // 增加亲密度才随仙友
            if (xianYouShiJianConfig.getShijian() == 4) {
                MapUtil.merge(xianYouShiJianConfig.getItemid(), reward);
                List<XianYouConfig> xianYouConfigs = ConfigDataManager.getInstance().getList(XianYouConfig.class);
                List<Integer> xianYouWeights = xianYouConfigs.stream().map(XianYouConfig::getWeight).collect(Collectors.toList());
                int XianYouIndex = RandomUtil.randomIndexByProb(xianYouWeights);
                XianYouConfig xianYouConfig = xianYouConfigs.get(XianYouIndex);
                builder.setGroupId(xianYouConfig.getId());

                Map<Integer, TwoTuple<Integer, Integer>> tupleMap = roleXianYou.getGroupInfo().computeIfAbsent(xianYouConfig.getGroup(), k -> new HashMap<>());
                TwoTuple<Integer, Integer> tuple = tupleMap.computeIfAbsent(xianYouConfig.getXianyou(), k -> new TwoTuple<>(0, 0));
                tuple.setFirst(tuple.getFirst() + 1);
                if (xianYouConfig.getQinmidu() > 0 && tuple.getFirst() >= xianYouConfig.getQinmidu()) {
                    tuple.setFirst(xianYouConfig.getQinmidu());
                    SecretaryManager.getInstance().reqSecretaryLevelUp(role, xianYouConfig.getXianyou(), false);
                }
                builder.setHaoGanDu(1);
            }
            // npc对话直接发奖
            if (xianYouShiJianConfig.getShijian() == 1) {
                MapUtil.merge(xianYouShiJianConfig.getItemid(), reward);
            }
            dataList.add(builder.build());
        }
        if (!isAdvert) {
            roleXianYou.setXianYouCount(roleXianYou.getXianYouCount() - xianYouCount);
        }
        DataCenter.updateData(roleXianYou);

        ResXianYouMessage msg = new ResXianYouMessage();
        msg.setProto(XianYouProtos.ResXianYou.newBuilder()
                .addAllDatas(dataList)
                .setXianYouCount(roleXianYou.getXianYouCount())
                .setCountTime(roleXianYou.getCountTime()).build());
        MessageUtil.sendMsg(msg, role.getId());

        BackPackStashUtil.increase(role, reward, LogAction.XIAN_YOU_REWARD, true);

        CountManager.getInstance().count(role, CountConst.CountType.XIAN_YOU_TOTAL_COUNT, CountConst.KEY_VALUE_DEFAULT, xianYouCount);

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.XIAN_YOU_COUNT, xianYouCount);
        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.XIAN_YOU_TOTAL_COUNT);
    }

    @Override
    public void useItemEnergy(Role role, int value) {
        RoleXianYou roleXianYou = role.findXianYou();
        roleXianYou.setXianYouCount(roleXianYou.getXianYouCount() + value);
        DataCenter.updateData(roleXianYou);

        sendXianYouCountMsg(role);
    }

    @Override
    public void reqAddXiuWei(Role role, int secretaryId) {
        RoleExtend roleExtend = RoleExtendManager.getInstance().getRoleExtend(role.getId());
        SecretaryData secretaryData = roleExtend.getSecretaryData();
        if (!secretaryData.getSecretaryMap().containsKey(secretaryId)) {
            log.error("仙游-请求增加修为-玩家:{}-{},没有这个秘书:{}", role.getRoleId(), role.getName(), secretaryId);
            return;
        }
        secretaryData.getSecretaryXianYouMap().merge(secretaryId, GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_YOU_ADD_INCOME), Integer::sum);
        SecretaryManager.getInstance().calSecretaryTotalIncome(role, secretaryData, -1);
        SecretaryManager.getInstance().reqSecretaryInfo(role);
    }

    @Override
    public void reqSendReward(Role role, int shiJianId) {
        XianYouShiJianConfig shiJianConfig = ConfigDataManager.getInstance().getById(XianYouShiJianConfig.class, shiJianId);
        if (shiJianConfig == null) {
            log.error("仙游-请求领取奖励-玩家:{}-{},事件id找不到配置:{}", role.getRoleId(), role.getName(), shiJianId);
            return;
        }
        if (shiJianConfig.getShijian() == 2) {
            BackPackStashUtil.increase(role, shiJianConfig.getItemid(), LogAction.XIAN_YOU_REWARD, true);
        }
    }

    @Override
    public void onRoleLogin(Role role) {
        sendXianYouCountMsg(role);
    }

    @Override
    public void onRoleMinute(Role role) {
        RoleXianYou roleXianYou = role.findXianYou();
        boolean isUpdate = false;

        int needTime = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_YOU_COUNT_TIME);
        int limitCount = GlobalUtil.getGlobalInt(GameConst.GlobalId.XIAN_YOU_COUNT_LIMIT);
        int nowOfMinutes = TimeUtil.getNowOfMinutes();
        if (roleXianYou.getXianYouCount() >= limitCount || roleXianYou.getCountTime() == 0) {
            roleXianYou.setCountTime(nowOfMinutes);
            isUpdate = true;
        }
        if (roleXianYou.getXianYouCount() < limitCount && nowOfMinutes - roleXianYou.getCountTime() >= needTime) {
            int time = nowOfMinutes - roleXianYou.getCountTime();
            roleXianYou.setXianYouCount(Math.min(limitCount, roleXianYou.getXianYouCount() + time / needTime));
            roleXianYou.setCountTime(nowOfMinutes);
            isUpdate = true;
        }
        if (isUpdate) {
            DataCenter.updateData(roleXianYou);
            sendXianYouCountMsg(role);
        }
    }

    private void sendXianYouCountMsg(Role role) {
        RoleXianYou roleXianYou = role.findXianYou();

        ResXianYouMessage msg = new ResXianYouMessage();
        msg.setProto(XianYouProtos.ResXianYou.newBuilder()
                .setXianYouCount(roleXianYou.getXianYouCount())
                .setCountTime(roleXianYou.getCountTime()).build());
        MessageUtil.sendMsg(msg, role.getId());
    }
}
