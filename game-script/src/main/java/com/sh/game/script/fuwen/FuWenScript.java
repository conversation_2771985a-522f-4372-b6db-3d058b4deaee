package com.sh.game.script.fuwen;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.backpack.ResBackpackChangedMessage;
import com.sh.game.common.communication.msg.system.fuwen.*;
import com.sh.game.common.config.converter.array.JinghaoIntArrayConverter;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.protos.BackPackProtos;
import com.sh.game.protos.FuwenProtos;
import com.sh.game.system.fuwen.entity.FuWen;
import com.sh.game.system.fuwen.script.IFuWenScript;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.role.entity.Hero;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * ATO：lrf;
 * 时间：2020/12/9 14:12;
 * 版本：1.0;
 * 描述：符文
 */
@Script
@Slf4j
public class FuWenScript implements IFuWenScript, IEventOnRoleLoginScript {

    @Override
    public void reqHuoLongLevel(Role role, long itemId) {
        if (role.getHero() == null) {
            return;
        }
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByUniqueId(itemId);
        if (item == null) {
            return;
        }
        ItemConfig itemConfig = item.findItemConfig();
        if (itemConfig == null) {
            return;
        }
        FireDragonLevelConfig levelConfig = ConfigDataManager.getInstance().getById(FireDragonLevelConfig.class, item.getCfgId());
        if (levelConfig == null) {
            return;
        }
        FireDragonLevelConfig nextLevelConfig = ConfigDataManager.getInstance().getById(FireDragonLevelConfig.class, levelConfig.getNext());
        //升满级了
        if (nextLevelConfig == null) {
            return;
        }
        Storage storage = backpack.fetchStorage(item.getWhere());
        if (storage == null) {
            return;
        }
        List<int[]> cost = levelConfig.getCost();
        if (cost != null && !cost.isEmpty()) {
            BackpackStash stash = new BackpackStash(role);
            stash.decrease(cost);
            if (!stash.commit(role, LogAction.FUWEN)) {
                return;
            }
        }
        //把原先得替换成新的
        Item copy = ItemUtil.copy(item);
        copy.setCfgId(nextLevelConfig.getId());


        List<ItemChange> changes = new ArrayList<>();
        ItemChange itemChange = storage.update(item.getIndex(), copy, LogAction.FUWEN.getCode());
        changes.add(itemChange);
        DataCenter.updateData(backpack);
        //背包变化
        ResBackpackChangedMessage msg = new ResBackpackChangedMessage();
        BackPackProtos.ResBackpackChanged.Builder backpackChanged = BackPackProtos.ResBackpackChanged.newBuilder();
        backpackChanged.setAction(LogAction.FUWEN.getCode());
        if (itemChange != null) {
            BackPackProtos.BackpackChangeBean.Builder bean = BackPackProtos.BackpackChangeBean.newBuilder();
            bean.setWhere(itemChange.getPlace().getWhere());
            backpackChanged.addChangedItems(bean.build());
            BackPackProtos.BackpackGridBean.Builder gridBean = BackPackProtos.BackpackGridBean.newBuilder();
            gridBean.setIndex(itemChange.getIndex());
            gridBean.setItem(ItemUtil.packCommonItemBean(itemChange.getNItem()));
            bean.addChanges(gridBean.build());
        }
        msg.setProto(backpackChanged.build());
        MessageUtil.sendMsg(msg, role.getId());

        role.updateBackpack(changes);

        ResHuoLongLevelMessage levelMessage = new ResHuoLongLevelMessage();
        levelMessage.setProto(FuwenProtos.ResHuoLongLevel.newBuilder().build());
        MessageUtil.sendMsg(levelMessage, role.getId());

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.FIRE_DRAGON_LEVEL);
    }

    /**
     * 检查位置是否开启
     *
     * @param role
     * @param index
     * @return
     */
    private boolean checkIndexOpen(Role role, int index) {
        if (role.getHero() == null) {
            return false;
        }
        FuWenConfig fuWenConfig = ConfigDataManager.getInstance().getById(FuWenConfig.class, index);
        if (fuWenConfig == null) {
            return false;
        }
        if (!ConditionUtil.validate(role, fuWenConfig.getCondition())) {
            return false;
        }
        return true;
    }

    @Override
    public void reqEquipFuWen(Role role, int index, long itemId) {
        if (!checkIndexOpen(role, index)) {
            return;
        }
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByUniqueId(itemId);
        if (item == null) {
            return;
        }
        ItemConfig itemConfig = item.findItemConfig();
        if (itemConfig == null) {
            return;
        }
        //同类的不能在装配了
        Map<Integer, FuWen> fuWenMap = role.getHero().getFuwen();
        boolean exitsSameType = fuWenMap.values().stream()
                .filter(fuWen -> fuWen != null && fuWen.getCfgId() > 0)
                .anyMatch(fuWen -> {
                    ItemConfig sunConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, fuWen.getCfgId());
                    if (sunConfig != null && sunConfig.getType() == itemConfig.getType() && fuWen.getIndex() != index) {
                        return true;
                    }
                    return false;
                });
        if (exitsSameType) {
            return;
        }

        FuWen oldfuWen = fuWenMap.getOrDefault(index, null);
        BackpackStash stash = new BackpackStash(role);
        if (oldfuWen != null && oldfuWen.getCfgId() > 0) {
            stash.increase(oldfuWen.getCfgId());
        }
        stash.decrease(item);
        if (!stash.commit(role, LogAction.FUWEN)) {
            return;
        }
        FuWen fuWen = new FuWen();
        fuWen.setCfgId(item.getCfgId());
        fuWen.setIndex(index);
        fuWenMap.put(index, fuWen);
        DataCenter.updateData(role);

        ResEquipFuWenMessage msg = new ResEquipFuWenMessage();
        FuwenProtos.ResEquipFuWen.Builder equiqFuWen = FuwenProtos.ResEquipFuWen.newBuilder();
        FuwenProtos.FuWenBean.Builder bean = FuwenProtos.FuWenBean.newBuilder();
        bean.setCfgId(item.getCfgId());
        bean.setIndex(index);
        equiqFuWen.setFuwen(bean);
        msg.setProto(equiqFuWen.build());
        MessageUtil.sendMsg(msg, role.getId());

        GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.PUTON_FUWEN_COUNT);
    }

    @Override
    public void reqOffFuWen(Role role, int index) {
        if (!checkIndexOpen(role, index)) {
            return;
        }
        Map<Integer, FuWen> fuwenInfo = role.getHero().getFuwen();
        FuWen fuWen = fuwenInfo.getOrDefault(index, null);
        if (fuWen == null || fuWen.getCfgId() <= 0) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.increase(fuWen.getCfgId());
        if (!stash.commit(role, LogAction.FUWEN)) {
            return;
        }
        fuwenInfo.put(index, new FuWen());
        DataCenter.updateData(role);
        ResOffFuWenMessage msg = new ResOffFuWenMessage();
        msg.setProto(FuwenProtos.ResOffFuWen.newBuilder()
                .setIndex(index)
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqFuWenEqual(Role role, int itemId, int count) {
        if (count <= 0) {
            return;
        }
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
        if (itemConfig == null) {
            return;
        }
        String fuwenDuiHuan = GlobalUtil.getGlobalValue(GameConst.GlobalId.FUWEN_DUIHUAN);
        int[] fuwenIntArray = (int[]) new JinghaoIntArrayConverter().convert(fuwenDuiHuan);
        if (fuwenIntArray.length < 3) {
            return;
        }
        int costId = fuwenIntArray[0];
        int targetId = fuwenIntArray[1];
        int getCount = fuwenIntArray[2];
        if (costId != itemId) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemId, count);
        boolean commit = stash.commit(role, LogAction.FUWEN);
        if (!commit) {
            return;
        }
        BackpackStash add = new BackpackStash(role);
        add.increase(targetId, (long) getCount * count);
        if (add.commit(role, LogAction.FUWEN)) {
            add.commitToMail2(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }

        ResFuWenEqualMessage msg = new ResFuWenEqualMessage();
        msg.setProto(FuwenProtos.ResFuWenEqual.newBuilder().build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqHuoLongStrong(Role role, int index) {
        Hero hero = role.getHero();
        if (hero == null) {
            return;
        }
        if (index != EquipConst.EquipIndex.HUO_LONG.getCls()) {
            return;
        }
        int huoLevel = hero.getHuoLevel();
        FuWenStrongConfig strongConfig = ConfigDataManager.getInstance().getById(FuWenStrongConfig.class, huoLevel);
        if (strongConfig == null) {
            return;
        }
        FuWenStrongConfig nextConfig = ConfigDataManager.getInstance().getById(FuWenStrongConfig.class, huoLevel + 1);
        //满级
        if (nextConfig == null) {
            return;
        }
        List<int[]> cost = strongConfig.getCost();
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(cost);
        if (!stash.commit(role, LogAction.FUWEN)) {
            return;
        }
        hero.setHuoLevel(nextConfig.getLevel());
        DataCenter.updateData(role);
        ResHuoLongStrongLevelMessage msg = new ResHuoLongStrongLevelMessage();
        msg.setProto(FuwenProtos.ResHuoLongStrongLevel.newBuilder()
                .setIndex(index)
                .setLevel(hero.getHuoLevel())
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void onRoleLogin(Role role) {
        Hero hero = role.getHero();
        if (hero == null) {
            return;
        }
        ResHuoLongStrongLevelMessage msg = new ResHuoLongStrongLevelMessage();
        msg.setProto(FuwenProtos.ResHuoLongStrongLevel.newBuilder()
                .setIndex(EquipConst.EquipIndex.HUO_LONG.getCls())
                .setLevel(hero.getHuoLevel())
                .build());
        MessageUtil.sendMsg(msg, role.getId());

        Map<Integer, FuWen> fuwen = role.getHero().getFuwen();
        ResFuWenInfoMessage fuWenInfoMessage = new ResFuWenInfoMessage();
        FuwenProtos.ResFuWenInfo.Builder fuWenInfo = FuwenProtos.ResFuWenInfo.newBuilder();
        FuwenProtos.FuWenBean.Builder bean;
        for (Map.Entry<Integer, FuWen> entry : fuwen.entrySet()) {
            FuWen value = entry.getValue();
            if (value == null || value.getCfgId() <= 0) {
                continue;
            }
            bean = FuwenProtos.FuWenBean.newBuilder();
            bean.setIndex(entry.getKey());
            bean.setCfgId(value.getCfgId());
            fuWenInfo.addFuwen(bean);
        }
        fuWenInfoMessage.setProto(fuWenInfo.build());
        MessageUtil.sendMsg(fuWenInfoMessage, role.getId());
    }

    @Override
    public void equipHuoLong(Role role) {
        Hero hero = role.getHero();
        if (hero == null) {
            return;
        }
        Backpack backpack = role.getBackpack();
        Storage storage = backpack.fetchStorage(BackpackConst.Place.HERO_EQUIP);
        if (storage == null) {
            return;
        }
        Map<Integer, Item> data = storage.getData();
        Item item = data.getOrDefault(EquipConst.EquipIndex.HUO_LONG.getCls(), null);
        if (item != null) {
            return;
        }
        int itemId = GlobalUtil.getGlobalInt(GameConst.GlobalId.HUO_LONG_FIRST_ID);
        Item buildItem = ItemUtil.create(itemId, 1, LogAction.FUWEN, role);
        int[][] useParam = buildItem.findItemConfig().getUseParam();
        if (useParam != null && useParam[0] != null && useParam[0].length > 0) {
            int huoLongValue = useParam[0][0];
            hero.setHuoLongValue(huoLongValue);
        }

        ResBackpackChangedMessage msg = new ResBackpackChangedMessage();
        BackPackProtos.ResBackpackChanged.Builder backpackChange = BackPackProtos.ResBackpackChanged.newBuilder();
        backpackChange.setAction(LogAction.FUWEN.getCode());
        msg.setProto(backpackChange.build());

        List<ItemChange> itemChanges = new ArrayList<>();
        itemChanges.add(storage.update(EquipConst.EquipIndex.HUO_LONG.getCls(), buildItem, LogAction.FUWEN.getCode()));
        BackPackProtos.BackpackChangeBean.Builder bean = BackPackProtos.BackpackChangeBean.newBuilder();
        bean.setWhere(BackpackConst.Place.HERO_EQUIP.getWhere());

        BackPackProtos.BackpackGridBean.Builder gridBean = BackPackProtos.BackpackGridBean.newBuilder();
        gridBean.setIndex(EquipConst.EquipIndex.HUO_LONG.getCls());
        gridBean.setItem(ItemUtil.packCommonItemBean(buildItem));
        bean.getChangesList().add(gridBean.build());
        backpackChange.getChangedItemsList().add(bean.build());
        msg.setProto(backpackChange.build());
        role.updateBackpack(itemChanges);
        role.updateBackpack(backpack, msg, LogAction.FUWEN);
    }

    @Override
    public void reqFuWenCompose(Role role, int itemId) {
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
        if (itemConfig == null) {
            return;
        }
        FuWenComposeConfig composeConfig = ConfigDataManager.getInstance().getById(FuWenComposeConfig.class, itemId);
        if (composeConfig == null) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(itemId, 1);
        stash.decrease(composeConfig.getCost());
        boolean commit = stash.commit(role, LogAction.FUWEN);
        if (!commit) {
            return;
        }
        ResFuWenComposeMessage msg = new ResFuWenComposeMessage();
        msg.setProto(FuwenProtos.ResFuWenCompose.newBuilder()
                .setItemId(itemId)
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }
}
