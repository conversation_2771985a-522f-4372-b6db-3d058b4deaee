package com.sh.game.script.dunPaiShengJi;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.dunPaiShengJi.ResDunPaiInfoMessage;
import com.sh.game.common.communication.msg.system.dunPaiShengJi.bean.SpecialUpBean;
import com.sh.game.common.config.cache.SdshengjiConfigCache;
import com.sh.game.common.config.model.AppearanceConfig;
import com.sh.game.common.config.model.SdshengjieConfig;
import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.SpecialUpConst;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.common.util.condition.impl.MapConditionValidator;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.game.system.dunPaiShengJi.entity.RoleDunPai;
import com.sh.game.system.dunPaiShengJi.script.IDunPaiUpScript;
import com.sh.game.system.touying.RoleLineUpManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Script
@Slf4j
public class DunPaiUpScript implements
        IDunPaiUpScript {

    @Override
    public void reqSpecialUp(Role role, int type) {
        SdshengjiConfigCache cache = ConfigCacheManager.getInstance().getCache(SdshengjiConfigCache.class);
        List<SdshengjieConfig> configList = cache.findConfigByType(type);
        if (configList == null) {
            log.error("特殊升阶系统,类型错误,角色:{},昵称:{},类型:{}", role.getRoleId(), role.getName(), type);
            return;
        }
        RoleDunPai roleDunPai = role.getSpecialUp().computeIfAbsent(type, v -> new RoleDunPai());
        if (roleDunPai.getStageId() <= 0) {
            int inItCfgId = SpecialUpConst.findInItCfgId(type);
            roleDunPai.setStageId(inItCfgId);
        }
        SdshengjieConfig config = ConfigDataManager.getInstance().getById(SdshengjieConfig.class, roleDunPai.getStageId());
        if (config == null) {
            log.error("特殊升阶系统,找不到配置表,角色:{},昵称:{},配置id:{}", role.getRoleId(), role.getName(), roleDunPai.getStageId());
            return;
        }
        SdshengjieConfig nextConfig = ConfigDataManager.getInstance().getById(SdshengjieConfig.class, config.getNextId());
        if (nextConfig == null) {
            return;
        }
        if (!ConditionUtil.validate(role, nextConfig.getConditions())) {
            return;
        }

        //玩家没有穿戴盾牌无法升级盾牌
        Item item = null;
        if (nextConfig.getPos() > 0 && type == SpecialUpConst.UpType.DUNPAI_TYPE) {
            //盾牌升级查找身上盾牌
            Backpack backpack = role.getBackpack();
            BackpackConst.Place equipPlace = RoleLineUpManager.getInstance().getUseEquip(role);
            Storage storage = backpack.fetchStorage(equipPlace);
            item = storage.fetch(nextConfig.getPos());
            if (item == null) {
                return;
            }
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(nextConfig.getCost());
        if (!stash.commit(role, LogAction.SPECIAL_UP_COST)) {
            return;
        }
        roleDunPai.setStageId(nextConfig.getId());
        DataCenter.updateData(role);

        AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, nextConfig.getFashion());
        if (appearanceConfig != null) {
            AppearanceManager.getInstance().onUnlock(role, appearanceConfig, null);
            AppearanceManager.getInstance().reqWear(role, appearanceConfig.getId());
        }
        if (item != null && nextConfig.getItemId() > 0) {
            BackpackStash upStash = new BackpackStash(role);
            upStash.update(item, item1 -> item1.setCfgId(nextConfig.getItemId()));
            upStash.commit(role, LogAction.SPECIAL_DUNPAI_UP);
        }

        log.info("特殊升阶系统,升级成功,角色:{},昵称:{},类型:{},升级后id:{}", role.getRoleId(), role.getName(), type, roleDunPai.getStageId());
        sendMsg(role);
    }

    /**
     * 初始化等级
     * @param role
     */
    private void verifyInItId(Role role) {
        SdshengjiConfigCache cache = ConfigCacheManager.getInstance().getCache(SdshengjiConfigCache.class);
        List<Integer> typeList = cache.findType();
        for (Integer type : typeList) {
            RoleDunPai roleDunPai = role.getSpecialUp().computeIfAbsent(type, v -> new RoleDunPai());
            if (roleDunPai.getStageId() > 0) {
                continue;
            }
            int inItCfgId = SpecialUpConst.findInItCfgId(type);
            SdshengjieConfig config = ConfigDataManager.getInstance().getById(SdshengjieConfig.class, inItCfgId);
            if (config == null) {
                continue;
            }
            SdshengjieConfig nextConfig = ConfigDataManager.getInstance().getById(SdshengjieConfig.class, config.getNextId());
            if (nextConfig == null) {
                continue;
            }
            if (!ConditionUtil.validate(role, nextConfig.getConditions())) {
                continue;
            }
            roleDunPai.setStageId(inItCfgId);
            DataCenter.updateData(role);
            log.info("特殊升阶系统,初始化等级成功,角色:{},昵称:{},升级类型:{},初始化id:{}", role.getRoleId(), role.getName(), type, inItCfgId);
        }
    }

    private void sendMsg(Role role) {
        ResDunPaiInfoMessage msg = new ResDunPaiInfoMessage();
        List<SpecialUpBean> dunPaiBean = new ArrayList<>();
        for (Map.Entry<Integer, RoleDunPai> entry : role.getSpecialUp().entrySet()) {
            RoleDunPai roleDunPai = entry.getValue();
            SpecialUpBean bean = new SpecialUpBean();
            bean.setType(entry.getKey());
            bean.setCfgId(roleDunPai.getStageId());
            dunPaiBean.add(bean);
        }
        msg.setDunPaiBean(dunPaiBean);
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void reqSpecialUpInfo(Role role) {
        verifyInItId(role);
        sendMsg(role);
    }
}
