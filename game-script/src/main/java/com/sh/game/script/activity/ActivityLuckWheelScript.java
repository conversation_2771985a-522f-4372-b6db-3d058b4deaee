package com.sh.game.script.activity;

import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.data.DataCenter;
import com.sh.game.script.activity.abc.AbstractActivityLuckWheelScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.RoleLuckWheelActivity;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2022-03-02
 **/
@Script
@Slf4j
public class ActivityLuckWheelScript extends AbstractActivityLuckWheelScript {
    @Override
    public int getType() {
        return ActivityConst.FIRST_LUCK_WHEEL;
    }

    @Override
    protected int findrechargeProgress(Role role, int activityId) {
        RoleLuckWheelActivity roleLuckWheelActivity = find(role.getRoleId());
        return roleLuckWheelActivity.getRechargeProgress();
    }

    @Override
    protected int findUsedCount(Role role, int activityId) {
        RoleLuckWheelActivity roleLuckWheelActivity = find(role.getRoleId());
        return roleLuckWheelActivity.getRaffleCount();
    }

    @Override
    protected void addUsedCount(Role role) {
        RoleLuckWheelActivity roleLuckWheelActivity = find(role.getRoleId());
        roleLuckWheelActivity.setRaffleCount(roleLuckWheelActivity.getRaffleCount() + 1);
        DataCenter.updateData(roleLuckWheelActivity);
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        if (isNotInActivity(role)) {
            return;
        }
        RoleLuckWheelActivity roleLuckWheelActivity = find(role.getRoleId());
        roleLuckWheelActivity.setRechargeProgress(roleLuckWheelActivity.getRechargeProgress() + rechargeConfig.getCount());
        DataCenter.updateData(roleLuckWheelActivity);
        reqInfo(role, getType());

        int activityID = getCurrentActivityID(role);
        log.info("幸运转盘-充值#玩家：{}，昵称：{}，幸运转盘活动:{}，中充值配置：{}，当前累计金额：{}", role.getRoleId(),
                role.getName(), activityID, rechargeConfig.getId(),
                roleLuckWheelActivity.getRechargeProgress());
    }

    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {
        RoleLuckWheelActivity roleLuckWheelActivity = find(role.getRoleId());
        roleLuckWheelActivity.setRaffleCount(0);
        roleLuckWheelActivity.setRechargeProgress(0);
        DataCenter.updateData(roleLuckWheelActivity);
        log.info("幸运转盘-活动开始#玩家：{}，昵称：{}，开启幸运转盘活动:{}", role.getRoleId(), role.getName(), schedule.getActivityID());
    }
}
