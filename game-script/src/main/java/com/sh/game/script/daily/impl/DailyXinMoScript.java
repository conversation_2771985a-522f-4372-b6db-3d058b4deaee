package com.sh.game.script.daily.impl;

import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.constant.MapConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.event.IEventOnDuplicateCompleteScript;
import com.sh.game.script.daily.abc.AbstractDailyTimedScript;
import com.sh.script.annotation.Script;


@Script
public class DailyXinMoScript extends AbstractDailyTimedScript implements
        IEventOnDuplicateCompleteScript {

    @Override
    public int getType() {
        return 8;
    }

    @Override
    public int onChallengeDuplicate(Role role, DuplicateConfig config) {
        return 0;
    }

    @Override
    public void onCompleteDuplicate(Role role, DuplicateConfig config, boolean success) {
        if (config.getDuplicateCate() == MapConst.DUPLICATE_TYPE.XINMO) {
            onDailyComplete(role, getType());
        }
    }
}
