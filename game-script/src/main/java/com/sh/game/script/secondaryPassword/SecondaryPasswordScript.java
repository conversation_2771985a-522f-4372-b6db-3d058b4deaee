package com.sh.game.script.secondaryPassword;

import com.sh.game.common.communication.msg.system.secondaryPassword.ResSecondaryPasswordInfoMessage;
import com.sh.game.common.communication.msg.system.secondaryPassword.ResSecondaryPasswordMessage;
import com.sh.game.common.constant.SecondPasswordConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.protos.SecondaryPasswordProtos;
import com.sh.game.system.role.entity.RoleSecondPassword;
import com.sh.game.system.role.script.IRoleOnMinuteScript;
import com.sh.game.system.secondaryPassword.script.ISecondaryPasswordScript;
import com.sh.game.system.user.NameManager;
import com.sh.script.annotation.Script;
import org.apache.commons.lang3.StringUtils;

/**
 * 二级密码
 */
@Script
public class SecondaryPasswordScript implements ISecondaryPasswordScript, IRoleOnMinuteScript, IEventOnRoleLoginScript {

    /**
     * 请求强制解锁
     *
     * @param role role
     */
    @Override
    public void reqForceUnlock(Role role) {
        RoleSecondPassword secondPassword = role.getSecondPassword();
        // 未设置密码
        if (secondPassword.getInstall() != SecondPasswordConst.INSTALL) {
            return;
        }
        int now = TimeUtil.getNowOfSeconds();
        if (secondPassword.getForceUnlockTime() != 0 && now < secondPassword.getForceUnlockTime()) {
            return;
        }
        secondPassword.setForceUnlockTime(now + SecondPasswordConst.LIMIT_THREE_DAYS);

        DataCenter.updateData(role);

        ResSecondaryPasswordMessage msg = new ResSecondaryPasswordMessage();
        msg.setProto(SecondaryPasswordProtos.ResSecondaryPassword.newBuilder()
                .setInstall(secondPassword.getInstall())
                .setIsLock(secondPassword.getIsLock())
                .setOptType(SecondPasswordConst.FORCE_UNLOCK_OPT)
                .setResult(SecondPasswordConst.OPT_SUCCESS)
                .setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES - secondPassword.getErrorCount())
                .setForceUnlockTime(secondPassword.getForceUnlockTime())
                .setResetCountTime(secondPassword.getResetTime())
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求上锁
     *
     * @param role role
     */
    @Override
    public void reqReLock(Role role) {
        RoleSecondPassword secondPassword = role.getSecondPassword();
        // 未设置密码
        if (secondPassword.getInstall() != SecondPasswordConst.INSTALL) {
            return;
        }
        // 已上锁
        if (secondPassword.getIsLock() == SecondPasswordConst.LOCK) {
            return;
        }
        secondPassword.setIsLock(SecondPasswordConst.LOCK);

        DataCenter.updateData(role);

        ResSecondaryPasswordMessage msg = new ResSecondaryPasswordMessage();
        msg.setProto(SecondaryPasswordProtos.ResSecondaryPassword.newBuilder()
                .setOptType(SecondPasswordConst.REQ_LOCK_OPT)
                .setResult(SecondPasswordConst.OPT_SUCCESS)
                .setInstall(secondPassword.getInstall())
                .setIsLock(secondPassword.getIsLock())
                .setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES - secondPassword.getErrorCount())
                .setForceUnlockTime(secondPassword.getForceUnlockTime())
                .setResetCountTime(secondPassword.getResetTime())
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求二级密码信息
     *
     * @param role role
     */
    @Override
    public void reqSecondaryPasswordInfo(Role role, int reqType) {
        ResSecondaryPasswordInfoMessage msg = new ResSecondaryPasswordInfoMessage();
        msg.setProto(SecondaryPasswordProtos.ResSecondaryPasswordInfo.newBuilder()
                .setInstall(role.getSecondPassword().getInstall())
                .setIsLock(role.getSecondPassword().getIsLock())
                .setLastTimes(Math.max(SecondPasswordConst.MAX_ERROR_TIMES - role.getSecondPassword().getErrorCount(), 0))
                .setForceUnlockTime(role.getSecondPassword().getForceUnlockTime())
                .setResetCountTime(role.getSecondPassword().getResetTime())
                .setReqType(reqType)
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求设置二级密码
     *
     * @param role         role
     * @param password     密码
     * @param surePassword 确认密码
     */
    @Override
    public void reqSetPassword(Role role, String password, String surePassword) {
        RoleSecondPassword secondPassword = role.getSecondPassword();
        // 已设置
        if (role.getLevel() < SecondPasswordConst.LIMIT_LEVEL || secondPassword.getInstall() == SecondPasswordConst.INSTALL) {
            return;
        }
        // 密码合法性校验
        if (!isValidPassword(password) || !isValidPassword(surePassword)) {
            return;
        }
        if (!password.equals(surePassword)) {
            return;
        }
        secondPassword.setPassword(password);
        secondPassword.setInstall(SecondPasswordConst.INSTALL);
        DataCenter.updateData(role);
        ResSecondaryPasswordMessage msg = new ResSecondaryPasswordMessage();
        msg.setProto(SecondaryPasswordProtos.ResSecondaryPassword.newBuilder()
                .setOptType(SecondPasswordConst.SET_PASSWORD_OPT)
                .setResult(SecondPasswordConst.OPT_SUCCESS)
                .setInstall(SecondPasswordConst.INSTALL)
                .setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES)
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求解锁
     *
     * @param role     role
     * @param password 密码
     */
    @Override
    public void reqUnlock(Role role, String password) {
        RoleSecondPassword secondPassword = role.getSecondPassword();
        // 未设置密码
        if (secondPassword.getInstall() != SecondPasswordConst.INSTALL) {
            return;
        }
        // 已解锁
        if (secondPassword.getIsLock() == SecondPasswordConst.UNLOCK) {
            return;
        }
        if (!isValidPassword(password)) {
            return;
        }
        if (secondPassword.getErrorCount() == SecondPasswordConst.MAX_ERROR_TIMES) {
            return;
        }
        int now = TimeUtil.getNowOfSeconds();
        if (secondPassword.getResetTime() != 0 && now < secondPassword.getResetTime()) {
            return;
        }
        ResSecondaryPasswordMessage msg = new ResSecondaryPasswordMessage();
        SecondaryPasswordProtos.ResSecondaryPassword.Builder protoBuilder = SecondaryPasswordProtos.ResSecondaryPassword.newBuilder();
        protoBuilder.setOptType(SecondPasswordConst.REQ_UNLOCK_OPT);
        protoBuilder.setInstall(secondPassword.getInstall());
        protoBuilder.setForceUnlockTime(secondPassword.getForceUnlockTime());
        if (secondPassword.getPassword().equals(password)) {
            secondPassword.setIsLock(SecondPasswordConst.UNLOCK);
            secondPassword.setErrorCount(0);
            secondPassword.setErrorTime(0);
            DataCenter.updateData(role);

            protoBuilder.setResult(SecondPasswordConst.OPT_SUCCESS);
            protoBuilder.setIsLock(secondPassword.getIsLock());
            protoBuilder.setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES);
            protoBuilder.setResetCountTime(secondPassword.getResetTime());
            msg.setProto(protoBuilder.build());
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        // 首次错误
//        String content = ConfigDataManager.getInstance().getById(TipsConfig.class, CommonTips.脚本_).getContent();
        TipUtil.show(role, CommonTips.脚本_密码错误);
        protoBuilder.setResult(SecondPasswordConst.OPT_FAIL);
        protoBuilder.setIsLock(secondPassword.getIsLock());
        if (secondPassword.getErrorCount() == 0) {
            secondPassword.setErrorTime(now);
            secondPassword.setErrorCount(1);
            DataCenter.updateData(role);

            protoBuilder.setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES - secondPassword.getErrorCount());
            protoBuilder.setResetCountTime(secondPassword.getResetTime());
            msg.setProto(protoBuilder.build());
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        // 错误距首次错误时间一小时以内
        if (now - secondPassword.getErrorTime() <= SecondPasswordConst.LIMIT_ONE_HOUR) {
            int errorCount = secondPassword.getErrorCount() + 1;
            secondPassword.setErrorCount(errorCount);
            // 错误次数到上限
            if (errorCount == SecondPasswordConst.MAX_ERROR_TIMES) {
                secondPassword.setErrorTime(0);
                secondPassword.setResetTime(now + SecondPasswordConst.LIMIT_ONE_HOUR);
                DataCenter.updateData(role);

                protoBuilder.setLastTimes(0);
                protoBuilder.setResetCountTime(now + SecondPasswordConst.LIMIT_ONE_HOUR);
                msg.setProto(protoBuilder.build());
                MessageUtil.sendMsg(msg, role.getId());
                return;
            }
            protoBuilder.setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES - secondPassword.getErrorCount());
            protoBuilder.setResetCountTime(secondPassword.getResetTime());
            DataCenter.updateData(role);
            msg.setProto(protoBuilder.build());
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        secondPassword.setErrorTime(now);
        secondPassword.setErrorCount(1);
        protoBuilder.setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES - secondPassword.getErrorCount());
        protoBuilder.setResetCountTime(secondPassword.getResetTime());
        DataCenter.updateData(role);
        msg.setProto(protoBuilder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 请求修改密码
     *
     * @param role         role
     * @param oldPassword  旧密码
     * @param newPassword  新密码
     * @param surePassword 确认密码
     */
    @Override
    public void reqUpdatePassword(Role role, String oldPassword, String newPassword, String surePassword) {
        RoleSecondPassword secondPassword = role.getSecondPassword();
        if (secondPassword.getInstall() != SecondPasswordConst.INSTALL) {
            return;
        }
        if (!isValidPassword(oldPassword) || !isValidPassword(newPassword) || !isValidPassword(surePassword)) {
            return;
        }
        if (!newPassword.equals(surePassword)) {
            return;
        }
        if (oldPassword.equals(newPassword)) {
            return;
        }
        if (secondPassword.getErrorCount() == SecondPasswordConst.MAX_ERROR_TIMES) {
            return;
        }
        int now = TimeUtil.getNowOfSeconds();
        if (secondPassword.getForceUnlockTime() != 0 && now < secondPassword.getForceUnlockTime()) {
            return;
        }
        ResSecondaryPasswordMessage msg = new ResSecondaryPasswordMessage();
        SecondaryPasswordProtos.ResSecondaryPassword.Builder protoBuilder = SecondaryPasswordProtos.ResSecondaryPassword.newBuilder();
        protoBuilder.setOptType(SecondPasswordConst.UPDATE_PASSWORD_OPT);
        protoBuilder.setInstall(secondPassword.getInstall());
        protoBuilder.setForceUnlockTime(secondPassword.getForceUnlockTime());
        if (secondPassword.getPassword().equals(oldPassword)) {
            secondPassword.setPassword(newPassword);
            secondPassword.setIsLock(SecondPasswordConst.LOCK);
            secondPassword.setErrorTime(0);
            secondPassword.setErrorCount(0);
            protoBuilder.setResult(SecondPasswordConst.OPT_SUCCESS);
            protoBuilder.setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES);
            protoBuilder.setIsLock(secondPassword.getIsLock());
            protoBuilder.setResetCountTime(secondPassword.getResetTime());
            DataCenter.updateData(role);
            msg.setProto(protoBuilder.build());
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        // 首次错误
        TipUtil.show(role, CommonTips.脚本_密码错误);
        protoBuilder.setResult(SecondPasswordConst.OPT_FAIL);
        protoBuilder.setIsLock(secondPassword.getIsLock());
        if (secondPassword.getErrorTime() == 0) {
            secondPassword.setErrorTime(now);
            secondPassword.setErrorCount(1);
            protoBuilder.setResetCountTime(secondPassword.getResetTime());
            protoBuilder.setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES - secondPassword.getErrorCount());
            DataCenter.updateData(role);
            msg.setProto(protoBuilder.build());
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        // 错误距首次错误时间一小时以内
        if (now - secondPassword.getErrorTime() <= SecondPasswordConst.LIMIT_ONE_HOUR) {
            int errorCount = secondPassword.getErrorCount() + 1;
            secondPassword.setErrorCount(errorCount);
            if (errorCount == SecondPasswordConst.MAX_ERROR_TIMES) {
                secondPassword.setResetTime(now + SecondPasswordConst.LIMIT_ONE_HOUR);
                secondPassword.setErrorTime(0);
                protoBuilder.setLastTimes(0);
                protoBuilder.setResetCountTime(now + SecondPasswordConst.LIMIT_ONE_HOUR);
                DataCenter.updateData(role);
                msg.setProto(protoBuilder.build());
                MessageUtil.sendMsg(msg, role.getId());
                return;
            }
            protoBuilder.setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES - secondPassword.getErrorCount());
            protoBuilder.setResetCountTime(secondPassword.getResetTime());
            DataCenter.updateData(role);
            msg.setProto(protoBuilder.build());
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        secondPassword.setErrorTime(now);
        secondPassword.setErrorCount(1);
        protoBuilder.setLastTimes(SecondPasswordConst.MAX_ERROR_TIMES - secondPassword.getErrorCount());
        protoBuilder.setResetCountTime(secondPassword.getResetTime());
        DataCenter.updateData(role);
        msg.setProto(protoBuilder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public boolean secondPasswordCheck(Role role) {
        RoleSecondPassword secondPassword = role.getSecondPassword();
        return secondPassword.getInstall() == SecondPasswordConst.INSTALL && secondPassword.getIsLock() == SecondPasswordConst.LOCK;
    }

    @Override
    public String getSecondPasswordByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        long rid = NameManager.getInstance().getRidByName(name);
        Role role = DataCenter.getRole(rid);
        if (role == null) {
            return null;
        }
        return role.getSecondPassword().getPassword();
    }

    /**
     * 密码合法性校验
     *
     * @param pwd 密码
     * @return
     */
    private boolean isValidPassword(String pwd) {
        if (StringUtils.isBlank(pwd) || pwd.length() != 4) {
            return false;
        }
        return pwd.matches("[0-9]+");
    }

    /**
     * 刷新次数和限制时间
     *
     * @param role role
     */
    @Override
    public void onRoleMinute(Role role) {
        RoleSecondPassword secondPassword = role.getSecondPassword();
        int now = TimeUtil.getNowOfSeconds();
        boolean flag = false;
        // 距首次错误时间超过一小时，重置次数
        if (secondPassword.getResetTime() == 0) {
            if (secondPassword.getErrorTime() != 0 && now > secondPassword.getErrorTime() + SecondPasswordConst.LIMIT_ONE_HOUR) {
                secondPassword.setErrorTime(0);
                secondPassword.setErrorCount(0);
                flag = true;
            }
        }
        if (secondPassword.getResetTime() != 0 && now > secondPassword.getResetTime()) {
            secondPassword.setErrorTime(0);
            secondPassword.setErrorCount(0);
            secondPassword.setResetTime(0);
            flag = true;
        }

        // 强制解密到期
        if (secondPassword.getForceUnlockTime() != 0 && now > secondPassword.getForceUnlockTime()) {
            secondPassword.setPassword(null);
            secondPassword.setInstall(0);
            secondPassword.setIsLock(0);
            secondPassword.setForceUnlockTime(0);
            flag = true;
        }
        if (flag) {
            DataCenter.updateData(role);
        }
    }

    /**
     * 登录重新上锁
     *
     * @param role role
     */
    @Override
    public void onRoleLogin(Role role) {
        int now = TimeUtil.getNowOfSeconds();
        RoleSecondPassword secondPassword = role.getSecondPassword();
        // 强制解锁到时间
        if (secondPassword.getForceUnlockTime() != 0 && now > secondPassword.getForceUnlockTime()) {
            secondPassword.setPassword(null);
            secondPassword.setInstall(0);
            secondPassword.setIsLock(0);
            secondPassword.setForceUnlockTime(0);
            DataCenter.updateData(role);
        }
        if (secondPassword.getInstall() == SecondPasswordConst.INSTALL && secondPassword.getIsLock() == SecondPasswordConst.UNLOCK) {
            secondPassword.setIsLock(SecondPasswordConst.LOCK);
            DataCenter.updateData(role);
        }
        reqSecondaryPasswordInfo(role, 0);
    }
}
