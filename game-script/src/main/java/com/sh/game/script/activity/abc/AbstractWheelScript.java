package com.sh.game.script.activity.abc;

import com.google.common.collect.Lists;
import com.sh.common.config.ConfigCacheManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.abc.bean.ItemBriefBean;
import com.sh.game.common.communication.msg.system.activity.ResWheelInfoMessage;
import com.sh.game.common.communication.msg.system.activity.ResWheelRaffleMessage;
import com.sh.game.common.config.cache.ActivityWheelCache;
import com.sh.game.common.config.model.ActivityWheelConfig;
import com.sh.game.common.config.model.ActivityWheelRewardConfig;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.ActionConst;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnActivityStatistic;
import com.sh.game.event.IEventOnRoleBackpackUpdateScript;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.event.IEventOnRoleRechargedScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.ActivityWheelData;
import com.sh.game.system.activity.entity.role.RoleActivityWheel;
import com.sh.game.system.activity.script.IActivityWheelScript;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 转盘活动
 *
 * <AUTHOR> Chen
 * @date 2022/07/25 13:18
 */
@Slf4j
public abstract class AbstractWheelScript extends AbstractActivityScript implements
        IActivityWheelScript,
        IEventOnActivityStatistic,
        IEventOnRoleRechargedScript,
        IEventOnRoleBackpackUpdateScript,
        IEventOnRoleMidnightScript {

    /**
     * 根据roleId获取积分转盘活动数据
     *
     * @param roleId 角色id
     * @return RoleActivityLuckWheel 角色转盘活动数据
     */
    @Override
    public RoleActivityWheel find(long roleId) {
        RoleActivityWheel roleActivityWheel = DataCenter.get(RoleActivityWheel.class, roleId);
        if (roleActivityWheel == null) {
            roleActivityWheel = new RoleActivityWheel();
            roleActivityWheel.setId(roleId);
            DataCenter.insertData(roleActivityWheel, true);
        }
        return roleActivityWheel;
    }

    /**
     * 请求积分转盘信息
     *
     * @param role 角色
     */
    @Override
    public void reqInfo(Role role) {
        RoleActivityWheel roleActivityWheel = find(role.getRoleId());
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        int activityId = schedule.getActivityID();
        ActivityWheelData wheelData = roleActivityWheel.getWheelMap().getOrDefault(activityId, new ActivityWheelData());
        ResWheelInfoMessage msg = new ResWheelInfoMessage();
        ActivityProtos.ResWheelInfo.Builder protoBuilder = ActivityProtos.ResWheelInfo.newBuilder();
        protoBuilder.setActivityId(activityId);
        protoBuilder.setRechargeProgress(wheelData.getRechargeProgress());
        protoBuilder.setScore(wheelData.getScore());
        protoBuilder.addAllScoreList(wheelData.getScoreList());
        protoBuilder.setTotalScore(wheelData.getTotalScore());
        protoBuilder.setDailyRecharge(wheelData.getDailyRecharge());
        protoBuilder.setRaffleCount(wheelData.getRaffleCount());
        wheelData.getRewardList().forEach(v -> protoBuilder.addRewardList(AbcProtos.ItemBriefBean.newBuilder()
                .setItemId(v.getFirst())
                .setItemCount(v.getSecond())
                .build()));
        protoBuilder.setClearTime(wheelData.getDay());
        protoBuilder.setWave(wheelData.getWave());
        msg.setProto(protoBuilder.build());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 请求积分转盘抽奖
     *
     * @param role          角色
     * @param activityId    活动id
     */
    @Override
    public void wheelRaffle(Role role, int activityId) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        if (activityId != schedule.getActivityID()) {
            return;
        }
        RoleActivityWheel roleActivityWheel = find(role.getRoleId());
        Map<Integer, ActivityWheelData> wheelMap = roleActivityWheel.getWheelMap();
        ActivityWheelData wheelData = wheelMap.getOrDefault(activityId, new ActivityWheelData());
        ActivityWheelCache cache = ConfigCacheManager.getInstance().getCache(ActivityWheelCache.class);

        int limit = cache.getLimit(activityId);
        if (limit > 0 && wheelData.getWave() >= limit) {
            log.error("积分转盘-领奖-次数不足, roleId: {} , roleName: {} , activityId: {} , activityType: {} , wave: {} , limit: {}", role.getRoleId(), role.getName(), activityId, getType(), wheelData.getWave(), limit);
            return;
        }

        //抽奖次数
        int count = wheelData.getRewardList().size();
        ActivityWheelRewardConfig wheelRewardConfig = cache.getWheelRewardConfig(activityId, count + 1).stream()
                .filter(v -> ConditionUtil.validate(role, v.getCondition()))
                .findFirst().orElse(null);
        if (wheelRewardConfig == null) {
            log.error("积分转盘-领奖-配置不存在, roleId: {} , roleName: {} , activityId: {} , activityType: {} , count: {}", role.getRoleId(), role.getName(), activityId, getType(), count);
            return;
        }

        if (!ConditionUtil.validate(role, wheelRewardConfig.getCondition())) {
            log.error("积分转盘-领奖-条件不通过, roleId: {} , roleName: {} , activityId: {} , activityType: {} , configId: {}", role.getRoleId(), role.getName(), activityId, getType(), wheelRewardConfig.getId());
            return;
        }

        int initScore = wheelData.getScore();
        if (initScore < wheelRewardConfig.getCost()) {
            log.error("积分转盘-领奖-积分不足, roleId: {} , roleName: {} , activityId: {} , activityType: {} , configId: {} , score: {} , cost: {}", role.getRoleId(), role.getName(), activityId, getType(), wheelRewardConfig.getId(), initScore, wheelRewardConfig.getCost());
            return;
        }

        List<Integer> weight = new ArrayList<>(wheelRewardConfig.getReward());
        List<int[]> reward = new ArrayList<>(wheelRewardConfig.getRewardshow());
        if (weight.size() != reward.size()) {
            log.error("积分转盘-领奖-权重或奖励配置错误, roleId: {} , roleName: {} , activityId: {} , activityType: {} , configId: {}", role.getRoleId(), role.getName(), activityId, getType(), wheelRewardConfig.getId());
            return;
        }
        Item item = randomItem(weight, reward, wheelData.getRewardList());
        if (item == null) {
            log.error("积分转盘-领奖-奖励配置错误, roleId: {} , roleName: {} , activityId: {} , activityType: {} , configId: {}", role.getRoleId(), role.getName(), activityId, getType(), wheelRewardConfig.getId());
            return;
        }

        //抽完后数据重置
        wheelData.getRewardList().add(new TwoTuple<>(item.getCfgId(), (int) item.getCount()));
        if (count + 1 >= cache.getWheelRewardCount(activityId, wheelRewardConfig.getRound())) {
            wheelData.setWave(wheelData.getWave() + 1);
            //最后一次要展示，不清空
            if (limit > 0 && wheelData.getWave() < limit) {
                wheelData.getRewardList().clear();
            }
        }

        wheelData.setActivityId(activityId);
        wheelData.setScore(initScore - wheelRewardConfig.getCost());
        wheelData.setRaffleCount(wheelData.getRaffleCount() + 1);
        wheelMap.put(activityId, wheelData);
        DataCenter.updateData(roleActivityWheel);

        BackpackStash stash = new BackpackStash(role);
        stash.increase(item);
        if (!stash.commit(role, LogAction.WHEEL_REWARD)) {
            log.info("积分转盘-领奖-背包已满通过邮件发送, roleId: {} , roleName: {} , activityId: {} , activityType: {} , configId: {}, itemConfigId: {} , itemCount: {}", role.getRoleId(), role.getName(), activityId, getType(), wheelRewardConfig.getId(), item.getCfgId(), item.getCount());
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }

        log.info("积分转盘-领奖-领取成功, roleId: {} , roleName: {} , activityId: {} , activityType: {} , configId: {}, itemConfigId: {} , itemCount: {}", role.getRoleId(), role.getName(), activityId, getType(), wheelRewardConfig.getId(), item.getCfgId(), item.getCount());

        ResWheelRaffleMessage msg = new ResWheelRaffleMessage();
        ActivityProtos.ResWheelRaffle proto = ActivityProtos.ResWheelRaffle.newBuilder()
                .setActivityId(activityId)
                .setReward(AbcProtos.ItemBriefBean.newBuilder()
                        .setItemId(item.getCfgId())
                        .setItemCount((int) item.getCount())
                        .build())
                .build();
        msg.setProto(proto);
        MessageUtil.sendMsg(msg, role.getRoleId());
        
        sendAnnounce(role, wheelRewardConfig, item);
    }

    /**
     * 发送公告
     *
     * @param role                  角色
     * @param wheelRewardConfig     转盘奖励配置
     * @param item                  道具
     */
    protected abstract void sendAnnounce(Role role, ActivityWheelRewardConfig wheelRewardConfig, Item item);

    /**
     * 根据权重随机道具
     * 权重和道具列表长度必须相等且不为空
     *
     * @param weight        权重列表
     * @param reward        道具列表
     * @param rewardList    已经获得的奖励
     * @return Item
     */
    protected Item randomItem(List<Integer> weight, List<int[]> reward, List<TwoTuple<Integer, Integer>> rewardList) {
        List<TwoTuple<Integer, Integer>> itemList = new ArrayList<>();
        for (int[] ints : reward) {
            if (ints.length < 2) {
                continue;
            }
            itemList.add(new TwoTuple<>(ints[0], ints[1]));
        }
        List<Integer> removeIndexList = new ArrayList<>();
        rewardList.forEach(v -> {
            Integer itemConfigId = v.getFirst();
            Integer count = v.getSecond();
            int index = itemList.indexOf(new TwoTuple<>(itemConfigId, count));
            if (index >= 0) {
                removeIndexList.add(index);
            }
        });
        //用以list倒序删除防止异常
        Collections.sort(removeIndexList);
        Collections.reverse(removeIndexList);
        removeIndexList.forEach(index -> {
            weight.remove(index.intValue());
            reward.remove(index.intValue());
        });
        int index = RandomUtil.randomIndexByProb(weight);
        if (index == -1) {
            log.error("积分转盘-随机道具-权重配置错误: \nweight: {} \nreward: {}\nrewardList: {}", weight, reward, rewardList);
            return null;
        }
        int[] ints = reward.get(index);
        if (ints.length < 2) {
            return null;
        }
        return ItemUtil.create(ints[0], ints[1], LogAction.WHEEL_REWARD);
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        int activityId = schedule.getActivityID();
        ActivityWheelCache cache = ConfigCacheManager.getInstance().getCache(ActivityWheelCache.class);
        int sourceType = cache.getSourceType(activityId);
        RoleActivityWheel roleActivityWheel = find(role.getRoleId());
        Map<Integer, ActivityWheelData> wheelMap = roleActivityWheel.getWheelMap();
        if (sourceType != ActivityConst.WheelSourceType.RECHARGE_SCORE) {
            return;
        }
        ActivityWheelData wheelData = wheelMap.getOrDefault(activityId, new ActivityWheelData());
        wheelData.setActivityId(activityId);
        int initDailyRecharge = wheelData.getDailyRecharge();
        wheelData.setDailyRecharge(initDailyRecharge + rechargeConfig.getCount());
        wheelData.setRechargeProgress(wheelData.getRechargeProgress() + rechargeConfig.getCount());

        List<ActivityWheelConfig> wheelConfigList = cache.getWheelConfigList(activityId);
        for (ActivityWheelConfig wheelConfig : wheelConfigList) {
            if (wheelConfig == null) {
                continue;
            }
            if (wheelData.getDailyRecharge() < wheelConfig.getRecharge()) {
                continue;
            }
            if (wheelData.getDailyScoreList().contains(wheelConfig.getId())) {
                continue;
            }
            Map<Integer, Integer> countMap = wheelData.getScoreList().stream().collect(Collectors.toMap(e -> e, e -> 1, Integer::sum));
            int count = countMap.getOrDefault(wheelConfig.getId(), 0);
            if (count >= wheelConfig.getCostMax()) {
                continue;
            }
            wheelData.getDailyScoreList().add(wheelConfig.getId());
            wheelData.getScoreList().add(wheelConfig.getId());
            int initScore = wheelData.getScore();
            wheelData.setScore(initScore + wheelConfig.getScore());
            wheelData.setTotalScore(wheelData.getTotalScore() + wheelConfig.getScore());
            log.info("积分转盘-充值获得积分, roleId: {} , roleName: {} , activityId: {} , activityType: {} ,  , score: {} -> {} , 领取挡位id: {} , 今日充值: {} -> {}", role.getRoleId(), role.getName(), activityId, getType(), initScore, wheelData.getScore(), wheelConfig.getId(), initDailyRecharge, wheelData.getDailyRecharge());
        }
        wheelMap.put(activityId, wheelData);
        DataCenter.updateData(roleActivityWheel);
        reqInfo(role);
    }

    @Override
    public void onRoleBackpackUpdate(Role role, List<ItemChange> changes) {
        if (isNotInActivity(role)) {
            return;
        }
        ActivitySchedule schedule = getAvailableSchedule(role);
        int activityId = schedule.getActivityID();
        ActivityWheelCache cache = ConfigCacheManager.getInstance().getCache(ActivityWheelCache.class);
        int sourceType = cache.getSourceType(activityId);
        if (sourceType != ActivityConst.WheelSourceType.CONSUME_ITEM) {
            return;
        }

        RoleActivityWheel roleActivityWheel = find(role.getRoleId());
        Map<Integer, ActivityWheelData> wheelMap = roleActivityWheel.getWheelMap();
        ActivityWheelData wheelData = wheelMap.getOrDefault(activityId, new ActivityWheelData());
        //先整合防止拆分后消耗导致不计数问题

        //积分是否改变
        boolean flag = false;

        changes = mergeChange(changes);
        for (ItemChange itemChange : changes) {
            if (!ActionConst.WHEEL_SCORE_GAIN_ACTIONS.contains(itemChange.getLogAction())) {
                continue;
            }

            Item oldItem = itemChange.getOItem();
            Item newItem = itemChange.getNItem();
            //是否有道具消耗
            if (oldItem == null) {
                continue;
            }
            if (newItem != null && (newItem.getCfgId() != oldItem.getCfgId() || newItem.getCount() >= oldItem.getCount())) {
                continue;
            }
            ActivityWheelConfig wheelConfig = cache.getWheelConfig(activityId, oldItem.getCfgId());
            if (wheelConfig == null) {
                continue;
            }
            flag = true;
            int costNum = (int) (oldItem.getCount() - (newItem == null ? 0 : newItem.getCount()));
            Integer num = wheelConfig.getCostFrom().get(1);
            int addScore = costNum / num;
            int initScore = wheelData.getScore();
            wheelData.setScore(initScore + addScore);
            wheelData.setTotalScore(wheelData.getTotalScore() + addScore);
            wheelData.getScoreList().add(wheelConfig.getId());
            wheelData.getDailyScoreList().add(wheelConfig.getId());
            log.info("积分转盘-消耗道具获得积分, roleId: {} , roleName: {} , activityId: {} , activityType: {} , score: {} -> {} , 领取挡位id: {}", role.getRoleId(), role.getName(), activityId, getType(), initScore, wheelData.getScore(), wheelConfig.getId());
        }

        if (flag) {
            wheelData.setActivityId(activityId);
            wheelMap.put(activityId, wheelData);
            DataCenter.updateData(roleActivityWheel);
            reqInfo(role);
        }
    }

    /**
     * 相同消耗cfgId的道具整合
     *
     * @param changes 道具变化列表
     * @return List<ItemChange> 整合后道具变化列表
     */
    protected List<ItemChange> mergeChange(List<ItemChange> changes) {
        Map<Integer, ItemChange> changeMap = new HashMap<>();
        for (ItemChange itemChange : changes) {
            if (!ActionConst.WHEEL_SCORE_GAIN_ACTIONS.contains(itemChange.getLogAction())) {
                continue;
            }
            if (itemChange.getOItem() == null) {
                continue;
            }
            ItemChange change = changeMap.get(itemChange.getOItem().getCfgId());
            if (change == null) {
                change = itemChange;
            } else {
                Item oldItem = change.getOItem();
                Item newItem = change.getNItem();
                if (itemChange.getOItem() != null) {
                    if (oldItem == null) {
                        change.setOItem(ItemUtil.copy(itemChange.getOItem()));
                    } else {
                        oldItem.setCount(oldItem.getCount() + itemChange.getOItem().getCount());
                    }
                }
                if (itemChange.getNItem() != null) {
                    if (newItem == null) {
                        change.setOItem(ItemUtil.copy(itemChange.getNItem()));
                    } else {
                        newItem.setCount(newItem.getCount() + itemChange.getNItem().getCount());
                    }
                }
            }
            changeMap.put(itemChange.getOItem().getCfgId(), change);
        }
        return Lists.newArrayList(changeMap.values());
    }

    /**
     * 初始化活动数据
     *
     * @param schedule  活动
     * @param role      角色
     */
    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {
        RoleActivityWheel roleActivityWheel = find(role.getRoleId());
        ActivityWheelData activityWheelData = new ActivityWheelData();
        activityWheelData.setActivityId(schedule.getActivityID());
        ActivityWheelCache cache = ConfigCacheManager.getInstance().getCache(ActivityWheelCache.class);
        int clearDay = cache.getClearDay(schedule.getActivityID());
        //按玩家活动开启天数计算
        activityWheelData.setDay(TimeUtil.dayZeroSecondsFromNow() + clearDay * (int) TimeUtil.ONE_DAY_IN_SECONDS);
        if (clearPersonalData()) {
            roleActivityWheel.getWheelMap().put(schedule.getActivityID(), activityWheelData);
        } else {
            roleActivityWheel.getWheelMap().putIfAbsent(schedule.getActivityID(), activityWheelData);
        }
        DataCenter.updateData(roleActivityWheel);
    }

    @Override
    public void onRoleMidnight(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        int activityId = schedule.getActivityID();
        RoleActivityWheel roleActivityWheel = find(role.getRoleId());
        ActivityWheelData wheelData = roleActivityWheel.getWheelMap().getOrDefault(activityId, new ActivityWheelData());
        wheelData.setActivityId(activityId);
        wheelData.setDay(TimeUtil.dayZeroSecondsFromTime(wheelData.getDay()));
        wheelData.setDailyRecharge(0);
        wheelData.getDailyScoreList().clear();

        ActivityWheelCache cache = ConfigCacheManager.getInstance().getCache(ActivityWheelCache.class);
        int clearDay = cache.getClearDay(activityId);
        int now = TimeUtil.getNowOfSeconds();
        if (clearDay > 0 && now >= wheelData.getDay()) {
            wheelData.setDay(wheelData.getDay() + clearDay * (int) TimeUtil.ONE_DAY_IN_SECONDS);
            wheelData.setRechargeProgress(0);
            wheelData.setRaffleCount(0);
            wheelData.getRewardList().clear();
            wheelData.getScoreList().clear();
            wheelData.setScore(0);
            wheelData.setTotalScore(0);
            wheelData.setWave(0);
            log.info("积分转盘-午夜事件-重置数据,roleId:{},roleName:{},clearDay:{},time:{}>{}", role.getRoleId(), role.getName(), clearDay, now, wheelData.getDay());
        }
        roleActivityWheel.getWheelMap().put(activityId, wheelData);
        DataCenter.updateData(roleActivityWheel);
        reqInfo(role);
    }
}
