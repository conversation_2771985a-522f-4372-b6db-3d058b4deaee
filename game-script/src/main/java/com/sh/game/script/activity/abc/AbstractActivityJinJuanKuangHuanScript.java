package com.sh.game.script.activity.abc;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.JinJuanKuangHuanConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.event.IEventOnActivityStatistic;
import com.sh.game.event.IEventOnMapDestroy;
import com.sh.game.event.IEventScheduleUpdateOnMidnightScript;
import com.sh.game.scene.MapProxy;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.system.activity.ActivityManager;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.script.IActivityJinJuanKuangHuanScript;
import com.sh.game.system.teleport.TeleportManager;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public abstract class AbstractActivityJinJuanKuangHuanScript extends AbstractActivityScript implements
        IActivityJinJuanKuangHuanScript,
        IEventScheduleUpdateOnMidnightScript,
        IEventOnActivityStatistic,
        IEventOnMapDestroy {

    @Override
    public void reqIntoKuangHuanMap(Role role, int cfgId) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        JinJuanKuangHuanConfig config = ConfigDataManager.getInstance().getById(JinJuanKuangHuanConfig.class, cfgId);
        if (config == null) {
            return;
        }
        if (config.getActivityId() != schedule.getActivityID()) {
            return;
        }
        if (!ConditionUtil.validate(role, config.getCondition())) {
            return;
        }
        MapProxy map = MapProxyManager.getInstance().getMap(config.getMap());
        if (map == null) {
            MapProxyManager.getInstance().alloc(config.getMap());
            map = MapProxyManager.getInstance().getMap(config.getMap());
            if (map == null) {
                return;
            }
        }
        int[] born = config.getBorn();
        if (born.length != 2) {
            return;
        }

        //传送
        TeleportManager.getInstance().teleport(role, map.getCfgId(), map.getId(), born[0], born[1], 0);
        log.info("狂欢活动{},请求传送进入副本,角色:{},昵称:{},地图id:{}", schedule.getActivityID(), role.getRoleId(), role.getName(), config.getMap());
    }

    @Override
    protected void onScheduleBegin(ActivitySchedule schedule) {
//        List<JinJuanKuangHuanConfig> configList = ConfigDataManager.getInstance().getList(JinJuanKuangHuanConfig.class);
//        for (JinJuanKuangHuanConfig config : configList) {
//            MapProxyManager.getInstance().alloc(config.getMap());
//        }
    }

    @Override
    public void scheduleUpdateOnMidnight() {
        ActivitySchedule schedule = ActivityManager.getInstance().getAvailable(getType());
        if (schedule == null) {
            return;
        }
        //踢人
        List<JinJuanKuangHuanConfig> configList = ConfigDataManager.getInstance().getList(JinJuanKuangHuanConfig.class);
        for (JinJuanKuangHuanConfig config : configList) {
            if (config.getActivityId() != schedule.getActivityID()) {
                continue;
            }

            MapProxy map = MapProxyManager.getInstance().getMap(config.getMap());
            if (map != null) {
                map.destroy();
            }
            MapProxyManager.getInstance().alloc(config.getMap());
        }
    }

    /**
     * 活动结束
     *
     * @param schedule
     */
    @Override
    protected void onScheduleEnd(ActivitySchedule schedule) {
        //踢人
        List<JinJuanKuangHuanConfig> configList = ConfigDataManager.getInstance().getList(JinJuanKuangHuanConfig.class);
        for (JinJuanKuangHuanConfig config : configList) {
            if (config.getActivityId() != schedule.getActivityID()) {
                continue;
            }

            MapProxy map = MapProxyManager.getInstance().getMap(config.getMap());
            if (map == null) {
                continue;
            }
            map.kickPlayer();
            map.destroy();
        }
    }

    @Override
    public void onMapDestroy(int mapCfgId, long mapId) {
        ActivitySchedule schedule = getAvailableSchedule(null);
        if (schedule == null) {
            return;
        }
        List<JinJuanKuangHuanConfig> configList = ConfigDataManager.getInstance().getList(JinJuanKuangHuanConfig.class);
        for (JinJuanKuangHuanConfig config : configList) {
            if (config.getActivityId() != schedule.getActivityID()) {
                continue;
            }
            if (mapCfgId != config.getMap()) {
                continue;
            }
            MapProxyManager.getInstance().alloc(config.getMap());
            log.info("金券狂欢-重置地图:{} {}", schedule.getActivityID(), mapCfgId);
        }
    }
}
