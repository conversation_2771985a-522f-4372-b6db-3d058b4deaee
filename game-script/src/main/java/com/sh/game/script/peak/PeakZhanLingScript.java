package com.sh.game.script.peak;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.system.peak.ResPeakZhanLingInfoMessage;
import com.sh.game.common.communication.notice.logic.player.DFLSJiangJinUpdateNotice;
import com.sh.game.common.communication.notice.logic.player.DFLSSeasonInfoNotice;
import com.sh.game.common.communication.notice.logic.player.DFLSSeasonSyncNotice;
import com.sh.game.common.config.cache.PeakCache;
import com.sh.game.common.config.model.*;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.TaskConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.sys.CommonData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.env.AppContext;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnGoalUpdateScript;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.event.IEventScheduleUpdateOnMidnightScript;
import com.sh.game.protos.PeakProtos;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.match.GameMatcherManager;
import com.sh.game.system.peak.entity.RolePeakZhanLing;
import com.sh.game.system.peak.script.IPeakZhanLingScript;
import com.sh.game.system.task.entity.TaskRecord;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 巅峰联赛战令
 *
 * <AUTHOR> Chen
 * @date 2022/11/21 9:39
 */
@Slf4j
@Script(order = 1)
public class PeakZhanLingScript implements
        IPeakZhanLingScript,
        IEventOnRoleMidnightScript,
        IEventOnGoalUpdateScript,
        IEventScheduleUpdateOnMidnightScript {

    @Override
    public RolePeakZhanLing find(long roleId) {
        RolePeakZhanLing rolePeakZhanLing = DataCenter.get(RolePeakZhanLing.class, roleId);
        if (rolePeakZhanLing == null) {
            rolePeakZhanLing = new RolePeakZhanLing();
            rolePeakZhanLing.setId(roleId);
            DataCenter.insertData(rolePeakZhanLing, true);
        }
        return rolePeakZhanLing;
    }


    /**
     * 请求战令信息
     *
     * @param role 角色
     */
    @Override
    public void reqInfo(Role role) {
        int season = getSeason(role);
        if (season < 0) {
            log.error("巅峰联赛战令-届数错误,role:{} {},season:{}", role.getRoleId(), role.getName(), season);
            return;
        }
        RolePeakZhanLing rolePeakZhanLing = find(role.getRoleId());
        PeakCache cache = ConfigCacheManager.getInstance().getCache(PeakCache.class);

        ResPeakZhanLingInfoMessage msg = new ResPeakZhanLingInfoMessage();
        PeakProtos.ResPeakZhanLingInfo.Builder peakZhanLingInfo = PeakProtos.ResPeakZhanLingInfo.newBuilder();

        TwoTuple<PeakZhanLingLevelConfig, Integer> tuple = cache.getLevelConfig(season, rolePeakZhanLing.getExp());
        if(tuple == null || tuple.getFirst() == null) {
            log.error("巅峰联赛战令-该届数等级配置不存在,role:{} {},season:{},exp:{}", role.getRoleId(), role.getName(), season, rolePeakZhanLing.getExp());
            return;
        }
        peakZhanLingInfo.setLevelId(tuple.getFirst().getId());
        peakZhanLingInfo.setExp(tuple.getSecond());

        peakZhanLingInfo.setBigPrize(rolePeakZhanLing.getBigPrize().getOrDefault(season, 0));
        peakZhanLingInfo.addAllRewardList(rolePeakZhanLing.getRewardList());

        for (List<TaskRecord> recordList : rolePeakZhanLing.getTaskMap().values()) {
            peakZhanLingInfo.addAllTaskBean(recordList.stream()
                    .map(TaskRecord::toDataBean)
                    .collect(Collectors.toList())
            );
        }

        peakZhanLingInfo.setBuyExpCount(rolePeakZhanLing.getBuyExpCount());
        msg.setProto(peakZhanLingInfo.build());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    /**
     * 请求购买战令进度
     *
     * @param role 角色
     */
    @Override
    public void reqBuyExp(Role role) {
        if (isDisabled()) {
            log.error("巅峰联赛战令-购买经验-不在时间内,role:{} {}, day:{}", role.getRoleId(), role.getName(), LocalDate.now().getDayOfMonth());
            return;
        }
        List<Integer> params = GlobalUtil.findJingHaoList(GameConst.GlobalId.DFLS_ZHAN_LING_BUY_EXP);
        if (params == null || params.size() < 4) {
            log.error("巅峰联赛战令-购买经验-配置参数750030错误,role:{} {},params:{}", role.getRoleId(), role.getName(), GlobalUtil.getGlobalValue(GameConst.GlobalId.DFLS_ZHAN_LING_BUY_EXP));
            return;
        }
        int itemConfigId = params.get(0);
        int count = params.get(1);
        int addExp = params.get(2);
        int limit = params.get(3);

        int day = GlobalUtil.getGlobalInt(GameConst.GlobalId.DFLS_ZHAN_LING_BUY_EXP_LIMIT_DAY);
        int currentDay = TimeUtil.getCurrentDayOfMonth();
        RolePeakZhanLing rolePeakZhanLing = find(role.getRoleId());
        int buyExpCount = rolePeakZhanLing.getBuyExpCount();
        if (currentDay < day && buyExpCount >= limit) {
            log.error("巅峰联赛战令-购买经验-次数上限,role:{} {},count:{}>={},day:{}<{}", role.getRoleId(), role.getName(), buyExpCount, limit, currentDay, day);
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(itemConfigId, count);
        if (!stash.commit(role, LogAction.DFLS_ZHAN_LING_BUY_EXP_COST)) {
            log.error("巅峰联赛战令-购买经验-道具消耗失败,role:{} {},itemId:{},count:{}", role.getRoleId(), role.getName(), itemConfigId, count);
            return;
        }

        rolePeakZhanLing.setBuyExpCount(buyExpCount + 1);
        DataCenter.updateData(rolePeakZhanLing);

        log.info("巅峰联赛战令-购买经验-成功,role:{} {},itemId:{},count:{},addExp:{}", role.getRoleId(), role.getName(), itemConfigId, count, addExp);
        addExp(role, addExp);
        addJackpot(role, count);
    }

    /**
     * 请求巅峰战令任务领取完成
     *
     * @param role     角色
     * @param configId 任务配置id
     */
    @Override
    public void reqCompleteTask(Role role, int configId) {
        if (isDisabled()) {
            log.error("巅峰联赛战令-完成任务-不在时间内,role:{} {}, configId:{}, day:{}", role.getRoleId(), role.getName(), configId, LocalDate.now().getDayOfMonth());
            return;
        }
        PeakZhanLingTaskConfig taskConfig = ConfigDataManager.getInstance().getById(PeakZhanLingTaskConfig.class, configId);
        if (taskConfig == null) {
            log.error("巅峰联赛战令-完成任务-配置不存在,role:{} {},configId:{}", role.getRoleId(), role.getName(), configId);
            return;
        }

        RolePeakZhanLing rolePeakZhanLing = find(role.getRoleId());
        List<TaskRecord> recordList = rolePeakZhanLing.getTaskMap().get(taskConfig.getType());
        if (recordList == null || recordList.isEmpty()) {
            log.error("巅峰联赛战令-完成任务-任务不存在,role:{} {},configId:{}", role.getRoleId(), role.getName(), configId);
            return;
        }

        TaskRecord taskRecord  = null;
        for (TaskRecord record : recordList) {
            if (record.getTaskId() == configId) {
                taskRecord = record;
                break;
            }
        }

        if (taskRecord == null || taskRecord.getState() != TaskConst.State.HAS_COMPLETE) {
            log.error("巅峰联赛战令-完成任务-任务状态异常,role:{} {},configId:{},record:{}", role.getRoleId(), role.getName(), configId, JSON.toJSONString(taskRecord));
            return;
        }
        taskRecord.setState(TaskConst.State.HAS_SUBMIT);
        DataCenter.updateData(rolePeakZhanLing);

        log.info("巅峰联赛战令-完成任务-成功,role:{} {},configId:{}", role.getRoleId(), role.getName(), configId);
        addExp(role, taskConfig.getExp());
    }

    /**
     * 请求领取战令奖励
     *
     * @param role         角色
     * @param configIdList 奖励配置id
     */
    @Override
    public void reqReward(Role role, List<Integer> configIdList) {
        if (configIdList == null || configIdList.isEmpty()) {
            log.error("巅峰联赛战令-领奖-配置id列表为空,role:{} {},configIdList:{}", role.getRoleId(), role.getName(), configIdList);
            return;
        }

        int season = getSeason(role);
        if (season <= 0) {
            log.error("巅峰联赛战令-领奖-届数错误,role:{} {},configIdList:{},season:{}", role.getRoleId(), role.getName(), configIdList, season);
            return;
        }

        RolePeakZhanLing rolePeakZhanLing = find(role.getRoleId());
        Set<Integer> configIdSet = new HashSet<>(configIdList);
        List<int[]> itemList = new ArrayList<>();
        List<Integer> rewardList = rolePeakZhanLing.getRewardList();
        PeakCache cache = ConfigCacheManager.getInstance().getCache(PeakCache.class);

        int guessingTicketSeason = 0;
        for (Integer configId : configIdSet) {
            PeakZhanLingLevelConfig config = ConfigDataManager.getInstance().getById(PeakZhanLingLevelConfig.class, configId);
            if (config == null) {
                log.error("巅峰联赛战令-领奖-配置不存在,role:{} {},configId:{},configIdList:{}", role.getRoleId(), role.getName(), configId, configIdList);
                return;
            }

            if (rewardList.contains(configId)) {
                log.error("巅峰联赛战令-领奖-重复领取,role:{} {},configId:{},configIdList:{}", role.getRoleId(), role.getName(), configId, configIdList);
                return;
            }

            if (config.getNum() != season) {
                log.error("巅峰联赛战令-领奖-届数错误,role:{} {},configId:{},configIdList:{},season:{},num:{}", role.getRoleId(), role.getName(), configId, configIdList, season, config.getNum());
                return;
            }

            int minExp = cache.getMinExp(season, configId);
            if (rolePeakZhanLing.getExp() < minExp) {
                log.error("巅峰联赛战令-领奖-经验不足,role:{} {},configId:{},configIdList:{},exp:{}<{}", role.getRoleId(), role.getName(), configId, configIdList, rolePeakZhanLing.getExp(), minExp);
                return;
            }

            if (config.getJingcaiquan() > 0 && guessingTicketSeason == 0) {
                guessingTicketSeason = config.getNum();
            }

            itemList.addAll(config.getReward());
        }
        rolePeakZhanLing.getRewardList().addAll(configIdSet);
        DataCenter.updateData(rolePeakZhanLing);

        if (guessingTicketSeason > 0) {
            CommonData commonData = SysDataProvider.get(CommonData.class);
            commonData.getPeakGuessingTicketRoleMap().compute(guessingTicketSeason, (k, v) -> {
               if (v == null) {
                   v = new ArrayList<>();
               }
               v.add(role.getRoleId());
               return v;
            });
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(itemList);
        if (!stash.commit(role, LogAction.DFLS_ZHAN_LING_REWARD)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }

        log.info("巅峰联赛战令-领奖-成功,role:{} {},configIdList:{},exp:{},已领取:{}", role.getRoleId(), role.getName(), configIdList, rolePeakZhanLing.getExp(), rolePeakZhanLing.getRewardList());
        reqInfo(role);
    }

    /**
     * 增加战令经验
     *
     * @param role   角色
     * @param addExp 增加的经验
     */
    @Override
    public void addExp(Role role, int addExp) {
        if (isDisabled()) {
            log.error("巅峰联赛战令-增加经验-不在时间内,role:{} {}, addExp:{}, day:{}", role.getRoleId(), role.getName(), addExp, LocalDate.now().getDayOfMonth());
            return;
        }

        int season = getSeason(role);
        if (season <= 0) {
            log.error("巅峰联赛战令-增加经验-届数错误,role:{} {},addExp:{},season:{}", role.getRoleId(), role.getName(), addExp, season);
            return;
        }

        RolePeakZhanLing rolePeakZhanLing = find(role.getRoleId());
        PeakCache cache = ConfigCacheManager.getInstance().getCache(PeakCache.class);

        int currentExp = rolePeakZhanLing.getExp();
        int finalExp = currentExp + addExp;

        rolePeakZhanLing.setExp(finalExp);
        DataCenter.updateData(rolePeakZhanLing);

        List<PeakZhanLingLevelConfig> levelConfigList = cache.getLevelConfigList(season, currentExp, finalExp);
        for (PeakZhanLingLevelConfig levelConfig : levelConfigList) {
            if (levelConfig.getMail() <= 0) {
                continue;
            }
            MailManager.getInstance().sendMail(role.getRoleId(), levelConfig.getMail(), null);
        }
        log.info("巅峰联赛战令-增加经验-成功,role:{} {},exp:{}->{},addExp:{},level:{}", role.getRoleId(), role.getName(), currentExp, finalExp, addExp, levelConfigList.stream().map(PeakZhanLingLevelConfig::getId).collect(Collectors.toList()));
        reqInfo(role);

    }

    /**
     * 打开巅峰战令宝箱
     *
     * @param role      角色
     * @param peakBoxId 宝箱配置id
     */
    @Override
    public void openBox(Role role, int peakBoxId) {
        RolePeakZhanLing rolePeakZhanLing = this.find(role.getRoleId());
        PeakZhanLingBoxConfig boxConfig = ConfigDataManager.getInstance().getById(PeakZhanLingBoxConfig.class, peakBoxId);
        if (boxConfig == null) {
            log.error("巅峰联赛战令-开宝箱-宝箱配置不存在,role:{} {},peakBoxId:{}", role.getRoleId(), role.getName(), peakBoxId);
            return;
        }
        int season = boxConfig.getNum();
        Map<Integer, Integer> bigPrizeMap = rolePeakZhanLing.getBigPrize();
        int bigPrizeCount = bigPrizeMap.getOrDefault(season, 0);
        Map<Integer, Integer> boxMap = rolePeakZhanLing.getBoxMap();
        int boxCount = boxMap.getOrDefault(season, 0);
        int index = 1;
        List<int[]> reward = new ArrayList<>();

        if (bigPrizeCount < 1 && boxCount + 1 >= boxConfig.getPrarm()) {
            //保底
            index = boxConfig.getBdReward() - 1;
            reward.addAll(boxConfig.getReward());
        } else {
            //随机
            List<Integer> probList = new ArrayList<>();
            int bigPrizeIndex = 0;
            for (int[] ints : boxConfig.getReward()) {
                if (ints.length < 3) {
                    log.error("巅峰联赛战令-开宝箱-参数错误1,role:{} {},peakBoxId:{},index:{}", role.getRoleId(), role.getName(), peakBoxId, index);
                    return;
                }
                bigPrizeIndex++;
                if (bigPrizeCount > 0 && bigPrizeIndex == boxConfig.getBdReward()) {
                    continue;
                }
                reward.add(ints);
                probList.add(ints[2]);

            }
            index = RandomUtil.randomIndexByProb(probList);
        }

        if (index < 0 || index >= reward.size()) {
            log.error("巅峰联赛战令-开宝箱-参数错误2,role:{} {},peakBoxId:{},reward:{}", role.getRoleId(), role.getName(), peakBoxId, boxConfig.getReward());
            return;
        }

        int[] ints = reward.get(index);
        if (ints.length < 3) {
            log.error("巅峰联赛战令-开宝箱-参数错误3,role:{} {},peakBoxId:{},index:{}", role.getRoleId(), role.getName(), peakBoxId, index);
            return;
        }
        int itemId = ints[0];
        int itemCount = ints[1];
        if (index == boxConfig.getBdReward() - 1) {
            bigPrizeMap.put(season, bigPrizeCount + 1);
        }

        boxMap.put(season, boxCount + 1);
        DataCenter.updateData(rolePeakZhanLing);

        log.info("巅峰联赛战令-开宝箱-成功,role:{} {},peakBoxId:{},itemId:{},itemCount:{},index:{},bigPrizeCount:{}->{},boxCount:{}->{}", role.getRoleId(), role.getName(), peakBoxId, itemId, itemCount, index, bigPrizeCount, bigPrizeMap.getOrDefault(season, 0), boxCount, boxMap.getOrDefault(season, 0));

        BackpackStash stash = new BackpackStash(role);
        stash.increase(itemId, itemCount);
        if (!stash.commit(role, LogAction.DFLS_ZHAN_LING_OPEN_BOX_REWARD)) {
            stash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }
    }


    /**
     * 接受任务
     *
     * @param role              角色
     * @param taskConfigList    可接受的任务列表
     */
    private void acceptTask(Role role, List<PeakZhanLingTaskConfig> taskConfigList) {
        RolePeakZhanLing rolePeakZhanLing = find(role.getRoleId());
        Map<Integer, List<TaskRecord>> taskMap = rolePeakZhanLing.getTaskMap();
        for (PeakZhanLingTaskConfig config : taskConfigList) {
            TaskRecord taskRecord = GoalManager.getInstance().createTaskRecord(role, config.getId(),
                    config.getGlos(), false, TaskConst.State.HAS_ACCEPT);
            taskMap.putIfAbsent(config.getType(), new ArrayList<>());
            taskMap.get(config.getType()).add(taskRecord);
        }
        DataCenter.updateData(rolePeakZhanLing);
        reqInfo(role);
    }

    /**
     * 注入奖池
     *
     * @param role      角色
     * @param count     充值额度
     */
    private void addJackpot(Role role, int count) {
        if (isJackpot()) {
            return;
        }
        DFLSJiangJinUpdateNotice notice = new DFLSJiangJinUpdateNotice();
        int rate = GlobalUtil.getGlobalInt(GameConst.GlobalId.DFLS_ZHAN_LING_JIANG_JIN_RATE);
        int jiangJin = (int) (count * rate / 10000D);
        notice.setCount(jiangJin);
        int dflsGroup = GameContext.getOption().getDflsGroup();
        notice.setDflsGroup(dflsGroup);
        notice.setHostId(GameContext.getHostId());
        notice.setPlatformId(GameContext.getOption().getPlatformId());
        GameMatcherManager.getInstance().sendNotice(notice);
        log.info("巅峰联赛战令-奖金池更新-notice发送成功,role:{} {},count:{},jiangjin:{},dflsGroup:{}", role.getRoleId(), role.getName(), count, jiangJin, dflsGroup);
    }

    @Override
    public void onRoleMidnight(Role role) {
        if (isDisabled()) {
            return;
        }

        int season = getSeason(role);
        if (season <= 0) {
            log.error("巅峰联赛战令-零点事件-届数错误,role:{} {},season:{}", role.getRoleId(), role.getName(), season);
            return;
        }

        PeakCache cache = ConfigCacheManager.getInstance().getCache(PeakCache.class);

        RolePeakZhanLing rolePeakZhanLing = find(role.getRoleId());
        rolePeakZhanLing.setBuyExpCount(0);
        int startTime = rolePeakZhanLing.getStartTime();
        int now = TimeUtil.getNowOfSeconds();

        //日常
        List<TaskRecord> dailyTaskRecordList = rolePeakZhanLing.getTaskMap().get(TaskConst.PeakZhanLingTaskType.DAILY);
        if (dailyTaskRecordList != null) {
            dailyTaskRecordList.clear();
        }
        //可接取的任务
        List<PeakZhanLingTaskConfig> canAcceptTaskList = new ArrayList<>(cache.findTypeTaskList(season, TaskConst.PeakZhanLingTaskType.DAILY));

        //周常
        boolean isWeekly = false;
        List<TaskRecord> weeklyTaskRecordList = rolePeakZhanLing.getTaskMap().get(TaskConst.PeakZhanLingTaskType.WEEKLY);
        if (weeklyTaskRecordList == null || weeklyTaskRecordList.isEmpty() || !TimeUtil.isSameWeek(weeklyTaskRecordList.get(0).getStartTime() * 1000L)) {
            isWeekly = true;
        }

        if (rolePeakZhanLing.getSeason() < season && season > 1) {
            log.info("巅峰联赛战令-每月重置战令数据,role:{} {},roleSeason:{},sysSeason:{}", role.getRoleId(), role.getName(), rolePeakZhanLing.getSeason(), season);
            rolePeakZhanLing.setExp(0);
            rolePeakZhanLing.setStartTime(now);
            rolePeakZhanLing.setSeason(season);
            isWeekly = true;
        }

        if (isWeekly) {
            if (weeklyTaskRecordList != null) {
                weeklyTaskRecordList.clear();
            }
            canAcceptTaskList.addAll(cache.findTypeTaskList(season, TaskConst.PeakZhanLingTaskType.WEEKLY));
        }

        DataCenter.updateData(rolePeakZhanLing);
        // 接取任务
        acceptTask(role, canAcceptTaskList);
        log.info("巅峰联赛战令-玩家零点事件,role:{} {},season:{},isWeekly:{}", role.getRoleId(), role.getName(), season, isWeekly);
    }

    /**
     * 玩家任务目标进度更新通知
     *
     * @param role      角色
     * @param goalType 目标类型
     * @param params   目标参数
     */
    @Override
    public void onGoalUpdate(Role role, int goalType, int... params) {
        if (isDisabled()) {
            return;
        }

        int season = getSeason(role);
        if (season <= 0) {
            log.error("巅峰联赛战令-目标进度更新-届数错误,role:{} {},season:{}", role.getRoleId(), role.getName(), season);
            return;
        }

        PeakCache cache = ConfigCacheManager.getInstance().getCache(PeakCache.class);
        List<Integer> collect = cache.findGoalTaskList(season, goalType);
        if (collect == null || collect.isEmpty()) {
            return;
        }
        RolePeakZhanLing rolePeakZhanLing = find(role.getRoleId());
        Map<Integer, List<TaskRecord>> taskMap = rolePeakZhanLing.getTaskMap();
        List<TaskRecord> updates = new ArrayList<>();
        for (List<TaskRecord> taskRecordList : taskMap.values()) {
            for (TaskRecord record : taskRecordList) {
                if (!collect.contains(record.getTaskId())) {
                    continue;
                }
                if (GoalManager.getInstance().doupdate(role, record, goalType, params)) {
                    updates.add(record);
                }
            }
        }
        if (updates.isEmpty()) {
            return;
        }
        DataCenter.updateData(rolePeakZhanLing);
        reqInfo(role);
    }

    /**
     * 判断巅峰联赛战令是否可用
     * 策划取消时间限制
     *
     * @return boolean true: 不可用 false: 可用
     */
    private boolean isDisabled() {
//        List<Integer> params = GlobalUtil.findShuXianList(GameConst.GlobalId.DFLS_ZHAN_LING_AVAILABLE_DAY);
//        if (params == null || params.size() < 2) {
//            log.error("巅峰联赛战令-是否可用判断-配置参数750044错误,params:{}", GlobalUtil.getGlobalValue(GameConst.GlobalId.DFLS_ZHAN_LING_AVAILABLE_DAY));
//            return true;
//        }
//
//        int start = params.get(0);
//        int end = params.get(1);
//
//        LocalDate now = LocalDate.now();
//        int dayOfMonth = now.getDayOfMonth();
//        return dayOfMonth < start || dayOfMonth > end;
        return false;
    }

    /**
     * 判断巅峰联赛战令是否可用
     * 策划取消时间限制
     *
     * @return boolean true: 不可用 false: 可用
     */
    private boolean isJackpot() {
        List<Integer> params = GlobalUtil.findShuXianList(GameConst.GlobalId.DFLS_ZHAN_LING_AVAILABLE_DAY);
        if (params == null || params.size() < 2) {
            log.error("巅峰联赛战令-是否可用注入奖金额-配置参数750044错误,params:{}", GlobalUtil.getGlobalValue(GameConst.GlobalId.DFLS_ZHAN_LING_AVAILABLE_DAY));
            return true;
        }

        int start = params.get(0);
        int end = params.get(1);

        LocalDate now = LocalDate.now();
        int dayOfMonth = now.getDayOfMonth();
        return dayOfMonth < start || dayOfMonth > end;
    }

    /**
     * 获取巅峰联赛届数
     *
     * @return int 巅峰联赛届数
     */
    private int getSeason(Role role) {
        CommonData commonData = SysDataProvider.get(CommonData.class);
        AtomicInteger season = new AtomicInteger();
        commonData.getSeasonMap().compute(GameContext.getOption().getDflsGroup(), (k, v) -> {
            if (v == null) {
                v = 0;
            }
            if (!TimeUtil.isSameMonth(commonData.getSeasonTime() * 1000L)) {
                //不是本月的数据
                season.set(v + 1);
            } else {
                if (TimeUtil.getCurrentDayOfMonth() < GlobalUtil.getGlobalInt(GameConst.GlobalId.DFLS_HAI_XUAN_SAI_OPEN_DAY)) {
                    season.set(v + 1);
                } else {
                    season.set(v);
                }
            }
            return v;
        });

        return season.get() == 0 ? 1 : season.get();
    }

    @Override
    public void scheduleUpdateOnMidnight() {
        if (isDisabled()) {
            return;
        }
        CommonData commonData = SysDataProvider.get(CommonData.class);
        if (!TimeUtil.isSameMonth(commonData.getSeasonTime() * 1000L)) {
            DFLSSeasonSyncNotice notice = new DFLSSeasonSyncNotice();
            notice.setDflsGroup(GameContext.getOption().getDflsGroup());
            notice.setSourceProcessorId(AppContext.getProcessor());
            notice.setHostId(GameContext.getHostId());
            GameMatcherManager.getInstance().sendNotice(notice);
            log.info("巅峰联赛战令-同步最新届数notice,time:{},season:{},dflsGroup:{}", commonData.getSeasonTime(), commonData.getSeasonTime(), GameContext.getOption().getDflsGroup());
        }
    }
}
