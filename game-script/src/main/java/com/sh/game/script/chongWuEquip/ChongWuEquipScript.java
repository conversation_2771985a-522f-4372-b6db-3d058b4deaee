package com.sh.game.script.chongWuEquip;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.abc.bean.RolePetQiangHuaBean;
import com.sh.game.common.communication.msg.system.chongWuEquip.ResChongWuEquipJianDingMessage;
import com.sh.game.common.communication.msg.system.chongWuEquip.ResChongWuUpSeccussMessage;
import com.sh.game.common.config.cache.RolePetJianDingConfigCache;
import com.sh.game.common.config.model.RolePetJianDingConfig;
import com.sh.game.common.config.model.RolePetQiangHuaConfig;
import com.sh.game.common.config.model.RolePetSkillConfig;
import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.RolePetQiangHuaConst;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.EquipData;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.event.IEventOnRoleBackpackUpdateScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.ChongWuEquipProtos;
import com.sh.game.system.chongWuEquip.entity.RolePetQiangHua;
import com.sh.game.system.chongWuEquip.script.IChongWuEquipScript;
import com.sh.game.system.equip.EquipManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;


@Script
@Slf4j
public class ChongWuEquipScript implements
        IChongWuEquipScript,
        IEventOnRoleBackpackUpdateScript {

    @Override
    public void reqChongWuEquipUp(Role role, int pos) {
        List<Integer> posList = GlobalUtil.findJingHaoList(GameConst.GlobalId.CHONGWU_EQUIP_POS);
        if (!posList.contains(pos)) {
            log.error("宠物装备强化系统,参数异常,角色:{},昵称:{},部位:{}", role.getRoleId(), role.getName(), pos);
            return;
        }
        List<int[]> conditions = GlobalUtil.findConditions(GameConst.GlobalId.CHONGWU_EQUIP_CONDITION);
        if (!ConditionUtil.validate(role, conditions)) {
            return;
        }
        Backpack backpack = role.getBackpack();
        Storage storage = backpack.fetchStorage(BackpackConst.Place.CHONGWU_EQUIP);
        Item item = storage.fetch(pos);
        if (item == null) {
            log.error("宠物装备强化系统,该部位未穿戴装备,角色:{},昵称:{},部位:{}", role.getRoleId(), role.getName(), pos);
            return;
        }
        RolePetQiangHua rolePetQiangHua = role.getRolePetQiangHua().computeIfAbsent(pos, k -> new RolePetQiangHua());
        if (rolePetQiangHua.getCfgId() <= 0) {
            rolePetQiangHua.setCfgId(RolePetQiangHuaConst.findInItCfgId(pos));
        }
        RolePetQiangHuaConfig config = ConfigDataManager.getInstance().getById(RolePetQiangHuaConfig.class, rolePetQiangHua.getCfgId());
        if (config == null) {
            return;
        }
        RolePetQiangHuaConfig nextConfig = ConfigDataManager.getInstance().getById(RolePetQiangHuaConfig.class, config.getNextId());
        if (nextConfig == null) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getCost());
        if (!stash.commit(role, LogAction.ROLE_PET_UP_COST)) {
            return;
        }
        rolePetQiangHua.setCfgId(nextConfig.getId());
        DataCenter.updateData(role);
        sendRolePetInfoMsg(role);
        log.info("宠物装备强化系统,强化成功,角色:{},昵称:{},部位:{},等级cfgId:{}", role.getRoleId(), role.getName(), pos, rolePetQiangHua.getCfgId());
    }

    /**
     * 返回宠物装备强化信息
     */
    private void sendRolePetInfoMsg(Role role) {
        Map<Integer, RolePetQiangHua> rolePetQiangHua = role.getRolePetQiangHua();
        ResChongWuUpSeccussMessage msg = new ResChongWuUpSeccussMessage();
        ChongWuEquipProtos.ResChongWuUpSeccuss.Builder chongWuSecccuss = ChongWuEquipProtos.ResChongWuUpSeccuss.newBuilder();
        List<AbcProtos.RolePetQiangHuaBean> posInfoList = new ArrayList<>();
        chongWuSecccuss.setSuccess(true);
        for (Map.Entry<Integer, RolePetQiangHua> entry : rolePetQiangHua.entrySet()) {
            Integer pos = entry.getKey();
            RolePetQiangHua petQiangHua = entry.getValue();
            if (petQiangHua == null) {
                continue;
            }
            AbcProtos.RolePetQiangHuaBean.Builder bean = AbcProtos.RolePetQiangHuaBean.newBuilder();
            bean.setPos(pos);
            bean.setCfgId(petQiangHua.getCfgId());
            posInfoList.add(bean.build());
        }
        chongWuSecccuss.addAllPos(posInfoList);
        msg.setProto(chongWuSecccuss.build());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void reqChongWuEquipJianDing(Role role, long equipId) {
        List<int[]> conditions = GlobalUtil.findConditions(GameConst.GlobalId.CHONGWU_EQUIP_CONDITION);
        if (!ConditionUtil.validate(role, conditions)) {
            return;
        }
        //鉴定
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByItemId(equipId, BackpackConst.Place.CHONGWU_BACKPACK, BackpackConst.Place.CHONGWU_EQUIP);
        if (item == null) {
            log.error("宠物装备强化系统,宠物装备鉴定,参数异常,角色:{},昵称:{},装备唯一id:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        if (item.getEquipData() != null && item.getEquipData().getPetEquipRate() > 0) {
            return;
        }
        RolePetJianDingConfigCache cache = ConfigCacheManager.getInstance().getCache(RolePetJianDingConfigCache.class);
        RolePetJianDingConfig config = cache.findConfigByItemId(item.getCfgId());
        if (config == null || config.getApp() == 1) {
            log.error("宠物装备强化系统,宠物装备鉴定,参数异常,角色:{},昵称:{},装备唯一id:{},配置id:{}", role.getRoleId(), role.getName(), equipId, item.getCfgId());
            return;
        }
        //随机品阶
        int quality = 0;
        if (config.getQuality() == RolePetQiangHuaConst.UnidentifiedType.TYPE) {
            List<int[]> rateProbConfig = GlobalUtil.findJingHaoAndShuXianList(GameConst.GlobalId.CHONGWU_EQUIP_RATE_PROB);
            List<Integer> rateProbList = new ArrayList<>();
            for (int[] ints : rateProbConfig) {
                if (ints.length != 2) {
                    continue;
                }
                rateProbList.add(ints[1]);
            }
            int rateProb = RandomUtil.randomIndexByProb(rateProbList);
            int[] qualityConfig = rateProbConfig.get(rateProb);
            quality = qualityConfig[0];
        }
        //随机品质
        RolePetJianDingConfig jianDingConfig = null;
        List<RolePetJianDingConfig> list = ConfigDataManager.getInstance().getList(RolePetJianDingConfig.class);
        int finalQuality = config.getQuality() == RolePetQiangHuaConst.UnidentifiedType.TYPE ? quality : config.getQuality();
        for (RolePetJianDingConfig rolePetJianDingConfig : list) {
            if (rolePetJianDingConfig.getQuality() != finalQuality
                    || rolePetJianDingConfig.getPos() != config.getPos()
                    || rolePetJianDingConfig.getApp() == 0) {
                continue;
            }
            jianDingConfig = rolePetJianDingConfig;
        }
        if (jianDingConfig == null) {
            return;
        }
        int[] rateRatio = jianDingConfig.getRatio();
        //品质值
        int qualityRate = RandomUtil.random(rateRatio[0], rateRatio[1]);

        //是否有技能
        //该品质随到技能的几率万分比
        int skillProb = 0;
        List<int[]> skillProbList = GlobalUtil.findJingHaoAndShuXianList(GameConst.GlobalId.CHONGWU_EQUIP_JIANDING_SKILL);
        for (int[] ints : skillProbList) {
            if (ints.length != 2 || ints[0] != jianDingConfig.getQuality()) {
                continue;
            }
            skillProb = ints[1];
            break;
        }
        boolean skill = RandomUtil.isGenerate(skillProb);
        //随机技能
        RolePetSkillConfig rolePetSkillConfig = null;
        if (skill) {
            rolePetSkillConfig = findProbSkill(jianDingConfig);
        }

        //扣除材料
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getCost());
        if (!stash.commit(role, LogAction.ROLE_PET_JIANDING_COST)) {
            return;
        }

        //应用
        int buff = 0;
        if (rolePetSkillConfig != null) {
            buff = rolePetSkillConfig.getBuff();
        }
        BackpackStash upStash = new BackpackStash(role);
        int finalBuff = buff;
        RolePetJianDingConfig finalJianDingConfig = jianDingConfig;
        upStash.update(item, item1 -> {
            item1.setCfgId(finalJianDingConfig.getItemid());
            if (item1.getEquipData() == null) {
                item1.setEquipData(new EquipData());
            }
            item1.getEquipData().setPetEquipRate(qualityRate);
            item1.getEquipData().setPetBuff(finalBuff);
        });
        upStash.commit(role, LogAction.ROLE_PET_JIANDING);
        //返回
        Item upItem = backpack.findItemByItemId(equipId, BackpackConst.Place.CHONGWU_BACKPACK, BackpackConst.Place.CHONGWU_EQUIP);
        ResChongWuEquipJianDingMessage msg = new ResChongWuEquipJianDingMessage();
        msg.setProto(ChongWuEquipProtos.ResChongWuEquipJianDing.newBuilder()
                .setItemBean(ItemUtil.packCommonItemBean(upItem))
                .build());
        MessageUtil.sendMsg(msg, role.getRoleId());
        log.info("宠物装备强化系统,宠物装备鉴定,鉴定成功,角色:{},昵称:{},装备唯一id:{},品质万分比:{},获得技能:{}",
                role.getRoleId(), role.getName(), upItem.getId(), upItem.getEquipData().getPetEquipRate(), upItem.getEquipData().getPetBuff());
    }

    /**
     * 随机技能
     *
     * @param jianDingConfig 当前装备的配置
     * @return 随机到的技能配置，null为未随机出技能
     */
    private RolePetSkillConfig findProbSkill(RolePetJianDingConfig jianDingConfig) {
        List<Integer> probList = new ArrayList<>();
        List<RolePetSkillConfig> configList = ConfigDataManager.getInstance().getList(RolePetSkillConfig.class);
        List<RolePetSkillConfig> probConfigList = new ArrayList<>();
        for (RolePetSkillConfig skillConfig : configList) {
            if (jianDingConfig.getQuality() != skillConfig.getQuality()) {
                continue;
            }
            probConfigList.add(skillConfig);
            probList.add(skillConfig.getPr());
        }
        int indexByProb = RandomUtil.randomIndexByProb(probList);
        return probConfigList.get(indexByProb);
    }

    @Override
    public void reqChongWuEquipXiLian(Role role, long equipId) {
        //系统开启condition
        List<int[]> conditions = GlobalUtil.findConditions(GameConst.GlobalId.CHONGWU_EQUIP_CONDITION);
        if (!ConditionUtil.validate(role, conditions)) {
            return;
        }
        //洗练
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByItemId(equipId, BackpackConst.Place.CHONGWU_BACKPACK, BackpackConst.Place.CHONGWU_EQUIP);
        if (item == null || item.getEquipData() == null) {
            log.error("宠物装备强化系统,宠物装备洗练,参数异常,角色:{},昵称:{},装备唯一id:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        RolePetJianDingConfigCache cache = ConfigCacheManager.getInstance().getCache(RolePetJianDingConfigCache.class);
        RolePetJianDingConfig config = cache.findConfigByItemId(item.getCfgId());
        if (config == null) {
            log.error("宠物装备强化系统,宠物装备洗练,参数异常,角色:{},昵称:{},装备唯一id:{},配置id:{}", role.getRoleId(), role.getName(), equipId, item.getCfgId());
            return;
        }
        int petBuff = item.getEquipData().getPetBuff();
        if (petBuff <= 0) {
            log.error("宠物装备强化系统,宠物装备洗练,当前装备未鉴定或点化出技能,角色:{},昵称:{},装备唯一id:{},配置id:{}", role.getRoleId(), role.getName(), equipId, item.getCfgId());
            return;
        }
        RolePetSkillConfig petSkillConfig = findProbSkill(config);
        if (petSkillConfig == null) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getXiLianCost());
        if (!stash.commit(role, LogAction.ROLE_PET_XILIAN_COST)) {
            return;
        }

        BackpackStash upStash = new BackpackStash(role);
        upStash.update(item, item1 -> item1.getEquipData().setPetBuff(petSkillConfig.getBuff()));
        upStash.commit(role, LogAction.ROLE_PET_XILIAN);
        log.info("宠物装备强化系统,宠物装备洗练,洗练成功,角色:{},昵称:{},装备唯一id:{},洗练技能id:{} -> {}", role.getRoleId(), role.getName(), equipId, petBuff, petSkillConfig.getBuff());
        //返回
        Item upItem = backpack.findItemByItemId(equipId, BackpackConst.Place.CHONGWU_BACKPACK, BackpackConst.Place.CHONGWU_EQUIP);
        ResChongWuEquipJianDingMessage msg = new ResChongWuEquipJianDingMessage();
        msg.setProto(ChongWuEquipProtos.ResChongWuEquipJianDing.newBuilder()
                .setItemBean(ItemUtil.packCommonItemBean(upItem))
                .build());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void reqChongWuEquipInfo(Role role) {
        sendRolePetInfoMsg(role);
    }

    @Override
    public void reqChongWuEquipDianHua(Role role, long equipId) {
        //系统开启condition
        List<int[]> conditions = GlobalUtil.findConditions(GameConst.GlobalId.CHONGWU_EQUIP_CONDITION);
        if (!ConditionUtil.validate(role, conditions)) {
            return;
        }
        //洗练
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByItemId(equipId, BackpackConst.Place.CHONGWU_BACKPACK, BackpackConst.Place.CHONGWU_EQUIP);
        if (item == null || item.getEquipData() == null) {
            log.error("宠物装备强化系统,宠物装备点化,参数异常,角色:{},昵称:{},装备唯一id:{}", role.getRoleId(), role.getName(), equipId);
            return;
        }
        RolePetJianDingConfigCache cache = ConfigCacheManager.getInstance().getCache(RolePetJianDingConfigCache.class);
        RolePetJianDingConfig config = cache.findConfigByItemId(item.getCfgId());
        if (config == null) {
            log.error("宠物装备强化系统,宠物装备点化,参数异常,角色:{},昵称:{},装备唯一id:{},配置id:{}", role.getRoleId(), role.getName(), equipId, item.getCfgId());
            return;
        }
        if (item.getEquipData().getPetBuff() > 0 || item.getEquipData().getPetEquipRate() <= 0) {
            log.error("宠物装备强化系统,宠物装备点化,当前装备已有技能,角色:{},昵称:{},装备唯一id:{},配置id:{}", role.getRoleId(), role.getName(), equipId, item.getCfgId());
            return;
        }
        RolePetSkillConfig petSkillConfig = findProbSkill(config);
        if (petSkillConfig == null) {
            return;
        }
        List<int[]> cost = GlobalUtil.findItemCost(GameConst.GlobalId.CHONGWU_EQUIP_DIANHUA_COST);
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(cost);
        if (!stash.commit(role, LogAction.ROLE_PET_XILIAN_COST)) {
            return;
        }

        BackpackStash upStash = new BackpackStash(role);
        upStash.update(item, item1 -> item1.getEquipData().setPetBuff(petSkillConfig.getBuff()));
        upStash.commit(role, LogAction.ROLE_PET_XILIAN);
        log.info("宠物装备强化系统,宠物装备点化,点化成功,角色:{},昵称:{},装备唯一id:{},洗练出配置id:{}", role.getRoleId(), role.getName(), equipId, petSkillConfig.getBuff());
        //返回
        Item upItem = backpack.findItemByItemId(equipId, BackpackConst.Place.CHONGWU_BACKPACK, BackpackConst.Place.CHONGWU_EQUIP);
        ResChongWuEquipJianDingMessage msg = new ResChongWuEquipJianDingMessage();
        msg.setProto(ChongWuEquipProtos.ResChongWuEquipJianDing.newBuilder()
                .setItemBean(ItemUtil.packCommonItemBean(upItem))
                .build());
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void onRoleBackpackUpdate(Role role, List<ItemChange> changes) {

    }
}
