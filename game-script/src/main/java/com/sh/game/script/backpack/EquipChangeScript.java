package com.sh.game.script.backpack;

import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.event.IEventOnEquipChangedScript;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.summary.SummaryManager;
import com.sh.script.IScript;
import com.sh.script.annotation.Script;
import org.apache.commons.collections.MapUtils;

import java.util.List;
import java.util.Map;


@Script
public class EquipChangeScript implements IScript, IEventOnEquipChangedScript {

    @Override
    public void onRoleShouJueEquipChanged(Role role, List<ItemChange> changes){
        for (ItemChange change : changes) {
            SummaryManager.getInstance().updateFacadeData(role.getId(), data -> {
                //顺便保存一下宠物,方便离线时查看
                data.setChongWuCfgID(role.getRoleChongWuCfgId());
                if (change.getNItem() == null) {
                    data.getShoujueEquips().remove(change.getIndex());
                } else {
                    data.getShoujueEquips().put(change.getIndex(), change.getNItem());
                }
            });
        }
    }
    @Override
    public void onRoleEquipChanged(Role role, List<ItemChange> changes) {
        for (ItemChange change : changes) {
            SummaryManager.getInstance().updateFacadeData(role.getId(), data -> {
                if (change.getNItem() == null) {
                    data.getRoleEquips().remove(change.getIndex());
                } else {
                    data.getRoleEquips().put(change.getIndex(), change.getNItem());
                }
            });

            updateEquipNotice(role, change, role.getRoleId());
        }
    }

    /**
     * 处理地图装备显示
     *
     * @param role
     * @param change
     * @param actorId
     */
    private void updateEquipNotice(Role role, ItemChange change, long actorId) {
        long uid = 0L;
        int id = 0;
        int durable = 0;
        int magicBloodStoneValue = 0;
        Item item = change.getNItem();
        if (item != null) {
            uid = item.getId();
            id = item.getCfgId();
            durable = item.eData() != null ? item.getEquipData().getDurable() : 0;
            magicBloodStoneValue = item.eData() != null ? item.getEquipData().getMagicBloodStoneValue() : 0;
        }

        int finalId = id;
        long finalUid = uid;
        int finalDurable = durable;
        int finalMagicBloodStoneValue = magicBloodStoneValue;
        role.proxyCall(proxy -> proxy.equipUpdateNotice(change.getIndex(), finalId, finalUid, finalDurable, finalMagicBloodStoneValue, actorId));
    }

    @Override
    public void onHeroEquipChanged(Hero hero, List<ItemChange> changes) {
        for (ItemChange change : changes) {
            SummaryManager.getInstance().updateFacadeData(hero.getRoleId(), data -> {
                if (change.getNItem() == null) {
                    data.getHeroEquips().remove(change.getIndex());
                } else {
                    data.getHeroEquips().put(change.getIndex(), change.getNItem());
                }
            });

            updateEquipNotice(hero.getRole(), change, hero.getId());
        }
    }

    @Override
    public void onRoleBirthChartEquipChanged(Role role, List<ItemChange> changes){
        for (ItemChange change : changes) {
            SummaryManager.getInstance().updateFacadeData(role.getId(), data -> {
                if (change.getNItem() == null) {
                    data.getBirthChartEquips().remove(change.getIndex());
                } else {
                    data.getBirthChartEquips().put(change.getIndex(), change.getNItem());
                }
            });
        }
    }
}
