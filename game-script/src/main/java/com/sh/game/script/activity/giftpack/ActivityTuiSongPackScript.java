package com.sh.game.script.activity.giftpack;

import com.sh.game.common.constant.ActivityConst;
import com.sh.game.script.activity.abc.AbstractGiftPackScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

/**
 * description: 推送礼包实现
 * date: 2024/4/23
 * author: chenbin
 */
@Slf4j
@Script(order = 1)
public class ActivityTuiSongPackScript extends AbstractGiftPackScript {
    @Override
    public int getType() {
        return ActivityConst.TUI_SONG_PACK;
    }
}
