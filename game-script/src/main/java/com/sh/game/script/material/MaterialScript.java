package com.sh.game.script.material;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.material.ResGetMaterialRewardMessage;
import com.sh.game.common.communication.msg.system.material.ResOpenMaterialInfoMessage;
import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.BoxUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleMapChanged;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.protos.MaterialProtos;
import com.sh.game.scene.MapProxy;
import com.sh.game.system.material.MaterialManager;
import com.sh.game.system.material.script.IMaterialScript;
import com.sh.script.annotation.Script;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.sh.game.common.constant.MapConst.DUPLICATE_TYPE.MATERIAL;

/**
 * 材料副本
 */
@Script
public class MaterialScript implements IMaterialScript, IEventOnRoleMidnightScript, IEventOnRoleMapChanged {

    @Override
    public void getMaterialReward(Role role, int type, int duplicateId) {
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
        if (player == null || player.getMapCfgId() != duplicateId) {
            return;
        }
        //TODO 错误依赖场景代码
        /*GameMap map = SceneManager.getInstance().getMap(player.getMapId());
        if (map == null || map.getState() != MapConst.MAP_STATE.CLOSING) {
            return;
        }*/
        Map<Long, Map<Integer, Boolean>> rewardMap = MaterialManager.getInstance().getRewardMap();
        Map<Integer, Boolean> integerBooleanMap = rewardMap.computeIfAbsent(role.getId(), k -> new HashMap<>());
        if (!integerBooleanMap.getOrDefault(duplicateId, false)) {
            return;
        }
        DuplicateConfig config = ConfigDataManager.getInstance().getById(DuplicateConfig.class, duplicateId);
        Map<Integer, Integer> materialCount = role.getRoleTask().getMaterialCount();
        if (materialCount == null || materialCount.isEmpty()) {
            materialCount = initMaterial(role);
        }
        if (config == null) return;
        int leftCount = materialCount.getOrDefault(duplicateId, 0);
        boolean materialFlag = role.getRoleTask().isMaterialFlag();
        if (leftCount <= 0 && materialFlag) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        if (type > 0) {
            //双倍领取
            if (config.getMultipleRewards() == null) {
                return;
            }
            int[] ints = config.getMultipleRewards().get(0);
            if (config.getMultipleRewards().size() <= 0 || ints.length < 4) {
                return;
            }
            stash.decrease(ints[1], ints[2]);
            stash.increase(BoxUtil.openBox(role, ints[3]));
        } else {
            //普通领取
            stash.increase(BoxUtil.openBox(role, config.getRewards()));
        }
        if (!stash.commit(role, LogAction.MATERIAL_REWARD)) {
            return;
        }
        if (materialFlag) {
            materialCount.put(duplicateId, leftCount - 1);
            role.getRoleTask().setMaterialFlag(false);
            DataCenter.updateData(role);
        }
        integerBooleanMap.put(duplicateId, false);

        ResGetMaterialRewardMessage msg = new ResGetMaterialRewardMessage();
        msg.setProto(MaterialProtos.ResGetMaterialReward.newBuilder()
                .setResult(0)
                .build());
        MessageUtil.sendMsg(msg, role.getId());
        openMaterialInfo(role);

    }

    /**
     * 初始化副本次数 不用count是因为count不好用了
     *
     * @param role
     * @return
     */
    private Map<Integer, Integer> initMaterial(Role role) {
        Map<Integer, Integer> materialCount = new HashMap<>();
        List<DuplicateConfig> list = ConfigDataManager.getInstance().getList(DuplicateConfig.class);
        List<DuplicateConfig> collect = list.stream().filter(config -> config.getDuplicateCate() == MATERIAL).collect(Collectors.toList());
        for (DuplicateConfig config : collect) {
            materialCount.put(config.getId(), config.getLimitTimes());
        }
        role.getRoleTask().setMaterialCount(materialCount);
        DataCenter.updateData(role.getRoleTask());
        return materialCount;
    }

    @Override
    public void openMaterialInfo(Role role) {
        Map<Integer, Integer> materialCount = role.getRoleTask().getMaterialCount();
        if (materialCount == null || materialCount.isEmpty()) {
            materialCount = initMaterial(role);
        }
        ResOpenMaterialInfoMessage msg = new ResOpenMaterialInfoMessage();
        MaterialProtos.ResOpenMaterialInfo.Builder openMaterialInfo = MaterialProtos.ResOpenMaterialInfo.newBuilder();
        List<MaterialProtos.MaterialCountBean> list = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : materialCount.entrySet()) {
            DuplicateConfig config = ConfigDataManager.getInstance().getById(DuplicateConfig.class, entry.getKey());
            if (config == null) continue;
            MaterialProtos.MaterialCountBean.Builder bean = MaterialProtos.MaterialCountBean.newBuilder();
            bean.setMapId(entry.getKey());
            bean.setCount(entry.getValue());
            list.add(bean.build());
        }
        openMaterialInfo.addAllMaterialCountBeans(list);
        msg.setProto(openMaterialInfo.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void onRoleMidnight(Role role) {
        initMaterial(role);
        openMaterialInfo(role);
    }

    @Override
    public void onRoleMapChanged(Role role, MapProxy mapProxy) {
        //保证每次进地图只能领奖一次
        DuplicateConfig config = ConfigDataManager.getInstance().getById(DuplicateConfig.class, mapProxy.getCfgId());
        if (config == null || config.getDuplicateCate() != MATERIAL) {
            return;
        }
        Map<Long, Map<Integer, Boolean>> rewardMap = MaterialManager.getInstance().getRewardMap();
        Map<Integer, Boolean> integerBooleanMap = rewardMap.computeIfAbsent(role.getId(), k -> new HashMap<>());
        integerBooleanMap.put(mapProxy.getCfgId(), true);
    }
}
