package com.sh.game.script.chongbang;

import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.RankConst;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.sys.CommonData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.ItemCoinUtil;
import com.sh.game.common.util.MapUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventScheduleUpdateOnMinuteScript;
import com.sh.game.script.activity.abc.AbstractChongBangActivity;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.school.entity.RoleSchoolData;
import com.sh.game.system.school.entity.RoleStudentData;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2025-03-11
 */
@Script
@Slf4j
public class UnionSchoolScript extends AbstractChongBangActivity implements IEventScheduleUpdateOnMinuteScript {

    @Override
    public int getType() {
        return ActivityConst.UNION_SCHOOL_RANK;
    }

    @Override
    protected int getGoalType() {
        return RankConst.RankScoreType.UNION_SCHOOL;
    }

    @Override
    public void reqRankInfo(Role role, int actId) {
        reqRankInfo(role, actId, 4);
    }

    @Override
    public void scheduleUpdateOnMinute() {
        ActivitySchedule schedule = getOpen();
        if (schedule == null) {
            return;
        }
        CommonData commonData = SysDataProvider.get(CommonData.class);
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        Map<Integer, Integer> chongBangUnionTime = commonData.getChongBangUnionTime();

        // 十分钟刷新一次
        // 加个Math.abs防止策划和测试跨天后出现负数
        if (Math.abs(nowOfSeconds - chongBangUnionTime.getOrDefault(getType(), 0)) < 600) {
            return;
        }
        List<Union> unions = DataCenter.getAllUnion();
        for (Union union : unions) {
            Map<Integer, Long> map = new HashMap<>();
            for (Long rid : union.getMemberInfos().keySet()) {
                Role role = DataCenter.get(Role.class, rid);
                if (role == null) {
                    continue;
                }
                RoleSchoolData roleSchool = role.findRoleSchool();
                if (roleSchool == null) {
                    continue;
                }
                for (RoleStudentData studentData : roleSchool.getStudentMap().values()) {
                    MapUtil.merge(studentData.getSecondAndFriendIncome(), map);
                }
            }
            if (!map.isEmpty()) {
                updateScore(union, getGoalType(), ItemCoinUtil.getCoinBigInteger(map));
            }
        }
        chongBangUnionTime.put(getType(), nowOfSeconds);
        DataCenter.updateData(commonData);
    }
}
