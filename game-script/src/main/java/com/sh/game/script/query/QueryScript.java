package com.sh.game.script.query;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.system.query.ResQueryBriefInfoMessage;
import com.sh.game.common.communication.msg.system.query.ResQueryListMessage;
import com.sh.game.common.communication.msg.system.query.ResQueryRoleIdMessage;
import com.sh.game.common.communication.msg.system.query.ResQueryRoleMessage;
import com.sh.game.common.communication.notice.logic.player.NewQueryRoleRetToServerNotice;
import com.sh.game.common.communication.notice.logic.player.NewQueryRoleToServerNotice;
import com.sh.game.common.communication.notice.logic.player.QueryRoleRetNotice;
import com.sh.game.common.communication.notice.scene.NewQueryRoleRetToSceneNotice;
import com.sh.game.common.communication.notice.scene.NewQueryRoleToSceneNotice;
import com.sh.game.common.config.cache.HoardCache;
import com.sh.game.common.config.model.AppearanceConfig;
import com.sh.game.common.config.model.HoardTypeConfig;
import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.entity.usr.*;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.protos.*;
import com.sh.game.remote.rpc.RPCConnection;
import com.sh.game.remote.rpc.client.ModuleClient;
import com.sh.game.server.ConnectionConst;
import com.sh.game.server.SessionManager;
import com.sh.game.system.hoard.entity.Hoard;
import com.sh.game.system.hoard.entity.HoardData;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.mount.entity.Mount;
import com.sh.game.system.query.script.IQueryScript;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.shenmo.bean.ShenMo;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.summary.entity.AppearanceData;
import com.sh.game.system.touying.RoleLineUpManager;
import com.sh.game.system.touying.entity.RoleLineUp;
import com.sh.game.system.union.UnionManager;
import com.sh.game.system.user.NameManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;


@Script
@Slf4j
public class QueryScript implements IQueryScript {

    /**
     * 通过玩家名称判断玩家不存在或者不在线
     *
     * @param role
     * @param name
     */
    @Override
    public void reqQueryRoleId(Role role, String name) {
        ResQueryRoleIdMessage msg = new ResQueryRoleIdMessage();
        QueryProtos.ResQueryRoleId.Builder protoBuilder = QueryProtos.ResQueryRoleId.newBuilder();
        protoBuilder.setName(name);
        long rid = NameManager.getInstance().getRidByName(name);
        if (rid == 0) {
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        if (!SessionManager.getInstance().isRoleOnline(rid)) {
            rid = 1;
        }
        protoBuilder.setRid(rid);
        msg.setProto(protoBuilder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqQuery(long srcId, Role target, int isHero) {
        Role source = DataCenter.getRole(srcId);

        QueryRoleRetNotice retNotice = new QueryRoleRetNotice();
        retNotice.setSrcId(srcId);
        retNotice.setSuccess(true);
        RoleSummary summary = SummaryManager.getInstance().getSummary(target.getId());
        if (summary == null) {
            retNotice.setSuccess(false);
        } else {
            retNotice.setBean(summaryToBean(summary, null));
        }

        // 如果查询的玩家存在应该是本服玩家查询，直接由自己发给自己，不存在则说明两个玩家不同服务器但是都在跨服
        if (source != null) {
            source.proxyCall(proxy -> proxy.sendNotice(retNotice));
        } else {
            target.proxyCall(proxy -> proxy.sendNotice(retNotice));
        }

    }

    /**
     * 转为显示bean
     *
     * @param summary
     * @param hero
     * @return
     */
    @Override
    public QueryProtos.QueryAvatarBean summaryToBean(RoleSummary summary, Hero hero) {
        // 获取玩家
        Role targetRole = DataCenter.getRole(summary.getId());

        QueryProtos.QueryAvatarBean.Builder bean = QueryProtos.QueryAvatarBean.newBuilder();
        bean.setRoleId(summary.getId());
        // bean.setChongWuCfgID(targetRole.getRoleChongWuCfgId());
        bean.setIp(summary.getIp());
        bean.setZhuanShengId(targetRole.getRoleAdvance().getZhuanshengId());
        bean.setRoleName(summary.getName());
        bean.setCareer(summary.getCareer());
        bean.setSex(summary.getSex());
        bean.setLevel(summary.getLevel());
        bean.setZhanYi(summary.getZhanyi());
        // bean.setFengHaoCfgId(targetRole.getFengHaoId());
        // bean.setLongHunLevel(targetRole.getWuDao().getLonghun());
        // bean.setNiePanLevel(targetRole.getWuDao().getNiepan());
        // bean.setXiaLvName(String.valueOf(xialvGuanHuanLv));
        // 技能信息
        Map<Integer, Skill> skillMap = targetRole.getRoleAdvance().getRoleSkill().getSkillMap();
        skillMap.values().forEach(skill -> {
            SkillProtos.SkillBean.Builder skillBean = SkillProtos.SkillBean.newBuilder();
            skillBean.setLevel(skill.getLevel());
            skillBean.setSkillId(skill.getSkillId());
            skillBean.setPractice(skill.getPractice());
            bean.addSkillBeanList(skillBean);
        });
        // TianshuItem tianshuItem = TianShuManager.getInstance().find(targetRole.getRoleId());
        // bean.setTianShuLevel(tianshuItem.getLevel());
        // bean.setTianShuExp(tianshuItem.getExp());
        // // 查看玩家显示元婴
        // List<AbcProtos.CommonYuanYingBean> roleYuanYing = YuanYingManager.getInstance().roleYuanYingToBean(targetRole);
        // bean.addAllYuanYings(roleYuanYing);

        bean.setHair(summary.getHair());
        bean.setUnionId(summary.getUnionId());
        bean.setUnionName(summary.getUnionName());
        Union union = UnionManager.getInstance().getUnion(targetRole);
        if (union != null) {
            bean.setUnionlevel(union.getUnionLevel());
            bean.setUnionMemberCount(union.getMemberInfos().size());
        }

        AppearanceData data = summary.getData();
        if (data != null) {
            Map<Integer, Item> allEquips = data.getRoleEquips();
            List<AbcProtos.CommonEquipBean> equipsInfo = getEquipsInfo(allEquips, hero);
            bean.addAllEquips(equipsInfo);
            bean.addAllFashions(fashionsToBean(data.getFashions()));
        }

        Attribute attribute = calFinalAttribute(targetRole);
        List<AbcProtos.CommonKeyValueLongBean> list = ItemUtil.packAttributeBean(attribute);
        bean.addAllAttribute(list);
        if (targetRole.isOnline()) {
            bean.setFightPower(attribute.calculateFightPower(targetRole.getCareer(), targetRole.getLevel()));
        } else {
            bean.setFightPower(targetRole.getFinalAttribute().calculateFightPower(targetRole.getCareer(), targetRole.getLevel()));
        }

        RoleNormal normal = targetRole.findNormal();
        RoleHuoBan huoBan = targetRole.findHuoBan();
        RoleLineUp roleLineUp = RoleLineUpManager.getInstance().getUseLineUp(targetRole);
        if (roleLineUp != null) {
            Map<Integer, Integer> shangZhenMap = roleLineUp.getPartnerMap();
            for (Map.Entry<Integer, Integer> entry : shangZhenMap.entrySet()) {
                Integer shangZhenType = entry.getKey();
                Integer huoBanId = entry.getValue();
                HuoBan ban = huoBan.getHuoBanBag().get(huoBanId);
                if (ban == null) {
                    continue;
                }
                AbcProtos.CommonKeyValueBean build = AbcProtos.CommonKeyValueBean.newBuilder().setKey(shangZhenType).setValue(huoBanId).build();
                bean.addShangZhen(build);
                HuoBanProto.HuoBanBean.Builder huoBanBuilder = HuoBanProto.HuoBanBean.newBuilder().setLevel(ban.getLevel()).setConfigId(ban.getConfigId());
                for (Map.Entry<Integer, Integer> e : ban.getSkillLevelMap().entrySet()) {
                    AbcProtos.CommonKeyValueBean keyValueBean = AbcProtos.CommonKeyValueBean.newBuilder().setKey(e.getKey()).setValue(e.getValue()).build();
                    huoBanBuilder.addSkill(keyValueBean);
                }
                bean.addHuoBan(huoBanBuilder.build());
            }

            RoleShenMo roleShenMo = targetRole.findShenMo();
            for (Integer shenMoId : roleLineUp.getShenMoList()) {
                ShenMo mo = roleShenMo.getShenMo(shenMoId);
                ShenMoProtos.ShenMoBean shenMoBean = ShenMoProtos.ShenMoBean.newBuilder().setShenMoId(mo.getConfigId()).setShenMoLevel(mo.getLevel()).build();
                bean.addShenMoBean(shenMoBean);
            }
            // 添加神魔共鸣属性
            roleShenMo.getShenMoResonanceMap().forEach((id, resonance) -> {
                AbcProtos.CommonKeyValueBean shenMoResonanceBean = AbcProtos.CommonKeyValueBean.newBuilder().setKey(resonance.getConfigId()).setValue(resonance.getLevel()).build();
                bean.addShenMoResonance(shenMoResonanceBean);
            });
            // 添加拥有神魔属性
            roleShenMo.getShenMoOwnMap().forEach((id, shenMo) -> {
                AbcProtos.CommonKeyValueBean shenMoOwnBean = AbcProtos.CommonKeyValueBean.newBuilder().setKey(shenMo.getConfigId()).setValue(shenMo.getLevel()).build();
                bean.addOwnShenMo(shenMoOwnBean);
            });

            Map<Integer, Integer> map = roleLineUp.findHoardTypeCount();
            HoardCache cache = ConfigCacheManager.getInstance().getCache(HoardCache.class);
            for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
                HoardTypeConfig typeConfig = cache.findTypeConfig(entry.getKey(), entry.getValue());
                if (typeConfig == null) {
                    continue;
                }
                bean.addHoardType(typeConfig.getId());
            }
            HoardData hoardData = normal.getHoardData();
            bean.setLevelId(hoardData.getLevelId());
            Attribute hoardAttr = new Attribute();
            for (Hoard hoard : roleLineUp.getHoardMap().values()) {
                hoardAttr.fixAdd(hoard.getAttr());
            }
            bean.addAllHoard(ItemUtil.packAttributeBean(hoardAttr));

            RoleAdvance advance = targetRole.getRoleAdvance();
            Mount mount = advance.getMount();
            bean.setUseMount(roleLineUp.getUseMount());
            bean.setMountQhCfgId(mount.getMountQhCfgId());
            bean.setUseFashion(mount.getUseMountFashion());
            int maxQuality = roleLineUp.getHoardMap().values().stream().map(Hoard::getQuality).max(Integer::compareTo).orElse(0);
            bean.setMaxHoardQuality(maxQuality);
        }

        //赚钱-点击
        normal.getRenderData().getClickIncome().forEach((id, count)-> {
            AbcProtos.CommonKeyValueLongBean.Builder clickIncomeBean = AbcProtos.CommonKeyValueLongBean.newBuilder();
            clickIncomeBean.setKey(id);
            clickIncomeBean.setValue(count);
            bean.addClickIncome(clickIncomeBean);
        });
        return bean.build();
    }

    private Attribute calFinalAttribute(Role role) {
        Attribute attribute = Attribute.copy(role.getAttribute());
        attribute.calculate(role.getCareer(), role.getLevel());
        long hp = AttributeEnum.MG_HP.getAttrValue(attribute);
        long hpMax = AttributeEnum.MG_HP_MAX.getAttrValue(attribute);
        if (hp > hpMax) {
            attribute.set(AttributeEnum.MG_HP, hpMax);
        }
        long mp = AttributeEnum.MG_MP.getAttrValue(attribute);
        long mpMax = AttributeEnum.MG_MP_MAX.getAttrValue(attribute);
        if (mp > mpMax) {
            attribute.set(AttributeEnum.MG_MP, hpMax);
        }
        return attribute;
    }

    private List<AbcProtos.CommonEquipBean> getEquipsInfo(Map<Integer, Item> allEquips, Hero hero) {
        List<AbcProtos.CommonEquipBean> outEquips = new ArrayList<>();
        AbcProtos.CommonEquipBean.Builder equipBean;
        for (Map.Entry<Integer, Item> entry : allEquips.entrySet()) {
            equipBean = AbcProtos.CommonEquipBean.newBuilder();
            equipBean.setIndex(entry.getKey());
            equipBean.setItem(ItemUtil.packCommonItemBean(entry.getValue()));
            // if (entry.getKey() == EquipConst.EquipIndex.HUO_LONG.getCls() && hero != null) {
            //     AbcProtos.CommonItemBean item = equipBean.getItem();
            //     if (item.getEquipData() == null) {
            //         item.toBuilder().setEquipData(AbcProtos.EquipDataBean.newBuilder());
            //     }
            //     AbcProtos.EquipDataBean.Builder equipData = item.getEquipData().toBuilder();
            //     equipData.setStarLevel(hero.getHuoLevel());
            //     equipData.setGemId(equipData.getGemId());
            //     equipData.setToubao(equipData.getToubao());
            // }
            outEquips.add(equipBean.build());
        }
        return outEquips;
    }

    @Override
    public void reqQueryList(Role role, List<Long> rids, int type) {
        if (rids == null || rids.isEmpty()) {
            return;
        }
        ResQueryListMessage msg = new ResQueryListMessage();
        QueryProtos.ResQueryList.Builder protoBuilder = QueryProtos.ResQueryList.newBuilder();
        RoleSummary summary;
        for (long rid : rids) {
            summary = SummaryManager.getInstance().getSummary(rid);
            if (summary == null) {
                continue;
            }
            protoBuilder.addBeanList(summaryToBean(summary, null));
        }
        protoBuilder.setType(type);
        msg.setProto(protoBuilder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqQueryBriefInfo(Role role, List<Long> ridList, int type) {
        if (ridList == null || ridList.isEmpty()) {
            return;
        }
        ResQueryBriefInfoMessage msg = new ResQueryBriefInfoMessage();
        QueryProtos.ResQueryBriefInfoMessage proto = QueryProtos.ResQueryBriefInfoMessage.newBuilder()
                .addAllBean(getBriefInfoBean(ridList))
                .setType(type)
                .build();
        msg.setProto(proto);
        MessageUtil.sendMsg(msg, role.getId());
    }

    private List<AbcProtos.RoleBriefInfoBean> getBriefInfoBean(Collection<Long> rids) {
        Set<Long> ridSet = new HashSet<>(rids);
        List<AbcProtos.RoleBriefInfoBean> res = new ArrayList<>();
        ridSet.forEach(rid -> {
            RoleSummary summary = SummaryManager.getInstance().getSummary(rid);
            if (summary != null) {
                res.add(getBriefInfoBean(summary));
            }
        });
        return res;
    }

    private AbcProtos.RoleBriefInfoBean getBriefInfoBean(RoleSummary summary) {
        return AbcProtos.RoleBriefInfoBean.newBuilder()
                .setRid(summary.getId())
                .setName(summary.getName())
                .setZhuanShengId(summary.getZhuanShengId())
                .setFightPower(summary.getFightPower())
                .build();
    }

    @Override
    public void queryRole(Role role, long queryRid) {
        RoleSummary summary = SummaryManager.getInstance().getSummary(queryRid);
        if (summary != null) {
            ResQueryRoleMessage msg = new ResQueryRoleMessage();
            QueryProtos.ResQueryRole proto = QueryProtos.ResQueryRole.newBuilder()
                    .setBean(summaryToBean(summary, null))
                    .build();
            msg.setProto(proto);
            MessageUtil.sendMsg(msg, role);
            return;
        }
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
        if (player == null) {
            return;
        }

        NewQueryRoleToSceneNotice notice = new NewQueryRoleToSceneNotice();
        int hostId = GameContext.getHostId();
        notice.setSourceHostId(hostId);
        notice.setSrcId(role.getId());
        notice.setTargetId(queryRid);
        ModuleClient client = GameContext.getGameServer().getSceneModule().getClient();
        List<RPCConnection> connList = client.getConnectionListByType(ConnectionConst.TYPE.CROSS);

        connList.forEach(connection -> notice.addHost(connection.getHostId()));
        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_COMMON, notice, 0L);
    }

    @Override
    public void queryRole(NewQueryRoleToServerNotice notice) {
        RoleSummary summary = SummaryManager.getInstance().getSummary(notice.getTargetId());
        if (summary == null) {
            return;
        }
        NewQueryRoleRetToSceneNotice ret = new NewQueryRoleRetToSceneNotice();
        ret.setBean(summaryToBean(summary, null));
        ret.setSrcId(notice.getSrcId());
        ret.setSourceHostId(notice.getSourceHostId());
        ModuleClient client = GameContext.getGameServer().getSceneModule().getClient();
        List<RPCConnection> connList = client.getConnectionListByType(ConnectionConst.TYPE.CROSS);

        connList.forEach(connection -> ret.addHost(connection.getHostId()));
        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_COMMON, ret, 0L);
    }

    @Override
    public void queryRole(NewQueryRoleRetToServerNotice notice) {
        Role role = DataCenter.get(Role.class, notice.getSrcId());
        if (role == null) {
            return;
        }
        ResQueryRoleMessage msg = new ResQueryRoleMessage();
        QueryProtos.ResQueryRole proto = QueryProtos.ResQueryRole.newBuilder()
                .setBean(notice.getBean())
                .build();
        msg.setProto(proto);
        MessageUtil.sendMsg(msg, role);
    }

    @Override
    public List<AbcProtos.CommonSlotBean> fashionsToBean(Map<Integer, Integer> fashions) {
        List<AbcProtos.CommonSlotBean> beans = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : fashions.entrySet()) {
            AbcProtos.CommonSlotBean.Builder commonSlotBean = AbcProtos.CommonSlotBean.newBuilder();
            commonSlotBean.setIndex(entry.getKey());
            commonSlotBean.setId(entry.getValue());
            beans.add(commonSlotBean.build());
        }
        return beans;
    }

    @Override
    public Map<Integer, Integer> fashionsToMap(List<Integer> fashions) {
        Map<Integer, Integer> fashionsMap = new HashMap<>();
        for (Integer fashion : fashions) {
            AppearanceConfig config = ConfigDataManager.getInstance().getById(AppearanceConfig.class, fashion);
            if (config != null) {
                fashionsMap.put(config.getType(), config.getId());
            }
        }
        return fashionsMap;
    }
}
