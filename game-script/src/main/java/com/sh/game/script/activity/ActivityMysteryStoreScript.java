package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.system.activity.ResActivityMysteryStoreMessage;
import com.sh.game.common.config.converter.list.JinhaoIntegerListConverter;
import com.sh.game.common.config.model.GlobalConfig;
import com.sh.game.common.config.model.MysteryStoreConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleMysteryStore;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.StringUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleHourScript;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.log.entity.ActivityMysteryStoreLog;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.script.IActivityMysteryStoreScript;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 神秘商店
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-11-20
 **/
@Slf4j
@Script
public class ActivityMysteryStoreScript extends AbstractActivityScript
        implements IActivityMysteryStoreScript, IEventOnRoleHourScript, IEventOnRoleMidnightScript {

    /**
     * 最大商品数量
     */
    private final static int GOODS_COUNT_MAX = 6;

    @Override
    public int getType() {
        return 1052;
    }

    /**
     * 活动开始前处理
     *
     * @param schedule
     * @param role
     */
    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {
        flushGoods(role, true);
    }

    @Override
    public RoleMysteryStore find(long rid) {
        RoleMysteryStore roleMysteryStore = DataCenter.get(RoleMysteryStore.class, rid);
        if (roleMysteryStore == null) {
            roleMysteryStore = new RoleMysteryStore();
            roleMysteryStore.setId(rid);
            DataCenter.insertData(roleMysteryStore, true);
        }

        return roleMysteryStore;
    }

    @Override
    public void reqInfo(Role role) {
        RoleMysteryStore roleMysteryStore = find(role.getRoleId());
        Map<Integer, Integer> currentGoods = roleMysteryStore.getCurrentGoods();

        List<AbcProtos.CommonKeyValueBean> goodList = new ArrayList<>();
        currentGoods.forEach((k, v) -> {
            AbcProtos.CommonKeyValueBean bean = AbcProtos.CommonKeyValueBean.newBuilder()
                    .setKey(k)
                    .setValue(v)
                    .build();
            goodList.add(bean);
        });

        ResActivityMysteryStoreMessage msg = new ResActivityMysteryStoreMessage();
        ActivityProtos.ResActivityMysteryStore proto = ActivityProtos.ResActivityMysteryStore.newBuilder()
                .addAllGoods(goodList)
                .build();
        msg.setProto(proto);
        MessageUtil.sendMsg(msg, role.getRoleId());
    }

    @Override
    public void reqFlushGoods(Role role) {
        GlobalConfig costConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.MYSTERY_STORE_FLUSH_COST);
        if (costConfig == null) {
            return;
        }
        List<int[]> costList = StringUtil.strToIntArrayList(costConfig.getValue(), Symbol.AND, Symbol.JINHAO);

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(costList);
        if (!stash.commit(role, LogAction.MYSTERY_STORE_FLUSH_COST)) {
            return;
        }

        flushGoods(role, false);
    }

    @Override
    public void reqBuyGoods(Role role, int cid) {
        if (isNotInActivity(role)) {
            return;
        }

        MysteryStoreConfig config = ConfigDataManager.getInstance().getById(MysteryStoreConfig.class, cid);
        if (config == null) {
            return;
        }

        RoleMysteryStore roleMysteryStore = find(role.getRoleId());
        Map<Integer, Integer> currentGoods = roleMysteryStore.getCurrentGoods();
        if (currentGoods.getOrDefault(cid, -1) != 0) {
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getPrice());
        if (!stash.commit(role, LogAction.MYSTERY_STORE_BUY_COST)) {
            return;
        }

        // 修改购买状态
        currentGoods.put(cid, 1);

        // 统计购买次数
        Map<Integer, Integer> dailyBuyCount = roleMysteryStore.getDailyBuyCount();
        dailyBuyCount.computeIfPresent(cid, (k, v) -> ++v);
        dailyBuyCount.putIfAbsent(cid, 1);

        DataCenter.updateData(roleMysteryStore);

        // 全部购买完，进行自动刷新
        if (!currentGoods.containsValue(0)) {
            flushGoods(role, false);
        }

        stash.increase(config.getItemId());
        if (!stash.commit(role, LogAction.MYSTERY_STORE_BUY_REWARD)) {
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(config.getItemId(), LogAction.MYSTERY_STORE_BUY_REWARD));
        }

        reqInfo(role);

        //添加神秘商店购买日志
        ActivityMysteryStoreLog log = new ActivityMysteryStoreLog(role);
        log.setRoleId(role.getId());
        log.setRoleName(role.getName());
        log.setAccount(role.getAccount());
        log.setPrice(config.getPrice());
        log.setItem(config.getItemId());
        log.setDailyBuyCount(dailyBuyCount.get(cid));
        log.setMysteryStoreId(config.getId());
        log.submit();

    }

    /**
     * 在线玩家整点判断刷新
     */
    @Override
    public void onRoleHour(Role role) {
        if (isNotInActivity(role)) {
            return;
        }

        GlobalConfig timeConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.MYSTERY_STORE_FLUSH_TIME);
        if (timeConfig == null) {
            return;
        }
        List<Integer> timeList = new JinhaoIntegerListConverter().convert(timeConfig.getValue());
        if (timeList.isEmpty()) {
            return;
        }

        RoleMysteryStore roleMysteryStore = find(role.getRoleId());
        int lastFlushGoodsTime = roleMysteryStore.getFlushGoodsTime();
        int nowOfSeconds = TimeUtil.getNowOfSeconds();

        // 配置的刷新时间点在 (上次刷新时间,当前时间] 之间则进行刷新
        for (Integer hour : timeList) {
            long flushTime = TimeUtil.getTodayZeroFromNowHourSeconds(hour);
            if (lastFlushGoodsTime < flushTime && flushTime <= nowOfSeconds) {
                flushGoods(role, true);
                break;
            }
        }

    }

    /**
     * 离线玩家刷新
     *
     * @param role
     * @param lastLoginTimestamp
     */
    @Override
    public void loginFlushMysteryStore(Role role, long lastLoginTimestamp) {

        /**
         * 活动未开启不刷新
         * 首次登陆时在角色活动开启时刷新
         */
        if (isNotInActivity(role) || lastLoginTimestamp == 0L) {
            return;
        }

        /**
         * 刷新条件（满足其中之一）
         * 1、距离上次登陆超过24小时
         * 2、配置的刷新时间点在 (上次刷新时间,当前时间] 之间
         */
        long nowOfMills = TimeUtil.getNowOfMills();
        int day = TimeUtil.betweenDay(lastLoginTimestamp * TimeUtil.ONE_MILLS, nowOfMills);
        if (day > 0) {
            flushGoods(role, true);
        } else {
            onRoleHour(role);
        }

    }

    /**
     * 跨天清空购买次数限制
     *
     * @param role 角色
     */
    @Override
    public void onRoleMidnight(Role role) {
        RoleMysteryStore roleMysteryStore = find(role.getRoleId());
        Map<Integer, Integer> dailyBuyCount = roleMysteryStore.getDailyBuyCount();
        dailyBuyCount.clear();
        DataCenter.updateData(roleMysteryStore);
    }

    /**
     * 刷新单个玩家神秘商品
     */
    private void flushGoods(Role role, boolean isAutoFlush) {

        RoleMysteryStore roleMysteryStore = find(role.getRoleId());
        Map<Integer, Integer> dailyBuyCount = roleMysteryStore.getDailyBuyCount();

        // 筛选符合条件的商品并未达到当日购买次数上限的记录
        List<MysteryStoreConfig> configList = ConfigDataManager.getInstance().getList(MysteryStoreConfig.class)
                .stream().filter(e ->
                        ConditionUtil.validate(role, e.getCondition())
                                && !(e.getDayLimit() > 0 && dailyBuyCount.getOrDefault(e.getId(), 0) >= e.getDayLimit())
                ).collect(Collectors.toList());

        if (configList.size() < GOODS_COUNT_MAX) {
            log.error("神秘商店满足条件的商品：{}，数量小于展示的上限数量：{}", configList, GOODS_COUNT_MAX);
            return;
        }

        Map<Integer, Integer> guaranteedMap = roleMysteryStore.getGuaranteedMap();

        // 必中项
        List<Integer> mustSelectCidList = new ArrayList<>();
        for (MysteryStoreConfig config : configList) {
            if (config.getNum() <= 0) {
                continue;
            }
            guaranteedMap.computeIfPresent(config.getId(), (k, v) -> ++v);
            guaranteedMap.putIfAbsent(config.getId(), 1);
            if (guaranteedMap.getOrDefault(config.getId(), 0) >= config.getNum()) {
                mustSelectCidList.add(config.getId());
            }
        }

        List<Integer> targetList = new ArrayList<>();
        if (mustSelectCidList.size() >= GOODS_COUNT_MAX) {
            // 必中项中大于最大数量
            targetList = mustSelectCidList.subList(0, GOODS_COUNT_MAX);
        } else if (mustSelectCidList.size() > 0) {
            targetList = mustSelectCidList.subList(0, mustSelectCidList.size());
        }

        // 必中项不可再被抽取
        List<Integer> finalTargetList = targetList;
        configList = configList.stream().filter(
                e -> !finalTargetList.contains(e.getId())).collect(Collectors.toList());

        // 必中项中数量不够则进行抽取补充
        int mustSelectSize = targetList.size();
        for (int i = mustSelectSize; i < GOODS_COUNT_MAX; i++) {
            int cid = randomConfigWeight(configList);
            if (cid < 0) {
                continue;
            }
            targetList.add(cid);
        }

        Map<Integer, Integer> currentGoods = roleMysteryStore.getCurrentGoods();
        currentGoods.clear();

        for (Integer cid : targetList) {
            MysteryStoreConfig config = ConfigDataManager.getInstance().getById(MysteryStoreConfig.class, cid);
            if (config == null) {
                continue;
            }
            // 清空次数统计
            guaranteedMap.remove(cid);

            // 添加到展示商品中
            currentGoods.put(cid, 0);
        }

        // 保存抽取到的商品
        roleMysteryStore.setCurrentGoods(currentGoods);

        // 自动刷新需要记录刷新时间
        if (isAutoFlush) {
            int nowOfSeconds = TimeUtil.getNowOfSeconds();
            roleMysteryStore.setFlushGoodsTime(nowOfSeconds);
        }
        DataCenter.updateData(roleMysteryStore);

        // 发送刷新信息
        reqInfo(role);

    }

    /**
     * 权重抽取(每次的抽中物需要移除)
     *
     * @param configList 满足抽取条件的配置表集合
     * @return 抽取到的配置表cid
     */
    private int randomConfigWeight(List<MysteryStoreConfig> configList) {

        // 计算权重
        int sumWeight = 0;
        List<Integer> weightList = new ArrayList<>();
        for (MysteryStoreConfig config : configList) {
            sumWeight += config.getValue();
            weightList.add(sumWeight);
        }

        int rangeValue = new Random().nextInt(sumWeight);
        int index = 0;
        for (int i = 0; i < weightList.size(); i++) {
            if (rangeValue < weightList.get(i)) {
                index = i;
                break;
            }
        }

        MysteryStoreConfig config = configList.get(index);
        if (config == null) {
            return -1;
        }
        configList.remove(index);

        return config.getId();
    }

}
