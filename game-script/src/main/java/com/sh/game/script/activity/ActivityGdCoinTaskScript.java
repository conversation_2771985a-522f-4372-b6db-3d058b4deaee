package com.sh.game.script.activity;

import com.sh.game.common.constant.ActivityConst;
import com.sh.game.script.activity.abc.AbstractActivityTaskScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

/**
 * 官斗冲榜-真币任务
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Slf4j
@Script(order = 1)
public class ActivityGdCoinTaskScript extends AbstractActivityTaskScript {

    @Override
    public int getType() {
        return ActivityConst.COIN_TASK;
    }
}
