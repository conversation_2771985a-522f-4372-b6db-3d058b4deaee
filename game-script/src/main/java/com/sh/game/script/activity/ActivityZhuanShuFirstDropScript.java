package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.equip.ResZhuanShuBoxMessage;
import com.sh.game.common.communication.msg.system.equip.ResZhuanShuMessage;
import com.sh.game.common.config.model.EquipZhuanshuConfig;
import com.sh.game.common.config.model.EquipZhuanshuGoalsConfig;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.sys.ActivityZhuanshuSysData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.event.IEventOnRoleDropMonsterScript;
import com.sh.game.protos.EquipProtos;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.role.RoleZhuanshuActivity;
import com.sh.game.system.activity.script.IActivityZhuanShuFirstDropScript;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 专属首爆
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-08-30
 **/
@Slf4j
@Script
public class ActivityZhuanShuFirstDropScript extends AbstractActivityScript implements
        IActivityZhuanShuFirstDropScript,
        IEventOnRoleDropMonsterScript {
    @Override
    public int getType() {
        return 1035;
    }

    @Override
    public RoleZhuanshuActivity find(long rid) {
        RoleZhuanshuActivity roleZhuanshuActivity = DataCenter.get(RoleZhuanshuActivity.class, rid);
        if (roleZhuanshuActivity == null) {
            roleZhuanshuActivity = new RoleZhuanshuActivity();
            roleZhuanshuActivity.setId(rid);
            DataCenter.insertData(roleZhuanshuActivity, true);
        }
        return roleZhuanshuActivity;
    }

    @Override
    public void reqInfo(Role role, int area) {
        Map<Integer, Integer> residueCount = findTotalCount();

        RoleZhuanshuActivity roleZhuanshuActivity = find(role.getId());
        Map<Integer, Integer> personalRequiredState = roleZhuanshuActivity.getRequiredState();


        List<EquipProtos.ZhuanShuBean> beans = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : residueCount.entrySet()) {
            EquipZhuanshuConfig config = ConfigDataManager.getInstance().getById(EquipZhuanshuConfig.class, entry.getKey());
            if (config == null || config.getAreaType() != area) {
                continue;
            }
            EquipProtos.ZhuanShuBean.Builder zhuanShuBean = EquipProtos.ZhuanShuBean.newBuilder();
            zhuanShuBean.setCfgId(entry.getKey());
            zhuanShuBean.setCount(entry.getValue());
            zhuanShuBean.setState(0);

            if (personalRequiredState.containsKey(entry.getKey())) {
                zhuanShuBean.setState(personalRequiredState.get(entry.getKey()));
            }
            beans.add(zhuanShuBean.build());
        }

        ResZhuanShuMessage msg = new ResZhuanShuMessage();
        EquipProtos.ResZhuanShu.Builder zhuanShu =  EquipProtos.ResZhuanShu.newBuilder();
        zhuanShu.addAllBeans(beans);
        msg.setProto(zhuanShu.build());
        MessageUtil.sendMsg(msg, role.getId());

        /**
         * 发送宝箱进度消息
         */
        sendMessage(role, area);
    }

    @Override
    public void reqFirstAcquire(Role role, int cfgId) {
        EquipZhuanshuConfig config = ConfigDataManager.getInstance().getById(EquipZhuanshuConfig.class, cfgId);
        if (config == null) {
            return;
        }

        RoleZhuanshuActivity roleZhuanshuActivity = find(role.getId());
        Map<Integer, Integer> personalRequiredState = roleZhuanshuActivity.getRequiredState();


        if (personalRequiredState.get(cfgId) == null) {
            log.info("玩家: {},的专属装备：{}，未获得", role.getId(), cfgId);
            return;
        }

        if (personalRequiredState.get(cfgId) == 2) {
            TipUtil.show(role, CommonTips.脚本_已经获取过了);
            log.info("玩家: {},的专属装备：{}，重复领取", role.getId(), cfgId);
            return;
        }

        Map<Integer, Integer> residueCount = findTotalCount();
        if (residueCount.getOrDefault(cfgId, 0) >= config.getLimit()) {
            TipUtil.show(role, CommonTips.脚本_全服剩余数量不足);
            log.info("玩家: {},的专属装备奖励：{}，全服剩余数量不足", role.getId(), cfgId);
            return;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.ZHUANSHU_COllECT_REWAED)) {
            TipUtil.show(role, CommonTips.脚本_背包空间不足);
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                    EmailConst.toMailAttach(config.getReward(), LogAction.ZHUANSHU_COllECT_REWAED));
        }

        // 改变为领取状态
        personalRequiredState.put(cfgId, 2);
        DataCenter.updateData(roleZhuanshuActivity);

        // 领取后计数全服数量
        addTotalCount(config.getId(), 1);


        ArrayList<EquipProtos.ZhuanShuBean> beans = new ArrayList<>();
        EquipProtos.ZhuanShuBean.Builder zhuanShuBean = EquipProtos.ZhuanShuBean.newBuilder();
        zhuanShuBean.setCfgId(cfgId);
        zhuanShuBean.setState(2);

        Map<Integer, Integer> dataResidueCount = findTotalCount();
        zhuanShuBean.setCount(dataResidueCount.getOrDefault(cfgId, 0));

        beans.add(zhuanShuBean.build());
        ResZhuanShuMessage msg = new ResZhuanShuMessage();
        EquipProtos.ResZhuanShu.Builder resZhuanShu = EquipProtos.ResZhuanShu.newBuilder();
        resZhuanShu.addAllBeans(beans);
        msg.setProto(resZhuanShu.build());
        MessageUtil.sendMsg(msg, role.getId());

        sendMessage(role, config.getAreaType());
    }

    @Override
    public void reqBoxAcquire(Role role, int cfgId) {
        EquipZhuanshuGoalsConfig config = ConfigDataManager.getInstance().getById(EquipZhuanshuGoalsConfig.class, cfgId);
        if (config == null) {
            return;
        }

        RoleZhuanshuActivity roleZhuanshuActivity = find(role.getId());
        Map<Integer, Integer> personalRequiredState = roleZhuanshuActivity.getRequiredState();

        if (personalRequiredState.size() < config.getZhuanshu_goals()) {
            log.info("玩家: {},不满足专属装备宝箱领取条件", role.getId());
            return;
        }

        List<Integer> requiredBoxCfgIdList = roleZhuanshuActivity.getRequiredBox().computeIfAbsent(config.getType(), k -> new ArrayList<>());


        if (requiredBoxCfgIdList.contains(cfgId)) {
            TipUtil.show(role, CommonTips.脚本_已经获取过了);
            log.info("玩家: {},专属装备宝箱重复领取", role.getId());
            return;
        }

        BackpackStash backpackStash = new BackpackStash(role);
        backpackStash.increase(config.getReward());
        if (!backpackStash.commit(role, LogAction.ZHUANSHU_COllECT_BOX_REWAED, false)) {
            TipUtil.show(role, CommonTips.脚本_背包空间不足领取失败);
            return;
        }

        TipUtil.show(role, CommonTips.脚本_奖励领取到背包);
        requiredBoxCfgIdList.add(cfgId);
        DataCenter.updateData(roleZhuanshuActivity);

        sendMessage(role, config.getType());
    }

    @Override
    public Map<Integer, Integer> findTotalCount() {
        ActivityZhuanshuSysData zhuanshuSysData = SysDataProvider.get(ActivityZhuanshuSysData.class);
        return zhuanshuSysData.getResidueCount();
    }

    @Override
    public void addTotalCount(int cfgId, int count) {
        ActivityZhuanshuSysData zhuanshuSysData = SysDataProvider.get(ActivityZhuanshuSysData.class);
        Map<Integer, Integer> residueCount = zhuanshuSysData.getResidueCount();

        residueCount.putIfAbsent(cfgId, count);
        if (count == 0) {
            return;
        }
        residueCount.computeIfPresent(cfgId, (k, v) -> v + count);
        DataCenter.updateData(zhuanshuSysData);
    }


    @Override
    public void onRoleDropMonster(Role role, List<Item> drops) {

        if (drops == null || drops.isEmpty()) {
            return;
        }

        int activityID = getCurrentActivityID(role);
        if (activityID == 0) {
            return;
        }

        List<EquipZhuanshuConfig> configList = ConfigDataManager.getInstance().getList(EquipZhuanshuConfig.class);
        if (configList == null || configList.isEmpty()) {
            return;
        }
        Set<Integer> dropItems = new HashSet<>();
        drops.forEach(item -> dropItems.add(item.getCfgId()));

        List<EquipZhuanshuConfig> collect = configList.stream().filter(config -> config.getActivityID() == activityID
                && dropItems.contains(config.getItem())).collect(Collectors.toList());

        if (collect.isEmpty()) {
            return;
        }


        RoleZhuanshuActivity roleZhuanshuActivity = find(role.getId());
        Map<Integer, Integer> personalRequiredState = roleZhuanshuActivity.getRequiredState();
        List<EquipProtos.ZhuanShuBean> beans = new ArrayList<>();
        for (EquipZhuanshuConfig config : collect) {

            // 先加全服数量再加个人的
            addTotalCount(config.getId(), 0);

            // 已获取过则不在重复处理
            if (personalRequiredState.containsKey(config.getId())) {
                continue;
            }

            // 玩家私有的专属解锁状态
            personalRequiredState.put(config.getId(), 2);

            EquipProtos.ZhuanShuBean.Builder zhuanShuBean = EquipProtos.ZhuanShuBean.newBuilder();
            zhuanShuBean.setCfgId(config.getId());
            zhuanShuBean.setState(1);

            Map<Integer, Integer> residueCount = findTotalCount();
            zhuanShuBean.setCount(residueCount.getOrDefault(config.getId(), 0));
            beans.add(zhuanShuBean.build());
        }

        if (beans.isEmpty()) {
            return;
        }

        DataCenter.updateData(roleZhuanshuActivity);

        ResZhuanShuMessage msg = new ResZhuanShuMessage();
        EquipProtos.ResZhuanShu.Builder zhuanShu = EquipProtos.ResZhuanShu.newBuilder();
        zhuanShu.addAllBeans(beans);
        msg.setProto(zhuanShu.build());
        MessageUtil.sendMsg(msg, role.getId());

        EquipZhuanshuConfig config = ConfigDataManager.getInstance().getById(EquipZhuanshuConfig.class, beans.get(0).getCfgId());
        if (config == null) {
            return;
        }
        sendMessage(role, config.getAreaType());

    }

    /**
     * 发送宝箱消息
     */
    private void sendMessage(Role role, int area) {
        RoleZhuanshuActivity roleZhuanshuActivity = find(role.getId());
        Map<Integer, Integer> personalRequiredState = roleZhuanshuActivity.getRequiredState();

        int progress = 0;
        EquipZhuanshuConfig config;
        for (Map.Entry<Integer, Integer> entry : personalRequiredState.entrySet()) {
            config = ConfigDataManager.getInstance().getById(EquipZhuanshuConfig.class, entry.getKey());
            if (config == null) {
                continue;
            }
            if (config.getAreaType() == area && entry.getValue() == 2) {
                progress++;
            }
        }

        ResZhuanShuBoxMessage msg = new ResZhuanShuBoxMessage();
        EquipProtos.ResZhuanShuBox.Builder resZhuanShu = EquipProtos.ResZhuanShuBox.newBuilder();
        resZhuanShu.setArea(area);
        resZhuanShu.setProgress(progress);
        resZhuanShu.addAllAcquiredBoxList(roleZhuanshuActivity.getRequiredBox().getOrDefault(area, new ArrayList<>()));
        MessageUtil.sendMsg(msg, role.getId());
    }
}