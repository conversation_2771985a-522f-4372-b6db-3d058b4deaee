package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.activity.ResActivityGouWuCheMessage;
import com.sh.game.common.config.model.ActivityShoppingCarConfig;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.CountConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.ActivityManager;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.script.IActivityGouWuCheScript;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@Script
@Slf4j

public class ActivityGouWuCheScript extends AbstractActivityScript implements IActivityGouWuCheScript {

    @Override
    public int getType() {
        return 1044;
    }


    @Override
    protected void onScheduleBeginPrivate(ActivitySchedule schedule, Role role) {
        super.onScheduleBeginPrivate(schedule, role);

        RoleActivity activity = findRoleActitiy(role);
        //新活动开启时清空购物车
        if (activity.getGouWuCheItemList().size()  > 0){
            activity.getGouWuCheItemList().clear();
            DataCenter.updateData(activity);
        }

    }

    /**
     * 获取当前购物车配置信息(当前分组及每日限购次数)
     * @param nActID
     */

    private ActivityGouWuCheData getGouWuCheData(int nActID) {
        ActivityGouWuCheData retData = new ActivityGouWuCheData();

        retData.groupID = 0;
        retData.buyLimit = 3;
        for (ActivityShoppingCarConfig config : ConfigDataManager.getInstance().getList(ActivityShoppingCarConfig.class)) {
            if (config.getActivityID() == nActID && ConditionUtil.validate(config.getConditions()) ){
                retData.groupID = config.getGruop();
                retData.buyLimit  = config.getPayLimit();
                break;
            }
        }
        return retData;
    }


    public boolean isAvailable(int nActivityID, Role role){
        if (ActivityManager.getInstance().getAvailable(this.getType()) == null){
            log.info("玩家{}请求购物车, 活动{}未开启", role.getName(), nActivityID);
            return false ;
        }
        return true;
    }
    /**
     * 查询当前购物车状态
     * @param nActivityID
     * @param role
     */
    @Override
    public void reqInfo(int nActivityID, Role role) {

        if (! isAvailable(nActivityID, role)) {
            return;
        }

        ActivityGouWuCheData activityConfig = getGouWuCheData(nActivityID);
        if (activityConfig == null){
            log.info("玩家{}请求购物车, 活动{}未找到匹配的数据", role.getName(), nActivityID);
            return;
        }

        RoleActivity activity = findRoleActitiy(role);
        int count = CountManager.getInstance().getCount(role, CountConst.CountType.DAY_GOUWUCHE_BUY_TIMES, nActivityID);

        ResActivityGouWuCheMessage retMessage = new ResActivityGouWuCheMessage();
        ActivityProtos.ResActivityGouWuChe proto = ActivityProtos.ResActivityGouWuChe.newBuilder()
                .setActivityID(nActivityID)
                .setBuyTimes(count)
                .setGruop(activityConfig.groupID)
                .setTotalTimes(activityConfig.buyLimit)
                .addAllItems(activity.getGouWuCheItemList())
                .build();
        retMessage.setProto(proto);
        MessageUtil.sendMsg(retMessage, role.getId());
    }

    @Override
    public void addShopCar(int nActivityID, Role role, int nId) {

        if (! isAvailable(nActivityID, role)) {
            return;
        }

        ActivityShoppingCarConfig itemConfig = ConfigDataManager.getInstance().getById(ActivityShoppingCarConfig.class, nId);
        if (itemConfig == null){
            log.info("玩家{}请求购物车, 活动{}未找到传入的数据{}", role.getName(), nActivityID, nId);
            return;
        }

        if (!ConditionUtil.validate(role, itemConfig.getConditions(), true)){
            log.info("玩家{}请求购物车, 活动{}未达到购买条件{}", role.getName(), nActivityID, nId);
            return;
        }

        RoleActivity activity = findRoleActitiy(role);
        //没有加入购物车才加入
        if (!activity.getGouWuCheItemList().contains(nId)){
            activity.getGouWuCheItemList().add(nId);
            DataCenter.updateData(activity);
            this.reqInfo(nActivityID, role);
        }
    }

    @Override
    public void delItem(int nActivityID, Role role, int nId) {
        if (! isAvailable(nActivityID, role)) {
            return;
        }

        RoleActivity activity = findRoleActitiy(role);

        int itemIndex = activity.getGouWuCheItemList().indexOf(nId);
        if (itemIndex >= 0){
            activity.getGouWuCheItemList().remove(itemIndex);
            DataCenter.updateData(activity);
        }else{
            log.info("玩家{}请求购物车{}, 删除商品时,购物车中并不存在{}", role.getName(), nActivityID, nId);
        }
        this.reqInfo(nActivityID, role);
    }

    @Override
    public void commitBuy(int nActivityID, Role role) {
        if (! isAvailable(nActivityID, role))  return;

        ActivityGouWuCheData activityConfig =  getGouWuCheData(nActivityID);
        if (activityConfig == null){
            log.info("玩家{}请求购物车, 活动{}未找到匹配的数据", role.getName(), nActivityID);
            return;
        }

        int count = CountManager.getInstance().getCount(role, CountConst.CountType.DAY_GOUWUCHE_BUY_TIMES, nActivityID);
        if (count >= activityConfig.buyLimit){
            //已达当日购买上限
            log.info("玩家{}请求购物车, 活动{}已达当日购买上限{}/{}", role.getName(), nActivityID, count, activityConfig.buyLimit);
            return;
        }

        RoleActivity activity = findRoleActitiy(role);
        int nShopingCarCount = activity.getGouWuCheItemList().size();
        //购物车为空
        if (nShopingCarCount <= 0){
            log.info("玩家{}请求购物车, 活动{}购物车为空", role.getName(), nActivityID);
            return;
        }

        //开始计算花费
        List<int[]> giveItems = new ArrayList<>();
        //计算需要消耗的总灵符数量 并返回需要给玩家的道具列表
        int nCostLf = calMoneyAndGiveItems(role,  activity.getGouWuCheItemList(), giveItems);

        //扣除灵符
        if (! costMoney(role, nCostLf )){
            return;
        }

        //发放物品
        giveItems(role, giveItems);

        //清空购物车
        activity.getGouWuCheItemList().clear();
        //记录次数
        CountManager.getInstance().count(role, CountConst.CountType.DAY_GOUWUCHE_BUY_TIMES, nActivityID, 1);
        DataCenter.updateData(activity);
        this.reqInfo(nActivityID, role);
    }

    /**
     * 扣除灵符
     */
    private boolean costMoney(Role role, int nCostLf){
        if (nCostLf <= 0){
            log.error("玩家{} 请求购物车购买时计算出的价格为{},取消此次购买", role.getName(), nCostLf);
            return false;
        }

        BackpackStash stash = new BackpackStash(role);
        stash.decrease(BagConst.ItemId.MONEY_BIND, nCostLf);
        if (!stash.commit(role, LogAction.GOUWUCHE_BUY)) {
            log.error("玩家{}请求购物车, 玩家购买时发现灵符不足", role.getName());
            return false;
        }
        return true;
    }

    /**
     * 计算需要消耗的总灵符数量 并返回需要给玩家的道具列表
     */
    private int calMoneyAndGiveItems(Role role,  List<Integer> gouWuCheItems, List<int[]> giveItems){
        int nCostLf = 0;
        for (int nID: gouWuCheItems){
            ActivityShoppingCarConfig itemConfig = ConfigDataManager.getInstance().getById(ActivityShoppingCarConfig.class, nID);

            if (itemConfig == null){
                log.info("玩家{}请求购物车购买, 发现{}未找到", role.getName(), nID);
                continue;
            }
            nCostLf = nCostLf + calItemCost(itemConfig, gouWuCheItems.size());

            giveItems.addAll(itemConfig.getItem());
        }
        return nCostLf;
    }

    /**
     * 根据当前购物车中的商品数量计算当前物品需要的花费
     * @param itemConfig
     * @param nShopingCarCount
     * @return
     */
    private int calItemCost(ActivityShoppingCarConfig itemConfig, int nShopingCarCount){
        int retCost = itemConfig.getCost();
        for (int[] cost : itemConfig.getDiscount()){
            if (cost.length < 2){
                continue;
            }

            if (nShopingCarCount >= cost[0]){
                retCost = cost[1];
            }
        }
        return  retCost;
    }

    /**
     * 发放已购物品给玩家
     * @param role
     * @param items
     */
    private void giveItems(Role role, List<int[]> items){
            BackpackStash stash = new BackpackStash(role);
            stash.increase(items);
            if (!stash.commit(role, LogAction.GOUWUCHE_BUY, false)) {
                MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW,
                        EmailConst.toMailAttach(items, LogAction.GOUWUCHE_BUY));
            }
    }

    private class ActivityGouWuCheData {
        public int groupID;
        public int buyLimit;
    }
}
