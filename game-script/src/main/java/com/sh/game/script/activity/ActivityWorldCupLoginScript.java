package com.sh.game.script.activity;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.abc.bean.CommonKeyValueBean;
import com.sh.game.common.communication.msg.system.activity.ResWorldCupLoginInfoMessage;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.config.model.WorldCupLoginConfig;
import com.sh.game.common.config.model.WorldCupLoginCountConfig;
import com.sh.game.common.constant.ActivityConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleNormal;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleRechargedScript;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.ActivityWorldCupLoginData;
import com.sh.game.system.activity.script.IActivityWorldCupLoginScript;
import com.sh.game.system.mail.MailManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/11/1 19:09
 */
@Script
@Slf4j
public class ActivityWorldCupLoginScript extends AbstractActivityScript implements IActivityWorldCupLoginScript, IEventOnRoleRechargedScript {

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        int proceedDays = schedule.getProceedDays();
        RoleNormal normal = role.findNormal();
        ActivityWorldCupLoginData data = normal.getLoginDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivityWorldCupLoginData());
        data.getRechargeDay().put(proceedDays, true);
        DataCenter.updateData(normal);

        log.info("签到活动，玩家充值，活动id {} 玩家id {} name {} 活动开启天数 {}", schedule.getActivityID(), role.getId(), role.getName(), proceedDays);

        info(role);
    }

    @Override
    public void info(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        RoleNormal normal = role.findNormal();
        ActivityWorldCupLoginData data = normal.getLoginDataMap().getOrDefault(schedule.getActivityID(), new ActivityWorldCupLoginData());

        ResWorldCupLoginInfoMessage msg = new ResWorldCupLoginInfoMessage();
        ActivityProtos.ResWorldCupLoginInfo.Builder protoBuilder = ActivityProtos.ResWorldCupLoginInfo.newBuilder();
        protoBuilder.setDay(schedule.findProceedDays());
        protoBuilder.addAllFreeReward(data.getFreeReward());
        protoBuilder.addAllPayReward(data.getPayReward());
        protoBuilder.addAllLeiJiReward(data.getLeiJiReward());
        for (Map.Entry<Integer, Boolean> entry : data.getRechargeDay().entrySet()) {
            protoBuilder.addRechargeDay(AbcProtos.CommonKeyValueBean.newBuilder()
                    .setKey(entry.getKey())
                    .setValue(entry.getValue() ? 1 : 0)
                    .build());
        }
        msg.setProto(protoBuilder.build());

        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 只能领当天的
     *
     * @param role
     * @param cid
     */
    @Override
    public void reqAcquireFree(Role role, int cid) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        WorldCupLoginConfig config = ConfigDataManager.getInstance().getById(WorldCupLoginConfig.class, cid);
        if (config == null) {
            log.error("签到活动，玩家请求领取免费奖励，配置找不到，活动id {} 玩家id {} name {} 配置id {}", schedule.getActivityID(), role.getId(), role.getName(), cid);
            return;
        }

        if (config.getActivityId() != schedule.getActivityID()) {
            log.error("签到活动，玩家请求领取免费奖励，配置与活动不匹配，活动id {} 玩家id {} name {} 配置id {}", schedule.getActivityID(), role.getId(), role.getName(), cid);
            return;
        }

        int proceedDays = schedule.findProceedDays();

        RoleNormal normal = role.findNormal();
        ActivityWorldCupLoginData data = normal.getLoginDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivityWorldCupLoginData());
        List<Integer> freeReward = data.getFreeReward();
        if (freeReward.contains(cid)) {
            log.error("签到活动，玩家请求领取免费奖励，玩家已领取过当前天数奖励，活动id {} 玩家id {} name {} 配置id {} 玩家领取情况 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, JSON.toJSONString(freeReward));
            return;
        }

        if (config.getDay() != proceedDays) {
            log.error("签到活动，玩家请求领取免费奖励，活动领取天数不对，活动id {} 玩家id {} name {} 配置id {} 活动开启天数 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, proceedDays);
            return;
        }

        freeReward.add(cid);
        DataCenter.updateData(normal);

        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.QIAN_DAO_LI_BAO)) {
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, EmailConst.toMailAttach(config.getReward(), LogAction.QIAN_DAO_LI_BAO));
        }

        log.info("签到活动，玩家请求领取免费奖励，领取成功，活动id {} 玩家id {} name {} 配置id {} 活动开启天数 {} 玩家领取情况 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, proceedDays, JSON.toJSONString(freeReward));
        info(role);
    }

    /**
     * 只能领当天的
     *
     * @param role
     * @param cid
     */
    @Override
    public void reqAcquirePay(Role role, int cid) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }

        WorldCupLoginConfig config = ConfigDataManager.getInstance().getById(WorldCupLoginConfig.class, cid);
        if (config == null) {
            log.error("签到活动，玩家请求领取付费奖励，配置找不到，活动id {} 玩家id {} name {} 配置id {}", schedule.getActivityID(), role.getId(), role.getName(), cid);
            return;
        }

        if (config.getActivityId() != schedule.getActivityID()) {
            log.error("签到活动，玩家请求领取付费奖励，配置与活动不匹配，活动id {} 玩家id {} name {} 配置id {}", schedule.getActivityID(), role.getId(), role.getName(), cid);
            return;
        }

        int proceedDays = schedule.findProceedDays();
        if (config.getDay() != proceedDays) {
            log.error("签到活动，玩家请求领取付费奖励，活动领取天数不对，活动id {} 玩家id {} name {} 配置id {} 活动开启天数 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, proceedDays);
            return;
        }

        RoleNormal normal = role.findNormal();
        ActivityWorldCupLoginData data = normal.getLoginDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivityWorldCupLoginData());
        List<Integer> payReward = data.getPayReward();
        if (payReward.contains(cid)) {
            log.error("签到活动，玩家请求领取付费奖励，玩家已领取过当前天数奖励，活动id {} 玩家id {} name {} 配置id {} 玩家领取情况 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, JSON.toJSONString(payReward));
            return;
        }

        if (!data.getRechargeDay().getOrDefault(proceedDays, false)) {
            log.error("签到活动，玩家请求领取付费奖励，今天没有充值，活动id {} 玩家id {} name {} 配置id {} 活动开启天数 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, proceedDays);
            return;
        }

        List<Integer> freeReward = data.getFreeReward();
        boolean giveFree = freeReward.contains(cid);

        if (!giveFree) {
            freeReward.add(cid);
        }

        payReward.add(cid);
        DataCenter.updateData(normal);

        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getRechargeReward());
        if (!giveFree) {
            stash.increase(config.getReward());
        }

        if (!stash.commit(role, LogAction.QIAN_DAO_LI_BAO)) {
            List<Item> items = EmailConst.toMailAttach(config.getRechargeReward(), LogAction.QIAN_DAO_LI_BAO);
            if (!giveFree) {
                items.addAll(EmailConst.toMailAttach(config.getReward(), LogAction.QIAN_DAO_LI_BAO));
            }
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, items);
        }

        if (giveFree) {
            log.info("签到活动，玩家请求领取付费奖励，领取成功，活动id {} 玩家id {} name {} 配置id {} 活动开启天数 {} 付费礼包领取情况 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, proceedDays, JSON.toJSONString(payReward));
        } else {
            log.info("签到活动，玩家请求领取付费奖励，玩家未领取免费奖励一并领取，活动id {} 玩家id {} name {} 配置id {} 活动开启天数 {} 付费礼包领取情况 {} 免费礼包领取情况 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, proceedDays, JSON.toJSONString(payReward), JSON.toJSONString(freeReward));
        }


        info(role);
    }

    @Override
    public void reqAcquireLeiJi(Role role, List<Integer> idList) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        for (Integer cid : idList) {
            WorldCupLoginCountConfig config = ConfigDataManager.getInstance().getById(WorldCupLoginCountConfig.class, cid);
            if (config == null) {
                log.error("签到活动，玩家请领取累计奖励，配置找不到，活动id {} 玩家id {} name {} 配置id {}", schedule.getActivityID(), role.getId(), role.getName(), cid);
                return;
            }

            if (config.getActivityId() != schedule.getActivityID()) {
                log.error("签到活动，玩家请领取累计奖励，配置与活动不匹配，活动id {} 玩家id {} name {} 配置id {}", schedule.getActivityID(), role.getId(), role.getName(), cid);
                return;
            }

            RoleNormal normal = role.findNormal();
            ActivityWorldCupLoginData data = normal.getLoginDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivityWorldCupLoginData());

            List<Integer> leiJiReward = data.getLeiJiReward();
            if (leiJiReward.contains(cid)) {
                log.error("签到活动，玩家请领取累计奖励，玩家已领取当前奖励，活动id {} 玩家id {} name {} 配置id {} 玩家领取详情 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, JSON.toJSONString(leiJiReward));
                return;
            }

            if (config.getCount() > data.getFreeReward().size()) {
                log.error("签到活动，玩家请领取累计奖励，玩家未达到条件，活动id {} 玩家id {} name {} 配置id {} 玩家签到天数 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, data.getFreeReward().size());
                return;
            }

            leiJiReward.add(cid);
            DataCenter.updateData(normal);

            BackpackStash stash = new BackpackStash(role);
            stash.increase(config.getReward());
            if (!stash.commit(role, LogAction.QIAN_DAO_LI_BAO)) {
                MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, EmailConst.toMailAttach(config.getReward(), LogAction.QIAN_DAO_LI_BAO));
            }

            log.info("签到活动，玩家请领取累计奖励，领取成功，活动id {} 玩家id {} name {} 配置id {} 玩家领取详情 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, JSON.toJSONString(leiJiReward));
        }

        info(role);
    }

    @Override
    public void reqBuQian(Role role, int cid) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        WorldCupLoginConfig config = ConfigDataManager.getInstance().getById(WorldCupLoginConfig.class, cid);
        if (config == null) {
            log.error("签到活动，玩家请求补签免费奖励，配置找不到，活动id {} 玩家id {} name {} 配置id {}", schedule.getActivityID(), role.getId(), role.getName(), cid);
            return;
        }

        if (config.getActivityId() != schedule.getActivityID()) {
            log.error("签到活动，玩家请求补签免费奖励，配置与活动不匹配，活动id {} 玩家id {} name {} 配置id {}", schedule.getActivityID(), role.getId(), role.getName(), cid);
            return;
        }

        int proceedDays = schedule.findProceedDays();
        if (config.getDay() > proceedDays) {
            log.error("签到活动，玩家请求补签免费奖励，不能补签未到达的天数，活动id {} 玩家id {} name {} 配置id {} 活动开启天数 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, proceedDays);
            return;
        }

        RoleNormal normal = role.findNormal();
        ActivityWorldCupLoginData data = normal.getLoginDataMap().computeIfAbsent(schedule.getActivityID(), k -> new ActivityWorldCupLoginData());
        List<Integer> freeReward = data.getFreeReward();
        if (freeReward.contains(cid)) {
            log.error("签到活动，玩家请求补签免费奖励，玩家当天奖励已领取，活动id {} 玩家id {} name {} 配置id {} 玩家领取详情 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, JSON.toJSONString(freeReward));
            return;
        }

        BackpackStash remove = new BackpackStash(role);
        remove.decrease(config.getCost());
        if (!remove.commit(role, LogAction.QIAN_DAO_LI_BAO_COST)) {
            return;
        }

        freeReward.add(cid);
        DataCenter.updateData(normal);

        BackpackStash add = new BackpackStash(role);
        add.increase(config.getReward());
        if (!add.commit(role, LogAction.QIAN_DAO_LI_BAO)) {
            MailManager.getInstance().sendMail(role.getId(), EmailConst.MailId.BACK_OVER_FLOW, EmailConst.toMailAttach(config.getReward(), LogAction.QIAN_DAO_LI_BAO));
        }

        log.info("签到活动，玩家请求补签免费奖励，补签成功，活动id {} 玩家id {} name {} 配置id {} 玩家领取详情 {}", schedule.getActivityID(), role.getId(), role.getName(), cid, JSON.toJSONString(freeReward));

        info(role);
    }

    @Override
    public int getType() {
        return ActivityConst.WORLD_CUP_LOGIN;
    }

}
