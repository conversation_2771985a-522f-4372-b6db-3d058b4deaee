package com.sh.game.script.identify;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.cache.EquipLocationCache;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.SpecialXiLianConfig;
import com.sh.game.common.constant.ConditionTypeConst;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.EquipData;
import com.sh.game.common.entity.backpack.item.IdentifyAttribute;
import com.sh.game.common.entity.backpack.item.IdentifyAttributeValue;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnEquipChangedScript;
import com.sh.game.system.condition.ConditionManager;
import com.sh.game.system.equip.EquipManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.identify.script.IIdentifyScript;
import com.sh.game.system.role.entity.Hero;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;


@Script
@Slf4j
public class IdentifyScript implements IIdentifyScript, IEventOnEquipChangedScript {

    @Override
    public void reqIdentify(Role role, long uid, List<Integer> lockAttrPos, List<Integer> lockBuffPos, int type) {
        Backpack backpack = DataCenter.getBackpack(role.getId());
        Item item = backpack.findItemByUniqueId(uid);
        if (item == null) {
            return;
        }
        if (item.getEquipData().isFengyin()) {
            return;
        }
        SpecialXiLianConfig config = ConfigDataManager.getInstance().getById(SpecialXiLianConfig.class,
                item.getCfgId() + "#" + type);
        if (config == null) {
            return;
        }
        // 校验锁定属性
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getIdentifycost());
        IdentifyAttribute oldAttr = item.getEquipData().getIdentifyAttribute();
        if (oldAttr == null && (lockAttrPos.size() > 0 || lockBuffPos.size() > 0)) {
            return;
        }
        for (int i = 0; i < lockAttrPos.size(); i++) {
            int pos = lockAttrPos.get(i);
            if (!oldAttr.getAttributes().containsKey(pos)) {
                return;
            }
        }
        if (lockAttrPos.size() > 0) {
            stash.decrease(config.getLockcost().get(lockAttrPos.size() - 1));
        }
        for (int i = 0; i < lockBuffPos.size(); i++) {
            int pos = lockBuffPos.get(i);
            if (!oldAttr.getBuffs().containsKey(pos)) {
                return;
            }
        }
        if (lockBuffPos.size() > 0) {
            stash.decrease(config.getBufflockcost().get(lockBuffPos.size() - 1));
        }

        if (!stash.commit(role, LogAction.EQUIP_IDENTIFY)) {
            return;
        }

        IdentifyAttribute newAttr = new IdentifyAttribute();
        IdentifyAttribute attribute = randomAttribute(role, config, lockAttrPos, oldAttr);
        if (attribute == null) {
            log.error("洗练时属性随机不出来，道具{}->{}", item.getCfgId(), type);
        } else {
            newAttr.setAttributes(attribute.getAttributes());
        }
        IdentifyAttribute buffAttr = randomBuff(role, config, lockBuffPos, oldAttr);
        if (buffAttr == null) {
            log.error("洗练时buff随机不出来，道具{}->{}", item.getCfgId(), type);
        } else {
            newAttr.setBuffs(buffAttr.getBuffs());
        }
        if (attribute == null && buffAttr == null) {
            return;
        }

//        item.getEquipData().setIdentifyAttribute(newAttr);

        final IdentifyAttribute newAttrFinal = newAttr;
        stash.update(item, up -> {
            up.getEquipData().setIdentifyAttribute(newAttrFinal);
        });
        stash.commit(role, LogAction.EQUIP_IDENTIFY);

        role.updateBackpack(stash.getCommitItemChanges());

        DataCenter.updateData(role);
        EquipManager.getInstance().refreshAttribute(role);

        if (config.getType() == 1){
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.JIAN_DING_COUNT, 1);
        }
    }

    public static IdentifyAttribute randomBuff(Role role, SpecialXiLianConfig config, List<Integer> lockBuffs,
                                               IdentifyAttribute oldAttr) {
        IdentifyAttribute result = new IdentifyAttribute();
        if (config == null) {
            return null;
        }

        for (int pos = 0; pos < config.getBuffnum().length; pos++) {
            if (lockBuffs.contains(pos)) {
                IdentifyAttributeValue cloneAttr = new IdentifyAttributeValue(oldAttr.getBuffs().get(pos));
                result.getBuffs().put(pos, cloneAttr);
                continue;
            }
            boolean check = RandomUtil.isGenerate(100, config.getBuffnum()[pos]);
            if (!check) {
                continue;
            }

            List<Integer> valueRates = new ArrayList<>();
            long[][] array2 = config.getBuffid().get(pos);
            for (long[] array : array2) {
                int career = (int) array[0];
                if (career == 0 || career == role.getCareer()) {
                    valueRates.add((int) array[2]);
                } else {
                    valueRates.add(0);
                }
            }
            int index = RandomUtil.randomIndexByProb(valueRates);
            long[] array = array2[index];
            int career = (int) array[0];
            int buffId = (int) array[1];

            result.getBuffs().put(pos, new IdentifyAttributeValue(career, buffId, 0));
        }

        return result;
    }

    public static IdentifyAttribute randomAttribute(Role role, SpecialXiLianConfig config, List<Integer> lockAttrs,
                                                    IdentifyAttribute oldAttr) {
        IdentifyAttribute result = new IdentifyAttribute();

        for (int pos = 0; pos < config.getIdentifynum().length; pos++) {
            if (lockAttrs.contains(pos)) {
                IdentifyAttributeValue cloneAttr = new IdentifyAttributeValue(oldAttr.getAttributes().get(pos));
                result.getAttributes().put(pos, cloneAttr);
                continue;
            }
            // 大组能否随机出来
            boolean check = RandomUtil.isGenerate(100, config.getIdentifynum()[pos]);
            if (!check) {
                continue;
            }
            // 大组里随机一个出来
            List<Integer> valueRates = new ArrayList<>();
            long[][] array2 = config.getAttrsection().get(pos);
            for (long[] array : array2) {
                int career = (int) array[0];
                if (career == 0 || career == role.getCareer()) {
                    valueRates.add((int) array[5]);
                } else {
                    valueRates.add(0);
                }
            }
            int index = RandomUtil.randomIndexByProb(valueRates);
            long[] array = array2[index];
            int career = (int) array[0];
            int attrId = (int) array[1];
            long rand = RandomUtil.random(array[3], array[4]);
            long value = rand * array[2] / config.getMaxrate();

            result.getAttributes().put(pos, new IdentifyAttributeValue(career, attrId, value));
        }

        return result;
    }

    @Override
    public void onRoleEquipChanged(Role role, List<ItemChange> changes) {
        EquipLocationCache cache = ConfigCacheManager.getInstance().getCache(EquipLocationCache.class);
        List<Integer> conditionRangeList = cache.findConditionRangeList();

        for (ItemChange change : changes) {
            if (change.getNItem() != null) {
                ItemConfig config = change.getNItem().findItemConfig();
                if (config.getZhuanshutip() == 1 && conditionRangeList.contains(config.getType())){
                    ConditionManager.getInstance().onCondition(role, ConditionTypeConst.EQUIP_ON_QUALITY);
                }
            }
        }
    }

    @Override
    public void onHeroEquipChanged(Hero hero, List<ItemChange> changes) {

    }

    @Override
    public void reqSave(Role role, long uid) {
//        TwoTuple<Long, EquipAttribute> identifyResult = IdentifyManager.identifyResult.get(role.getId());
//        if (uid != identifyResult.first) {
//            return;
//        }
//        Backpack backpack = DataCenter.getBackpack(role.getId());
//        Item item = backpack.findItemByUniqueId(uid);
//        if (item == null) {
//            return;
//        }
//
//        BackpackStash stash = new BackpackStash(role);
//        stash.update(item, up -> up.eData().setIdentifyAttribute(identifyResult.second));
//        if (!stash.commit(role, LogAction.EQUIP_IDENTIFY)) {
//            return;
//        }
//
//        ResIdentifySaveMessage msg = new ResIdentifySaveMessage();
//        msg.setUid(item.getId());
//        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void unlockFengyin(Role role, long lid) {
        Backpack backpack = role.getBackpack();
        Item item = backpack.findItemByUniqueId(lid);
        if (item == null) {
            return;
        }
        EquipData equipData = item.getEquipData();
        if (equipData == null) {
            return;
        }
        // 是否可以封印,或者被封印了
        if (!equipData.hasXilian() && equipData.isFengyin()) {
            return;
        }
        SpecialXiLianConfig config = ConfigDataManager.getInstance().getById(SpecialXiLianConfig.class, item.getCfgId() + "#" + 2);
        if (config == null || config.getFengyincost().isEmpty()) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        stash.decrease(config.getFengyincost());
        stash.update(item, up -> up.getEquipData().setFengyin(true));
        stash.commit(role, LogAction.EQUIP_IDENTIFY);
    }

    @Override
    public boolean checkTrade(Item item) {
        EquipData equipData = item.getEquipData();
        if (equipData == null) {
            // 非装备
            return true;
        }
        if (equipData.hasXilian()) {
            // 洗练装备
            SpecialXiLianConfig config = ConfigDataManager.getInstance().getById(SpecialXiLianConfig.class, item.getCfgId() + "#" + 1);
            if (config == null) {
                return false;
            }
            // 可直接交易的鉴定装备或者封印后的鉴定装备
            return config.getTrade() != 0 || equipData.isFengyin();
        }
        // 非洗练装备
        return true;
    }
}
