package com.sh.game.script.zhuansheng;

import com.google.common.primitives.Ints;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.zhuansheng.ResStructureInfoMessage;
import com.sh.game.common.communication.msg.system.zhuansheng.ResZhuanshengExpMessage;
import com.sh.game.common.communication.msg.system.zhuansheng.ResZhuanshengInfoMessage;
import com.sh.game.common.communication.msg.system.zhuansheng.ResZhuanshengMessage;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.StructureConfig;
import com.sh.game.common.config.model.ZhuanShengConfig;
import com.sh.game.common.constant.AttributeConst;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.ZhuanShengConst;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleAdvance;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleAttributeCountScript;
import com.sh.game.event.IEventOnRoleStructureUpScript;
import com.sh.game.event.IEventOnRoleZhuanShengUpScript;
import com.sh.game.log.entity.RoleZhuanshengLog;
import com.sh.game.protos.ZhuanshengProtos;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.game.system.backpack.BackpackManager;
import com.sh.game.system.render.RenderManager;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.zhuansheng.ZhuanShengManager;
import com.sh.game.system.zhuansheng.script.IZhuanShengScript;
import com.sh.script.ScriptEngine;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 转生系统
 */
@Slf4j
@Script
public class ZhuanShengScript implements IZhuanShengScript, IEventOnRoleAttributeCountScript {
    @Override
    public void reqZhuanSheng(Role role, long actorId) {
        RoleAdvance roleAdvance = role.getRoleAdvance();
        int expId = BagConst.ItemId.ROLE_ZHUANSHENG;
        IAvatar avatar = role;
        boolean hero = false;
        if (role.getId() != actorId) {
            if (role.getHero() == null) {
                return;
            }
            roleAdvance = role.getHero().getRoleAdvance();
            expId = BagConst.ItemId.HERO_ZHUANSHENG;
            avatar = role.getHero();
            hero = true;
        }
        int zhuanshengId = roleAdvance.getZhuanshengId();
        if (zhuanshengId == 0) {
            zhuanshengId = ZhuanShengConst.firstZhuanShengId;
            roleAdvance.setZhuanshengId(zhuanshengId);
        }

        ZhuanShengConfig zhuanShengConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, zhuanshengId);
        if (zhuanShengConfig == null) {
            return;
        }
        List<int[]> condition = zhuanShengConfig.getCondition();
        boolean check = ConditionUtil.validate(avatar, condition, true);
        if (!check) {
            return;
        }
        //当前轮结束 开启下一轮
        int nextId = zhuanShengConfig.getNextId();
        ZhuanShengConfig nextConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, nextId);
        //到顶了
        if (nextConfig == null) {
            return;
        }
        //扣道具
        int exp = zhuanShengConfig.getExp();
        List<int[]> cost = new ArrayList<>(zhuanShengConfig.getCost());
        if (exp > 0) {
            int[] needExp = {expId, exp};
            cost.add(needExp);
        }
        if (!cost.isEmpty()) {
            BackpackConst.Place where = hero ? BackpackConst.Place.BACKPACK_HERO : BackpackConst.Place.BACKPACK;
            boolean success = BackpackManager.getInstance().
                    costItem(role, cost, LogAction.ZHUAN_SHENG, null, where);
            if (!success) {
                return;
            }
        }
        int probability = zhuanShengConfig.getProbability();

        if (probability <= 0 || RandomUtil.random(0, 100) <= probability) {
            roleAdvance.setZhuanshengId(nextId);
            List<Integer> list = nextConfig.getFashionId();
            if (!list.isEmpty()) {
                for (Integer fashionId : list) {
                    AppearanceManager.getInstance().conditionWear(role, fashionId, true);
                }
            }
            //转生日志
            RoleZhuanshengLog zhuanShengLog = new RoleZhuanshengLog(role);
            zhuanShengLog.setHero(hero ? 1 : 0);
            zhuanShengLog.setOldId(zhuanShengConfig.getId());
            zhuanShengLog.setNewId(zhuanShengConfig.getNextId());
            zhuanShengLog.setAccount(role.getAccount());
            zhuanShengLog.submit();
            if (hero) {
                onHeroAttributeChange(role.getHero());
            } else {
                onRoleAttributeChange(role);
                SummaryManager.getInstance().updateZhuansheng(role.getId(), nextId);
                RenderManager.getInstance().fromZhuanSheng(role, nextConfig.getAddAttr(), false);
            }
            DataCenter.updateData(roleAdvance);
        }
        ScriptEngine.invoke1tn(IEventOnRoleZhuanShengUpScript.class, script -> script.onRoleZhuanShengUp(role, actorId));

        ResZhuanshengMessage msg = new ResZhuanshengMessage();
        msg.setProto(ZhuanshengProtos.ResZhuansheng.newBuilder()
                .setZhuansheng(ZhuanshengProtos.ZhuanShengBean.newBuilder()
                        .setActorId(actorId)
                        .setZhuanshengId(roleAdvance.getZhuanshengId())
                        .build())
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqZhuanShengInfo(Role role) {
        int zhuanshengId = role.getRoleAdvance().getZhuanshengId();
        if (zhuanshengId == 0) {
            role.getRoleAdvance().setZhuanshengId(ZhuanShengConst.firstZhuanShengId);
            DataCenter.updateData(role);
        }
        ResZhuanshengInfoMessage message = new ResZhuanshengInfoMessage();
        List<ZhuanshengProtos.ZhuanShengBean> zhuanShengList = new ArrayList<>();
        zhuanShengList.add(ZhuanshengProtos.ZhuanShengBean.newBuilder()
                .setActorId(role.getId())
                .setZhuanshengId(role.getRoleAdvance().getZhuanshengId())
                .build());
        if (role.getHero() != null) {
            RoleAdvance roleAdvance = role.getHero().getRoleAdvance();
            int heroZhuanSheng = roleAdvance.getZhuanshengId();
            if (heroZhuanSheng == 0) {
                roleAdvance.setZhuanshengId(ZhuanShengConst.firstZhuanShengId);
                DataCenter.updateData(role);
            }
            zhuanShengList.add(ZhuanshengProtos.ZhuanShengBean.newBuilder()
                    .setActorId(role.getHero().getId())
                    .setZhuanshengId(role.getHero().getRoleAdvance().getZhuanshengId())
                    .build());
        }
        message.setProto(ZhuanshengProtos.ResZhuanshengInfo.newBuilder()
                .addAllZhuansheng(zhuanShengList)
                .build());
        MessageUtil.sendMsg(message, role.getId());
    }

    @Override
    public AttributeConst.AttributeType getAttributeType() {
        return AttributeConst.AttributeType.Role.ZHUANSHENG;
    }

    @Override
    public void onHeroAttributeCount(Hero hero) {
        int zhuanshengId = hero.getRoleAdvance().getZhuanshengId();
        ZhuanShengConfig zhuanShengConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, zhuanshengId);
        if (zhuanShengConfig == null) {
            return;
        }
        Attribute attribute = new Attribute();
        attribute.fixAdd(zhuanShengConfig.getAttr(), hero.getCareer());
        hero.getAttributes().put(getAttributeType(), attribute);
    }

    @Override
    public void onRoleAttributeCount(Role role) {
        int zhuanshengId = role.getRoleAdvance().getZhuanshengId();
        ZhuanShengConfig zhuanShengConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, zhuanshengId);
        if (zhuanShengConfig == null) {
            return;
        }
        Attribute attribute = new Attribute();
        attribute.fixAdd(zhuanShengConfig.getAttr(), role.getCareer());
        role.getAttributes().put(getAttributeType(), attribute);
    }

    /**
     * 这里比较特殊的是 扣除道具会主角和英雄一起判断一起消耗 先从主角背包扣
     *
     * @param role
     * @param itemId
     * @param actorId
     */
    @Override
    public void reqZhuanShengExp(Role role, int itemId, long actorId) {
        if (role.getId() != actorId && role.getHero() == null) {
            return;
        }
        boolean isHero = role.getId() != actorId;
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemId);
        if (itemConfig == null || itemConfig.getUseType() != 165) {
            return;
        }

        RoleAdvance roleAdvance = isHero ? role.getHero().getRoleAdvance() : role.getRoleAdvance();
        int zhuanshengId = roleAdvance.getZhuanshengId() <= 0 ? ZhuanShengConst.firstZhuanShengId : roleAdvance.getZhuanshengId();
        ZhuanShengConfig zhuanShengConfig = ConfigDataManager.getInstance().getById(ZhuanShengConfig.class, zhuanshengId);
        if (zhuanShengConfig == null) {
            return;
        }
        //转数
        int zhuan = zhuanShengConfig.getLevel();
        int usePlay = itemConfig.getUsePlay()[0];
        //只能使用比当前大一转的道具
        if (usePlay - zhuan != 1) {
            return;
        }

        int expId = isHero ? BagConst.ItemId.HERO_ZHUANSHENG : BagConst.ItemId.ROLE_ZHUANSHENG;
        int myExp = role.getBackpack().getCoin().getOrDefault(expId, 0L).intValue();
        int needExp = ZhuanShengManager.getInstance().getNeedExp(zhuanshengId, zhuan);
        //最大的能获得exp
        int canGainExp = needExp - myExp;
        if (canGainExp <= 0) {
            return;
        }

        int exp = itemConfig.getUseParam()[0][0];

        Backpack backpack = role.getBackpack();
        long bagCount = backpack.fetchCountLByCfgId(itemId, BackpackConst.Place.BACKPACK);
        long heroCount = backpack.fetchCountLByCfgId(itemId, BackpackConst.Place.BACKPACK_HERO);

        //数量太多会浪费
        int needMaxCount = canGainExp % exp == 0 ? canGainExp / exp : canGainExp / exp + 1;
        long seeCount = isHero ? heroCount : bagCount;
        long costCount = seeCount < needMaxCount ? seeCount : needMaxCount;
        List<int[]> costItems = new ArrayList<>();
        int[] cost = {itemId, Ints.checkedCast(costCount)};
        costItems.add(cost);
        BackpackConst.Place where = isHero ? BackpackConst.Place.BACKPACK_HERO : BackpackConst.Place.BACKPACK;
        boolean success = BackpackManager.getInstance().
                costItem(role, costItems, LogAction.ZHUAN_SHENG, null, where);
        if (!success) {
            return;
        }
        BackpackStash stash = new BackpackStash(role);
        long gainExp = exp * costCount + myExp > canGainExp ? canGainExp : exp * costCount;
        stash.increase(expId, gainExp);
        stash.commit(role, LogAction.ZHUAN_SHENG);
        ResZhuanshengExpMessage msg = new ResZhuanshengExpMessage();
        msg.setProto(ZhuanshengProtos.ResZhuanshengExp.newBuilder().build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    @Override
    public void reqStructureInfo(Role role) {
        int structureId = role.getRoleAdvance().getStructureId();

        ResStructureInfoMessage message = new ResStructureInfoMessage();
        List<ZhuanshengProtos.ZhuanShengBean> zhuanShengList = new ArrayList<>();
        zhuanShengList.add(ZhuanshengProtos.ZhuanShengBean.newBuilder()
                .setActorId(role.getId())
                .setZhuanshengId(structureId)
                .build());
        message.setProto(ZhuanshengProtos.ResStructureInfo.newBuilder()
                .addAllZhuansheng(zhuanShengList)
                .build());
        MessageUtil.sendMsg(message, role.getId());
    }

    @Override
    public void reqStructure(Role role, long actorId) {
        RoleAdvance roleAdvance = role.getRoleAdvance();
        int structureId = roleAdvance.getStructureId();

        StructureConfig structureConfig = ConfigDataManager.getInstance().getById(StructureConfig.class, structureId);
        if (structureConfig == null) {
            return;
        }
        List<int[]> condition = structureConfig.getCondition();
        boolean check = ConditionUtil.validate(role, condition, true);
        if (!check) {
            return;
        }
        //当前轮结束 开启下一轮
        int nextId = structureConfig.getNextId();
        StructureConfig nextConfig = ConfigDataManager.getInstance().getById(StructureConfig.class, nextId);
        //到顶了
        if (nextConfig == null) {
            return;
        }
        DataCenter.updateData(roleAdvance);
        ScriptEngine.invoke1tn(IEventOnRoleStructureUpScript.class, script -> script.onRoleStructureUp(role, actorId));

        ResZhuanshengMessage msg = new ResZhuanshengMessage();
        msg.setProto(ZhuanshengProtos.ResZhuansheng.newBuilder()
                .setZhuansheng(ZhuanshengProtos.ZhuanShengBean.newBuilder()
                        .setActorId(actorId)
                        .setZhuanshengId(roleAdvance.getStructureId())
                        .build())
                .build());
        MessageUtil.sendMsg(msg, role.getId());
    }
}
