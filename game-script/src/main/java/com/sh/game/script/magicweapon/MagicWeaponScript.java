package com.sh.game.script.magicweapon;

import com.alibaba.fastjson.JSON;
import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.ThreeTuple;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.communication.msg.system.magicweapon.ResCompoundMagicWeaponMessage;
import com.sh.game.common.communication.msg.system.magicweapon.ResMagicWeaponSlotInfoMessage;
import com.sh.game.common.config.cache.MagicWeaponSlotCache;
import com.sh.game.common.config.model.EquipLocationConfig;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.MagicWeaponCompoundConfig;
import com.sh.game.common.config.model.MagicWeaponSlotConfig;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.EmailConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.item.MagicWeapon;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleLoginScript;
import com.sh.game.log.entity.RoleMagicWeaponLog;
import com.sh.game.log.entity.abs.BaseLog;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.MagicWeaponProtos;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.magicweapon.entity.RoleMagicWeaponSlot;
import com.sh.game.system.magicweapon.script.IMagicWeaponScript;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Chen
 * @date 2022/6/22 19:03
 */
@Slf4j
@Script
public class MagicWeaponScript implements
        IMagicWeaponScript,
        IEventOnRoleLoginScript {

    /**
     * 请求打造法宝
     *
     * @param role      角色
     * @param configId  cfg_fabaohecheng表id
     */
    @Override
    public void compound(Role role, int configId) {
        MagicWeaponCompoundConfig compoundConfig = ConfigDataManager.getInstance().getById(MagicWeaponCompoundConfig.class, configId);
        if (compoundConfig == null) {
            return;
        }

        BackpackStash costStash = new BackpackStash(role);
        costStash.decrease(compoundConfig.getCost());
        if (!costStash.commit(role, LogAction.MAGIC_WEAPON_COMPOUND_COST)) {
            return;
        }

        //随机装备
        Item equip = randomEquip(compoundConfig);
        if (equip == null) {
            log.error("法宝-打造-道具不存在, roleId: {} , roleName: {} , compoundConfigId: {}", role.getRoleId(), role.getName(), compoundConfig.getId());
            return;
        }

        if (equip.getCfgId() == compoundConfig.getBuffItem()) {
            //随机buffId
            int buffId = this.randomBuff(compoundConfig);
            equip.getEquipData().setMagicWeaponBuffId(buffId);
        } else {
            //随机额外属性万分比
            int magicWeaponAttributeRate = randomAttributeRate(compoundConfig);
            equip.getEquipData().setMagicWeaponAttributeRate(magicWeaponAttributeRate);
        }

        BackpackStash rewardStash = new BackpackStash(role);
        rewardStash.increase(equip);

        if (!rewardStash.commit(role, LogAction.MAGIC_WEAPON_COMPOUND_REWARD)) {
            log.info("法宝-打造-背包已满,发送到邮箱, roleId: {} , roleName: {} , compoundConfigId: {} , itemConfigId: {} , attRate: {} , buffId: {}"
                    , role.getRoleId(), role.getName(), compoundConfig.getId(), equip.getCfgId(), equip.getEquipData().getMagicWeaponAttributeRate(), equip.getEquipData().getMagicWeaponBuffId());
            rewardStash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }

        log.info("法宝-打造-成功, roleId: {} , roleName: {} , compoundConfigId: {} , itemConfigId: {} , attRate: {} , buffId: {}"
                , role.getRoleId(), role.getName(), compoundConfig.getId(), equip.getCfgId(), equip.getEquipData().getMagicWeaponAttributeRate(), equip.getEquipData().getMagicWeaponBuffId());

        AnnounceManager.getInstance().post(compoundConfig.getAnnounce(), 0L, role, equip);

        ResCompoundMagicWeaponMessage msg = new ResCompoundMagicWeaponMessage();
        msg.setItemConfigId(equip.getCfgId());
        MessageUtil.sendMsg(msg, role.getRoleId());

        RoleMagicWeaponLog weaponLog = new RoleMagicWeaponLog(role);
        weaponLog.setAction(3);
        weaponLog.setMagicCfgId(equip.getCfgId());
        weaponLog.setMagicUniqueId(equip.getId());
        weaponLog.submit();
    }

    /**
     * 请求开孔
     *
     * @param role     角色
     * @param configId equip_fabaokong槽位id
     */
    @Override
    public void reqSlot(Role role, int configId) {
        RoleMagicWeaponSlot magicWeaponSlot = findSlot(role);
        doSlot(role, configId, magicWeaponSlot);
        reqSlotInfo(role);
    }

    /**
     * 实际执行开孔
     *
     * @param role     角色
     * @param configId equip_fabaokong槽位id
     */
    private void doSlot(Role role, int configId, RoleMagicWeaponSlot magicWeaponSlot) {

        List<Integer> slotIdList = magicWeaponSlot.getSlotIdList();

        //已经开孔
        if (slotIdList.contains(configId)) {
            return;
        }

        MagicWeaponSlotConfig slotConfig = ConfigDataManager.getInstance().getById(MagicWeaponSlotConfig.class, configId);
        if (slotConfig == null) {
            log.error("法宝-开孔-配置不存在, roleId: {} , roleName: {} , configId: {}", role.getRoleId(), role.getName(), configId);
            return;
        }

        if (!ConditionUtil.validate(role, slotConfig.getCondition())) {
            return;
        }

        BackpackStash costStash = new BackpackStash(role);
        if (slotConfig.getCost() != null) {
            costStash.decrease(slotConfig.getCost());
        }
        if (!costStash.commit(role, LogAction.MAGIC_WEAPON_SLOT_COST)) {
            return;
        }

        slotIdList.add(configId);
        DataCenter.updateData(magicWeaponSlot);

    }

    /**
     * 获取角色法宝开孔信息
     *
     * @param role 角色
     * @return RoleMagicWeaponSlot 角色法宝开孔信息
     */
    @Override
    public RoleMagicWeaponSlot findSlot(Role role) {
        RoleMagicWeaponSlot magicWeaponSlot = DataCenter.get(RoleMagicWeaponSlot.class, role.getRoleId());
        if (magicWeaponSlot == null) {
            magicWeaponSlot = new RoleMagicWeaponSlot();
            magicWeaponSlot.setId(role.getRoleId());
            DataCenter.insertData(magicWeaponSlot);
        }
        return autoSlot(role, magicWeaponSlot);
    }

    /**
     * 请求发送法宝信息
     *
     * @param role 角色
     */
    @Override
    public void reqSlotInfo(Role role) {
        RoleMagicWeaponSlot magicWeaponSlot = findSlot(role);
        doSendInfo(role.getRoleId(), magicWeaponSlot);
    }

    /**
     * 执行发送法宝信息消息
     *
     * @param magicWeaponSlot 法宝信息
     */
    public void doSendInfo(long roleId, RoleMagicWeaponSlot magicWeaponSlot) {
        ResMagicWeaponSlotInfoMessage msg = new ResMagicWeaponSlotInfoMessage();
        MagicWeaponProtos.ResMagicWeaponSlotInfo.Builder magicWeaponSlotInfo = MagicWeaponProtos.ResMagicWeaponSlotInfo.newBuilder();
        magicWeaponSlotInfo.addAllConfigIdList(magicWeaponSlot.getSlotIdList());

        List<AbcProtos.MagicWeaponBlendBean> weaponList = new ArrayList<>();
        magicWeaponSlot.getMagicWeaponMap().forEach((slotConfigId, magicWeapon) -> {
            AbcProtos.MagicWeaponBlendBean.Builder bean = AbcProtos.MagicWeaponBlendBean.newBuilder();
            bean.setSlotConfigId(slotConfigId);
            bean.setItemConfigId(magicWeapon.getItemConfigId());
            bean.setMagicWeaponRate(magicWeapon.getMagicWeaponAttributeRate());
            bean.setMagicWeaponBuffId(magicWeapon.getMagicWeaponBuffId());
            weaponList.add(bean.build());
        });
        magicWeaponSlotInfo.addAllWeaponList(weaponList);
        msg.setProto(magicWeaponSlotInfo.build());
        MessageUtil.sendMsg(msg, roleId);
    }

    /**
     * 请求法器镶嵌
     *
     * @param role          角色
     * @param weaponList    镶嵌法器列表
     */
    @Override
    public void reqWeaponInlay(Role role, List<MagicWeaponProtos.MagicWeaponBean> weaponList) {
        if (weaponList.size() <= 0) {
            log.error("法宝-融合-法宝列表为空, roleId: {} , roleName: {}", role.getRoleId(), role.getName());
            return;
        }

        Backpack backpack = DataCenter.get(Backpack.class, role.getId());

        RoleMagicWeaponSlot roleMagicWeaponSlot = findSlot(role);

        List<Integer> slotIdList = roleMagicWeaponSlot.getSlotIdList();
        Map<Integer, MagicWeapon> magicWeaponMap = roleMagicWeaponSlot.getMagicWeaponMap();
        Map<Integer, MagicWeapon> magicWeaponAgencyMap = new HashMap<>(magicWeaponMap);

        //消耗道具汇总
        List<int[]> costList = new ArrayList<>();
        List<Item> weaponCostList = new ArrayList<>();

        //用于判断是否有重复的孔位
        List<Integer> slotConfigIdList = new ArrayList<>();
        List<Integer> weaponConfigIdList = new ArrayList<>();

        MagicWeaponSlotCache cache = ConfigCacheManager.getInstance().getCache(MagicWeaponSlotCache.class);

        List<RoleMagicWeaponLog> logList = new ArrayList<>();

        //校验
        for (MagicWeaponProtos.MagicWeaponBean weaponBean : weaponList) {
            long weaponId = weaponBean.getWeaponId();
            int slotConfigId = weaponBean.getSlotConfigId();
            //孔没开通
            if (!slotIdList.contains(slotConfigId)) {
                log.error("法宝-融合-孔位没有开通id, roleId: {} , roleName: {} slotConfigId: {}", role.getRoleId(), role.getName(), slotConfigId);
                continue;
            }

            //该位置已有法器
            if (magicWeaponAgencyMap.get(slotConfigId) != null) {
                log.error("法宝-融合-该孔位已有法宝, roleId: {} , roleName: {} , configId: {} , ", role.getRoleId(), role.getName(), slotConfigId);
                continue;
            }

            //法宝
            Item magicWeaponItem = backpack.findItemByUniqueId(weaponId);
            if (magicWeaponItem == null) {
                log.error("法宝-融合-法宝不存在, roleId: {} , roleName: {} itemId: {}", role.getRoleId(), role.getName(), weaponId);
                continue;
            }

            //法宝配置
            ItemConfig weaponConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, magicWeaponItem.getCfgId());
            if (weaponConfig == null) {
                log.error("法宝-融合-法宝配置不存在, roleId: {} , roleName: {} , itemConfigId: {} , ", role.getRoleId(), role.getName(), magicWeaponItem.getCfgId());
                continue;
            }

            if (weaponConfig.getWuhuntype() != 4) {
                log.error("法宝-融合-融合道具不属于法宝, roleId: {} , roleName: {} , itemConfigId: {} , wuhuntype: {}", role.getRoleId(), role.getName(), magicWeaponItem.getCfgId(), weaponConfig.getWuhuntype());
                continue;
            }

            //法宝孔配置
            MagicWeaponSlotConfig slotConfig = ConfigDataManager.getInstance().getById(MagicWeaponSlotConfig.class, slotConfigId);
            if (slotConfig == null) {
                log.error("法宝-融合-法宝孔位配置不存在, roleId: {} , roleName: {} , configId: {} , ", role.getRoleId(), role.getName(), slotConfigId);
                continue;
            }

            int[] sameList = cache.getSameList(magicWeaponItem.getCfgId());

            if (!isCheck(magicWeaponAgencyMap, cache.getAssociationConfigList(slotConfig.getPos1(), slotConfigId), sameList)) {
                log.error("法宝-融合-不能融合相同的法宝, roleId: {} , roleName: {} , itemConfigId: {} , ", role.getRoleId(), role.getName(), magicWeaponItem.getCfgId());
                continue;
            }

            int equipLocationId = weaponConfig.getType();
            EquipLocationConfig equipLocationConfig = ConfigDataManager.getInstance().getById(EquipLocationConfig.class, equipLocationId);
            if (equipLocationConfig == null) {
                log.error("法宝-融合-法宝部位配置不存在, roleId: {} , roleName: {} , itemConfigId: {} , equipLocationId: {} , ", role.getRoleId(), role.getName(), weaponConfig.getId(), equipLocationId);
                continue;
            }

            if (!ArrayUtils.contains(equipLocationConfig.getPos1(), slotConfig.getPos1())) {
                log.error("法宝-融合-法宝孔位不匹配, roleId: {} , roleName: {} , magicPos1: {} , slotPos1: {}", role.getRoleId(), role.getName(), equipLocationConfig.getPos1(), slotConfig.getPos1());
                continue;
            }

            //设置法器装备
            MagicWeapon newWeapon = new MagicWeapon();
            newWeapon.setItemConfigId(magicWeaponItem.getCfgId());
            newWeapon.setMagicWeaponAttributeRate(magicWeaponItem.getEquipData().getMagicWeaponAttributeRate());
            newWeapon.setMagicWeaponBuffId(magicWeaponItem.getEquipData().getMagicWeaponBuffId());
            magicWeaponAgencyMap.put(slotConfigId, newWeapon);

            slotConfigIdList.add(slotConfigId);
            weaponConfigIdList.add(magicWeaponItem.getCfgId());

            weaponCostList.add(magicWeaponItem);
            costList.addAll(slotConfig.getIncost());

            RoleMagicWeaponLog weaponLog = new RoleMagicWeaponLog(role);
            weaponLog.setAction(0);
            weaponLog.setMagicUniqueId(weaponBean.getWeaponId());
            weaponLog.setMagicCfgId(weaponConfig.getId());
            logList.add(weaponLog);
        }

        Set<Integer> slotConfigIdSet = new HashSet<>(slotConfigIdList);
        if (slotConfigIdSet.size() != slotConfigIdList.size()) {
            log.error("法宝-融合-重复的孔位id, roleId: {} , roleName: {} idList: {}", role.getRoleId(), role.getName(), slotConfigIdList);
            return;
        }

        Set<Integer> weaponConfigIdSet = new HashSet<>(weaponConfigIdList);
        if (weaponConfigIdSet.size() != weaponConfigIdList.size()) {
            log.error("法宝-融合-重复的法宝id, roleId: {} , roleName: {} idList: {}", role.getRoleId(), role.getName(), weaponConfigIdList);
            return;
        }

        BackpackStash costStash = new BackpackStash(role);
        costStash.decrease(weaponCostList);
        costStash.decrease(costList);
        if (!costStash.commit(role, LogAction.MAGIC_WEAPON_SLOT_IN_COST)) {
            log.error("法宝-融合-道具不足消耗失败 roleId: {} , roleName: {} , idList: {}", role.getRoleId(), role.getName(), weaponConfigIdList);
            return;
        }

        roleMagicWeaponSlot.setMagicWeaponMap(magicWeaponAgencyMap);
        DataCenter.updateData(roleMagicWeaponSlot);

        log.info("法宝-融合-成功, roleId: {} , roleName: {} \nidList: {}", role.getRoleId(), role.getName(), JSON.toJSONString(weaponList));

        doSendInfo(role.getRoleId(), roleMagicWeaponSlot);

        //提交日志
        logList.forEach(BaseLog::submit);
    }

    /**
     * 请求法器分离
     *
     * @param role             角色
     * @param slotConfigIdList 孔位配置id列表
     */
    @Override
    public void reqSeparate(Role role, List<Integer> slotConfigIdList) {
        RoleMagicWeaponSlot roleMagicWeaponSlot = findSlot(role);

        //结果法宝
        Map<Integer, MagicWeapon> magicWeaponMap = roleMagicWeaponSlot.getMagicWeaponMap();
        //没有镶嵌法宝
        if (magicWeaponMap.size() <= 0) {
            return;
        }
        Map<Integer, MagicWeapon> magicWeaponAgencyMap = new HashMap<>(magicWeaponMap);

        Set<Integer> slotConfigIdSet = new HashSet<>(slotConfigIdList);
        if (slotConfigIdSet.size() != slotConfigIdList.size()) {
            log.error("法宝-分离-重复的孔位id, roleId: {} , roleName: {} idList: {}", role.getRoleId(), role.getName(), slotConfigIdList);
            return;
        }

        //获得、消耗道具汇总
        List<Item> rewardList = new ArrayList<>();
        List<int[]> costList = new ArrayList<>();

        List<RoleMagicWeaponLog> logList = new ArrayList<>();

        for (Integer slotConfigId : slotConfigIdList) {
            MagicWeapon magicWeapon = magicWeaponAgencyMap.get(slotConfigId);
            if (magicWeapon == null) {
                log.error("法宝-分离-该孔位不存在法宝, roleId: {} , roleName: {} , configId: {} , ", role.getRoleId(), role.getName(), slotConfigId);
                continue;
            }

            MagicWeaponSlotConfig slotConfig = ConfigDataManager.getInstance().getById(MagicWeaponSlotConfig.class, slotConfigId);
            if (slotConfig == null) {
                log.error("法宝-分离-孔位配置不存在, roleId: {} , roleName: {} , configId: {} , ", role.getRoleId(), role.getName(), slotConfigId);
                continue;
            }

            Item weaponItem = ItemUtil.create(magicWeapon.getItemConfigId(), 1, LogAction.MAGIC_WEAPON_SLOT_OUT_REWARD);

            weaponItem.getEquipData().setMagicWeaponAttributeRate(magicWeapon.getMagicWeaponAttributeRate());
            weaponItem.getEquipData().setMagicWeaponBuffId(magicWeapon.getMagicWeaponBuffId());

            magicWeaponAgencyMap.remove(slotConfigId);

            costList.addAll(slotConfig.getOutcost());
            rewardList.add(weaponItem);

            RoleMagicWeaponLog weaponLog = new RoleMagicWeaponLog(role);
            weaponLog.setAction(1);
            weaponLog.setMagicCfgId(weaponItem.getCfgId());
            logList.add(weaponLog);
        }

        BackpackStash costStash = new BackpackStash(role);
        costStash.decrease(costList);
        if (!costStash.commit(role, LogAction.MAGIC_WEAPON_SLOT_OUT_COST)) {
            return;
        }

        roleMagicWeaponSlot.setMagicWeaponMap(magicWeaponAgencyMap);
        DataCenter.updateData(roleMagicWeaponSlot);

        BackpackStash rewardStash = new BackpackStash(role);
        rewardStash.increase(rewardList);
        if (!rewardStash.commit(role, LogAction.MAGIC_WEAPON_SLOT_OUT_REWARD)) {
            log.info("法宝-分离-法宝,背包已满, roleId: {} , roleName: {}", role.getRoleId(), role.getName());
            rewardStash.commitToMail2(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW, false);
        }

        log.info("法宝-分离-成功, roleId: {} , roleName: {} , idList: {} \n 分离后装备位上法宝: {}", role.getRoleId(), role.getName(), slotConfigIdList, JSON.toJSONString(magicWeaponMap));

        doSendInfo(role.getRoleId(), roleMagicWeaponSlot);

        //提交日志
        logList.forEach(BaseLog::submit);
    }

    /**
     * 获取部位法宝信息
     *
     * @param role  角色
     * @param where 背包类型
     * @param pos   部位
     * @return RoleMagicWeaponSlot 角色法宝开孔信息
     */
    @Override
    public Map<Integer, MagicWeapon> find(Role role, int where, int pos) {
        //没有穿戴的装备不返回法宝信息
        if (where != BackpackConst.Place.EQUIP.getWhere()) {
            return new HashMap<>();
        }
        RoleMagicWeaponSlot roleMagicWeaponSlot = findSlot(role);
        MagicWeaponSlotCache cache = ConfigCacheManager.getInstance().getCache(MagicWeaponSlotCache.class);
        List<Integer> configList = cache.getAssociationConfigList(pos);
        return roleMagicWeaponSlot.getMagicWeaponMap().entrySet().stream()
                //过滤不属于关联部位的法宝信息
                .filter(v -> configList.contains(v.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    /**
     * 自动开启免费条件孔
     *
     * @param role              角色
     * @param magicWeaponSlot   法宝开孔信息
     * @return RoleMagicWeaponSlot 法宝开孔信息
     */
    private RoleMagicWeaponSlot autoSlot(Role role, RoleMagicWeaponSlot magicWeaponSlot) {
        MagicWeaponSlotCache slotCache = ConfigCacheManager.getInstance().getCache(MagicWeaponSlotCache.class);
        List<Integer> slotIdList = magicWeaponSlot.getSlotIdList();

        //过滤出未开启的免费条件孔
        ArrayList<Integer> copyList = new ArrayList<>(slotCache.getFreeList());
        copyList.removeAll(slotIdList);
        copyList.forEach(configId -> {
            doSlot(role, configId, magicWeaponSlot);
        });
        return magicWeaponSlot;
    }

    /**
     * 根据权重随机获取额外属性万分比
     *
     * @param compoundConfig 法宝合成配置
     * @return int 额外属性万分比
     */
    private int randomAttributeRate(MagicWeaponCompoundConfig compoundConfig) {
        //first: rateList 属性万分比列表  second: weightList 权重列表
        TwoTuple<List<Integer>, List<Integer>> attributeTuple = new TwoTuple<>(new ArrayList<>(), new ArrayList<>());

        for (int[] params : compoundConfig.getRandomAtt()) {
            if (params.length >= 2) {
                attributeTuple.getFirst().add(params[0]);
                attributeTuple.getSecond().add(params[1]);
            }
        }
        int index = RandomUtil.randomIndexByProb(attributeTuple.getSecond());
        return attributeTuple.getFirst().get(index);
    }

    /**
     * 根据权重随机获取装备
     *
     * @param compoundConfig 法宝合成配置
     * @return Item 装备道具
     */
    private Item randomEquip(MagicWeaponCompoundConfig compoundConfig) {
        //first: itemConfigIdList 道具配置id列表  second: countList 数量列表   third: weightList 权重列表
        ThreeTuple<List<Integer>, List<Integer>, List<Integer>> itemTuple = new ThreeTuple<>(new ArrayList<>(), new ArrayList<>(), new ArrayList<>());
        for (int[] params : compoundConfig.getRandom()) {
            if (params.length >= 3) {
                itemTuple.getFirst().add(params[0]);
                itemTuple.getSecond().add(params[1]);
                itemTuple.getThird().add(params[2]);
            }
        }

        int index = RandomUtil.randomIndexByProb(itemTuple.getThird());
        ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, itemTuple.getFirst().get(index));
        //配置不存在
        if (itemConfig == null) {
            log.error("法宝-打造-道具配置不存在, compoundConfigId: {} , itemConfigId: {}", compoundConfig.getId(), itemTuple.getFirst().get(index));
            return null;
        }
        return ItemUtil.create(itemConfig.getId(), itemTuple.getSecond().get(index), LogAction.MAGIC_WEAPON_COMPOUND_REWARD);
    }

    /**
     * 根据权重随机获取装备
     *
     * @param compoundConfig 法宝合成配置
     * @return Item 装备道具
     */
    private int randomBuff(MagicWeaponCompoundConfig compoundConfig) {
        //first: buffIdList buffId列表  second: weightList 权重列表
        TwoTuple<List<Integer>, List<Integer>> buffTuple = new TwoTuple<>(new ArrayList<>(), new ArrayList<>());

        for (int[] params : compoundConfig.getRandonmBuff()) {
            if (params.length >= 2) {
                buffTuple.getFirst().add(params[0]);
                buffTuple.getSecond().add(params[1]);
            }
        }
        int index = RandomUtil.randomIndexByProb(buffTuple.getSecond());
        return buffTuple.getFirst().get(index);
    }


    /**
     * 校验
     * 是否有融合相同的法宝
     *
     * @param magicWeaponMap        当前融合的法宝
     * @param associationConfigList 关联的部位idList
     * @param sameList              需要融合的法宝相同id列表
     * @return boolean true:通过 false:不通过
     */
    private boolean isCheck(Map<Integer, MagicWeapon> magicWeaponMap, List<Integer> associationConfigList, int[] sameList) {
        for (Integer configId : associationConfigList) {
            MagicWeapon magicWeapon = magicWeaponMap.getOrDefault(configId, new MagicWeapon());
            if (ArrayUtils.contains(sameList, magicWeapon.getItemConfigId())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 计算法宝属性加成
     *
     * @param percent   属性倍率(万分比)
     * @param attribute 基础属性
     * @param career    角色职业
     * @return Attribute 属性
     */
    private Attribute calAttrPercent(int percent,Map<Integer, Long>[] attribute,int career) {
        Attribute attr = new Attribute();
        attr.fixAdd(attribute, career);
        for (Map.Entry<Integer, Long> entry : attr.getAttributeMap().entrySet()) {
            long value = entry.getValue() * percent / 10000;
            entry.setValue(value);
        }
        return attr;
    }

    /**
     * 登录结束
     * 用于修复 法宝镶嵌在装备 的版本
     * 将已经镶嵌在装备的法宝拆卸返还给玩家
     *
     * @param role 角色
     */
    @Override
    public void onRoleLogin(Role role) {
        //拆卸法宝、补偿融合消耗的元宝,通过邮件发送
        Backpack backpack = DataCenter.get(Backpack.class, role.getId());

        List<Item> weaponAddList = new ArrayList<>();
        List<int[]> addList = new ArrayList<>();

        List<Item> itemList = backpack.findItemByItemType(BagConst.ItemType.EQUIP, BackpackConst.Browse.BACKPACK_EQUIP_ALL);
        BackpackStash equipStash = new BackpackStash(role);
        for (Item equip : itemList) {
            if (equip.getEquipData() == null) {
                continue;
            }
            //标记是否有拆卸
            boolean flag = false;
            for (Map.Entry<Integer, MagicWeapon> magicWeaponEntry : equip.getEquipData().getMagicWeaponMap().entrySet()) {
                int slotConfigId = magicWeaponEntry.getKey();
                MagicWeapon magicWeapon = magicWeaponEntry.getValue();
                Item weaponItem = ItemUtil.create(magicWeapon.getItemConfigId(), 1, LogAction.MAGIC_WEAPON_SLOT_OUT_REWARD);
                MagicWeaponSlotConfig slotConfig = ConfigDataManager.getInstance().getById(MagicWeaponSlotConfig.class, slotConfigId);
                weaponItem.getEquipData().setMagicWeaponAttributeRate(magicWeapon.getMagicWeaponAttributeRate());
                weaponItem.getEquipData().setMagicWeaponBuffId(magicWeapon.getMagicWeaponBuffId());
                weaponAddList.add(weaponItem);
                addList.addAll(slotConfig.getIncost());
                flag = true;
            }
            if (flag) {
                equipStash.update(equip, up -> {
                    up.getEquipData().getMagicWeaponMap().clear();
                });
            }
        }

        if (equipStash.getStash().isEmpty() && equipStash.getUpdate().isEmpty() && equipStash.getCache().isEmpty() && addList.isEmpty()) {
            return;
        }

        log.info("法宝-功能修改修复数据, roleId: {} roleName: {} \n equip: {} \n add: {}", role.getRoleId(), role.getName(), JSON.toJSONString(equipStash.getUpdate()), addList);


        if (!equipStash.commit(role, LogAction.MAGIC_WEAPON_SLOT_OUT_EQUIP)) {
            return;
        }
        role.updateBackpack(equipStash.getCommitItemChanges());

        BackpackStash addStash = new BackpackStash(role);
        addStash.increase(weaponAddList);
        addStash.increase(addList);
        addStash.commitToMail(role.getRoleId(), EmailConst.MailId.BACK_OVER_FLOW);
    }
}
