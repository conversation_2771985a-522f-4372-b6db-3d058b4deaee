package com.sh.game.script.activity;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.activity.ResActivityCycleRechargeDaysInfoMessage;
import com.sh.game.common.config.model.LeiChongHuiKuiConfig;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.CycleRechargeConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleActivity;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleMidnightScript;
import com.sh.game.event.IEventOnRoleRechargedScript;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.script.activity.abc.AbstractActivityScript;
import com.sh.game.system.activity.entity.ActivitySchedule;
import com.sh.game.system.activity.entity.role.CycleRechargeData;
import com.sh.game.system.activity.script.IActivityCycleRechargeDaysScript;
import com.sh.game.system.mail.MailManager;
import com.sh.game.system.rewardinfo.RewardInfoManager;
import com.sh.script.annotation.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 周期连充
 */
@Slf4j
@Script
public class ActivityCycleRechargeDaysScript extends AbstractActivityScript implements IActivityCycleRechargeDaysScript,
        IEventOnRoleRechargedScript, IEventOnRoleMidnightScript {

    @Override
    public int getType() {
        return 1034;
    }

    @Override
    public void onRoleRecharged(Role role, RechargeConfig rechargeConfig) {
        if (isNotInActivity(role)) {
            return;
        }

        RoleActivity roleActivity = findRoleActitiy(role);
        CycleRechargeData data = roleActivity.getCycleRechargeData();
        if (!data.isDailyFirst()) {
            int minAmount = GlobalUtil.getGlobalInt(GameConst.GlobalId.LEI_TIAN_RECHARGE);
            if (role.getRecharge().getRechargedDaily() < minAmount) {
                return;
            }
            data.setDailyFirst(true);
            data.setTotalDays(data.getTotalDays() + 1);
            DataCenter.updateData(roleActivity);

            reqDaysInfo(role);
        }
    }

    /**
     * 连充信息
     *
     * @param role
     */
    @Override
    public void reqDaysInfo(Role role) {
        if (isNotInActivity(role)) {
            return;
        }

        verifNextCycle(role);

        RoleActivity roleActivity = findRoleActitiy(role);
        CycleRechargeData data = roleActivity.getCycleRechargeData();

        ResActivityCycleRechargeDaysInfoMessage msg = new ResActivityCycleRechargeDaysInfoMessage();
        ActivityProtos.ResActivityCycleRechargeDaysInfo.Builder protoBuilder = ActivityProtos.ResActivityCycleRechargeDaysInfo.newBuilder();
        protoBuilder.setDays(data.getTotalDays());
        protoBuilder.setCurrentCycle(data.getDaysRechargeRound());
        protoBuilder.setEndTime(data.getDaysRechargeEndTime());

        for (LeiChongHuiKuiConfig config : ConfigDataManager.getInstance().getList(LeiChongHuiKuiConfig.class)) {
            if (config.getCycle() != 0 && config.getCycle() != data.getDaysRechargeRound()) {
                continue;
            }
            ActivityProtos.ActivityStatusBean.Builder bean = ActivityProtos.ActivityStatusBean.newBuilder();
            bean.setCid(config.getId());
            bean.setStatus(data.getReceiveDay().contains(config.getId()) ? 1 : 0);
            protoBuilder.addStatus(bean);
        }
        msg.setProto(protoBuilder.build());
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 领取天数奖励
     *
     * @param role
     * @param id
     */
    @Override
    public void reqAcquireDays(Role role, int id) {
        if (isNotInActivity(role)) {
            return;
        }

        LeiChongHuiKuiConfig config = ConfigDataManager.getInstance().getById(LeiChongHuiKuiConfig.class, id);
        if (config == null) {
            return;
        }
        RoleActivity roleActivity = findRoleActitiy(role);
        CycleRechargeData data = roleActivity.getCycleRechargeData();
        if (data.getDaysRechargeRound() != config.getCycle() && config.getCycle() != 0) {
            reqDaysInfo(role);
            return;
        }
        if (!ConditionUtil.validate(role, config.getCondition())) {
            log.error("累天充值活动:{},领取奖励,条件不满足,玩家:{},昵称：{},玩家轮次:{},领取奖励id:{}", config.getActivityID(), role.getId(),
                    role.getName(), data.getDaysRechargeRound(), id);
            return;
        }
        if (data.getTotalDays() < config.getCount()) {
            TipUtil.show(role.getId(), CommonTips.脚本_条件不满足);
            return;
        }
        if (data.getReceiveDay().contains(config.getId())) {
            TipUtil.show(role.getId(), CommonTips.脚本_已经获取过了);
            return;
        }
        // 发放奖励
        BackpackStash stash = new BackpackStash(role);
        stash.increase(config.getReward());
        if (!stash.commit(role, LogAction.CYCLE_DAYS_RECHARGE)) {
            return;
        }
        data.getReceiveDay().add(config.getId());
        DataCenter.updateData(roleActivity);
        RewardInfoManager.getInstance().resRewardInfo(role, LogAction.CYCLE_DAYS_RECHARGE.getCode(), config.getReward());
        reqDaysInfo(role);
    }

    /**
     * 刷新玩家连充数据
     *
     * @param role
     */
    private void refreshData(Role role, CycleRechargeData data) {
        List<List<int[]>> unReceive = new ArrayList<>();
        for (LeiChongHuiKuiConfig config : ConfigDataManager.getInstance().getList(LeiChongHuiKuiConfig.class)) {
            if (config.getActivityType() != getType()) {
                continue;
            }
            if (config.getCycle() != 0 && config.getCycle() != data.getDaysRechargeRound()) {
                continue;
            }
            if (data.getTotalDays() >= config.getCount() && !data.getReceiveDay().contains(config.getId())) {
                unReceive.add(config.getReward());
            }
        }
        // 发送未领取的奖励
        if (!unReceive.isEmpty()) {
            List<Item> items = unReceive.stream()
                    .flatMap(List::stream).collect(Collectors.groupingBy(pair -> pair[0], Collectors.summingInt(pair -> pair[1])))
                    .entrySet().stream()
                    .map(entry -> ItemUtil.create(entry.getKey(), entry.getValue(), LogAction.CYCLE_DAYS_RECHARGE))
                    .collect(Collectors.toList());
            MailManager.getInstance().sendMail(role.getId(), CycleRechargeConst.DAYS_RECHARGE_MAIL, items);
        }
        data.setTotalDays(0);
        data.getReceiveDay().clear();
    }

    @Override
    protected void onScheduleEndPrivate(ActivitySchedule schedule, Role role) {
        RoleActivity roleActivity = findRoleActitiy(role);
        CycleRechargeData data = roleActivity.getCycleRechargeData();
        refreshData(role, data);
        DataCenter.updateData(roleActivity);
    }

    private void verifNextCycle(Role role) {
        RoleActivity roleActivity = findRoleActitiy(role);
        CycleRechargeData data = roleActivity.getCycleRechargeData();

        List<LeiChongHuiKuiConfig> configs = ConfigDataManager.getInstance().getList(LeiChongHuiKuiConfig.class);
        Map<Integer, List<LeiChongHuiKuiConfig>> map = configs.stream().collect(Collectors.groupingBy(LeiChongHuiKuiConfig::getCycle));

        List<LeiChongHuiKuiConfig> leiChongHuiKuiConfigs = map.get(data.getDaysRechargeRound());
        if (CollectionUtils.isEmpty(leiChongHuiKuiConfigs)) {
            return;
        }
        List<LeiChongHuiKuiConfig> nextConfigs = map.get(data.getDaysRechargeRound() + 1);
        if (CollectionUtils.isEmpty(nextConfigs)){
            return;
        }
        List<Integer> configIds = leiChongHuiKuiConfigs.stream().map(LeiChongHuiKuiConfig::getId).collect(Collectors.toList());
        Integer maxCycle = Collections.max(map.keySet(), Comparator.naturalOrder());
        if (new HashSet<>(data.getReceiveDay()).containsAll(configIds) && data.getDaysRechargeRound() < maxCycle && ConditionUtil.validate(role, nextConfigs.get(0).getCondition())) {
            data.setDaysRechargeRound(nextConfigs.get(0).getCycle());
            refreshData(role, data);
            DataCenter.updateData(roleActivity);
        }
    }

    @Override
    public void onRoleMidnight(Role role) {
        ActivitySchedule schedule = getAvailableSchedule(role);
        if (schedule == null) {
            return;
        }
        RoleActivity roleActivity = findRoleActitiy(role);
        CycleRechargeData data = roleActivity.getCycleRechargeData();
        // 重置今日是否可累计
        data.setDailyFirst(false);

        List<LeiChongHuiKuiConfig> configs = ConfigDataManager.getInstance().getList(LeiChongHuiKuiConfig.class);
        List<LeiChongHuiKuiConfig> filterConfigs = configs.stream()
                .filter(config -> config.getCount() == 0 && config.getActivityID() == schedule.getActivityID())
                .collect(Collectors.toList());
        // 删除每日赠送，让玩家隔天还能领取
        for (LeiChongHuiKuiConfig filterConfig : filterConfigs) {
            data.getReceiveDay().removeIf(x -> x == filterConfig.getId());
        }
        DataCenter.updateData(roleActivity);

        reqDaysInfo(role);
    }
}
