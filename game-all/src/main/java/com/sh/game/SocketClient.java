package com.sh.game;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;

import java.nio.charset.Charset;

/**
 * <AUTHOR>
 * @date 2019/7/4 19:42
 */
public class SocketClient {


    public static void main(String[] args) throws InterruptedException {
        NioEventLoopGroup workerGroup = new NioEventLoopGroup(4);
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(workerGroup);

        bootstrap.channel(NioSocketChannel.class);
        //bootstrap.option(ChannelOption.SO_BACKLOG, 1024);
        //bootstrap.option(ChannelOption.SO_RCVBUF, 128 * 1024);
        //bootstrap.option(ChannelOption.SO_SNDBUF, 128 * 1024);
        bootstrap.handler(new LoggingHandler(LogLevel.DEBUG));
        bootstrap.handler(new ChannelInitializer<SocketChannel>(){

            @Override
            protected void initChannel(SocketChannel ch) throws Exception {
                ch.pipeline().addLast(new StringEncoder(Charset.forName("UTF8")));
                ch.pipeline().addLast(new StringDecoder(Charset.forName("UTF8")));
                ch.pipeline().addLast(new ChannelInboundHandlerAdapter() {
                    @Override
                    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
                        //Thread.sleep(1000);
                        System.out.println(msg);
                        System.out.println();
                    }
                });
            }
        });


        ChannelFuture connect = bootstrap.connect("*************", 8888);
        //ChannelFuture connect = bootstrap.connect("127.0.0.1", 8888);
        connect.sync();

        while(true) {
            Thread.sleep(1000000L);
        }



    }


}
