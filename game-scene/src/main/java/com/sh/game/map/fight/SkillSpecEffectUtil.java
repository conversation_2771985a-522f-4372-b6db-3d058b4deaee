package com.sh.game.map.fight;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.cd.CDUtil;
import com.sh.game.common.communication.msg.map.ResPiaoZiEffectMessage;
import com.sh.game.common.communication.msg.map.fight.ResPassiveSkillNoticeMessage;
import com.sh.game.common.config.model.SkillConditionConfig;
import com.sh.game.common.config.model.SkillConfig;
import com.sh.game.common.config.model.SkillSpecialConfig;
import com.sh.game.common.constant.CdConst;
import com.sh.game.common.constant.FightConst;
import com.sh.game.common.constant.SkillConst;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.util.ParamAddUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.protos.FightProtos;
import com.sh.game.protos.MapProtos;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/5/6
 * @Desc : to do anything
 */
@Slf4j
public class SkillSpecEffectUtil {

    public static int calSkillSpecAdd(Performer performer, Skill skill, ParamType paramType) {
        return calSkillSpecAdd(performer, skill.getSkillId(), paramType);
    }

    public static int calSkillSpecAdd(Performer performer, int skillId, ParamType paramType) {
        int paramAdd = 0;
        Set<Integer> skillSpecIds = performer.getSkillSpecEffectMap().get(skillId);
        if (CollectionUtils.isEmpty(skillSpecIds)) {
            return paramAdd;
        }

        long now = TimeUtil.getNowOfMills();
        boolean cool = CDUtil.isCool(performer, CdConst.CdType.SPECIAL_SKILL, paramType.getIndex(), now);
        if (!cool) {
            return paramAdd;
        }

        for (Integer skillSpecId : skillSpecIds) {
            SkillSpecialConfig skillSpecialConfig = ConfigDataManager.getInstance().getById(SkillSpecialConfig.class, skillSpecId);
            int param = paramType.get(skillSpecialConfig);
            if (param == 0) {
                continue;
            }

            paramAdd += param;

            //增加cd
            if (paramType == ParamType.SKILL_CD_CLEAR_RATE) {
                if (RandomUtil.isGenerate(10000, param)) {
                    CDUtil.addCd(performer, CdConst.CdType.SPECIAL_SKILL, paramType.getIndex(), now + skillSpecialConfig.getSkillSpecialCdTime());
                    paramAdd = 1;
                    break;
                } else {
                    paramAdd = 0;
                }
            } else {
                if (skillSpecialConfig.getSkillSpecialCdTime() > 0) {
                    CDUtil.addCd(performer, CdConst.CdType.SPECIAL_SKILL, paramType.getIndex(), now + skillSpecialConfig.getSkillSpecialCdTime());
                }
            }
        }
        return paramAdd;
    }

    public enum ParamType {
        RATE(1) {//技能比例

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getRate();
            }
        },
        EXTRA_HURT(2) {//技能额外伤害

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getExtraHurt();
            }
        },
        PK_HURT(3) {//对玩家伤害加成万分比

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getPkHurt();
            }
        },
        HURT(4) {//对玩家伤害数值

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getHurt();
            }
        },
        MON_HURT_ADD(5) {//对怪物伤害加成万分比

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getMonHurtAdd();
            }
        },
        MON_HURT_ADD_FIX(6) {//对怪物伤害数值

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getMonHurtAddFix();
            }
        },
        CD_TIME(6) {//技能冷却数值

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getCdTime();
            }
        },
        MAX_TARGET(7) {//攻击目标数

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getMaxtarget();
            }
        },
        ADD_BUFF_TIME(8) {//增加技能buff持续时间

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getBuffTime();
            }
        },
        SKILL_CD_CLEAR_RATE(9) {//技能重置cd万分比概率

            @Override
            public int get(SkillSpecialConfig config) {
                return config.getSkillCdClearRate();
            }
        },
        ;

        private int index;

        ParamType(int index) {
            this.index = index;
        }

        public abstract int get(SkillSpecialConfig config);

        public int getIndex() {
            return index;
        }
    }

    //----------------------------------------被动技能效果--------------------------------------

    /**
     * 释放主动技能前检测被动触发
     */
    public static void triggerPassiveOnCast(Skill skill, Performer performer, FightResult ret) {
        for (Skill passiveSkill : performer.getPassiveSkillMap().values()) {
            SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, passiveSkill.getSkillId());
            SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, passiveSkill.getSkillId() + Symbol.JINHAO + passiveSkill.getLevel());

            if (skillConditionConfig == null) {
                continue;
            }

            switch (skillConfig.getHandlerType()) {
                case SkillConst.HandlerType.ADD_ATTRIBUTE: {
                    if (PlayerManager.checkCanPracticeAdd(performer, passiveSkill)) {
                        ret.practiceAddSet.add(passiveSkill.getSkillId());
                    }
                    break;
                }
                case SkillConst.HandlerType.XUE_YING_DAO_FA: {
                    //每次消耗HP量|低于总血量万分比不触发|触发概率万分比|值1#值2#值3#值4|指定技能#指定技能
                    Map<Integer, Set<Integer>> skillSpecEffectMap = performer.getSkillSpecEffectMap();
                    int costHp = ParamAddUtil.calSkillConditionParam(skillSpecEffectMap, skillConditionConfig, 0, 0);
                    int triggerHpPercent = ParamAddUtil.calSkillConditionParam(skillSpecEffectMap, skillConditionConfig, 1, 0);
                    int triggerProbability = ParamAddUtil.calSkillConditionParam(skillSpecEffectMap, skillConditionConfig, 2, 0);
                    int[] canTriggerSkills = ParamAddUtil.calSkillConditionParams(skillSpecEffectMap, skillConditionConfig, 4);

                    boolean include = false;
                    for (int canTriggerSkill : canTriggerSkills) {
                        if (skill.getSkillId() == canTriggerSkill) {
                            include = true;
                            break;
                        }
                    }
                    if (!include) {
                        continue;
                    }



                    /**
                     * 从记录获取本次释放概率
                     */

                    Boolean passiveSkillNotice = null;
                    if (performer instanceof PlayerActor) {
                        passiveSkillNotice = ((PlayerActor) performer).isPassiveSkillNotice();
                    }

                    /**
                     * 设置下次概率
                     * 1、计算本次概率
                     * 2、设置奔驰概率到记录
                     * 3、如果如果为ture，则需要通知
                     */
                    //判断本次概率并且设置
                    boolean passiveSkillNext = RandomUtil.isGenerate(10000, triggerProbability);
                    if (performer instanceof PlayerActor) {
                        //更新下次结果
                        ((PlayerActor) performer).setPassiveSkillNotice(passiveSkillNext);
                    }

                    //判断预警
                    if (passiveSkillNext) {
                        ResPassiveSkillNoticeMessage noticeMessage = new ResPassiveSkillNoticeMessage();
                        noticeMessage.setProto(FightProtos.ResPassiveSkillNotice.newBuilder()
                                .setSkillId(passiveSkill.getSkillId())
                                .build());
                        Module.MSG_TRANSFORMER.sendMsg(noticeMessage, performer.getRid());
                    }

                    if (passiveSkillNotice == null) {
                        passiveSkillNotice = RandomUtil.isGenerate(10000, triggerProbability);
                    }

                    //不释放
                    if (!passiveSkillNotice) {
                        continue;
                    }

                    int hpPercent = (int) (performer.getHp() * 10000.0 / performer.getFinalAttribute().findMaxHp());
                    if (hpPercent < triggerHpPercent) {
                        continue;
                    }

                    int[] addValues = ParamAddUtil.calSkillConditionParams(skillSpecEffectMap, skillConditionConfig, 3);
                    if (addValues.length != 4) {
                        log.error("配置技能参数不正确->{}", passiveSkill.getSkillId());
                        continue;
                    }

                    if (performer.getHp() <= costHp) {
                        continue;
                    }

                    if (ret.buffEffect.skillHurtAdd == null) {
                        ret.buffEffect.skillHurtAdd = new int[4];
                    }
                    int[] skillHurtAdd = ret.buffEffect.skillHurtAdd;

                    for (int i = 0; i < addValues.length; i++) {
                        skillHurtAdd[i] += addValues[i];
                    }

                    performer.setHp(performer.getHp() - costHp);
                    performer.sendHpMpChangeMessage();
                    if (performer.isPlayer()) {
                        ResPiaoZiEffectMessage piaoMsg = new ResPiaoZiEffectMessage();
                        MapProtos.ResPiaoZiEffect.Builder piaoZiEffect = MapProtos.ResPiaoZiEffect.newBuilder();
                        piaoZiEffect.setTargetId(performer.getRid());
                        piaoZiEffect.setParam(costHp);
                        piaoZiEffect.setPiaozi(FightConst.HurtType.NORMAL_HURT.type());
                        piaoMsg.setProto(piaoZiEffect.build());
                        Module.MSG_TRANSFORMER.sendMsg(piaoMsg, performer.getRid());
                    }

                    if (PlayerManager.checkCanPracticeAdd(performer, passiveSkill)) {
                        ret.practiceAddSet.add(passiveSkill.getSkillId());
                    }
                    ret.addPassiveEffect(passiveSkill.getSkillId());
                    break;
                }
                case SkillConst.HandlerType.CAN_YING_DAO_FA: {
                    //触发技能ID|触发技能等级|触发概率万分比|指定技能1#指定技能2
                    Map<Integer, Set<Integer>> skillSpecEffectMap = performer.getSkillSpecEffectMap();
                    int triggerSkillId = ParamAddUtil.calSkillConditionParam(skillSpecEffectMap, skillConditionConfig, 0, 0);
                    int triggerSkillLevel = ParamAddUtil.calSkillConditionParam(skillSpecEffectMap, skillConditionConfig, 1, 0);
                    int triggerProbability = ParamAddUtil.calSkillConditionParam(skillSpecEffectMap, skillConditionConfig, 2, 0);
                    int[] canTriggerSkills = ParamAddUtil.calSkillConditionParams(skillSpecEffectMap, skillConditionConfig, 3);

                    boolean include = false;
                    for (int canTriggerSkill : canTriggerSkills) {
                        if (skill.getSkillId() == canTriggerSkill) {
                            include = true;
                            break;
                        }
                    }
                    if (!include) {
                        continue;
                    }

                    if (!RandomUtil.isGenerate(10000, triggerProbability)) {
                        continue;
                    }

                    //放到当前技能释放完毕后去释放
                    Skill triggerSkill = new Skill();
                    triggerSkill.setSkillId(triggerSkillId);
                    triggerSkill.setLevel(triggerSkillLevel);
                    ret.buffEffect.canYingTriggerSkill = triggerSkill;

                    if (PlayerManager.checkCanPracticeAdd(performer, passiveSkill)) {
                        ret.practiceAddSet.add(passiveSkill.getSkillId());
                    }
                    ret.addPassiveEffect(passiveSkill.getSkillId());
                    break;
                }
            }
        }
    }

    /**
     * 怪物死亡时检测被动触发
     */
    public static void triggerPassiveOnMonsterDie(Performer killer, Performer target, FightResult ret) {
        for (Skill passiveSkill : killer.getPassiveSkillMap().values()) {
            SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, passiveSkill.getSkillId());
            SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, passiveSkill.getSkillId() + Symbol.JINHAO + passiveSkill.getLevel());

            if (skillConditionConfig == null) {
                continue;
            }

            switch (skillConfig.getHandlerType()) {
                case SkillConst.HandlerType.ADD_EXP: {
                    //概率#经验加成万分比
                    int triggerProbability = ParamAddUtil.calSkillConditionParam(killer.getSkillSpecEffectMap(), skillConditionConfig, 0, 0);
                    if (!RandomUtil.isGenerate(10000, triggerProbability)) {
                        continue;
                    }

                    int expAddPercent = ParamAddUtil.calSkillConditionParam(killer.getSkillSpecEffectMap(), skillConditionConfig, 0, 1);
                    target.getBuffState().setExpAddPercent(expAddPercent);

                    if (PlayerManager.checkCanPracticeAdd(killer, passiveSkill)) {
                        ret.practiceAddSet.add(passiveSkill.getSkillId());
                    }
                    break;
                }
                case SkillConst.HandlerType.MO_HUN_SHU: {
                    //概率#等级参数万分比
                    int triggerProbability = ParamAddUtil.calSkillConditionParam(killer.getSkillSpecEffectMap(), skillConditionConfig, 0, 0);
                    if (!RandomUtil.isGenerate(10000, triggerProbability)) {
                        continue;
                    }

                    int levelRate = ParamAddUtil.calSkillConditionParam(killer.getSkillSpecEffectMap(), skillConditionConfig, 0, 1);

                    killer.setMp(Math.min((int) killer.getFinalAttribute().findMaxMp(), killer.getMp() + (int) (target.getLevel() * levelRate / 10000.0)));
                    killer.sendHpMpChangeMessage();

                    if (PlayerManager.checkCanPracticeAdd(killer, passiveSkill)) {
                        ret.practiceAddSet.add(passiveSkill.getSkillId());
                    }
                    break;
                }
            }
        }
    }
}
