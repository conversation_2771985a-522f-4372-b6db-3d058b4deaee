package com.sh.game.map.obj;

import com.sh.game.common.communication.notice.scene.EscortUpdateToPlayerNotice;
import com.sh.game.common.constant.EscortConst;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.map.Module;
import com.sh.game.map.SceneManager;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 镖车场景对象
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/7/26.
 */
@Setter
@Getter
@Slf4j
public class EscortActor extends MonsterActor {
    private long roleId;
    private long unionId;
    private int host;
    private int escortCid;
    private int route;//押镖routeCid
    private int expire;
    /**
     * 状态
     * 0 默认
     * 1 放弃
     * 2 成功
     */
    private int status = 0;

    @Override
    public int getType() {
        return MapObjectType.ESCORT;
    }

    @Override
    public boolean penetrate(IMapObject stand, int penetrable) {
        return true;
    }

    @Override
    public void setHp(long hp) {
        super.setHp(hp);

        sync();
    }

    @Override
    public void setKillerId(long killerId) {

        status = EscortConst.Status.ROBBED_STATE.getStatus();

        GameMap map = SceneManager.getInstance().getMap(getMapId());
        IMapObject object = map.getObjectMap().get(killerId);
        if (object != null) {
            if (object instanceof ServantActor) {
                long realKillerId = ((ServantActor) object).getMaster().getRid();
                super.setKillerId(realKillerId);
            } else {
                super.setKillerId(killerId);
            }
        }

        sync();
    }




    public void sync() {
        if (roleId > 0) {
            EscortUpdateToPlayerNotice notice = new EscortUpdateToPlayerNotice();
            notice.setRoleId(roleId);
            notice.setEscortCid(escortCid);
            notice.setEscorting(getId());
            notice.setMapId(getMapId());
            notice.setHp(getHp());
            Point point = getPoint();
            if (point != null) {
                notice.setX(point.x);
                notice.setY(point.y);
            }
            notice.setStatus(status);
            notice.setExpire(expire);
            notice.setKillerId(this.getKillerId());
            notice.addHost(host);
            Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, roleId);
        }

    }

}
