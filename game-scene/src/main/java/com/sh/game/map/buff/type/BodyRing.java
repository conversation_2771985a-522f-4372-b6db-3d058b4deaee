package com.sh.game.map.buff.type;

import com.sh.game.common.config.model.BuffConfig;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.map.buff.BuffParamEnhanceUtil;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;

/**
 * author: wiley
 * QQ   : 510600102
 * Date: 2020/12/14 11:17
 * desc: 护身戒指
 */
public class BodyRing extends AbsBuffEffect {

    @Override
    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
        buff.initParam(b -> {
            int[] param = BuffParamEnhanceUtil.calParameters(performer, buff.config(), 0);
            if (param.length < 3) {
                return;
            }
            BuffState buffState = performer.getBuffState();
            buffState.setBodyRing(param);
            buffState.increase(BuffState.State.BODY_RING, stack);
        });
    }

    @Override
    public void onRemove(Performer performer, Buff buff, int stack) {
        performer.getBuffState().decrease(BuffState.State.BODY_RING, stack);
    }

    @Override
    public boolean onAppendCheck(Performer performer, BuffConfig config, Performer caster, FightResult.BuffImmediateEffect effect, long time, int source) {
        BuffState buffState = performer.getBuffState();
        return !buffState.inStateOf(BuffState.State.BODY_RING);
    }
}
