package com.sh.game.map.fight.handler;

import com.sh.game.common.constant.RoleConst;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.effect.impl.EmptyEffect;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.obj.ServantActor;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/18 14:10
 */
public class XinLingLianJieHandler extends AbstractHandler{

    @Override
    public SkillEffect getEffect() {
        return new EmptyEffect(this);
    }

    @Override
    public boolean fly(List<Performer> targetList) {
        return false;
    }

    @Override
    public void cast(FightResult ret, Skill skill, GameMap map, Performer caster, List<Performer> targetList, Point targetPoint) {
        targetList = new ArrayList<>();
        if (!caster.isPlayer() || caster.getCareer() != RoleConst.Career.DAO) {
            return;
        }
        PlayerActor player = (PlayerActor) caster;
        List<ServantActor> list = player.getServantList();
        targetList.addAll(list);
        targetList.add(caster);
        super.cast(ret, skill, map, caster, targetList, targetPoint);
        if (ret.buffEffect.newBuffs != null) {
            ret.castSkillSuc = true;
        }
    }
}
