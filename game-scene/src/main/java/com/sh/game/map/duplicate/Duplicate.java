package com.sh.game.map.duplicate;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.map.duplicate.ResDuplicateBasicInfoMessage;
import com.sh.game.common.communication.msg.map.duplicate.ResDuplicateEndMessage;
import com.sh.game.common.communication.msg.map.duplicate.ResDuplicateGoalInfoMessage;
import com.sh.game.common.communication.msg.map.duplicate.ResPerformTotalHpMessage;;
import com.sh.game.common.communication.notice.CompleteDuplicateNotice;
import com.sh.game.common.communication.notice.PlayerExitNotice;
import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.constant.MapConst;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.map.MonsterHpDto;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.InstanceManager;
import com.sh.game.map.Module;
import com.sh.game.map.duplicate.condition.DuplicateConditionFactory;
import com.sh.game.map.duplicate.condition.DuplicateEndCondition;
import com.sh.game.map.entity.DuplicateSpawn;
import com.sh.game.map.obj.MapObjectType;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.scene.Topography;
import com.sh.game.protos.DuplicateProtos;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import static com.sh.game.common.constant.MapConst.DUPLICATE_CONDITION_TYPE.*;

/**
 * 游戏副本
 *
 * <AUTHOR> 2017年6月1日 上午1:51:21
 */
@Slf4j
@Getter
@Setter
public class Duplicate extends GameMap {
    /**
     *
     */
    private int createType;
    /**
     * 副本创建时间
     */
    protected long createTime;
    /**
     * 副本持续时间,-1无限存在
     */
    protected int totalTime = -1;
    /**
     * 等待玩家重新进入副本的时间
     */
    private int waitingReEnterTime;
    /**
     * 副本是否成功
     */
    public boolean success;
    /**
     * 成功条件
     */
    protected DuplicateEndCondition[][] successConditions;
    /**
     * 通用刷怪
     */
    protected DuplicateSpawn dSpawn;

    protected int clearPlayerTimes = 5;

    public Duplicate() {
    }

    @Override
    public void init(long key, int host, MapConfig config, Topography topography, Object param) {
        super.init(key, host, config, topography, param);

        this.state = MapConst.MAP_STATE.WAITING;
        DuplicateConfig duplicateConfig = ConfigDataManager.getInstance().getById(DuplicateConfig.class, cfgId);
        if (duplicateConfig == null) {
            log.error("duplicate init failed, duplicate config not found:{}", cfgId);
            return;
        }

        this.setCreateType(duplicateConfig.getOpenType());
        this.setCreateTime(TimeUtil.getNowOfMills());
        this.setWaitingCloseTime(duplicateConfig.getWaitingCloseTime() * 1000);

        if (duplicateConfig.getTotalTime().length > 0) {
            this.setTotalTime(duplicateConfig.getTotalTime()[0]);
        }

        DuplicateEndCondition[][] successConditions = createCondition(duplicateConfig.getSucCondition());
        this.setSuccessConditions(successConditions);

        onInit();
    }

    @Override
    public void setdSpawn(DuplicateSpawn dSpawn) {
        this.dSpawn = dSpawn;
    }

    @Override
    public DuplicateSpawn getdSpawn() {
        return dSpawn;
    }

    // ======副本的生命周期============
    public boolean waiting() {
        // 系统副本直接返回true
        // 有玩家在副本返回true
        DuplicateConfig config = ConfigDataManager.getInstance().getById(DuplicateConfig.class, this.cfgId);
        return config.getOpenType() != MapConst.DUPLICATE_CREATE_TYPE.PERSONAL || !this.getPlayerMap().isEmpty();
    }

    /**
     * 关闭副本
     *
     * @param delta
     * @return 等待关闭副本的时间结束以后，返回true，否则 false
     */
    public boolean close(int delta) {
        this.waitingCloseTime -= delta;
        return this.waitingCloseTime <= 0;

    }

    @Override
    public boolean isDuplicate() {
        return true;
    }

    @Override
    public void destroy() {
        this.success = false;
        super.destroy();
    }

    @Override
    public void clearPlayers() {
        // 处理残留玩家
        DuplicateConfig duplicateConfig = ConfigDataManager.getInstance().getById(DuplicateConfig.class, this.getCfgId());
        if (duplicateConfig == null) {
            super.clearPlayers();
            return;
        }
        // 如果副本一直尝试玩家退出地图没有成功，则表明玩家当前已经不在当前地图
        if (clearPlayerTimes <= 0) {
            log.error("副本清除玩家次数超过上限，当前玩家可能已经不在当前副本，强制清除副本内玩家.副本id:{},cfgId:{},name:{}", getId(), getCfgId(), getName());
            getPlayerMap().clear();
            return;
        }
        clearPlayerTimes--;
        PlayerActor[] playerActors = getPlayerMap().values().toArray(new PlayerActor[0]);
        for (PlayerActor playerActor : playerActors) {
            InstanceManager.getInstance().exitDuplicateDeliver(this, playerActor, duplicateConfig);
        }
        this.setWaitingCloseTime(30000);
    }

    /**
     * 检查是否完成副本胜利条件
     *
     * @return
     */
    public boolean checkEnd(int delta) {
        // 检查副本完成条件
        boolean success = false;
        for (DuplicateEndCondition[] conditionArray : successConditions) {

            boolean subSuccess = true;
            for (DuplicateEndCondition condition : conditionArray) {
                if (condition == null) {
                    subSuccess = false;
                    continue;
                }
                if (!condition.check()) {
                    subSuccess = false;
                    break;
                }
            }
            if (subSuccess) {// type#value|type1#value1&type2#value2|type3#value3
                success = true;
                break;
            }
        }

        if (success) {
            this.setSuccess(true);
            return true;
        }
        // 检查时间
        if (totalTime > 0 && (TimeUtil.getNowOfMills() - this.createTime) / 1000 > totalTime) {
            return true;
        }
        //log.info("remainTime:{}, totalTime:{}", (TimeUtil.getNowOfMills() - this.createTime)/1000,totalTime);


        // 检查重新进入
        if (createType == MapConst.DUPLICATE_CREATE_TYPE.PERSONAL && this.getPlayerMap().size() <= 0) {
            waitingReEnterTime -= delta;
            return waitingReEnterTime <= 0;
        }

        return false;
    }

    @Override
    public void enterPlayer(PlayerActor player, Point point) {
        beforePlayerEnter(player, point);
        super.enterPlayer(player, point);
    }

    @Override
    public void afterEnterMap(PlayerActor playerActor) {
        super.afterEnterMap(playerActor);
        afterPlayerEnter(playerActor, playerActor.getPoint());
        sendDuplicateGoal(playerActor.getRid());
    }

    @Override
    public boolean isReady() {
        return this.state == MapConst.MAP_STATE.RUNNING || this.state == MapConst.MAP_STATE.CLOSING;
    }


    // ===============副本常用事件================================

    /**
     * 玩家进入之前
     *
     * @param player
     * @param point
     */
    public void beforePlayerEnter(PlayerActor player, Point point) {

    }

    /**
     * 玩家进入之后
     *
     * @param player player
     * @param point  point
     */
    public void afterPlayerEnter(PlayerActor player, Point point) {

        if (createType == MapConst.DUPLICATE_CREATE_TYPE.PERSONAL) {
            // 重置断线后的等待时间
            DuplicateConfig cfg = ConfigDataManager.getInstance().getById(DuplicateConfig.class, this.cfgId);
            this.waitingReEnterTime = cfg.getWaitingReEnterTime() * 1000;
        }

        // 发送副本基本信息
        ResDuplicateBasicInfoMessage res = new ResDuplicateBasicInfoMessage();
        DuplicateProtos.ResDuplicateBasicInfo.Builder duplicateBasicInfo = DuplicateProtos.ResDuplicateBasicInfo.newBuilder();
        duplicateBasicInfo.setCfgId(this.cfgId);
        duplicateBasicInfo.setCreateTime((int) (this.createTime / 1000));
        duplicateBasicInfo.setEndTime((int) (this.createTime / 1000) + this.totalTime);
        duplicateBasicInfo.setInstnceId(this.cfgId);
        res.setProto(duplicateBasicInfo.build());
        Module.MSG_TRANSFORMER.sendMsg(res, player.getRid());
    }

    /**
     * 怪物死亡
     *
     * @param monster monsterId
     */
    @Override
    public void onMonsterDie(MonsterActor monster) {
        super.onMonsterDie(monster);
        if (monster.getType() != MapObjectType.MONSTER) {
            return;
        }

        if (dSpawn != null) {
            dSpawn.onMonsterDie();
            updateDuplicateGoal(0, TYPE_KILL_ALL_MONSTER, dSpawn);
        }
        updateDuplicateGoal(0, TYPE_KILL_SPECIAL_MONSTER, monster);

    }

    /**
     * 玩家死亡
     *
     * @param playerActor playerActor
     */
    @Override
    public void onPlayerDie(PlayerActor playerActor) {
        super.onPlayerDie(playerActor);
        onDuplicatePlalyerDie(playerActor);
    }

    public void onSuperPlayerDie(PlayerActor playerActor) {
        super.onPlayerDie(playerActor);
    }

    /**
     * 副本玩家死亡
     *
     * @param actor
     */
    protected void onDuplicatePlalyerDie(PlayerActor actor) {
        setSuccess(false);
        onClose();
    }

    public void initParam(Object param) {
    }

    public boolean initDuplicate() {
        return true;
    }

    private DuplicateEndCondition[][] createCondition(List<int[][]> conditions) {
        if (conditions.isEmpty()) {
            return new DuplicateEndCondition[0][0];
        }

        DuplicateEndCondition[][] ret = new DuplicateEndCondition[conditions.size()][];

        for (int j = 0; j < conditions.size(); j++) {
            int[][] conditionOr = conditions.get(j);
            DuplicateEndCondition[] or = new DuplicateEndCondition[conditionOr.length];
            for (int i = 0; i < or.length; i++) {
                int[] and = conditionOr[i];
                int type = and[0];
                int[] value;
                if (and.length > 1) {
                    value = new int[and.length - 1];
                    System.arraycopy(and, 1, value, 0, value.length);
                } else {
                    value = new int[0];
                }
                or[i] = DuplicateConditionFactory.create(type, value);
            }
            ret[j] = or;
        }
        return ret;
    }

    /**
     * 副本初始化事件
     */
    public void onInit() {
        dSpawn = new DuplicateSpawn(this, 0);
        updateDuplicateGoal(0, TYPE_KILL_ALL_MONSTER, dSpawn);
    }

    /**
     * 副本关闭事件，做一些结算面板啊，结束信息的发送， 以及奖励相关的计算
     */
    public void onClose() {
        ResDuplicateEndMessage res = new ResDuplicateEndMessage();
        DuplicateProtos.ResDuplicateEnd.Builder duplicateEnd = DuplicateProtos.ResDuplicateEnd.newBuilder();
        duplicateEnd.setSucess(success ? 1 : 0);
        duplicateEnd.setWaitingTime(this.waitingCloseTime);
        duplicateEnd.setCfgId(cfgId);
        res.setProto(duplicateEnd.build());
        Module.MSG_TRANSFORMER.sendMapMessage(res, this);


        boolean isSuccess = isSuccess();
        // complete notice
        for (PlayerActor player : playerMap.values()) {
            CompleteDuplicateNotice notice = new CompleteDuplicateNotice();
            notice.setRid(player.getRid());
            notice.setDuplicateID(cfgId);
            notice.setSuccess(isSuccess);
            notice.addHost(player.getHostId());
            Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, player.getRid());
        }
    }

    public ResPerformTotalHpMessage createMonsterHPMessage() {
        ResPerformTotalHpMessage msg = new ResPerformTotalHpMessage();
        DuplicateProtos.ResPerformTotalHp.Builder performTotalHp = DuplicateProtos.ResPerformTotalHp.newBuilder();
        for (MonsterActor monster : this.getMonsterMap().values()) {
            DuplicateProtos.MonsterHpInfo.Builder bean = DuplicateProtos.MonsterHpInfo.newBuilder();
            bean.setMonsterCfgId(monster.getConfigId());
            bean.setMonsterId(monster.getId());
            bean.setMonsterHp(monster.getHp());
            if (!monster.isDead()) {
                bean.setX(monster.getPoint().getX());
                bean.setY(monster.getPoint().getY());
            }
            performTotalHp.addMonsterList(bean);
        }
        msg.setProto(performTotalHp.build());
        return msg;
    }

    public void syncMonsterState() {
        ResPerformTotalHpMessage msg = createMonsterHPMessage();
        Module.MSG_TRANSFORMER.sendMapMessage(msg, this);
    }

    @Override
    public MapConst.MAP_STATE getState() {
        return state;
    }

    @Override
    public void setState(MapConst.MAP_STATE state) {
        this.state = state;
    }

    public void exitDuplicate(PlayerActor playerActor) {
        PlayerExitNotice notice = new PlayerExitNotice();
        notice.setRid(playerActor.getRid());
        notice.setCfgId(cfgId);
        for (MonsterActor actor : monsterMap.values()) {
            if (actor.getType() == MapObjectType.MONSTER) {
                long hp = actor.getHp();
                if (hp < 0) {
                    hp = 0;
                }
                MonsterHpDto dto = new MonsterHpDto(actor.getConfigId(), actor.getPoint().x, actor.getPoint().y,
                        hp, actor.getDir());
                notice.getHps().add(dto);
            }
        }
        notice.addHost(playerActor.getHostId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, playerActor.getRid());
    }

    @Override
    public void heart(int delta) {
        super.heart(delta);
        if (dSpawn != null && dSpawn.update()) {
            updateDuplicateGoal(0, TYPE_KILL_ALL_MONSTER, dSpawn);
        }
        updateDuplicateGoal(0, TYPE_TIME_END, new int[]{totalTime,
                (int) ((TimeUtil.getNowOfMills() - this.createTime) / 1000)});
    }

    /**
     * update when spawn
     *
     * @param rid
     */
    public void updateDuplicateGoal(long rid, int type, Object obj) {
        boolean update = false;
        for (DuplicateEndCondition[] conditionArray : successConditions) {
            for (DuplicateEndCondition condition : conditionArray) {
                if (condition == null) {
                    continue;
                }
                if (condition.getType() != type) {
                    continue;
                }
                if (condition.update(obj)) {
                    update = true;
                }
            }
        }

        if (update) {
            sendDuplicateGoal(rid);
        }
    }

    /**
     * 发送进度信息
     *
     * @param rid
     */
    public void sendDuplicateGoal(long rid) {
        List<DuplicateProtos.DuplicateGoal> goals = new ArrayList<>();
        for (DuplicateEndCondition[] conditionArray : this.successConditions) {
            for (DuplicateEndCondition condition : conditionArray) {
                if (condition == null) {
                    continue;
                }
                goals.add(condition.pack());
            }
        }

        ResDuplicateGoalInfoMessage msg = new ResDuplicateGoalInfoMessage();
        msg.setProto(DuplicateProtos.ResDuplicateGoalInfo.newBuilder()
                .addAllGoals(goals)
                .build());
        if (rid > 0) {
            Module.MSG_TRANSFORMER.sendMsg(msg, rid);
        } else {
            Module.MSG_TRANSFORMER.sendMapMessage(msg, this);
        }
    }

    @Override
    public boolean needSendRelive() {
        return false;
    }

}
