package com.sh.game.map.heart;

import com.sh.concurrent.ScheduledEvent;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.buff.BuffManager;
import com.sh.game.map.obj.PlayerCloneActor;
import com.sh.game.map.scene.GameMap;

import java.util.Map;

/**
 * 玩家分身心跳事件
 *
 * <AUTHOR>
 * 2017年5月18日 下午8:37:04
 */
public class PlayerCloneHeartEvent extends ScheduledEvent {


    private long lastHurtTime = TimeUtil.getNowOfMills();

    GameMap map;

    public PlayerCloneHeartEvent(GameMap map) {
        super(-1, 100);
        this.map = map;
    }

    public PlayerCloneHeartEvent(long end) {
        super(end);
    }

    @Override
    public void doAction() {

        if (!Module.isReady()) {
            return;
        }

        if (!map.isReady()) {
            return;
        }

        long curTime = TimeUtil.getNowOfMills();

        int delta = (int) (curTime - lastHurtTime);

        lastHurtTime = curTime;

        Map<Long, PlayerCloneActor> playerCloneMap = map.getPlayerCloneMap();
        PlayerCloneActor[] cloneActors = playerCloneMap.values().toArray(new PlayerCloneActor[0]);

        //检查buffer
        for (PlayerCloneActor cloneActor : cloneActors) {
            cloneActor.getMachine().updateMachine(map, delta);

            BuffManager.getInstance().onUpdateByHeart(map, cloneActor, curTime, delta);
        }
    }
}
