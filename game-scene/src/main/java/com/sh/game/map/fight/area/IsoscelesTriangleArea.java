package com.sh.game.map.fight.area;

import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.util.GeomUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 等腰三角形.
 * <p>该三角形的正前方顶点是以当前技能最多能打到的点为基础，最远距离不超过areadis,该三角形是等腰三角形</p>
 * <P>下图中，M为在作用范围内，P点沿着指定方向能够达到的最远的一个点</p>
 * --------------<br>
 * ------M-------<br>
 * -----AAA------<br>
 * ----AAAAA-----<br>
 * ---AAAAAAA----<br>
 * ------P-------<br>
 *
 * <AUTHOR>
 */
public class IsoscelesTriangleArea extends AttackArea {

    @Override
    public List<Point> findAreaPoint(GameMap map, Point point, int areaDir, int[] areaDisInts) {
        int areaDis = areaDisInts[0];
        if ((areaDis & 0x1) == 0) areaDir += 1;
        byte[] cVector = GeomUtil.getVector(areaDir);
        byte[] rVector = GeomUtil.getVector(areaDir + 2);

        List<Point> ret = new ArrayList<>();
        int d = 0;
        for (int w = areaDir; w > 0; w -= 2) {
            d ++;
            int px = point.x + cVector[0] * d;
            int py = point.y + cVector[1] * d;
            int r = w >>> 2;
            for (int i = -r; i <= r; i ++) {
                Point p = map.getPoint(px + rVector[0] * r, py + rVector[1] * r);
                if (p != null) {
                    ret.add(p);
                }
            }
        }

        return ret;
    }
}
