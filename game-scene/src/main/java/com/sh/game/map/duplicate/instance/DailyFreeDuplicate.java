package com.sh.game.map.duplicate.instance;

import com.sh.game.map.duplicate.Duplicate;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.Point;

/**
 * 每日免费副本
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/11/1.
 */
public class DailyFreeDuplicate extends Duplicate {

    @Override
    public void afterPlayerEnter(PlayerActor player, Point point) {
        super.afterPlayerEnter(player, point);
    }
}
