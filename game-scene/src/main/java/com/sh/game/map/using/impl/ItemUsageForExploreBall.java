package com.sh.game.map.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.using.AbsUsingValidator;

public class ItemUsageForExploreBall extends AbsUsingValidator {

    @Override
    public void apply(PlayerActor player, ItemConfig config, int count) {
        PlayerManager.getInstance().exploreMonster(player, config.getUseParam()[0][0]);
    }
}
