package com.sh.game.map.obj;

import com.sh.game.common.entity.backpack.item.Item;
import lombok.Getter;
import lombok.Setter;

import java.util.concurrent.atomic.AtomicBoolean;


@Getter
@Setter
public class GroundItem extends NonPerformer {

	private Item item;

	private int section;

	private int monsterId;
	
	/**
	 * 掉落时间
	 */
	private int dropTime;
	
	/**
	 * 停留时间
	 */
	private int totalTime;

	/**
	 * 归属
	 */
	private long owner;

	/**
	 * 队伍归属
	 */
	private long ownerTeam;

	/**
	 * 行会归属
	 */
	private long ownerUnion;

	/**
	 * 归属者保护时间
	 */
	private int ownerProtectedExpire;

	/**
	 * 丢弃者
	 */
	private long discarder;

	private AtomicBoolean picking = new AtomicBoolean(false);

	@Override
	public int getType() {
		return MapObjectType.ITEM;
	}

	@Override
	public boolean penetrate(IMapObject stand, int penetrable) {
		if(stand.getType() == MapObjectType.ITEM) {
			return false;
		}
		return true;
	}

	@Override
	public boolean overlying(IMapObject stand, int penetrable) {
		return false;
	}

}
