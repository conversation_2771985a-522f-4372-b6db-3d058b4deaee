package com.sh.game.map.daily.controller.type;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.union.ResUnionBossDieMessage;
import com.sh.game.common.communication.msg.system.union.ResUnionBossEndMessage;
import com.sh.game.common.communication.msg.system.union.ResUnionBossPersonRankingMessage;
import com.sh.game.common.communication.msg.system.union.ResUnionBossUnionRankingMessage;
import com.sh.game.common.communication.notice.UnionBossRankingSyncNotice;
import com.sh.game.common.config.model.DailyUnionBossConfig;
import com.sh.game.common.config.model.GlobalConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.containers.Ranking;
import com.sh.game.common.entity.containers.RankingData;
import com.sh.game.common.util.BoxUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.SceneManager;
import com.sh.game.map.daily.controller.Controller;
import com.sh.game.map.duplicate.instance.UnionStationDuplicate;
import com.sh.game.map.entity.DuplicateSpawn;
import com.sh.game.map.mail.SceneMailManager;
import com.sh.game.map.obj.*;
import com.sh.game.map.scene.GameMap;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.UnionProtos;
import lombok.extern.slf4j.Slf4j;

import java.math.BigInteger;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.sh.game.common.constant.MapConst.UNION_BOSS_MAP_ID;

/**
 * 行会boss
 */
@Slf4j
public class UnionBossController extends Controller {
    private Map<Long, Integer> mapUpdateTime = new ConcurrentHashMap<>();
    private Map<Long, Ranking> scores = new ConcurrentHashMap<>();

    @Override
    public int[] getMaps() {
        return new int[]{MapConst.UNION_BOSS_MAP_ID};
    }

    @Override
    public void onBegin(Object param) {
        super.onBegin(param);

        mapUpdateTime.clear();
        scores.clear();
        //0是行会排名
        scores.put(0L, new Ranking());
    }

    @Override
    public void onFinish() {
        super.onFinish();

        Ranking unionRanking = scores.get(0L);
        // 行会竞拍
        Map<Long, Integer> unionRankingMap = new HashMap<>();
        Map<Long, List<Item>> unionAuctionMap = new HashMap<>();
        if (unionRanking != null) {
            for (int i = 1; i <= unionRanking.getSort().size(); i++) {
                RankingData rankingData = unionRanking.getSort().get(i - 1);
                if (rankingData.getValue().compareTo(BigInteger.ZERO) <= 0) {
                    continue;
                }
                int ranking = i > 3 ? 999 : i;
                unionRankingMap.put(rankingData.getKey(), i);
                ResUnionBossEndMessage msg = new ResUnionBossEndMessage();
                DailyUnionBossConfig dailyUnionBossConfig = ConfigDataManager.getInstance().getById(DailyUnionBossConfig.class, 1 + Symbol.JINHAO + ranking);
                if (dailyUnionBossConfig != null) {
                    List<Item> auctionItems = BoxUtil.openBox(dailyUnionBossConfig.getReward());
                  //TODO auction
                    //auctionItems.forEach(item -> msg.getUnions().add(ItemUtil.packCommonItemBean(item).build()));
                    unionAuctionMap.put(rankingData.getKey(), auctionItems);
                }
                Map<Long, GameMap> maps = SceneManager.getInstance().getMaps(UNION_BOSS_MAP_ID);
                for (GameMap value : maps.values()) {
                    UnionStationDuplicate duplicate = (UnionStationDuplicate) value;
                    if (duplicate == null) {
                        continue;
                    }
                    if (duplicate.getUnionId() == rankingData.getKey()) {
                        Module.MSG_TRANSFORMER.sendMapMessage(msg, value);
                    }
                }
            }
        }

        //个人排行奖励
        Map<Long, Map<Long, Long>> unionScoreMap = new HashMap<>();
        for (Map.Entry<Long, Ranking> entry : scores.entrySet()) {
            if (entry.getKey() <= 0L) {
                continue;
            }
            Map<Long, Long> longLongMap = unionScoreMap.computeIfAbsent(entry.getKey(), k -> new HashMap<>());
            int ranking = 0;
            for (RankingData rankingData : entry.getValue().getSort()) {
                if (rankingData.getValue().compareTo(BigInteger.ZERO) <= 0) {
                    continue;
                }
                ranking++;
                if (ranking > 10) {
                    ranking = 999;
                }
                DailyUnionBossConfig config = ConfigDataManager.getInstance().getById(DailyUnionBossConfig.class, 2 + Symbol.JINHAO + ranking);
                if (config == null) {
                    break;
                }

                BackpackStash stash = new BackpackStash();
                List<Item> rewards = BoxUtil.openBox(config.getReward());
                stash.increase(rewards);
                if (ranking <= 10) {
                    stash.commitToMail(rankingData.getKey(), 4018, ranking);
                }
                DailyUnionBossConfig awardConfig = ConfigDataManager.getInstance().getById(DailyUnionBossConfig.class, 2 + Symbol.JINHAO + 999);
                if (awardConfig == null) {
                    break;
                }
                // 发送参与奖
                SceneMailManager.getInstance().sendMail(rankingData.getKey(), EmailConst.MailId.UNION_BOOS_AWARD, BoxUtil.openBox(awardConfig.getReward()));
                if (rankingData.getValue().compareTo(BigInteger.ZERO) > 0) {
                    longLongMap.put(rankingData.getKey(), rankingData.getValue().longValue());
                }
            }
        }

        UnionBossRankingSyncNotice notice = new UnionBossRankingSyncNotice();
        notice.setRankings(unionRankingMap);
        notice.setUnionAuctionMap(unionAuctionMap);
        notice.setUnionScoreMap(unionScoreMap);
        notice.addHost(Module.NET.getAllClientId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_COMMON, notice, 0L);
    }

    @Override
    public void onMapBegin(GameMap map) {
        super.onMapBegin(map);

        UnionStationDuplicate duplicate = (UnionStationDuplicate) map;
        Ranking unionRanking = scores.get(0L);
        unionRanking.change(duplicate.getUnionId(), BigInteger.valueOf(0), rankingData -> {
            rankingData.setName(duplicate.getUnionName());
            rankingData.setHost(duplicate.getRemoteHostId());
        });
        scores.put(duplicate.getUnionId(), new Ranking());
        map.setdSpawn(new DuplicateSpawn(map, getDailyType()));

        map.getPlayerMap().forEach((k, player) -> onMapPlayerEnter(map, player));
    }

    @Override
    public void onMapFinish(GameMap map) {
        super.onMapFinish(map);

        map.setdSpawn(null);
        map.getMonsterMap().forEach((k, v) -> {
            v.setDropPrevent(true);
            v.setDieDelay(0);
            v.setDead(true);
        });
    }

    @Override
    public void onMapPlayerEnter(GameMap map, PlayerActor player) {
        super.onMapPlayerEnter(map, player);

        UnionStationDuplicate duplicate = (UnionStationDuplicate) map;
        Ranking actorRanking = scores.computeIfAbsent(duplicate.getUnionId(), k -> new Ranking());
        actorRanking.change(player.getRid(), BigInteger.valueOf(0), rankingData -> {
            rankingData.setName(player.getName());
            rankingData.setHost(player.getHostId());
        });
        sendPersonRank(map, player);
        sendUnionRank(map, player);
    }

    @Override
    public void onMapHurt(GameMap map, Performer caster, Performer target, long hurt, long show) {
        super.onMapHurt(map, caster, target, hurt, show);
        if (show <= 0) {
            return;
        }
        if (caster == null) {
            return;
        }
        /**
         * 非 猪脚，英雄，宠物 return
         */
        if (caster != null && caster.getType() != MapObjectType.PLAYER && caster.getType() != MapObjectType.HERO && caster.getType() != MapObjectType.SERVANT) {
            return;
        }
        if (target != null && target.getType() != MapObjectType.MONSTER) {
            return;
        }

        /**
         * 寻找猪脚
         */
        PlayerActor actor;
        if (caster instanceof ServantActor) {
            actor = (PlayerActor) caster.getMaster();
        }else{
            actor = (PlayerActor) caster;
        }

        if (actor == null) {
            log.error("行会boss伤害找不到actor:casterName:{},casterId:{},confid:{}", caster.getName(), caster.getId(), caster.getConfigId());
            return;
        }


        if (actor.getUnion() == null || actor.getUnion().getId() <= 0) {
            return;
        }

        long unionId = actor.getUnion().getId();

        Ranking unionRanking = scores.computeIfAbsent(0L, k -> new Ranking());
        unionRanking.change(unionId, BigInteger.valueOf(show));
        Ranking actorRanking = scores.computeIfAbsent(unionId, k -> new Ranking());
        actorRanking.change(actor.getRid(), BigInteger.valueOf(show));
    }

    @Override
    public void onMapHeart(GameMap map, int delta) {
        super.onMapHeart(map, delta);
        DuplicateSpawn spawn = map.getdSpawn();
        if (spawn == null) {
            return;
        }
        if (map.getPlayerMap().isEmpty()) {
            return;
        }
        int now = TimeUtil.getNowOfSeconds();
        int updateTime = mapUpdateTime.getOrDefault(map.getId(), 0);
        if (now - updateTime <= 1) {
            return;
        }
        for (MonsterActor monsterActor : map.getMonsterMap().values()) {
            if (!monsterActor.isDead()) {
                int passTime = now - monsterActor.getBornTime();
                int relieveTime = GlobalConfig.unionBossSurvivals.getOrDefault(monsterActor.getConfigId(), 0);
                if (relieveTime <= 0) {
                    continue;
                }
                long hp = monsterActor.getFinalAttribute().findMaxHp() * (relieveTime - passTime) / relieveTime;
                if (hp < 0) {
                    hp = 0;
                }
                monsterActor.setHp(hp);
                monsterActor.sendHpMpChangeMessage(FightConst.HurtType.NORMAL_HURT.type());
                if (hp <= 0) {
                    monsterActor.setDead(true);
                    ResUnionBossDieMessage msg = new ResUnionBossDieMessage();
                    msg.setProto(UnionProtos.ResUnionBossDie.newBuilder()
                            .setMonsterId(monsterActor.getConfigId())
                            .build());
                    Module.MSG_TRANSFORMER.sendMapMessage(msg, map);
                }
            }
        }
        mapUpdateTime.put(map.getId(), now);
        sendUnionRank(map, null);
        sendPersonRank(map, null);
    }

    /**
     * 发送行会排行
     *
     * @param map
     * @param player
     */
    private void sendUnionRank(GameMap map, PlayerActor player) {
        DuplicateSpawn spawn = map.getdSpawn();
        if (spawn == null) {
            return;
        }
        UnionStationDuplicate duplicate = (UnionStationDuplicate) map;
        ResUnionBossUnionRankingMessage msg = getUnionRankMsg(duplicate.getUnionId());
        if (player != null) {
            Module.MSG_TRANSFORMER.sendMsg(msg, player.getRid());
        } else {
            Module.MSG_TRANSFORMER.sendMapMessage(msg, map);
        }
    }

    /**
     * 发送个人排行
     *
     * @param map
     * @param player
     */
    private void sendPersonRank(GameMap map, PlayerActor player) {
        ResUnionBossPersonRankingMessage msg;
        UnionStationDuplicate duplicate = (UnionStationDuplicate) map;
        if (player != null) {
            msg = getPersonaRankMsg(duplicate.getUnionId(), player.getRid());
            Module.MSG_TRANSFORMER.sendMsg(msg, player.getRid());
        } else {
            Collection<PlayerActor> values = map.getPlayerMap().values();
            for (PlayerActor value : values) {
                msg = getPersonaRankMsg(duplicate.getUnionId(), value.getRid());
                Module.MSG_TRANSFORMER.sendMsg(msg, value.getRid());
            }
        }
    }

    /**
     * 获取行会排行信息
     *
     * @param unionId
     * @return
     */
    private ResUnionBossUnionRankingMessage getUnionRankMsg(long unionId) {
        ResUnionBossUnionRankingMessage msg = new ResUnionBossUnionRankingMessage();
        UnionProtos.ResUnionBossUnionRanking.Builder protoBuilder = UnionProtos.ResUnionBossUnionRanking.newBuilder();
        Ranking unionRanking = scores.get(0L);
        if (unionRanking != null) {
            for (int i = 1; i <= unionRanking.getSort().size(); i++) {
                RankingData rankingData = unionRanking.getSort().get(i - 1);
                if (rankingData.getKey() == unionId) {
                    AbcProtos.CommonRankingBean.Builder bean = AbcProtos.CommonRankingBean.newBuilder();
                    bean.setRanking(i);
                    bean.setUid(rankingData.getKey());
                    bean.setName(rankingData.getName());
                    bean.setScore(rankingData.getValue().toString());
                    protoBuilder.setMyUnionInfo(bean);
                }
                if (i < 4) {
                    AbcProtos.CommonRankingBean.Builder bean = AbcProtos.CommonRankingBean.newBuilder();
                    bean.setRanking(i);
                    bean.setUid(rankingData.getKey());
                    bean.setName(rankingData.getName());
                    bean.setScore(rankingData.getValue().toString());
                    protoBuilder.addUnionRankings(bean);
                }
            }
        }
        msg.setProto(protoBuilder.build());
        return msg;
    }

    /**
     * 获取个人排行信息
     *
     * @param unionId
     * @param uid
     * @return
     */
    private ResUnionBossPersonRankingMessage getPersonaRankMsg(long unionId, long uid) {
        ResUnionBossPersonRankingMessage msg = new ResUnionBossPersonRankingMessage();
        UnionProtos.ResUnionBossPersonRanking.Builder protoBuilder = UnionProtos.ResUnionBossPersonRanking.newBuilder();
        Ranking actorRanking = unionId > 0 ? scores.get(unionId) : null;
        if (actorRanking != null) {
            for (int i = 1; i <= actorRanking.getSort().size(); i++) {
                RankingData rankingData = actorRanking.getSort().get(i - 1);
                if (rankingData.getKey() == uid) {
                    AbcProtos.CommonRankingBean.Builder bean = AbcProtos.CommonRankingBean.newBuilder();
                    bean.setRanking(i);
                    bean.setUid(rankingData.getKey());
                    bean.setName(rankingData.getName());
                    bean.setScore(rankingData.getValue().toString());
                    protoBuilder.setMyPersonalInfo(bean);
                }
                if (i < 11) {
                    AbcProtos.CommonRankingBean.Builder bean = AbcProtos.CommonRankingBean.newBuilder();
                    bean.setRanking(i);
                    bean.setUid(rankingData.getKey());
                    bean.setName(rankingData.getName());
                    bean.setScore(rankingData.getValue().toString());
                    protoBuilder.addPersonalRankings(bean);
                }
            }
        }
        msg.setProto(protoBuilder.build());
        return msg;
    }
}
