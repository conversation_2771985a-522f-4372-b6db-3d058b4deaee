package com.sh.game.map.duplicate.instance;

import com.sh.game.common.communication.msg.map.duplicate.ResPerformTotalHpMessage;
import com.sh.game.common.constant.ChatConst;
import com.sh.game.map.Module;
import com.sh.game.map.announce.AnnounceManager;
import com.sh.game.map.duplicate.Duplicate;
import com.sh.game.map.obj.MonsterActor;

/**
 * <AUTHOR>
 * @data 2020/9/23 10:28
 */
public class HinderRewardDuplicate extends Duplicate {

    private int updateTick = 0;

    @Override
    public void heart(int delta) {
        super.heart(delta);
        updateTick -= delta;
        if (updateTick <= 0) {
            updateTick = 5000;
            ResPerformTotalHpMessage msg = createMonsterHPMessage();
            Module.MSG_TRANSFORMER.sendMapMessage(msg, this);
        }
    }

    @Override
    public void onMonsterDie(MonsterActor monster) {
        super.onMonsterDie(monster);
        AnnounceManager.getInstance().post(ChatConst.AnnounceId.HINDER_WINNER_END,0);
    }
}
