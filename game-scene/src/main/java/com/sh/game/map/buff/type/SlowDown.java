package com.sh.game.map.buff.type;

import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;

/**
 * author: wiley
 * QQ   : 510600102
 * Date: 2020/12/10 15:37
 * desc: 减速效果
 */
public class SlowDown extends AbsBuffEffect {

    @Override
    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
        int moveSpeed = Math.min(1, performer.getMoveSpeed() - 1);
        if (moveSpeed == performer.getMoveSpeed()) {
            return;
        }
        performer.setMoveSpeed(moveSpeed);
        performer.getBuffState().increase(BuffState.State.SLOW_DOWN, stack);
    }

    @Override
    public void onRemove(Performer performer, Buff buff, int stack) {
        performer.setMoveSpeed(performer.getMoveSpeed() + 1);
        performer.getBuffState().decrease(BuffState.State.SLOW_DOWN, stack);
    }
}
