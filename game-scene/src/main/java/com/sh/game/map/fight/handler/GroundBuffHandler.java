package com.sh.game.map.fight.handler;

import com.google.common.primitives.Ints;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.SkillConditionConfig;
import com.sh.game.common.config.model.SkillConfig;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.util.ParamAddUtil;
import com.sh.game.map.buff.BuffManager;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.effect.impl.EmptyEffect;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;

import java.util.List;

/**
 * 地面buff类技能
 *
 * <AUTHOR>
 * 2017年6月6日 下午9:39:15
 */
public class GroundBuffHandler extends AbstractHandler {

    @Override
    public SkillEffect getEffect() {
        return new EmptyEffect(this);
    }

    @Override
    public boolean fly(List<Performer> targetList ) {
        return false;
    }

    @Override
    public void cast(FightResult ret, Skill skill, GameMap map, Performer caster, List<Performer> targetList, Point targetPoint) {
        SkillConfig config = ConfigDataManager.getInstance().getById(SkillConfig.class, skill.getSkillId());
        if (config == null) {
            return;
        }
        SkillConditionConfig conditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
        if (conditionConfig == null) {
            return;
        }
        if (config.getAreaDir() == 1) {
            targetPoint = caster.getPoint();
        }
        int[] buffs = ParamAddUtil.calSkillConditionBuff(caster.getSkillSpecEffectMap(), conditionConfig);
        if (!BuffManager.getInstance().append(map, Ints.asList(buffs), targetPoint, config.getAreaType(), config.getAreaDis(), caster, ret.buffEffect)) {
            return;
        }

        ret.castSkillSuc = true;
    }
}
