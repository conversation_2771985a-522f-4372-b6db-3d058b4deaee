package com.sh.game.map.fsm;

import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.constant.HeroConst;
import com.sh.game.common.constant.MonsterType;
import com.sh.game.common.constant.ServantConst;
import com.sh.game.map.obj.*;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.scene.PointState;
import com.sh.game.map.util.GeomUtil;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang3.ArrayUtils;

@Log4j2
public class ServantAIUtil {
    /**
     * 仆从进入地图
     *
     * @param actor
     * @param map
     * @param mapConfig
     */
    public static void enterServant(PlayerActor actor, GameMap map, MapConfig mapConfig) {
        actor.getServantList().forEach(servantActor -> {
            if (servantActor == null) {
                return;
            }

            if (ArrayUtils.contains(mapConfig.getMonsterType(), servantActor.getMonsterType())) {
                return;
            }
            if (!servantActor.isDead()) {
                Point masterPoint = actor.getPoint();
                if (masterPoint == null) {
                    return;
                }
                Point servantPoint = map.getRoundRandomStandAblePoint(servantActor, masterPoint.x, masterPoint.y);
                map.enterServant(servantActor, servantPoint);
                log.info("召唤怪进入：{}, map:{}", servantActor.getName(), map.getCfgId());
            }
        });
        if (actor.getServantFightState() == ServantConst.FightState.LOCK) {
            PlayerManager.getInstance().switchServantFightState(actor.getRid(), HeroConst.FightState.ATTACK, 0);
        }
    }

    public static void enterPet(PlayerActor actor, GameMap map) {
        if (actor.getChongWuActor() == null) {
            return;
        }

        Point masterPoint = actor.getPoint();
        if (masterPoint == null) {
            return;
        }
        Point servantPoint = map.getRoundRandomStandAblePoint(actor.getChongWuActor(), masterPoint.x, masterPoint.y);
        map.enterChongWu(actor.getChongWuActor(), servantPoint);

        log.info("玩家id:{}->name:{} 的宠物进入：{}, map:{}",actor.getId(),actor.getName(), actor.getChongWuActor().getName(), map.getCfgId());
    }

    public static Performer findTarget(GameMap map, Performer master, ServantActor servantActor) {
        int fightState = servantActor.getFightState();
        if (fightState == ServantConst.FightState.ATTACK) {
            if (servantActor.getMonsterType() == MonsterType.SKILL_CALL) {
                return unlockFindTarget(map, master, servantActor, false);
            } else if (servantActor.getMonsterType() == MonsterType.ITEM_CALL) {
                return unlockFindTarget(map, master, servantActor, true);
            } else {
                return unlockFindTarget(map, master, servantActor, false);
            }
        } else if (fightState == ServantConst.FightState.LOCK) {
            return lockFindTarget(map, master, servantActor);
        }
        return null;
    }


    /**
     * 优先级：主人攻击目标>攻击主人目标>自己攻击目标>攻击自己目标>随机目标
     *
     * @param map
     * @param master
     * @param servantActor
     * @param masterPriority
     * @return
     */
    private static Performer unlockFindTarget(GameMap map, Performer master, ServantActor servantActor, boolean masterPriority) {
        Performer masterTarget = findMasterTarget(map, master, servantActor);
        if (masterPriority) {
            if (masterTarget != null) {
                return masterTarget;
            }
        }

        if (servantActor.getFightTarget() > 0) {
            IMapObject obj = map.getObject(servantActor.getFightTarget());
            if (obj == null || obj.isDead() || !obj.isPerformer()) {
                servantActor.setFightTarget(0);
            } else {
                Performer target = (Performer) obj;
                if (target.getId() == master.getWhoMyTarget()
                        || GeomUtil.distance(target.getPoint(), servantActor.getPoint()) <= servantActor.getToAttackArea()) {
                    return target;
                } else {
                    servantActor.setFightTarget(0);
                }
            }
        }

        if (masterTarget != null) {
            return masterTarget;
        }

        //寻找最近的目标
        return findNearestMonster(map, master, servantActor);
    }

    private static Performer findMasterTarget(GameMap map, Performer master, ServantActor servantActor) {
        Performer target;
        // 寻找主人攻击的目标
        if (master.getWhoMyTarget() != 0) {
            target = (Performer) map.getObject(master.getWhoMyTarget());
            if (target != null && !target.isDead() && servantActor.isEnemy(target, true)) {
                int dis = GeomUtil.distance(target.getPoint(), master.getPoint());
                if (dis <= 20) {
//                    servantActor.setWhoMyTarget(target.getId());
                    return target;
                }
            }
        }
        // 寻找攻击主人的目标
        if (master.getWhoAttackMe() != 0) {
            target = (Performer) map.getObject(master.getWhoAttackMe());
            if (target != null && !target.isDead() && servantActor.isEnemy(target, true)) {
                int dis = GeomUtil.distance(target.getPoint(), master.getPoint());
                if (dis <= 20) {
//                    servantActor.setWhoMyTarget(target.getId());
                    return target;
                }
            }
        }
        return null;
    }

    /**
     * 锁定状态，寻找目标
     * 锁定只寻找猪脚攻击对象
     *
     * @param map
     * @param master
     * @param servantActor
     * @return
     */
    private static Performer lockFindTarget(GameMap map, Performer master, ServantActor servantActor) {

        long masterAttackId = master.getWhoMyTarget();
        long servantAttackerId = servantActor.getFightTarget();

        //如果主人的攻击对象不为空，并且不一致，则设置宠物攻击对象为masterid
        if (masterAttackId != 0 && servantAttackerId != masterAttackId) {
            servantActor.setFightTarget(masterAttackId);
        }

        IMapObject obj = map.getObject(servantActor.getFightTarget());
        if (obj != null && !obj.isDead() && obj.isPerformer()) {
            return (Performer) obj;
        }
        return null;
    }

    private static Performer findNearestMonster(GameMap map, Performer master, ServantActor servantActor) {
        return (Performer) map.getObject(servantActor.getPoint().x, servantActor.getPoint().y, servantActor.getToAttackArea(),
                point -> {
                    PointState pointState = map.getPointState(point);
                    return pointState == null || (!pointState.isSafe() && !pointState.isHasTransmitEvent());
                },
                iMapObject -> {
                    if (!iMapObject.isPerformer()) {
                        return false;
                    }
                    if (iMapObject.isDead()) {
                        return false;
                    }
                    if (!servantActor.isEnemy(iMapObject, true)) {
                        return false;
                    }
                    if (iMapObject.getType() != MapObjectType.MONSTER) {
                        return false;
                    }
                    MonsterActor monsterActor = (MonsterActor) iMapObject;
                    if (MonsterAIUtil.isInvisible(servantActor, monsterActor)) {
                        return false;
                    }
                    if (monsterActor.getAiType() == AIType.GUARD_ARCHER || monsterActor.getAiType() == AIType.GUARD_SWORD) {
                        return false;
                    }
                    return true;
                });

    }

    public static Performer findTarget(GameMap map, Performer master, XiaLvActor servantActor) {
        int fightState = servantActor.getFightState();
        if (fightState == ServantConst.FightState.ATTACK) {
            if (servantActor.getMonsterType() == MonsterType.SKILL_CALL) {
                return unlockFindTarget(map, master, servantActor, false);
            } else if (servantActor.getMonsterType() == MonsterType.ITEM_CALL) {
                return unlockFindTarget(map, master, servantActor, true);
            } else {
                return unlockFindTarget(map, master, servantActor, true);
            }
        } else if (fightState == ServantConst.FightState.LOCK) {
            if (servantActor.getFightTarget() > 0) {
                return (Performer) map.getObject(servantActor.getFightTarget());
            }
            return lockFindTarget(map, master, servantActor);
        }
        return null;
    }


    /**
     * 优先级：主人攻击目标>攻击主人目标>自己攻击目标>攻击自己目标>随机目标
     *
     * @param map
     * @param master
     * @param servantActor
     * @param masterPriority
     * @return
     */
    private static Performer unlockFindTarget(GameMap map, Performer master, XiaLvActor servantActor, boolean masterPriority) {
        Performer masterTarget = findMasterTarget(map, master, servantActor);
        if (masterPriority) {
            if (masterTarget != null) {
                return masterTarget;
            }
        }

        if (servantActor.getFightTarget() > 0) {
            IMapObject obj = map.getObject(servantActor.getFightTarget());
            if (obj == null || obj.isDead() || !obj.isPerformer()) {
                servantActor.setFightTarget(0);
            } else {
                Performer target = (Performer) obj;
                if (target.getId() == master.getWhoMyTarget()
                        || GeomUtil.distance(target.getPoint(), servantActor.getPoint()) <= servantActor.getToAttackArea()) {
                    return target;
                } else {
                    servantActor.setFightTarget(0);
                }
            }
        }

        if (masterTarget != null) {
            return masterTarget;
        }

        //寻找最近的目标
        return findNearestMonster(map, master, servantActor);
    }

    private static Performer findMasterTarget(GameMap map, Performer master, XiaLvActor servantActor) {
        Performer target;
        // 寻找主人攻击的目标
        if (master.getWhoMyTarget() != 0) {
            target = (Performer) map.getObject(master.getWhoMyTarget());
            if (target != null && !target.isDead() && servantActor.isEnemy(target, true)) {
                int dis = GeomUtil.distance(target.getPoint(), master.getPoint());
                if (dis <= 20) {
//                    servantActor.setWhoMyTarget(target.getId());
                    return target;
                }
            }
        }
        // 寻找攻击主人的目标
        if (master.getWhoAttackMe() != 0) {
            target = (Performer) map.getObject(master.getWhoAttackMe());
            if (target != null && !target.isDead() && servantActor.isEnemy(target, true)) {
                int dis = GeomUtil.distance(target.getPoint(), master.getPoint());
                if (dis <= 20) {
//                    servantActor.setWhoMyTarget(target.getId());
                    return target;
                }
            }
        }
        return null;
    }

    /**
     * 锁定状态，寻找目标
     * 锁定只寻找猪脚攻击对象
     * @param map
     * @param master
     * @param servantActor
     * @return
     */
    private static Performer lockFindTarget(GameMap map, Performer master, XiaLvActor servantActor) {

        long masterAttackId = master.getWhoMyTarget();
        long servantAttackerId = servantActor.getFightTarget();

        //如果主人的攻击对象不为空，并且不一致，则设置宠物攻击对象为masterid
        if (masterAttackId != 0 && servantAttackerId != masterAttackId) {
            servantActor.setFightTarget(masterAttackId);
        }

        IMapObject obj = map.getObject(servantActor.getFightTarget());
        if (obj != null && !obj.isDead() && obj.isPerformer()) {
            return (Performer) obj;
        }
        return null;
    }

    private static Performer findNearestMonster(GameMap map, Performer master, XiaLvActor servantActor) {
        return (Performer) map.getObject(servantActor.getPoint().x, servantActor.getPoint().y, servantActor.getToAttackArea(),
                point -> {
                    PointState pointState = map.getPointState(point);
                    return pointState == null || (!pointState.isSafe() && !pointState.isHasTransmitEvent());
                },
                iMapObject -> {
                    if (!iMapObject.isPerformer()) {
                        return false;
                    }
                    if (iMapObject.isDead()) {
                        return false;
                    }
                    if (!servantActor.isEnemy(iMapObject, true)) {
                        return false;
                    }
                    if (iMapObject.getType() != MapObjectType.MONSTER) {
                        return false;
                    }
                    MonsterActor monsterActor = (MonsterActor) iMapObject;
                    if (MonsterAIUtil.isInvisible(servantActor, monsterActor)) {
                        return false;
                    }
                    if (monsterActor.getAiType() == AIType.GUARD_ARCHER || monsterActor.getAiType() == AIType.GUARD_SWORD) {
                        return false;
                    }
                    return true;
                });

    }
}
