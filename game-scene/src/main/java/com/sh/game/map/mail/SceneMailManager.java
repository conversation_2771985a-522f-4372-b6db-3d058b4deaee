package com.sh.game.map.mail;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.notice.MailSendNotice;
import com.sh.game.common.config.model.MailConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.util.ItemUtil;
import com.sh.game.common.util.SerializerUtil;
import com.sh.game.map.Module;
import com.sh.game.map.Player;
import com.sh.game.map.player.PlayerManager;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
public class SceneMailManager {
    private static final SceneMailManager INSTANCE = new SceneMailManager();

    private SceneMailManager() {}

    public static SceneMailManager getInstance() {
        return INSTANCE;
    }


    public void sendMail(long roleId, int mailId, List<Item> attachments, Object ... args) {
        MailConfig config = ConfigDataManager.getInstance().getById(MailConfig.class, mailId);
        if (config == null) {
            log.error("mail config not found: {}", mailId);
            return;
        }

        if (attachments == null && config.getBoxReward() != null) {
            attachments = new ArrayList<>();
            for (int[] ints: config.getBoxReward()) {
                if (ints.length < 2) {
                    continue;
                }
                attachments.add(ItemUtil.create(ints[0], ints[1], LogAction.EMAIL_GET));
            }
        }

        MailSendNotice notice = new MailSendNotice();
        notice.setRoleId(roleId);
        notice.setMailCfgId(config.getId());
        notice.setParams(Arrays.asList(args));
        notice.setAttachments(attachments);
        Player player = PlayerManager.getInstance().getPlayer(roleId);
        if (player != null) {
            notice.addHost(player.getRemoteHostId());
        } else {
            Module.NET.getAllClient().forEach(connection -> notice.addHost(connection.getHostId()));
        }
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, roleId);
    }
}
