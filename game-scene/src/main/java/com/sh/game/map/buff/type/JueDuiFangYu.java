package com.sh.game.map.buff.type;

import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.util.TipUtil;

/**
 * <AUTHOR>
 * @Description: 神威绝对防御buff
 * @Date 2022-05-09 09:26
 **/
public class JueDuiFangYu extends AbsBuffEffect {

    @Override
    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
        buff.initParam(b -> {
            if (!performer.isPlayer()) {
                return;
            }
            PlayerActor playTarget = (PlayerActor) performer;
            //本次攻击无效,加tip
            performer.getBuffState().setShenWeiDefendCount(performer.getBuffState().getShenWeiDefendCount() + 1);
            TipUtil.show(playTarget.getRoleId(), CommonTips.神威绝对防御);
        });
    }
}
