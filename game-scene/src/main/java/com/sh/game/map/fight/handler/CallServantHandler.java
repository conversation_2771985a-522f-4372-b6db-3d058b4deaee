package com.sh.game.map.fight.handler;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.monster.ResUpdateServantSizeMessage;
import com.sh.game.common.config.cache.MonsterEnticeConfigCache;
import com.sh.game.common.config.model.MonsterConfig;
import com.sh.game.common.config.model.MonsterEnticeConfig;
import com.sh.game.common.config.model.SkillConditionConfig;
import com.sh.game.common.constant.ServantConst;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.util.ParamAddUtil;
import com.sh.game.map.Module;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.effect.impl.EmptyEffect;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.MapObjectFactory;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.obj.ServantActor;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.servant.ServantActorManager;
import com.sh.game.protos.MonsterProtos;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 召唤术
 *
 * <AUTHOR>
 * 2017年5月31日 上午9:00:30
 */
@Slf4j
public class CallServantHandler extends AbstractHandler {

    @Override
    public SkillEffect getEffect() {
        return new EmptyEffect(this);
    }

    @Override
    public void cast(FightResult ret, Skill skill, GameMap map, Performer caster, List<Performer> targetList, Point targetPoint) {
        if (!caster.isPlayer()) {
            return;
        }

        SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
        if (skillConditionConfig == null) {
            return;
        }

        Map<Integer, Set<Integer>> skillSpecEffectMap = caster.getSkillSpecEffectMap();
        int[][] paramArrays = ParamAddUtil.calSkillConditionAllParams(skillSpecEffectMap, skillConditionConfig);

        boolean success = false;

        List<ServantActor> servantList = ((PlayerActor) caster).getServantList();

        int count = paramArrays.length;

        for (int[] ints : paramArrays) {
            if (ints.length != 3) {
                log.error("召唤怪物配置有问题,rid:{},name:{},skillId->{},level->{}", caster.getId(), caster.getName(), skill.getSkillId(), skill.getLevel());
                continue;
            }

            int monsterId = ints[0];
            int initLevel = ints[1];
            int maxLevel = ints[2];

            if (initLevel > maxLevel) {
                log.error("召唤怪物配置有问题,rid:{},name:{},skillId->{},level->{}", caster.getId(), caster.getName(), skill.getSkillId(), skill.getLevel());
                continue;
            }

            MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterId);
            if (monsterConfig == null) {
                log.error("召唤怪物配置有问题,rid:{},name:{},找不到配置的怪物id:[{}]", caster.getId(), caster.getName(), monsterId);
                continue;
            }

            MonsterEnticeConfigCache cache = ConfigCacheManager.getInstance().getCache(MonsterEnticeConfigCache.class);
            MonsterEnticeConfig curLevelConfig = cache.getConfigByLevel(monsterId, initLevel);
            MonsterEnticeConfig maxLevelConfig = cache.getConfigByLevel(monsterId, maxLevel);
            if (curLevelConfig == null || maxLevelConfig == null) {
                log.error("召唤怪物配置有问题,rid:{},name:{},skillId->{},level->{}", caster.getId(), caster.getName(), skill.getSkillId(), skill.getLevel());
                continue;
            }

            ServantActor replaceTarget = null;

            //获取本技能召唤的怪物
            List<ServantActor> servantActors = servantList.stream().filter(servantActor -> skill.getSkillId() == servantActor.getSkillId()).collect(Collectors.toList());
            //获取本技能id
            int thisSkillId = 0;
            for (ServantActor servantActor : servantActors) {
                thisSkillId = servantActor.getSkillId();
                break;
            }
            //数量不够，不做替换判断
            if (skill.getSkillId() == thisSkillId && servantActors.size() >= count) {
                replaceTarget = ((PlayerActor) caster).getReplaceServant(curLevelConfig, monsterConfig, skill.getSkillId());
            }

            //低优先级不替换,同优先级重新召唤刷新该技能召唤怪属性
            if (replaceTarget != null && curLevelConfig.getReplace() < replaceTarget.getReplace()) {
                continue;
            }

            ServantActor servantActor = MapObjectFactory.createServant(caster, monsterId, initLevel, maxLevel, ServantConst.From.SKILL_CALL, null, null, skill.getSkillId());
            if (servantActor == null) {
                continue;
            }

            if (replaceTarget != null) {
                map.removeServant(replaceTarget);
                servantList.remove(replaceTarget);
            }
            Point enterPoint = ServantActorManager.getInstance().findServantEnterPoint(map, servantActor, caster.getPoint());
            map.enterServant(servantActor, enterPoint);
            servantList.add(servantActor);


            //更新玩家宠物数量
            int size = servantList.size();
            if (size > 0) {
                ResUpdateServantSizeMessage updateServantSizeMessage = new ResUpdateServantSizeMessage();
                MonsterProtos.ResUpdateServantSize.Builder updateServantSize = MonsterProtos.ResUpdateServantSize.newBuilder();
                updateServantSize.setSize(size);
                updateServantSizeMessage.setProto(updateServantSize.build());
                Module.MSG_TRANSFORMER.sendMsg(updateServantSizeMessage, caster.getId());
            }


            success = true;
        }

        if (success) {
            ret.castSkillSuc = true;
        }
    }
}
