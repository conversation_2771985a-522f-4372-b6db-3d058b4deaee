package com.sh.game.map.using;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.notice.RoleMapItemUsingConfirmNotice;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.map.Module;
import com.sh.game.map.Player;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.using.impl.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Slf4j
public class UsingManager {
    private static final UsingManager INSTANCE = new UsingManager();
    private static final Map<Integer, AbsUsingValidator> VALIDATORS = new HashMap<>();

    static {
        VALIDATORS.put(101, new ItemUsageForLiquidRecover());
        VALIDATORS.put(102, new ItemUsageForLiquidForce());
        VALIDATORS.put(103, new ItemUsageForLiquidStore());
        VALIDATORS.put(104, new ItemUsageForLiquidBuff());
        VALIDATORS.put(105, new ItemUsageForLiquidTime());
        VALIDATORS.put(108, new ItemUsageForPkValue());
        VALIDATORS.put(109, new ItemUsageForLoyalty());

        VALIDATORS.put(124, new ItemUsageForExploreBall());
        VALIDATORS.put(125, new ItemUsageForExpPromote());
        VALIDATORS.put(126, new ItemUsageForServantCall());
        VALIDATORS.put(135, new ItemUsageForServantLevelUp());
        VALIDATORS.put(162, new ItemUsageForEvilGodCalling());
        VALIDATORS.put(163, new ItemUsageForEvilGodCalling());

        VALIDATORS.put(422, new ItemUsageForRecoveryCard());
    }

    private UsingManager() {
    }

    public static UsingManager getInstance() {
        return INSTANCE;
    }


    public void verify(long roleId, long itemId, int itemCfgId, int itemCount, List<Integer> params) {
        Player player = PlayerManager.getInstance().getPlayer(roleId);
        if (player == null) {
            return;
        }
        ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, itemCfgId);
        if (config == null) {
            log.error("item config not found: {}", itemCfgId);
            return;
        }
        AbsUsingValidator validator = VALIDATORS.get(config.getUseType());
        if (validator == null || !validator.validate(player.getActor(), config)) {
            return;
        }

        RoleMapItemUsingConfirmNotice notice = new RoleMapItemUsingConfirmNotice();
        notice.setRoleId(player.getId());
        notice.setItemId(itemId);
        notice.setItemCount(itemCount);
        notice.setParams(params);
        notice.addHost(player.getRemoteHostId());

        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, player.getId());
    }

    public void apply(long roleId, long actorId, int itemCfgId, int count) {
        Player player = PlayerManager.getInstance().getPlayer(roleId);
        if (player == null) {
            return;
        }
        ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, itemCfgId);
        if (config == null) {
            log.error("item config not found: {}", itemCfgId);
            return;
        }
        PlayerActor playerActor = roleId == actorId ? player.getActor() : player.getActorMap().getOrDefault(actorId, null);
        AbsUsingValidator validator = VALIDATORS.get(config.getUseType());
        if (validator != null && playerActor != null) {
            validator.apply(playerActor, config, count);
        }
    }
}
