package com.sh.game.map.fight.msg;

import com.sh.game.common.communication.msg.map.fight.ReqCancelHeroCombSkillMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.map.player.PlayerManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求取消英雄释放合体技</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.MAP_PLAYER)
public class ReqCancelHeroCombSkillHandler extends AbstractHandler<ReqCancelHeroCombSkillMessage> {

    @Override
    public void doAction(ReqCancelHeroCombSkillMessage msg) {
        PlayerManager.getInstance().cancelHeroCombSkill(msg.getSession().getId());
    }

}
