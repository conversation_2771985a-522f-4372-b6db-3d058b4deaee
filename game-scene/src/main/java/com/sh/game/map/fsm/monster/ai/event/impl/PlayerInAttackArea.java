package com.sh.game.map.fsm.monster.ai.event.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.MonsterAiConfig;
import com.sh.game.map.fsm.MonsterAIUtil;
import com.sh.game.map.fsm.monster.ai.event.AbstractEventAI;
import com.sh.game.map.fsm.monster.ai.event.Event;
import com.sh.game.map.obj.MapObjectType;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;

//玩家和狗进入警戒范围
public class PlayerInAttackArea extends AbstractEventAI {

    @Override
    public void check(Event event, GameMap map, MonsterActor monster, int dt) {
        if (monster.isDead()) {
            return;
        }

        MonsterAiConfig config = ConfigDataManager.getInstance().getById(MonsterAiConfig.class, event.getCfgId());
        int range;
        if (config.getConditonvalue().length > 0 && config.getConditonvalue()[0] > 0) {
            range = config.getConditonvalue()[0];
        } else {
            range = monster.getToAttackArea();
        }

        Performer performer = MonsterAIUtil.findPlayerInRange(map, monster, range, MapObjectType.PLAYER, MapObjectType.HERO, MapObjectType.SERVANT);
        if (performer == null) {
            event.setDelay(0);
            return;
        }

        if (canTrigger(event, map, monster, dt)) {
            trigger(event, map, monster, dt);
        }
    }
}
