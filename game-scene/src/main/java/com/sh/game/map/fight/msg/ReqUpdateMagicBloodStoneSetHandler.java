package com.sh.game.map.fight.msg;

import com.sh.game.common.communication.msg.map.fight.ReqUpdateMagicBloodStoneSetMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.protos.FightProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>请求更新魔血石设置</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.MAP_PLAYER)
public class ReqUpdateMagicBloodStoneSetHandler extends AbstractHandler<ReqUpdateMagicBloodStoneSetMessage> {

    @Override
    public void doAction(ReqUpdateMagicBloodStoneSetMessage msg) {
        FightProtos.ReqUpdateMagicBloodStoneSet updateMagicBloodStoneSet = msg.getProto();
        PlayerManager.getInstance().updateMagicBloodStoneSet(msg.getSession().getId(), updateMagicBloodStoneSet.getSetHpValue(), updateMagicBloodStoneSet.getSetMpValue());
    }

}
