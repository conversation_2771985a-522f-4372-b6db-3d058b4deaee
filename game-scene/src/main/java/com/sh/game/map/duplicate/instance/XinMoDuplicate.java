package com.sh.game.map.duplicate.instance;

import com.sh.game.map.duplicate.Duplicate;
import com.sh.game.map.obj.MapObjectFactory;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.Point;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/6/16
 * 心魔试炼
 */
public class XinMoDuplicate extends Duplicate {
    int monsterKill = 0; // 小怪计数
    public static final Map<Integer, Integer> map = new HashMap<>();

    @Override
    public void onMonsterDie(MonsterActor monster) {
        super.onMonsterDie(monster);
        monsterKill++;
        if (monsterKill == 20) {
            summonBoss();
        }
    }

    void summonBoss() {
        MonsterActor actor = MapObjectFactory.createMonster(getCfgId(), false);
        Point point = getPoint(30, 33);
        enterMonster(actor, point, true);
    }
}
