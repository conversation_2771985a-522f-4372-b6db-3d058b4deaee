package com.sh.game.map.fight.handler;

import com.sh.game.common.constant.AttributeEnum;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.event.EventUtil;
import com.sh.game.map.event.MapEventType;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.effect.impl.EmptyEffect;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.listener.SkillCastEvent;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;

import java.util.List;

/**
 * 持续治疗技能
 */
public class CureHandler extends AbstractHandler {

    @Override
    public SkillEffect getEffect() {
        return new EmptyEffect(this);
    }

    @Override
    public void cast(FightResult ret, Skill skill, GameMap map, Performer caster, List<Performer> targetList, Point targetPoint) {
        long time = TimeUtil.getNowOfMills();
        for (Performer t : targetList) {
            //可治疗
            if (t.getHp() < AttributeEnum.HP.getAttrValue(t.getFinalAttribute())) {
                FightResult.FightEffectRet effectRet = effect(skill, caster, t, ret);
                ret.addEffect(effectRet);
                EventUtil.fireEvent(MapEventType.ON_SKILL_CAST, new SkillCastEvent(caster, t, skill, ret.buffEffect, time));
                ret.castSkillSuc = true;
            }
        }
    }
}
