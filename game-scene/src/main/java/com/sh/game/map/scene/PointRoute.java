package com.sh.game.map.scene;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2019/8/5 21:27
 */
@Getter
@Setter
public class PointRoute {

    // A*寻路相关的属性-总消耗
    private int f;

    // A*寻路相关的属性-h
    private int h;

    // A*寻路相关的属性-g
    private int g;

    // A*寻路相关的属性-父节点
    private Point p;

    public void clear() {
        f = 0;
        h = 0;
        g = 0;
        p = null;
    }
}
