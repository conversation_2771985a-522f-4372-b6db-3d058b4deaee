package com.sh.game.map.fsm.monster.ai.servant;

import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.GameMap;

public class ServantRestAI extends ServantAI {

    @Override
    public boolean activeUpdate(GameMap map, MonsterActor monster, int dt) {
        return true;
    }

    @Override
    public boolean battleUpdate(GameMap map, MonsterActor monster, int dt) {
        return true;
    }

}
