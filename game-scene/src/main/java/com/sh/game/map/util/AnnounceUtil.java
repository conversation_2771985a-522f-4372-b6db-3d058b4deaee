package com.sh.game.map.util;

import com.sh.game.map.scene.GameMap;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/2/13
 * @Desc : to do anything
 */
@Slf4j
public class AnnounceUtil {

    public static void announceToWorld(int announceId, Object... params) {
//        Module.MSG_TRANSFORMER.sendToWorld(buildAnnounceMessage(announceId, params));
    }

    public static void announceToMap(GameMap map, int announceId, Object... params) {
//        Module.MSG_TRANSFORMER.sendMapMessage(buildAnnounceMessage(announceId, params), map);
    }

    public static void announceToRids(int announceId, Set<Long> rids, Object... params) {
//        Module.MSG_TRANSFORMER.sendMsgToRids(buildAnnounceMessage(announceId, params), rids);
    }

    public static void announceToHost(int announceId, Object... params) {
//        ResAnnounceMessage msg = new ResAnnounceMessage();
//        msg.setParams(buildAnnounceList(announceId, params));
//        Module.MSG_TRANSFORMER.sendToHost(msg);
    }

//    private static ResAnnounceMessage buildAnnounceMessage(int announceId, Object... params) {
////        ResAnnounceMessage msg = new ResAnnounceMessage();
////        msg.setParams(buildAnnounceList(announceId, params));
////        return msg;
//    }
//
//    public static List<String> buildAnnounceList(int announceId, Object... params) {
//        List<String> list = new ArrayList<>();
//        for (Object param : params) {
//            if (param == null) {
//                log.info("【】|【】发送公告时为null announceId{} {}", announceId, JSON.toJSONString(params));
//                continue;
//            }
//            if (param instanceof Long) {
//                long l = (long) param;
//                int longHighInt = Cast.getLongHighInt(l);
//                int longLowInt = Cast.getLongLowInt(l);
//                String result = String.valueOf(longLowInt) + "_" + String.valueOf(longHighInt);
//                list.add(result);
//            } else if (param instanceof Item) {
//                Item item = (Item) param;
//                ItemConfig itemConfig = ConfigDataManager.getInstance().getById(ItemConfig.class, item.getItemId());
//                if (itemConfig != null && itemConfig.getItemtype() == BagConst.ItemType.EQUIP) {
//                    String str = "";
//                    JiPinAtt jiPinAtt = item.getJiPinAtt();
//                    if (jiPinAtt != null) {
//                        AttributeScoreCache scoreCache = ConfigCacheManager.getInstance().getCache(AttributeScoreCache.class);
//                        int fightPower = AttributeEnum.calculateFightValue(jiPinAtt.getAttrMap(), scoreCache.getAttrScoreMapByCareer(RoleConst.Career.ZHAN));
//                        long hp = jiPinAtt.getAttrMap().getOrDefault(AttributeEnum.HP.getType(), 0L);
//                        long atk = jiPinAtt.getAttrMap().getOrDefault(AttributeEnum.PHY_ATK.getType(), 0L);
//                        long def = jiPinAtt.getAttrMap().getOrDefault(AttributeEnum.PHY_DEF.getType(), 0L);
//                        if (str.isEmpty()) {
//                            str = item.getItemId() + "_" + 0 + "_" + 0 + "_" + 0 + "_" + 0 + "_" + 0 + "_" + fightPower + "_" + hp + "_" + atk + "_" + def;
//                        } else {
//                            str = str + fightPower + "_" + hp + "_" + atk + "_" + def;
//                        }
//                    }
//                    if (str.isEmpty()) {
//                        list.add(String.valueOf(item.getItemId()));
//                    } else {
//                        list.add(str);
//                    }
//                } else {
//                    list.add(String.valueOf(item.getItemId()));
//                }
//            } else {
//                list.add(param.toString());
//            }
//        }
//
//        list.add(0, String.valueOf(announceId));
//        return list;
//    }
}
