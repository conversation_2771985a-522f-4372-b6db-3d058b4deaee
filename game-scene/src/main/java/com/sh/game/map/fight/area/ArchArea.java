package com.sh.game.map.fight.area;

import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.util.GeomUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-10-10
 * 半圆
 * <p>具体如下形状</p>
 * --------------<br>
 * ----AAA-------<br>
 * -----PA------<br>
 * --------------<br>
 * --------------<br>
 **/
public class ArchArea extends AttackArea {

    @Override
    public List<Point> findAreaPoint(GameMap map, Point point, int areaDir, int[] areaDisInts) {
        List<Point> ret = new ArrayList<>();
        for (int i = -2; i <= 2; i++) {
            byte vector[] = GeomUtil.getVector(areaDir + i);
            Point p = map.getPoint(point.x + vector[0], point.y + vector[1]);
            if (p != null) {
                ret.add(p);
            }
        }

        // 移除左侧点
        byte vector[] = GeomUtil.getVector(areaDir + 6);
        Point p = map.getPoint(point.x + vector[0], point.y + vector[1]);
        if (p != null) {
            ret.remove(p);
        }

        return ret;
    }
}
