package com.sh.game.map.fsm.monster.ai.event.impl;

import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.fsm.monster.ai.event.AbstractEventAI;
import com.sh.game.map.fsm.monster.ai.event.Event;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.GameMap;

//目标死亡才能重生 在sleep状态下检测
public class TargetDeadBeforeRelive extends AbstractEventAI {

    @Override
    public void check(Event event, GameMap map, MonsterActor monster, int dt) {
        if (event.getCalls() == null || event.getCalls().isEmpty()) {
            return;
        }

        boolean allDead = true;
        for (MonsterActor target : event.getCalls()) {
            if (target != null && !target.isDead()) {
                allDead = false;
                break;
            }
        }

        if (!allDead) {
            event.setDelay(0);
            //sleep检测需要延迟nextReliveTime
            monster.getMachine().getAiData().setNextReliveTimeDelay(true);
            return;
        }

        if (canTrigger(event, map, monster, dt)) {
            trigger(event, map, monster, dt);
        }

        monster.getMachine().getAiData().setNextReliveTimeDelay(false);
        long now = TimeUtil.getNowOfMills();
        if (monster.getMachine().getAiData().getNextReliveTime() < now) {
            monster.getMachine().getAiData().setNextReliveTime(now + 1000);
        }
    }
}
