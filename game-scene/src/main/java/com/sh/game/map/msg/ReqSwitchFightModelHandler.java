package com.sh.game.map.msg;

import com.sh.game.map.player.PlayerManager;
import com.sh.game.common.communication.msg.map.ReqSwitchFightModelMessage;
import com.sh.game.protos.MapProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>请求切换攻击模式</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 09:11:10
 */
public class ReqSwitchFightModelHandler extends AbstractHandler<ReqSwitchFightModelMessage> {

    @Override
    public void doAction(ReqSwitchFightModelMessage msg) {
        MapProtos.ReqSwitchFightModel switchFightModel = msg.getProto();
        PlayerManager.getInstance().switchFightModel(msg.getSession().getId(),switchFightModel.getFightModel(),true);
    }

}
