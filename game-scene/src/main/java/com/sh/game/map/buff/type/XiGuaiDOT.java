package com.sh.game.map.buff.type;


import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.map.fight.ResFightMessage;
import com.sh.game.common.communication.msg.map.fight.ResFightResultMessage;
import com.sh.game.common.config.model.BuffConfig;
import com.sh.game.common.config.model.MonsterConfig;
import com.sh.game.common.constant.FightConst;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.fight.FightUtil;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.scene.PointState;
import com.sh.game.protos.FightProtos;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class XiGuaiDOT extends AbsBuffEffect {


    @Override
    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
        buff.initParam(b -> {
            BuffConfig buffConfig = buff.config();
            int[][] parameter = buffConfig.getParameter();
            if (parameter.length <= 0 || parameter[0].length <= 0) {
                return;
            }
            int[][] extraParam = buffConfig.getExtraParam();
            if (extraParam.length <= 0) {
                return;
            }
            buff.getParams().add(buffConfig.getParameter()[0][0]);
            buff.getParams().add(extraParam[0][0]);
            buff.getParams().add(buffConfig.getParameter()[0][2]);
            buff.setLoop(buffConfig.getParameter()[0][1]);
            buff.setClock(TimeUtil.getNowOfMills());
        });
    }

    @Override
    public boolean canUpdate(Buff buff, long time) {
        if (buff.getClock() > time) {
            return false;
        }
        buff.setClock(buff.getClock() + buff.getLoop());
        return true;
    }

    @Override
    public void onUpdate(GameMap map, Performer performer, Buff buff, long time, int delta) {
        if (performer.isDead()) {
            return;
        }
        if (buff.getParams().size() <= 0) {
            return;
        }
        PointState pointState = map.getPointState(performer.getPoint());
        //判断是否在安全区
        if (pointState != null && pointState.isSafe()) {
            return;
        }
        List<MonsterActor> viewMonster = findViewMonster(map, performer, buff);
        //没有可攻击的行为对象不返回协议
        if (viewMonster.size() <= 0) {
            return;
        }
        long finalHurt = buff.getParams().get(0);
        //发送伤害消息
        ResFightMessage msg = new ResFightMessage();
        FightProtos.ResFight.Builder fight = FightProtos.ResFight.newBuilder();
        ResFightResultMessage retMsg = new ResFightResultMessage();
        FightProtos.ResFightResult.Builder fightResultMsg = FightProtos.ResFightResult.newBuilder();
        for (MonsterActor monsterActor : viewMonster) {
            if (monsterActor == null) {
                continue;
            }
            Point point = monsterActor.getPoint();
            if (point == null) {
                continue;
            }
            FightResult.FightEffectRet effectRet = new FightResult.FightEffectRet();
            effectRet.hurt = finalHurt;
            effectRet.showHurt = finalHurt;
            FightResult fightResult = new FightResult();
            FightUtil.hurt(FightUtil.HurtFrom.BUFF, fightResult, effectRet, performer, monsterActor, time, false);

            //发送消息
            performer.sendHpMpChangeMessage();
            FightProtos.HurtTarget.Builder hurtTarget = FightProtos.HurtTarget.newBuilder();
            hurtTarget.setTargetId(monsterActor.getId());
            hurtTarget.setSourceId(performer.getId());
            hurtTarget.setHp(monsterActor.getHp());
            hurtTarget.setInner(monsterActor.getMp());
            FightProtos.HurtTypeInfo.Builder hurtTypeInfo = FightProtos.HurtTypeInfo.newBuilder();
            if (buff.getConfig().getPiaozi() > 0) {
                hurtTypeInfo.setHurtType(buff.getConfig().getPiaozi());
            } else {
                hurtTypeInfo.setHurtType(FightConst.HurtType.NORMAL_HURT.type());
            }
            hurtTypeInfo.setHurt(effectRet.showHurt);
            hurtTarget.addHurtTypeList(hurtTypeInfo);
            fightResultMsg.addHurtList(hurtTarget);

            //组装战斗协议
            fight.addTargetIdList(monsterActor.getId());
        }
        //没怪不发消息
        if (fight.getTargetIdListList().size() == 0 || fightResultMsg.getHurtListList().size() == 0) {
            return;
        }
        //技能id
        int skillId = buff.getParams().get(2) == null ? 0 : buff.getParams().get(2);
        fightResultMsg.setSkillId(skillId);
        retMsg.setProto(fightResultMsg.build());
        Module.MSG_TRANSFORMER.sendMsg(retMsg, performer.getRid());
        //返回战斗协议
        Point point = performer.getPoint();
        fight.setSkillId(skillId);
        fight.setSkillLevel(1);
        if (point != null) {
            fight.setX(point.getX());
            fight.setY(point.getY());
        }
        fight.setAttackerId(performer.getId());
        fight.setAttackerDir(performer.getDir());
        fight.setFly(fight.getTargetIdListList().isEmpty());
        fight.setSkillEffect(true);
        msg.setProto(fight.build());
        Module.MSG_TRANSFORMER.sendMsg(msg, performer.getRid());
    }

    private List<MonsterActor> findViewMonster(GameMap map, Performer performer, Buff buff) {
        List<Integer> params = buff.getParams();
        List<MonsterActor> monsterActorList = new ArrayList<>();
        if (params.size() < 2) {
            return monsterActorList;
        }
        Point performerPoint = performer.getPoint();
        if (performerPoint == null) {
            return monsterActorList;
        }
        Map<Long, MonsterActor> monsterMap = map.getMonsterMap();
        for (Map.Entry<Long, MonsterActor> entry : monsterMap.entrySet()) {
            MonsterActor monsterActor = entry.getValue();
            if (monsterActor == null) {
                continue;
            }
            MonsterConfig monsterConfig = ConfigDataManager.getInstance().getById(MonsterConfig.class, monsterActor.getConfigId());
            if (monsterConfig == null) {
                continue;
            }
            //不受吸怪影响
            if (monsterConfig.getIsSkill() == 1) {
                continue;
            }
            //不对已死亡怪物释放
            if (monsterActor.isDead()) {
                continue;
            }
            Point monsterPoint = monsterActor.getPoint();
            if (monsterPoint == null) {
                continue;
            }
            //不在范围
            if (!map.isInCircleArea(monsterPoint.getX(), monsterPoint.getY(), performerPoint.getX(), performerPoint.getY(), params.get(1) == null ? 0 : params.get(1))) {
                continue;
            }
            monsterActorList.add(monsterActor);
        }
        return monsterActorList;
    }

    @Override
    public void onBuffLogin(Buff buff, long time) {
        if (buff.getClock() > time) {
            return;
        }
        long offline = time - buff.getClock();
        buff.setClock(buff.getClock() + offline);
        buff.setExpire(buff.getExpire() + offline);
    }
}
