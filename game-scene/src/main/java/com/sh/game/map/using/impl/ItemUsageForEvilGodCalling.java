package com.sh.game.map.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.TipsConfig;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.map.SceneManager;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.scene.PointState;
import com.sh.game.map.using.AbsUsingValidator;
import com.sh.game.map.util.TipUtil;

public class ItemUsageForEvilGodCalling extends AbsUsingValidator {

    @Override
    public boolean validate(PlayerActor player, ItemConfig config) {
        Point point = player.getPoint();
        if (point == null) {
            return false;
        }
        GameMap map = SceneManager.getInstance().getMap(player);
        PointState pointState = map.getPointState(point);
        if (pointState != null && pointState.isSafe()) {
            TipUtil.show(player.getRid(), CommonTips.场景_此道具不能在安全区使用);
            return false;
        }

        return true;
    }
}
