package com.sh.game.map.move.msg;

import com.sh.game.map.move.MoveManager;
import com.sh.game.common.communication.msg.map.move.ReqWalkMessage;
import com.sh.game.protos.MoveProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>请求慢走</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 09:11:11
 */
public class ReqWalkHandler extends AbstractHandler<ReqWalkMessage> {

    @Override
    public void doAction(ReqWalkMessage msg) {
        MoveProtos.ReqWalk walk = msg.getProto();
        MoveManager.getInstance().playerWalk(msg.getSession().getId(), walk.getX(), walk.getY());
    }

}
