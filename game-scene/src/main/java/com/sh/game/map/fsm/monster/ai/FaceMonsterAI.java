package com.sh.game.map.fsm.monster.ai;


import com.sh.game.map.fsm.AIData;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.util.GeomUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 贴脸怪ai
 */
@Slf4j
public class FaceMonsterAI extends ActiveAI {

    @Override
    protected int findSkill(GameMap map, MonsterActor monster, Performer target) {
        if (target != null && GeomUtil.distance(monster, target) > 1) {
            AIData aiData = monster.getMachine().getAiData();
            int step = aiData.getRunAndHitWalkStep() + 1;
            if (step > 1) {
                step = 0;
            }
            aiData.setRunAndHitWalkStep(step);
            if (step > 0) {
                return 0;
            }
        }

        return super.findSkill(map, monster, target);
    }
}
