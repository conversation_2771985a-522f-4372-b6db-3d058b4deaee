package com.sh.game.map.buff.type;

import com.sh.game.common.communication.msg.map.ResPiaoZiEffectMessage;
import com.sh.game.common.communication.msg.map.fight.ResFightMessage;
import com.sh.game.common.constant.FightConst;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.SceneManager;
import com.sh.game.map.buff.BuffParamEnhanceUtil;
import com.sh.game.map.fight.FightUtil;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.IMapObject;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.protos.FightProtos;
import com.sh.game.protos.MapProtos;
import lombok.extern.log4j.Log4j2;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * author: wiley
 * QQ   : 510600102
 * Date: 2020/12/25 15:49
 * desc: 火焰特戒
 */
@Log4j2
public class FlameRing extends AbsBuffEffect {

    @Override
    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
        buff.initParam(b -> {
            GameMap map = SceneManager.getInstance().getMap(performer);
            if (map == null) {
                return;
            }
            if (caster == null || performer == null || performer.isDead() || performer.getPoint() == null) {
                return;
            }
            int[] params = BuffParamEnhanceUtil.calParameters(performer, buff.config(), 0);
            if (params.length < 2) {
                return;
            }
            int skillId = params[0];
            int hurt = params[1];
            FightResult.FightEffectRet effectRet = new FightResult.FightEffectRet();
            effectRet.hurt = hurt;
            effectRet.showHurt = hurt;

            FightResult ret = new FightResult();
            FightUtil.hurt(FightUtil.HurtFrom.BUFF, ret, effectRet, caster, performer, TimeUtil.getNowOfMills(), false);
            //发送消息
            ResPiaoZiEffectMessage effectMessage = new ResPiaoZiEffectMessage();
            MapProtos.ResPiaoZiEffect.Builder piaoziEffect = MapProtos.ResPiaoZiEffect.newBuilder();
            piaoziEffect.setTargetId(performer.getId());
            piaoziEffect.setParam(effectRet.showHurt);
            piaoziEffect.setPiaozi(FightConst.HurtType.NORMAL_HURT.type());
            effectMessage.setProto(piaoziEffect.build());
            Module.MSG_TRANSFORMER.sendRoundMessage(effectMessage, performer);
            // 如果死了需要发死亡消息
            log.info("对象[{}]->[{}]受到火焰特戒伤害,当前血量{}/{}", performer.getId(), performer.getName(), performer.getHp(), performer.getFinalAttribute().findMaxHp());
            Point point = performer.getPoint();
            //组装释放结果消息
            ResFightMessage res = new ResFightMessage();
            FightProtos.ResFight.Builder fight = FightProtos.ResFight.newBuilder();
            fight.setSkillId(skillId);
            fight.setSkillLevel(1);
            fight.setX(point.x);
            fight.setY(point.y);
            fight.addTargetIdList(performer.getId());
            fight.setAttackerId(caster.getId());
            fight.setAttackerDir(caster.getDir());
            fight.setSkillEffect(false);
            fight.setFly(false);
            Map<Long, IMapObject> watchers = map.getAoi().getWatchers(performer.getPoint());
            Set<Long> sendIds = new HashSet<>(watchers.keySet());
            res.setProto(fight.build());
            Module.MSG_TRANSFORMER.sendMsgToRids(res, sendIds);
        });
    }
}
