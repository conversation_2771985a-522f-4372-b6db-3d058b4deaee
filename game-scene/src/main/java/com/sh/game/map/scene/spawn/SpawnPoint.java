package com.sh.game.map.scene.spawn;

import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.map.obj.IMapObject;
import com.sh.game.map.obj.MapObject;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.scene.PointState;

import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public abstract class SpawnPoint<T extends MapObject> {

    protected GameMap map;
    protected int id;
    protected int count;
    protected int x;
    protected int y;
    protected int r;
    protected int d;
    protected List<int[]> condition;
    protected int delay;
    protected int initTime;

    protected Set<Long> spawns = null;

    protected boolean noSafePoint;

    /**
     * 是否能重复刷新
     */
    protected boolean noRenter;


    public SpawnPoint() {

    }

    public void init(GameMap map, int id, int count, int x, int y, int r, int d, List<int[]> condition, int delay) {
        this.map = map;
        this.id = id;
        this.count = count;
        this.x = x;
        this.y = y;
        this.r = r;
        this.d = d;
        this.condition = condition;
        this.delay = delay;
        this.initTime = 0;
    }

    public void init(GameMap map, int id, int count, int x, int y, int r, int d, List<int[]> condition, int delay,boolean noSafePoint) {
        init(map, id, count, x, y, r, d, condition, delay);
        this.noSafePoint = noSafePoint;
    }

    public void update() {
        if (ConditionUtil.validate(condition) && available()) {
            if (spawns == null) {
                if (delay > 0) {
                    int now = TimeUtil.getNowOfSeconds();
                    if (initTime == 0) {
                        initTime = now + delay;
                    }
                    if (now < initTime) {
                        return;
                    }
                }

                if (!checkRefreshTime()) {
                    return;
                }

                spawns = new HashSet<>();

                if (noRenter) {
                    return;
                }

                List<Point> points = map.getPoints(x, y, r, true);
                if (points == null || points.isEmpty()) {
                    return;
                }
                int size = points.size();
                Collections.shuffle(points);
                int index = 0;
                for (int n = 1; n <= count; n++) {
                    if (index >= size - 1) {
                        index = 0;
                    }
                    Point point = points.get(index++);
                    if (point == null) {
                        continue;
                    }
                    if (noSafePoint) {
                        PointState state = map.getPointState(point);
                        if (state != null) {
                            if (state.isBlock() && state.isSafe()) {
                                continue;
                            }
                        }
                    }
                    T actor = createActor(id);
                    if (actor == null) {
                        continue;
                    }

                    actor.setSpx(x);
                    actor.setSpy(y);
                    actor.setSpr(r);
                    actor.setDir(d);
                    actor.setBirthPoint(point);
                    spawns.add(actor.getId());
                    mapEnter(actor, point);
                }

                onSpawnEnter(map);
            }
        } else {
            if (spawns != null) {
                spawns.forEach(id -> {
                    IMapObject mapObject = map.getObject(id);
                    if (mapObject != null) {
                        mapLeave((T) mapObject);
                    }
                });

                spawns = null;
                initTime = 0;
                onSpawnLeave(map);
            }
        }
    }

    protected boolean available() {
        return true;
    }

    protected abstract T createActor(int id);

    protected abstract void mapEnter(T actor, Point point);

    protected abstract void mapLeave(T actor);

    protected void onSpawnEnter(GameMap map) {

    }

    protected void onSpawnLeave(GameMap map) {

    }

    public int getInitTime() {
        return initTime;
    }

    public int getId() {
        return id;
    }

    public int getDelay() {
        return delay;
    }

    public boolean checkRefreshTime() {
        return true;
    }
}
