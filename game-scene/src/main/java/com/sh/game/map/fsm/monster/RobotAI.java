package com.sh.game.map.fsm.monster;

import com.sh.game.common.constant.MapConst;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.MonsterManager;
import com.sh.game.map.fsm.FSMState;
import com.sh.game.map.fsm.MonsterAIUtil;
import com.sh.game.map.fsm.monster.ai.AbstractAI;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.obj.RobotActor;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.util.GeomUtil;


public class RobotAI extends AbstractAI {

    @Override
    public int checkStateTransitionOnActive(GameMap map, MonsterActor monster) {
        return super.checkStateTransitionOnActive(map, monster);
    }

    @Override
    public int checkStateTransitionOnBattle(GameMap map, MonsterActor monster) {
        return super.checkStateTransitionOnBattle(map, monster);
    }

    @Override
    public int checkStateTransitionOnDie(GameMap map, MonsterActor monster) {
        if (!monster.isDead()) {
            return FSMState.ACTIVE;
        }

        if (TimeUtil.getNowOfMills() - monster.getMachine().getAiData().getDieTime() >= 5000) {
            ((RobotActor) monster).relive();
            return FSMState.ACTIVE;
        }

        return FSMState.DIE;
    }

    @Override
    public boolean activeUpdate(GameMap map, MonsterActor monster, int dt) {
        if (!((RobotActor) monster).onUpdate(map)) {
            return false;
        }

        Performer nearestPerformer = MonsterAIUtil.findNearestFightAblePerformer(map, monster, monster.getToAttackArea());
        if (nearestPerformer != null) {
            MonsterManager.getInstance().addThreat(monster, nearestPerformer, false);
            //monster.getMachine().getAiData().setClockSleep(0);
        }

        return true;
    }

    @Override
    public boolean battleEnter(GameMap map, MonsterActor monster) {
        ((RobotActor) monster).onBattleEnter();
        return super.battleEnter(map, monster);
    }

    @Override
    public boolean battleExit(GameMap map, MonsterActor monster) {
        ((RobotActor) monster).onBattleExit();
        return super.battleExit(map, monster);
    }

    @Override
    public boolean battleUpdate(GameMap map, MonsterActor monster, int dt) {
        if (!((RobotActor) monster).onUpdate(map)) {
            return false;
        }

        if (!super.battleUpdate(map, monster, dt)) {
            return false;
        }

        Performer target = (Performer) map.getObject(monster.getFightTarget());
        if(target == null) {
            monster.setFightTarget(0);
            return false;
        }

        int sleepTime = 0;
        int skillId = findSkill(map, monster, target);
        if (skillId == 0) {
            if (monster.getMoveSpeed() > 0) {
                if (GeomUtil.distance(monster.getPoint(), target.getPoint()) > 1) {
                    MonsterAIUtil.chaseWalk(map, monster, target);
                }
                sleepTime = MapConst.SPEED.WALK;
            }
        } else {
            MonsterAIUtil.monsterCastSkill(monster, target, skillId);
            sleepTime = MonsterAIUtil.calSleepTime(monster);
        }
        monster.getMachine().getAiData().setClockSleep(sleepTime);
        return true;
    }

    @Override
    public boolean dieUpdate(GameMap map, MonsterActor monster, int dt) {
        return super.dieUpdate(map, monster, dt);
    }

}
