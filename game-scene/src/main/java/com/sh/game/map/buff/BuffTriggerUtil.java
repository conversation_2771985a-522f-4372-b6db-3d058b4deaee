package com.sh.game.map.buff;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.entity.IIKVBean;
import com.sh.game.common.config.model.BuffConfig;
import com.sh.game.common.config.model.QiLinBiBuffReplaceConfig;
import com.sh.game.common.entity.buff.BuffConst;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.obj.PlayerActor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;

@Slf4j
public class BuffTriggerUtil {

    public static void triggerBuff(Performer caster, Performer target, BuffConst.TriggerType triggerType, FightResult.BuffImmediateEffect buffEffect, long time) {
        if (caster == null || target == null) {
            return;
        }

        List<Integer> bufferTypeList = BuffConst.TriggerType.getTriggerBuffType(triggerType);
        if (bufferTypeList == null || bufferTypeList.isEmpty()) {
            return;
        }

        for (int bufferType : bufferTypeList) {
            Set<Integer> buffTriggerSet = caster.getBuffTriggerMap().get(bufferType);
            if (buffTriggerSet == null || buffTriggerSet.isEmpty()) {
                continue;
            }

            List<BuffConfig> configList = new ArrayList<>(buffTriggerSet.size());
            for (int bufferId : buffTriggerSet) {
                BuffConfig buffConfig = ConfigDataManager.getInstance().getById(BuffConfig.class, bufferId);
                if (buffConfig == null) {
                    continue;
                }
                configList.add(buffConfig);
            }
            configList.sort(Comparator.comparingInt(BuffConfig::getSort));
            for (BuffConfig buffConfig : configList) {
                boolean careerHit =
                        (buffConfig.getOccupationTarget() == null || buffConfig.getOccupationTarget().length == 0);
                if (buffConfig.getOccupationTarget() != null) {
                    for (int career : buffConfig.getOccupationTarget()) {
                        if (career == caster.getCareer() || caster.getCareer() == 0) {
                            careerHit = true;
                            break;
                        }
                    }
                }
                if (!careerHit) {
                    continue;
                }
                if (buffConfig.getTrigger() != triggerType.type()) {
                    continue;
                }
                Performer buffTarget;
                if (buffConfig.getIsGood() == 1) {
                    buffTarget = caster.isFriend(target, true) ? target : caster;
                } else {
                    buffTarget = caster.isEnemy(target, true) ? target : null;
                }
                if (buffTarget == null) {
                    continue;
                }
                int addSource = (triggerType == BuffConst.TriggerType.INIT ? BuffConst.AddSource.FORCE : BuffConst.AddSource.NORMAL);

                //释放具体的buffId
                int converterBuffId = buffIdConverter_QiLinBi(caster, buffConfig.getId());
                BuffManager.getInstance().append(buffTarget, converterBuffId, caster, buffEffect, time, addSource);
            }
        }
    }


    /**
     * 麒麟臂根据人物穿戴改变buff
     *
     * buffId转换
     * @return  如果有转换，则使用新id,没有转换则使用原有id
     */
    private static int buffIdConverter_QiLinBi(Performer caster, int buffId){

        QiLinBiBuffReplaceConfig replaceConfig = ConfigDataManager.getInstance().getById(QiLinBiBuffReplaceConfig.class, buffId);
        if (replaceConfig == null) {
            return buffId;
        }


        //必须是玩家
        if (caster instanceof PlayerActor) {
            IIKVBean kv = replaceConfig.getFashion();
            if (((PlayerActor) caster).getAppearanceSets().contains(kv.getValue())) {
                return replaceConfig.getBuffId() <= 0 ? buffId : replaceConfig.getBuffId();
            }
        }

        return buffId;


    }




}
