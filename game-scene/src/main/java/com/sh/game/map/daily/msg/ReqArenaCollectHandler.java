package com.sh.game.map.daily.msg;

import com.sh.game.common.communication.msg.system.daily.ReqArenaCollectMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.map.daily.DailyManager;
import com.sh.game.protos.DailyProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>跨服竞技场采集</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.MAP_PLAYER)
public class ReqArenaCollectHandler extends AbstractHandler<ReqArenaCollectMessage> {

    @Override
    public void doAction(ReqArenaCollectMessage msg) {
        DailyProtos.ReqArenaCollect arenaCollect = msg.getProto();
        DailyManager.getInstance().reqArenaCollect(msg.getSession().getId(), arenaCollect.getMid());
    }

}
