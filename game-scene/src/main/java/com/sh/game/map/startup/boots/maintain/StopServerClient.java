package com.sh.game.map.startup.boots.maintain;

import com.sh.client.Client;
import com.sh.client.ClientBuilder;
import com.sh.client.DefaultMessagePool;
import com.sh.client.NetworkEventlistenerAdapter;
import com.sh.game.common.communication.msg.system.back.ReqBackLoginMessage;
import com.sh.game.common.communication.msg.system.back.ReqCloseServerMessage;
import com.sh.game.common.communication.msg.system.back.ResBackLoginMessage;
import com.sh.game.common.communication.msg.system.back.ResCloseServerMessage;
import com.sh.game.map.startup.boots.maintain.msg.ResBackLoginHandler;
import com.sh.game.map.startup.boots.maintain.msg.ResCloseServerHandler;
import com.sh.game.option.ServerOption;
import com.sh.game.protos.BackProtos;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 */
public class StopServerClient {
    private static final Logger LOGGER = LoggerFactory.getLogger(StopServerClient.class);

    public static void main(String[] args) {

        try {
            String configPath;
            if (args.length > 0) {
                configPath = args[0];
            } else {
                configPath = StopServerClient.class.getResource("/").getPath() + "config.properties";
            }
            ServerOption option = new ServerOption();
            option.build(configPath);

            ClientBuilder builder = new ClientBuilder();

            //只关本机的服务器
            builder.setHost("localhost");
            builder.setPort(option.getBackServerPort());
            builder.setEventlistener(new NetworkEventlistenerAdapter());

            builder.setConsumer((channel, msg) -> {
                ResCloseServerMessage res = (ResCloseServerMessage) msg;
                BackProtos.ResCloseServer proto = res.getProto();
                LOGGER.info(proto.getInfo());
                if (proto.getCode() == -1) {
                    LOGGER.info("local exit...");
                    System.exit(0);
                }
            });

            DefaultMessagePool pool = new DefaultMessagePool();
            pool.register(new ResCloseServerMessage(), ResCloseServerHandler.class);
            pool.register(new ResBackLoginMessage(), ResBackLoginHandler.class);
            builder.setMsgPool(pool);

            builder.setNeedReconnect(false);
            builder.setPooled(false);

            Client client = builder.createClient();
            client.connect(true);
            login(client, option.getBackLoginSign());

            ReqCloseServerMessage req = new ReqCloseServerMessage();

            client.sendMsg(req);
            int count = 10;
            while (count > 0) {
                Thread.sleep(10 * 1000);
                count--;
            }
        } catch (Exception e) {
            LOGGER.error("关服发生错误", e);
        }

        System.exit(0);

    }

    public static void login(Client client, String sign) {
        ReqBackLoginMessage msg = new ReqBackLoginMessage();
        msg.setProto(BackProtos.ReqBackLogin.newBuilder()
                .setLoginName(sign)
                .build());
        client.sendSyncMsg(msg, 3_000);
    }
}
