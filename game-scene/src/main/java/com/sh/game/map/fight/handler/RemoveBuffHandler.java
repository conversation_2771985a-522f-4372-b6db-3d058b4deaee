package com.sh.game.map.fight.handler;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.SkillConditionConfig;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.util.ParamAddUtil;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.event.EventUtil;
import com.sh.game.map.Player;
import com.sh.game.map.event.MapEventType;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.effect.impl.HurtEffect;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.listener.SkillCastEvent;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 移除buff
 */
@Slf4j
public class RemoveBuffHandler extends AbstractHandler {

    @Override
    public SkillEffect getEffect() {
        return new HurtEffect(this);
    }

    @Override
    public void cast(FightResult ret, Skill skill, GameMap map, Performer caster, List<Performer> targetList, Point targetPoint) {
        long time = TimeUtil.getNowOfMills();
        for (Performer t : targetList) {
            ret.buffEffect.setBuffOp(() -> EventUtil.fireEvent(MapEventType.ON_SKILL_CAST, new SkillCastEvent(caster, t, skill, ret.buffEffect, time)));

            FightResult.FightEffectRet effectRet = effect(skill, caster, t, ret);
            setMyTargetAndAttackMe(caster, t, effectRet);

            Player casterPlayer = PlayerManager.getInstance().getPlayer(caster.getRid());
            Player targetPlayer = PlayerManager.getInstance().getPlayer(t.getRid());
            if (casterPlayer != null && targetPlayer != null && casterPlayer != targetPlayer) {
                targetPlayer.getAttackMeMap().put(casterPlayer.getId(), time);
            }
            ret.addEffect(effectRet);
        }

        SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
        if (skillConditionConfig == null) {
            return;
        }

        int[][] buffArrays = ParamAddUtil.calSkillConditionAllParams(caster.getSkillSpecEffectMap(), skillConditionConfig);
        if (buffArrays == null || buffArrays.length == 0) {
            return;
        }

        Map<Integer, Integer> probabilityMap = new HashMap<>();
        for (int[] ints : buffArrays) {
            if (ints.length != 2) {
                log.error("移除buff技能的skillCondition参数配置有问题->{}", skillConditionConfig.getId());
                continue;
            }
            int buffId = ints[0];
            int probability = ints[1];
            probabilityMap.put(buffId, probability);
        }

        for (int i = 0; i < ret.getEffectList().size(); i++) {
            FightResult.FightEffectRet effectRet = ret.getEffectList().get(i);
            Performer performer = effectRet.performer;
            Map<Integer, Buff> buffMap = performer.getBuffs().getBuffMap();
            for (Map.Entry<Integer, Buff> entry : buffMap.entrySet()) {
                Buff buff = entry.getValue();
                int probability = probabilityMap.getOrDefault(buff.getId(), 0);
                if (!RandomUtil.isGenerate(10000, probability)) {
                    continue;
                }
                buff.setExpire(time);
            }
        }

        ret.castSkillSuc = !ret.getEffectList().isEmpty();
    }
}
