package com.sh.game.map.listener;

import com.sh.game.common.entity.buff.BuffConst;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.event.EventType;
import com.sh.game.event.IListener;
import com.sh.game.map.Player;
import com.sh.game.map.buff.BuffTriggerUtil;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.servant.ServantActorManager;

/**
 * 角色属性变更监听
 *
 * <AUTHOR>
 * @since 2022/5/23 14:23
 */
public class AttributeChangeListener implements IListener<Player> {

    /**
     * 执行事件监听
     *
     * @param type
     * @param player    角色
     */
    @Override
    public void update(EventType type, Player player) {
        if (player != null) {
            PlayerActor actor = player.getActor();
            if (actor != null) {
                ServantActorManager.getInstance().updateServantFinalAttribute<PERSON><PERSON><PERSON>aster(actor);
                BuffTriggerUtil.triggerBuff(actor, actor, BuffConst.TriggerType.INIT, null, TimeUtil.getNowOfMills());
            }
        }

    }
}
