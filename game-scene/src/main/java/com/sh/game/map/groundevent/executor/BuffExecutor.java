package com.sh.game.map.groundevent.executor;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.BuffConfig;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.buff.BuffManager;
import com.sh.game.map.groundevent.Event;
import com.sh.game.map.groundevent.EventExecutor;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.GameMap;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/8/11 19:08
 */
@Slf4j
public class BuffExecutor extends EventExecutor {

    private int buffId;

    public BuffExecutor(int buffId) {
        super(BUFF);
        this.buffId = buffId;
    }

    @Override
    public void execute(GameMap map, Event event, PlayerActor player) {
        if (player.isDead()) {
            return;
        }
        BuffConfig config = ConfigDataManager.getInstance().getById(BuffConfig.class, buffId);
        if (config == null) {
            return;
        }
        BuffManager.getInstance().append(player, buffId, player, null, TimeUtil.getNowOfMills());
        log.info("地图buff事件触发，向玩家添加buff，玩家id {} name {} 地图 {} buffId {} name {}", player.getId(), player.getName(), map.getCfgId(), config.getId(), config.getName());
        addTriggerTimes(event);
    }
}
