package com.sh.game.map.fsm.monster.ai.event;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.MonsterAiConfig;
import com.sh.game.map.fsm.monster.ai.event.impl.*;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.GameMap;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class EventAIUtil {

    private static final Map<Integer, AbstractEventAI> EVENT_AI_TYPE_MAP = new HashMap<>();
    private static final Map<Integer, List<MonsterAiConfig>> CONFIG_MAP = new HashMap<>();

    static {
        initConfig();

        EVENT_AI_TYPE_MAP.put(0, new DefaultEventAI());
        EVENT_AI_TYPE_MAP.put(1, new PlayerInAttackArea());
        EVENT_AI_TYPE_MAP.put(2, new PlayerOutAttackArea());
        EVENT_AI_TYPE_MAP.put(3, new HpPercentLess());
        EVENT_AI_TYPE_MAP.put(4, new SelfDeadCheckCallerRelive());
        EVENT_AI_TYPE_MAP.put(5, new CheckTargetDead());
        EVENT_AI_TYPE_MAP.put(6, new CheckBeAttackSkillId());
        EVENT_AI_TYPE_MAP.put(7, new SelfInit());
        EVENT_AI_TYPE_MAP.put(8, new TargetDeadBeforeRelive());
        EVENT_AI_TYPE_MAP.put(9, new CheckOutFight());
        EVENT_AI_TYPE_MAP.put(10, new CastSkill());
        EVENT_AI_TYPE_MAP.put(11, new CheckHurtValue());
    }

    private static void initConfig() {
        List<MonsterAiConfig> monsterAiConfigs = ConfigDataManager.getInstance().getList(MonsterAiConfig.class);
        monsterAiConfigs.forEach(monsterAiConfig -> {
            if(monsterAiConfig.getCreate() == 0) {
                return;
            }
            List<MonsterAiConfig> configs = CONFIG_MAP.computeIfAbsent(monsterAiConfig.getMonsterid(), k -> new ArrayList<>());
            configs.add(monsterAiConfig);
        });
    }

    public static Map<Integer, List<MonsterAiConfig>> getConfigMap() {
        return CONFIG_MAP;
    }

    public static void initEventData(MonsterActor monster) {
        List<MonsterAiConfig> monsterAiConfigs = CONFIG_MAP.get(monster.getConfigId());
        if (monsterAiConfigs == null) {
            return;
        }

        EventData eventData = new EventData();
        for (MonsterAiConfig monsterAiConfig : monsterAiConfigs) {
            initCollection(monster, monsterAiConfig);

            Event event = new Event(eventData, monsterAiConfig.getId());
            eventData.getEventList().add(event);
        }

        monster.getMachine().getAiData().setEventData(eventData);
    }

    public static void initCollection(MonsterActor monster, MonsterAiConfig monsterAiConfig) {
        monster.getMachine().getAiData().setAttackSkillIdSet(monsterAiConfig.getCondtion() == 6 ? new HashSet<>() : null);
        monster.getMachine().getAiData().setCastSkillIdSet(monsterAiConfig.getCondtion() == 10 ? new HashSet<>() : null);
        monster.getMachine().getAiData().setHurtValueSet(monsterAiConfig.getCondtion() == 11 ? new HashSet<>() : null);
    }

    public static void checkEvents(GameMap map, MonsterActor monster, int dt) {
        EventData eventData = monster.getMachine().getAiData().getEventData();
        if (eventData == null) {
            return;
        }

        List<Event> removeList = null;
        for (Event event : eventData.getEventList()) {
            boolean needRemove = eventData.getBanSet().contains(event.getCfgId());
            MonsterAiConfig config = ConfigDataManager.getInstance().getById(MonsterAiConfig.class, event.getCfgId());
            if (!needRemove && config == null) {
                log.error("monster_ai配置id{}不存在", event.getCfgId());
                needRemove = true;
            }
            AbstractEventAI eventType = config == null ? null : EVENT_AI_TYPE_MAP.get(config.getCondtion());
            if (!needRemove && eventType == null) {
                log.error("monster_ai配置id{}的事件类型不存在", event.getCfgId());
                needRemove = true;
            }
            if (!needRemove && config.getTimes() > 0) {
                int curEffectTime = eventData.getEffectTimeMap().getOrDefault(config.getId(), 0);
                if (curEffectTime >= config.getTimes()) {
                    needRemove = true;
                }
            }
            if (needRemove) {
                if (removeList == null) {
                    removeList = new ArrayList<>();
                }
                removeList.add(event);
                continue;
            }

            eventType.check(event, map, monster, dt);
        }

        if (removeList != null) {
            removeList.forEach(event -> eventData.getEventList().remove(event));
        }
    }

}
