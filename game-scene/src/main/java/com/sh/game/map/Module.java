package com.sh.game.map;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.annotation.ConfigDataCustomization;
import com.sh.game.common.communication.msg.AllMessagePool;
import com.sh.game.common.config.check.ConfigLoadChecker;
import com.sh.game.common.msg.AutoRegisterRpcService;
import com.sh.game.common.util.ExecutorUtil;
import com.sh.game.common.util.IDUtil;
import com.sh.game.map.event.MapEventRegister;
import com.sh.game.map.module.*;
import com.sh.game.map.startup.MapHandlerPool;
import com.sh.game.map.startup.boots.MapContext;
import com.sh.game.map.startup.boots.maintain.JvmCloseHook;
import com.sh.game.map.startup.boots.maintain.MaintainNet;
import com.sh.game.notice.NoticeCenter;
import com.sh.game.notice.NoticePool;
import com.sh.game.option.ServerOption;
import com.sh.game.server.AbstractHandlerPool;
import com.sh.game.server.CommandRouter;
import com.sh.game.server.ModuleConst;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Slf4j
public class Module {


    public final static MsgTransformer MSG_TRANSFORMER;

    public final static NoticeTransformer NOTICE_TRANSFORMER;

    public final static NoticePool NOTICE_POOL;

    public final static AllMessagePool MSG_POOL;

    public final static AbstractHandlerPool MAP_HANDLER_POOL;

    public final static CommandRouter COMMAND_ROUTER;

    public final static NoticeCenter NOTICE_CENTER;

    public static SceneModuleNet NET;

    public final static MaintainNet MAINTAIN_NET;

    private static boolean local;

    private static boolean ready;

    static {
        MSG_TRANSFORMER = new MsgTransformer();

        NOTICE_TRANSFORMER = new NoticeTransformer();

        NOTICE_POOL = new NoticePool("com.sh.game.map.notice");

        MSG_POOL = AllMessagePool.newInstance(ModuleConst.MODULE_STR_TO_INT);

        MAP_HANDLER_POOL = new MapHandlerPool();

        COMMAND_ROUTER = new MapCommandRouter();

        NOTICE_CENTER = new NoticeCenter();

        MAINTAIN_NET = new MaintainNet();
    }

    public static void init(ServerOption option, boolean local) throws Exception {
        Module.local = local;
        MapContext.init(option);

        //1.初始化游戏环境，比如说配置文件，ID生成器
        loadEnv(option, local);


        //2.构建维护的模块
        buildMaintain(option.getBackServerPort());


        //3.启动所有定时任务
        startScheduleTask();

        AutoRegisterRpcService.initHandler(MAP_HANDLER_POOL,"com.sh.game.map");
        //4.创建网络
        buildNet(option.getGameServerPort());
        ready = true;
    }


    public static void loadEnv(ServerOption option, boolean local) throws Exception {
        if (!local) {
            //初始化ID工具类
            IDUtil.init(option.getServerId(), option.getPlatformId());

            //加载策划配置文件
            ConfigDataManager.getInstance().init(ConfigDataCustomization.newInstance().setPkg("com.sh.game.common.config").setSkipLine(2).setChecker(ConfigLoadChecker.getInstance()).setPath(option.getConfigDataPath()));
        }

        //注册事件
//        MapEventRegister.registerPreparedListeners();

        //初始化地图
//        SceneManager.getInstance().initMaps();
    }

    public static void buildNet(int port) {
        if (local) {
            NET = new LocalSceneModuleNet();
        } else {
            NET = new SceneModuleNet();
        }
        NET.setHostId(MapContext.getHostId());
        NET.setPort(port);
        NET.setModuleServerListener(new SceneModuleListener());
        SceneTransformReceiver transformReceiver = new SceneTransformReceiver();
        NET.setMessageReceiver(transformReceiver);
        NET.setNoticeReceiver(transformReceiver);
        NET.start(port);

        NET.setNoticeSign(NOTICE_POOL.getSign());
        log.info("RPC网络模块初始化完毕...");
    }

    public static void buildMaintain(int port) {
        if (local) {
            return;
        }
        MAINTAIN_NET.start(port);
        //关闭钩子
        Runtime.getRuntime().addShutdownHook(new JvmCloseHook());
        log.info("维护某块网络初始化完毕...");
    }


    public static void startScheduleTask() {
        //检查Player的状态，是否存活
        ExecutorUtil.scheduleWithFixedDelay(new PlayerAliveCheckTask(), 5000, 30 * 1000, TimeUnit.MILLISECONDS);
        log.info("定时事件初始化完毕...");
    }

    public static boolean isReady() {
        return ready;
    }


}
