package com.sh.game.map.fsm.xialv;

import com.sh.game.common.communication.msg.system.xialv.ResXiaLvDieTimeMessage;
import com.sh.game.common.communication.notice.scene.XiaLvDieStateNotice;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.Player;
import com.sh.game.map.fsm.AIData;
import com.sh.game.map.fsm.FSMState;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.obj.XiaLvActor;
import com.sh.game.map.player.PlayerManager;
import com.sh.game.map.scene.GameMap;
import com.sh.game.protos.XialvProtos;

public class XiaLvDieState extends FSMState<XiaLvActor> {

    public XiaLvDieState(int type, XiaLvActor performer) {
        super(type, performer);
    }

    @Override
    public void enter(GameMap map) {
        AIData aiData = performer.getMachine().getAiData();
        long lastDieTime = TimeUtil.getNowOfMills();
        aiData.setDieTime(lastDieTime);
        performer.setDeadTime(lastDieTime);

        dieNotify(lastDieTime);
    }

    private void dieNotify(long lastDieTime) {
        // 通知逻辑服保存死亡时间
        Performer master = performer.getMaster();
        if (master == null) {
            return;
        }
        Player player = PlayerManager.getInstance().getPlayer(master.getRid());
        if (player == null) {
            return;
        }
        PlayerActor actor = player.getActor();
        if (actor == null) {
            return;
        }

        XiaLvDieStateNotice notice = new XiaLvDieStateNotice();
        notice.setRid(master.getRid());
        notice.setLastDieTime(lastDieTime);
        actor.sendNotice(ProcessorId.SERVER_PLAYER, notice);

        //  通知客户端侠侣死亡时间
        ResXiaLvDieTimeMessage msg = new ResXiaLvDieTimeMessage();
        msg.setProto(XialvProtos.ResXiaLvDieTime.newBuilder()
                .setDieTimeStamp(lastDieTime)
                .build());
        Module.MSG_TRANSFORMER.sendMsg(msg, actor.getRid());

    }

    @Override
    public void update(GameMap map, int delta) {
        if (map.getObjectMap().containsKey(performer.getId())) {
            map.removeXiaLv(performer);
        }
    }

    @Override
    public int checkTransition(GameMap map, int dt) {
        if (!performer.isDead()) {
            return ACTIVE;
        }
        return DIE;
    }

    @Override
    public void exit(GameMap map) {

    }
}
