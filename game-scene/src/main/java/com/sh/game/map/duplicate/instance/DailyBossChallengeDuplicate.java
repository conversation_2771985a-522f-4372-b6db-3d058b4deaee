package com.sh.game.map.duplicate.instance;

import com.sh.game.common.communication.notice.PlayerExitNotice;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.map.MonsterHpDto;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.Module;
import com.sh.game.map.duplicate.Duplicate;
import com.sh.game.map.obj.MapObjectType;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.Topography;

import java.util.ArrayList;
import java.util.List;

public class DailyBossChallengeDuplicate extends Duplicate {

    List<MonsterHpDto> list;

    /**
     * 副本创建时间（毫秒）
     */
    long duplicateElapsedTime;

    @Override
    public void init(long key, int host, MapConfig config, Topography topography, Object param) {
        super.init(key, host, config, topography, param);

        Object[] objects = (Object[]) param;
        duplicateElapsedTime = (long) objects[1];
        list = (List<MonsterHpDto>) objects[0];

        // 副本未结束并且未完成挑战保留时间和怪物
        if (TimeUtil.getNowOfMills() - duplicateElapsedTime < this.totalTime * 1000L && duplicateElapsedTime != 0L) {
            if (!list.isEmpty()) {
                this.setCreateTime(duplicateElapsedTime);
                spawn.reset(0, list);
            }
        }

    }

    @Override
    public void exitDuplicate(PlayerActor playerActor) {
        PlayerExitNotice notice = new PlayerExitNotice();
        notice.setRid(playerActor.getRid());
        notice.setCfgId(cfgId);
        for (MonsterActor actor : monsterMap.values()) {
            if (actor.getType() == MapObjectType.MONSTER) {
                long hp = actor.getHp();
                if (hp < 0) hp = 0;
                MonsterHpDto dto = new MonsterHpDto(actor.getConfigId(), actor.getPoint().x, actor.getPoint().y,
                        hp, actor.getDir());
                notice.getHps().add(dto);
            }

        }
        notice.setCreateTime(this.getCreateTime());
        notice.addHost(playerActor.getHostId());
        Module.NOTICE_TRANSFORMER.sendNotice(ProcessorId.SERVER_PLAYER, notice, playerActor.getRid());
    }

    @Override
    public void onMonsterDie(MonsterActor monster) {
        super.onMonsterDie(monster);

        syncMonsterState();
    }

    @Override
    public void onClose() {
        super.onClose();

        List<MonsterActor> list = new ArrayList<>();
        list.addAll(monsterMap.values());
        for (MonsterActor monsterActor : list) {
            removeMonster(monsterActor);
        }
    }
}
