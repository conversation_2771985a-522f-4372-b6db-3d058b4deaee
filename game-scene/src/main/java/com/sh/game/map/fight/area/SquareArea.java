package com.sh.game.map.fight.area;

import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.util.GeomUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 正方形
 * <p>具体如下形状</p>
 * --------------<br>
 * -----AAA------<br>
 * -----APA------<br>
 * -----AAA------<br>
 * --------------<br>
 *
 * <AUTHOR>
 */
public class SquareArea extends AttackArea {

    @Override
    public List<Point> findAreaPoint(GameMap map, Point point, int areaDir, int[] areaDisInts) {
        List<Point> ret = new ArrayList<>();
        byte[] ints = GeomUtil.getPointRoundOffset(areaDisInts[0]);
        for (int i = 0; i < ints.length; i+=2) {
            Point p = map.getPoint(point.x + ints[i], point.y + ints[i+1]);
            if (p != null) {
                ret.add(p);
            }
        }
        return ret;
    }

}
