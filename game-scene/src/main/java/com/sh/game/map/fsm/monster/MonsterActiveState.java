package com.sh.game.map.fsm.monster;

import com.sh.game.map.fsm.FSMState;
import com.sh.game.map.fsm.monster.ai.AIFactory;
import com.sh.game.map.obj.MonsterActor;
import com.sh.game.map.scene.GameMap;

public class MonsterActiveState extends FSMState<MonsterActor> {

    public MonsterActiveState(int type, MonsterActor performer) {
        super(type, performer);
    }

    @Override
    public void enter(GameMap map) {
        AIFactory.getAI(performer.getAiType()).activeEnter(map, performer);
    }

    @Override
    public void exit(GameMap map) {
        AIFactory.getAI(performer.getAiType()).activeExit(map, performer);
    }

    @Override
    public void update(GameMap map, int delta) {
        AIFactory.getAI(performer.getAiType()).activeUpdate(map, performer, delta);
    }

    @Override
    public int checkTransition(GameMap map, int dt) {
        return AIFactory.getAI(performer.getAiType()).checkStateTransitionOnActive(map, performer);
    }

}
