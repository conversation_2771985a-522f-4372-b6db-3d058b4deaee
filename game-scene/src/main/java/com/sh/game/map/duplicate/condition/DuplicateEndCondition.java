package com.sh.game.map.duplicate.condition;

import com.sh.game.common.constant.MapConst;
import com.sh.game.protos.DuplicateProtos;
import lombok.Getter;

/**
 * 副本完成条件(失败或者成功)
 *
 * <AUTHOR>
 * 2017年6月18日 下午6:11:17
 */
@Getter
public abstract class DuplicateEndCondition {

    /**
     * 条件类型
     *
     * @see MapConst.DUPLICATE_CONDITION_TYPE
     */
    private int type;

    /**
     * 条件参数，该参数大部分都是读取第一个值，某些特殊的可能会有两个，比如说 坐标、杀怪顺序等等
     */
    protected int[] value;
    /**
     * 目标进度
     */
    protected int[] progress;


    public DuplicateEndCondition(int type, int[] value) {
        this.type = type;
        this.value = value;
    }

    public abstract boolean check();

    public abstract boolean update(Object obj);

    public DuplicateProtos.DuplicateGoal pack() {
        DuplicateProtos.DuplicateGoal.Builder goal = DuplicateProtos.DuplicateGoal.newBuilder();
        if (progress != null) {
            for (int v : progress) {
                goal.addParams(v);
            }
        }
        return goal.build();
    }

    public abstract void setCount(int count);
}
