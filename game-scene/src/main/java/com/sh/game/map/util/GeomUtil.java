package com.sh.game.map.util;

import com.google.common.primitives.Ints;
import com.sh.game.common.util.RandomUtil;
import com.sh.game.map.constant.Dir;
import com.sh.game.map.obj.IMapObject;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.scene.PointState;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
public class GeomUtil {

    /**
     * 最大距离
     */
    public final static int MAX_POINT_RANGE = 20;

    /**
     * 附近偏移的缓存
     */
    private static byte[][] POINT_ROUND_OFFSET = new byte[20][];
    /**
     * 方向向量
     */
    public static final byte[][] DIRECTION_VECTOR = new byte[][]{
            new byte[]{0, -1},    // 0 ↑
            new byte[]{1, -1},    // 1 ↗
            new byte[]{1, 0},    // 2 →
            new byte[]{1, 1},    // 3 ↘
            new byte[]{0, 1},    // 4 ↓
            new byte[]{-1, 1},    // 5 ↙
            new byte[]{-1, 0},    // 6 ←
            new byte[]{-1, -1}        // 7 ↖
    };

    /**
     * 获取方向向量
     *
     * @param dir 方向
     * @return 方向向量
     */
    public static final byte[] getVector(int dir) {
        return DIRECTION_VECTOR[dir & 7];
    }

    /**
     * 通过坐标向量获取方向
     *
     * @param vx 绝对值请确保为1
     * @param vy 绝对值请确保为1
     * @return 方向
     */
    public static final int getDirection(int vx, int vy) {
        int v = 0;
        for (byte[] vector : DIRECTION_VECTOR) {
            if (vector[0] == vx && vector[1] == vy) {
                return v;
            }
            v++;
        }
        //0，0返回随机方向
        return RandomUtil.random(0, 7);
    }

    /**
     * 通过两个坐标获取方向
     *
     * @param originPoint
     * @param targetPoint
     * @return 方向
     */
    public static final int getDirection(Point originPoint, Point targetPoint) {
        if (originPoint == null || targetPoint == null) {
            return 0;
        }
        int vx = targetPoint.x - originPoint.x;
        int vy = targetPoint.y - originPoint.y;
        if (vx != 0) vx /= Math.abs(vx);
        if (vy != 0) vy /= Math.abs(vy);
        return getDirection(vx, vy);
    }

    /**
     * 获取某个区域对于中心区域坐标的偏移（正方形），由近及远、顺时针方向查找和存储
     *
     * @param range 最远距离中心点的格子数, 0：当前点，1：9个点，2：25个点 ...
     * @return
     */
    public static byte[] getPointRoundOffset(int range) {
        if (range < 0) {
            return new byte[0];
        }
        range = Math.min(range, 100);
        if (range >= POINT_ROUND_OFFSET.length) { //数组超长，自动进行扩展
            byte[][] offset = new byte[range + 1][];
            System.arraycopy(POINT_ROUND_OFFSET, 0, offset, 0, POINT_ROUND_OFFSET.length);
            POINT_ROUND_OFFSET = offset;
        }

        byte[] rounds = POINT_ROUND_OFFSET[range];
        if (rounds == null) {
            rounds = new byte[2 * (range * 2 + 1) * (range * 2 + 1)];
            int index = 0;
            byte x = 0;
            byte y = 0;
            rounds[index++] = x;
            rounds[index++] = y;
            for (int r = 1; r <= range; r++) {
                for (int d = 0; d <= 4; d++) {
                    byte[] vector = getVector(d * 2);
                    while (vector[0] * x < r && vector[1] * y < r) {
                        x += vector[0];
                        y += vector[1];
                        rounds[index++] = x;
                        rounds[index++] = y;
                    }
                }
            }

            POINT_ROUND_OFFSET[range] = rounds;
        }

        return rounds;
    }

    public static Dir getDir(Point fromPoint, Point toPoint) {
        if (fromPoint == null || toPoint == null) {
            return Dir.NONE;
        }
        return getDir(fromPoint.getX(), fromPoint.getY(), toPoint.getX(), toPoint.getY());
    }

    public static Dir getDir(int fx, int fy, int tx, int ty) {
        int colDiff = tx - fx;
        int rowDiff = ty - fy;
        if (colDiff > 0) { //右
            if (rowDiff < 0) { //上
                return Dir.RIGHT_TOP;
            } else if (rowDiff > 0) {
                return Dir.RIGHT_BOTTOM;
            } else {
                return Dir.RIGHT;
            }
        } else if (colDiff < 0) { //左
            if (rowDiff < 0) { //上
                return Dir.LEFT_TOP;
            } else if (rowDiff > 0) {
                return Dir.LEFT_BOTTOM;
            } else {
                return Dir.LEFT;
            }
        } else {
            if (rowDiff < 0) {
                return Dir.TOP;
            } else if (rowDiff > 0) {
                return Dir.BOTTOM;
            } else {
                return Dir.NONE;
            }
        }
    }

    public static final int distance(Performer a, Performer b) {
        return distance(a.getPoint(), b.getPoint());
    }

    public static final int distance(Point a, Point b) {
        if (a == null || b == null)
            return Integer.MAX_VALUE;
        return distance(a.getX(), a.getY(), b.getX(), b.getY());
    }

    public static final int distance(int x1, int y1, int x2, int y2) {
        return Math.max(Math.abs(x1 - x2), Math.abs(y1 - y2));
    }

    /**
     * 获取曼哈顿距离
     *
     * @param pos
     * @return
     */
    public static final int getManhattanDistance(Point pos, Point end) {
        return Math.abs(pos.getX() - end.getX()) + Math.abs(pos.getY() - end.getY());
    }


    public static Point nextDirPoint(GameMap map, Point point, int dir) {
        if (point == null) {
            return null;
        }
        byte[] vector = getVector(dir);
        Point p = map.getPoint(point.x + vector[0], point.y + vector[1]);
        if (p == null) {
            return null;
        }

        PointState pointState = map.getPointState(p);
        if (pointState != null && pointState.isBlock()) {
            return null;
        }

        return p;
    }

    /**
     * 获取反方向的第distance个点
     *
     * @param map
     * @param point
     * @param dir
     * @param distance
     * @return
     */
    public static Point previousDirNumPoint(GameMap map, Point point, int dir, int distance) {
        if (point == null) {
            return null;
        }
        byte[] vector = getVector(dir);
        Point p = map.getPoint(point.x - distance * vector[0], point.y - distance * vector[1]);
        if (p == null) {
            return null;
        }

        PointState pointState = map.getPointState(p);
        if (pointState != null && pointState.isBlock()) {
            return null;
        }

        return p;
    }

    public static int[] countDirectionAddition(Dir dir) {
        int[] add = new int[2];
        switch (dir) {
            case TOP:
                add[1] = -1;
                break;
            case RIGHT_TOP:
                add[1] = -1;
                add[0] = 1;
                break;
            case RIGHT:
                add[0] = 1;
                break;
            case RIGHT_BOTTOM:
                add[1] = 1;
                add[0] = 1;
                break;
            case BOTTOM:
                add[1] = 1;
                break;
            case LEFT_BOTTOM:
                add[1] = 1;
                add[0] = -1;
                break;
            case LEFT:
                add[0] = -1;
                break;
            case LEFT_TOP:
                add[1] = -1;
                add[0] = -1;
                break;
            case NONE:
                add[1] = 0;
                add[0] = 0;
                break;
        }

        return add;
    }

    public static void changeDir(Performer caster, int dir, Point point) {
        if (point != null) {//计算转向
            int targetDir = GeomUtil.getDir(caster.getPoint(), point).getIndex();
            if (targetDir == Dir.NONE.getIndex()) {
                if (dir != Dir.NONE.getIndex()) {
                    caster.setDir(dir);
                }
            } else {
                if (targetDir != caster.getDir()) {
                    //LOGGER.error("修改朝向:old->{}，new->{}" ,caster.getDir() , dir );
                    caster.setDir(targetDir);
                }
            }
        } else {
            if (dir != Dir.NONE.getIndex()) {
                caster.setDir(dir);
            }
        }
    }

    /**
     * 获取目标范围内的IMapObject
     *
     * @param gameMap
     * @param point
     * @param range
     * @param types
     * @return
     */
    public static Collection<IMapObject> getObjectListByRangeAndType(GameMap gameMap, Point point, int range, int... types) {
        if (gameMap == null || point == null) {
            return Collections.EMPTY_SET;
        }
        Set<IMapObject> set = new HashSet<>(point.getObjectList(gameMap));
        byte[] offsets = getPointRoundOffset(range);
        for (int i = 0; i < offsets.length; i += 2) {
            Point p = gameMap.getPoint(point.getX() + offsets[i], point.getY() + offsets[i + 1]);
            if (p == null || p == point) {
                continue;
            }
            set.addAll(p.getObjectList(gameMap));
        }
        boolean all = types.length == 0;
        return set.stream()
                .filter(mapObject -> all || Ints.asList(types).contains(mapObject.getType()))
                .collect(Collectors.toList());
    }

    public static void main(String[] args) {
        byte[] rounds = getPointRoundOffset(5);
        // draw
        for (int i = 0; i < rounds.length; i++) {
            rounds[i] += 10;
        }

        int[][] area = new int[20][20];
        for (int i = 0; i < rounds.length; i += 2) {
            area[rounds[i + 1]][rounds[i]] = i / 2 + 1;
        }

        for (int i = 0; i < 20; i++) {
            String s = "";
            for (int j = 0; j < 20; j++) {
                s += String.format("%3d", area[i][j]);
            }
            System.out.println(s);
        }
    }

}
