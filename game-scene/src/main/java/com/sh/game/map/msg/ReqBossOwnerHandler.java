package com.sh.game.map.msg;

import com.sh.game.map.player.PlayerManager;
import com.sh.game.common.communication.msg.map.ReqBossOwnerMessage;
import com.sh.game.protos.MapProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>请求boss归属</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 09:11:10
 */
public class ReqBossOwnerHandler extends AbstractHandler<ReqBossOwnerMessage> {

    @Override
    public void doAction(ReqBossOwnerMessage msg) {
        MapProtos.ReqBossOwner bossOwner = msg.getProto();
        PlayerManager.getInstance().fetchBossOwnerInfo(msg.getSession().getId(), bossOwner.getBossId());
    }

}
