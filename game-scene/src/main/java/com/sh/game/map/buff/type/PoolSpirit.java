package com.sh.game.map.buff.type;

import com.sh.game.common.constant.FightConst;
import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.map.buff.BuffMessageUtil;
import com.sh.game.map.buff.BuffParamEnhanceUtil;
import com.sh.game.map.fight.FightUtil;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;

public class PoolSpirit extends AbsBuffEffect {

    @Override
    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
        //param: 0 store 1 reply
        buff.initParam(b -> {
            int store = 0;
            int reply = 0;

            if (oldBuff != null && !oldBuff.getParams().isEmpty()) {
                store += oldBuff.fetchParam(0);
            }

            int[][] extraParam = BuffParamEnhanceUtil.calExtraParam(caster, buff.config());
            for (int i = 0; i < extraParam.length; i++) {
                if (extraParam[i].length == 3 && (extraParam[i][0] == 0 || extraParam[i][0] == caster.getCareer())) {
                    reply = extraParam[i][1];
                    store += extraParam[i][2];
                    break;
                }
            }

            buff.setLoop(1000);
            if (buff.getClock() <= 0) {
                buff.setClock(TimeUtil.getNowOfMills());
            }

            buff.getParams().add(store);
            buff.getParams().add(reply);
        });
    }

    @Override
    public boolean canUpdate(Buff buff, long time) {
        if (buff.getClock() > time) {
            return false;
        }
        buff.setClock(buff.getClock() + buff.getLoop());
        return true;
    }

    @Override
    public void onUpdate(GameMap map, Performer performer, Buff buff, long time, int delta) {
        if (performer.isDead() || performer.getMp() >= performer.getFinalAttribute().findMaxMp()) {
            return;
        }

        int store = buff.fetchParam(0);
        int spirit = buff.fetchParam(1);
        //有问题
        if (spirit <= 0) {
            buff.getParams().set(0, 0);
            buff.setForever(false);
            buff.setInvalid(true);
            return;
        }

        FightUtil.spirit(spirit, performer);

        performer.sendHpMpChangeMessage(FightConst.HurtType.RECOVER_HP.type());

        store -= spirit;
        buff.getParams().set(0, store);
        if (store <= 0) {
            buff.setForever(false);
            buff.setInvalid(true);
        } else {
            BuffMessageUtil.sendUpdateMsg(performer, buff);
        }
    }
}
