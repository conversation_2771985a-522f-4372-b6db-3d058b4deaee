package com.sh.game.map.fight.area;

import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.util.GeomUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 前方N个格子
 * <p>如图</p>
 * --------------<br>
 * -----A--------<br>
 * -----A--------<br>
 * -----P------<br>
 * --------------<br>
 * --------------<br>
 *
 * <AUTHOR>
 */
public class FrontPointArea extends AttackArea {

    @Override
    public List<Point> findAreaPoint(GameMap map, Point point, int areaDir, int[] areaDisInts) {
        List<Point> ret = new ArrayList<>();
        byte[] vector = GeomUtil.getVector(areaDir);
        for (int d = 1; d <= areaDisInts[0]; d ++) {
            Point p = map.getPoint(point.x + vector[0] * d, point.y + vector[1] * d);
            if (p != null) {
                ret.add(p);
            } else {
                break;
            }
        }

        return ret;
    }

}
