package com.sh.game.map.fight.area;

import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.util.GeomUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * 三条斜线区域
 * <p>具体看图。</p>
 * -------------<br>
 * --A--A--A--<br>
 * ---A-A-A---<br>
 * -----A------<br>
 * -----P------<br>
 *
 * <AUTHOR>
 * @date 2022/08/25 16:59
 */
public class ObliqueLineThreeArea extends AttackArea {

    @Override
    public List<Point> findAreaPoint(GameMap map, Point point, int areaDir, int[] areaDisInts) {
        List<Point> ret = new ArrayList<>();
        int areaDis = 3;
        if (areaDisInts != null && areaDisInts.length > 0) {
            areaDis = areaDisInts[0];
        }
        for (int dir = -1; dir <= 1; dir++) {
            byte[] vector = GeomUtil.getVector(areaDir + dir);
            for (int dis = 0; dis <= areaDis; dis++)  {
                Point p = map.getPoint(point.getX() + vector[0] * dis, point.getY() + vector[1] * dis);
                if (p != null) {
                    ret.add(p);
                }
            }
        }
        return ret;
    }
}
