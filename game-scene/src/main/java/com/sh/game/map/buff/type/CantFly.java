package com.sh.game.map.buff.type;

import com.sh.game.common.entity.buff.Buff;
import com.sh.game.common.entity.buff.BuffState;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.obj.MapObjectType;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.obj.PlayerActor;

public class CantFly extends AbsBuffEffect {

    @Override
    public void onAppend(Performer performer, Buff buff, int stack, Performer caster, FightResult.BuffImmediateEffect effect, Buff oldBuff) {
        performer.getBuffState().increase(BuffState.State.CANT_FLY, stack);
        if (performer.getType() == MapObjectType.PLAYER) {
            ((PlayerActor) performer).addBuff(buff.getId());
        }
    }

    @Override
    public void onRemove(Performer performer, Buff buff, int stack) {
        performer.getBuffState().decrease(BuffState.State.CANT_FLY, stack);
        if (performer.getType() == MapObjectType.PLAYER) {
            ((PlayerActor) performer).removeBuff(buff.getId());
        }
    }
}
