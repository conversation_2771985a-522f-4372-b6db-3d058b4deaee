package com.sh.game.map.duplicate.condition;

import com.sh.game.map.entity.DuplicateSpawn;

/**
 * <AUTHOR> qq:285197243
 * @date 2021/9/24
 */
public class DuplicateKillAllMonsterCondition extends DuplicateEndCondition {
    public DuplicateKillAllMonsterCondition(int type, int[] value) {
        super(type, value);
        progress = new int[6];
    }

    @Override
    public boolean check() {
        return progress[1] >= progress[0];
    }

    @Override
    public boolean update(Object obj) {
        DuplicateSpawn spawn = (DuplicateSpawn)obj;
        progress[0] = spawn.getMonsterTotal();
        progress[1] = spawn.getMonsterKillings();
        progress[2] = spawn.getSpawns().size();
        progress[3] = spawn.getWaveCur();
        progress[4] = spawn.getWaveTotal();
        progress[5] = spawn.getWaveKillings();
        return true;
    }

    @Override
    public void setCount(int count) {
        progress[1] = count;
    }
}
