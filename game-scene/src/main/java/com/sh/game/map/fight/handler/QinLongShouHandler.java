package com.sh.game.map.fight.handler;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.map.fight.ResSkillPosChangeMessage;
import com.sh.game.common.config.model.SkillConditionConfig;
import com.sh.game.common.constant.Symbol;
import com.sh.game.common.entity.skill.Skill;
import com.sh.game.common.util.ParamAddUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.event.EventUtil;
import com.sh.game.map.Module;
import com.sh.game.map.event.MapEventType;
import com.sh.game.map.fight.effect.SkillEffect;
import com.sh.game.map.fight.effect.impl.HurtEffect;
import com.sh.game.map.fight.entity.FightResult;
import com.sh.game.map.listener.SkillCastEvent;
import com.sh.game.map.move.MoveManager;
import com.sh.game.map.obj.Performer;
import com.sh.game.map.scene.GameMap;
import com.sh.game.map.scene.Point;
import com.sh.game.map.util.GeomUtil;
import com.sh.game.protos.FightProtos;

import java.util.List;

public class QinLongShouHandler extends AbstractHandler {

    @Override
    public SkillEffect getEffect() {
        return new HurtEffect(this);
    }

    @Override
    public void cast(FightResult ret, Skill skill, GameMap map, Performer caster, List<Performer> targetList, Point targetPoint) {
        SkillConditionConfig skillConditionConfig = ConfigDataManager.getInstance().getById(SkillConditionConfig.class, skill.getSkillId() + Symbol.JINHAO + skill.getLevel());
        if (skillConditionConfig == null) {
            return;
        }

        int maxDis = ParamAddUtil.calSkillConditionParam(caster.getSkillSpecEffectMap(), skillConditionConfig, 0, 0);
        int preTime = ParamAddUtil.calSkillConditionParam(caster.getSkillSpecEffectMap(), skillConditionConfig, 0, 1);
        int delayTime = ParamAddUtil.calSkillConditionParam(caster.getSkillSpecEffectMap(), skillConditionConfig, 0, 2);

        Point point = caster.getPoint();
        Performer obj = null;
        for (Performer t : targetList) {
//            if (t.getType() != MapObjectType.MONSTER) {
//                continue;
//            }

//            if (t.getLevel() > caster.getLevel()) {
//                continue;
//            }

            if (!canMove(map, caster, t)) {
                continue;
            }

            int dis = GeomUtil.distance(t.getPoint(), caster.getPoint());
            if (dis > maxDis) {
                continue;
            }

            int dir = GeomUtil.getDirection(point, t.getPoint());
            Point tPoint = null;
            Point mPoint = null;
            for (int i = 0; i < 8; i ++) {
                mPoint = GeomUtil.nextDirPoint(map, point, dir + i);
                if (mPoint == null) {
                    continue;
                }
                if (!mPoint.canStand(map, t)) {
                    continue;
                }
                tPoint = mPoint;
                break;
            }

            if (tPoint == null) {
                continue;
            }

            obj = t;
            MoveManager.getInstance().playerMove(t, tPoint, false);
            ResSkillPosChangeMessage res = new ResSkillPosChangeMessage();
            FightProtos.ResSkillPosChange.Builder skillPosChange = FightProtos.ResSkillPosChange.newBuilder();
            skillPosChange.setTargetId(obj.getId());
            skillPosChange.setDir(obj.getDir());
            skillPosChange.setX(obj.getPoint().x);
            skillPosChange.setY(obj.getPoint().y);
            skillPosChange.setSkillId(skill.getSkillId());
            skillPosChange.setSkillLevel(skill.getLevel());
            skillPosChange.setCasterId(caster.getId());
            skillPosChange.setTime(preTime * dis);
            res.setProto(skillPosChange.build());
            Module.MSG_TRANSFORMER.sendRoundMessage(res, caster);
            break;
        }

        if (obj != null) {
            FightResult.FightEffectRet effectRet = effect(skill, caster, obj, ret);
            ret.addEffect(effectRet);
            ret.castSkillSuc = true;
            EventUtil.fireEvent(MapEventType.ON_SKILL_CAST, new SkillCastEvent(caster, obj, skill, ret.buffEffect, TimeUtil.getNowOfMills()));
        }
    }

    //    private boolean canMove(Performer performer) {
//        if (performer.getBuffState().inStateOf(BuffState.State.AVOID_REPEL)) {
//            return false;
//        }
//
//        if (performer.getType() == MapObjectType.MONSTER) {
//            MonsterActor monster = (MonsterActor) performer;
//            return monster.isCanRepel();
//        }
//
//        return true;
//    }
}
