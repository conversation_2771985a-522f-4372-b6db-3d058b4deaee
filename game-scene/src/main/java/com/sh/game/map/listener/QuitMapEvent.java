package com.sh.game.map.listener;

import com.sh.game.map.obj.PlayerActor;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/2/28
 * @Desc : to do anything
 */
@Getter
@Setter
public class QuitMapEvent {

    public static final byte OFFLINE = 1;

    public static final byte CHANGE_MAP = 2;

    PlayerActor actor;
    byte reason;

    public QuitMapEvent(PlayerActor actor, byte reason) {
        this.actor = actor;
        this.reason = reason;
    }
}
