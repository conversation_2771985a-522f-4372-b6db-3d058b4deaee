package com.sh.game.map.duplicate.instance;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.tuple.ThreeTuple;
import com.sh.game.common.config.model.ChangAnMiJIngConfig;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.map.duplicate.Duplicate;
import com.sh.game.map.obj.GroundItem;
import com.sh.game.map.obj.MapObjectFactory;
import com.sh.game.map.obj.PlayerActor;
import com.sh.game.map.scene.Point;
import com.sh.game.map.scene.Topography;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/7/5 15:20
 */
@Slf4j
public class ChangAnMiJingDuplicate extends Duplicate {

    /**
     * 地图掉落物
     * key: ChangAnMiJIngConfig表id
     * value：地图道具唯一id
     */
    private Map<Integer, Long> groundItemIdBycfgMap = new ConcurrentHashMap<>();


    @Override
    public void init(long key, int host, MapConfig config, Topography topography, Object param) {
        super.init(key, host, config, topography, param);
        if (param == null) {
            return;
        }

        Object[] objects = (Object[]) param;
        List<ThreeTuple<Integer, Integer, Integer>> list = (List<ThreeTuple<Integer, Integer, Integer>>) objects[0];
        for (ThreeTuple<Integer, Integer, Integer> tuple : list) {
            GroundItem groundItem = spawnGroundItem(tuple.getFirst(), tuple.getSecond(), tuple.getThird());
            if (groundItem == null) {
                continue;
            }
            groundItemIdBycfgMap.put(tuple.getFirst(), groundItem.getId());
        }

        log.info("长安秘境-创建地图#地图id:{},掉落物:{}", this.getId(), list);
    }

    /**
     * 更新地面掉落物
     *
     * @param groundItems      掉落物
     * @param groundItemCounts 掉落物数量
     */
    public void updateGroundItem(Map<Integer, Integer> groundItems, Map<Integer, Integer> groundItemCounts) {
        List<Integer> removeCfgId = new ArrayList<>();
        for (Map.Entry<Integer, Long> entry : groundItemIdBycfgMap.entrySet()) {
            int cfgId = entry.getKey();
            long uid = entry.getValue();
            if (groundItems.containsKey(cfgId)) {
                continue;
            }
            GroundItem groundItem = itemMap.get(uid);
            if (groundItem != null) {
                removeItem(groundItem);
            }
            // 移除
            removeCfgId.add(cfgId);
        }
        for (int rmCid : removeCfgId) {
            groundItemIdBycfgMap.remove(rmCid);
        }
        for (Map.Entry<Integer, Integer> entry : groundItems.entrySet()) {
            int cfgId = entry.getKey();
            int itemCfgId = entry.getValue();
            if (groundItemIdBycfgMap.containsKey(cfgId) || cfgId <= 0) {
                continue;
            }
            // 生成
            GroundItem groundItem = spawnGroundItem(cfgId, itemCfgId, groundItemCounts.getOrDefault(cfgId, 0));
            if (groundItem == null) {
                continue;
            }
            groundItemIdBycfgMap.put(cfgId, groundItem.getId());
        }
    }

    private GroundItem spawnGroundItem(int cfgId, int itemCid, int count) {
        // 生成
        ChangAnMiJIngConfig config = ConfigDataManager.getInstance().getById(ChangAnMiJIngConfig.class, cfgId);
        if (config == null) {
            log.warn("长安秘境-创建地图道具失败#配置不存在，配置id {} 道具id {} 地图id {} 地图唯一id {}",
                    cfgId, itemCid, getCfgId(), getId());
            return null;
        }
        int[] pos = config.getPos();
        if (pos == null || pos.length < 2) {
            log.error("长安秘境，创建地图道具，配置坐标错误，配置id {} 地图id {} 地图唯一id {}", config.getId(), getCfgId(), getId());
            return null;
        }

        int x = pos[0];
        int y = pos[1];
        Point point = getPoint(x, y);
        if (point == null) {
            log.error("长安秘境，创建地图道具，配置坐标拿不到，配置id {} 地图id {} 地图唯一id {}", config.getId(), getCfgId(), getId());
            return null;
        }
        if (count <= 0) {
            return null;
        }
        GroundItem groundItem = MapObjectFactory.createItem(itemCid, count, 0, null, 0, Integer.MAX_VALUE);
        if (groundItem == null) {
            log.warn("长安秘境-创建地图道具失败#配置id {} 道具id {} 地图id {} 地图唯一id {}",
                    cfgId, itemCid, getCfgId(), getId());
        } else {
            enterItem(groundItem, point);
        }
        return groundItem;
    }

    /**
     * 该副本不可拾取，掉落仅做展示
     *
     * @param player
     * @param groundItem
     * @return
     */
    @Override
    public Boolean onPickUp(PlayerActor player, List<GroundItem> groundItem) {
        return false;
    }

}
