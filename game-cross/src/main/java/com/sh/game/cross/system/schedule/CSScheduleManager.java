package com.sh.game.cross.system.schedule;

import com.sh.game.common.util.ExecutorUtil;
import com.sh.game.common.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Calendar;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/8/11
 * @Desc : to do anything
 */
@Slf4j
public class CSScheduleManager {
    private static CSScheduleManager ourInstance = new CSScheduleManager();

    public static CSScheduleManager getInstance() {
        return ourInstance;
    }

    private CSScheduleManager() {
    }

    public void start() {

        Calendar c = Calendar.getInstance();
        int second = c.get(Calendar.SECOND);

        int millisecond = c.get(Calendar.MILLISECOND);

        int minute = c.get(Calendar.MINUTE);

        int hour = c.get(Calendar.HOUR_OF_DAY);

        long dayDelay = TimeUtil.ONE_DAY_IN_MILLISECONDS - (hour * 60 * 60 * 1000 + minute * 60 * 1000 + second * 1000 + millisecond);

        log.info("距离零点剩余：【{}】MS", dayDelay);
        long minuteDelay = TimeUtil.ONE_MINUTE_IN_MILLISECONDS - (second * 1000 + millisecond);

        //距离整分剩余
        log.info("距离整分剩余：【{}】MS", minuteDelay);

        //距离整秒剩余
        long secondDelay = 1000 - millisecond;

        log.info("距离整秒剩余：【{}】MS", secondDelay);

        long hourDelay = TimeUtil.ONE_HOUR_IN_MILLISECONDS - (minute * 60 * 1000 + second * 1000 + millisecond);

        log.info("距离整点剩余：【{}】MS", hourDelay);

        // 服务器心跳/秒 (包含了零点事件)
        ExecutorUtil.scheduleAtFixedRate(new CrossServerHeartTask(), secondDelay, 1000);
    }
}
