package com.sh.game.cross.system.element.msg;

import com.sh.game.common.communication.msg.map.element.ReqPRCElementRankMessage;
import com.sh.game.cross.system.element.CrossERManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求元素副本排行信息</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-03-11 11:10:01
 */
public class ReqPRCElementRankHandler extends AbstractHandler<ReqPRCElementRankMessage> {

    @Override
    public void doAction(ReqPRCElementRankMessage msg) {
        CrossERManager.getInstance().reqPRCElementRank(msg.getSession(), msg.getRoleId());
    }

}
