package com.sh.game.cross.system.element;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.map.remote.ResPRCElementRankEndAwardMessage;
import com.sh.game.common.communication.msg.map.remote.ResPRCElementRankMessage;
import com.sh.game.common.communication.msg.map.remote.bean.ElementRankBean;
import com.sh.game.common.config.model.GlobalConfig;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.Symbol;
import com.sh.game.map.module.client.RemoteHostManager;
import com.sh.game.map.startup.RemoteHost;
import com.sh.game.common.util.TimeUtil;
import com.sh.server.Session;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/3/11
 * @Desc : to do anything
 */
@Slf4j
public class CrossERManager {
    private static CrossERManager ourInstance = new CrossERManager();

    public static CrossERManager getInstance() {
        return ourInstance;
    }

    private final int SHOW_RANK_LIMIT = 20;

    private final int RANK_LIMIT = 1000;

    private final static Map<Long, ElementPlayer> rankMap = Maps.newHashMap();

    private final static Set<Long> playerIds = Sets.newHashSet();

    private static List<ElementPlayer> rankList = Lists.newArrayList();

    private Pair<Integer, Integer> rankRewardDate;

    private int endMinutes;

    private CrossERManager() {
    }

    public void init() {
        playerIds.addAll(ERPersistManager.getInstance().queryPlayerIdList());

        List<ElementPlayer> elementPlayers = ERPersistManager.getInstance().queryList(RANK_LIMIT);
        for (ElementPlayer elementPlayer : elementPlayers) {
            rankMap.put(elementPlayer.getId(), elementPlayer);
        }
        rankList.addAll(elementPlayers);

        GlobalConfig timeConfig = ConfigDataManager.getInstance().getById(GlobalConfig.class, GameConst.GlobalId.ELEMENT_SETTLE_TIME);
        String[] split = timeConfig.getValue().split(Symbol.JINHAO);
        int beginMinutes = Integer.parseInt(split[0]);
        rankRewardDate = new Pair<>(7, (int) (beginMinutes + TimeUtil.ONE_HOUR_IN_MINUTES * 0.5));
        endMinutes = Integer.parseInt(split[1]);
    }

    public void reqUpdateElementRankData(long roleId, String roleName, int score) {
        ElementPlayer elementPlayer = rankMap.computeIfAbsent(roleId, k -> new ElementPlayer());
        elementPlayer.setId(roleId);
        elementPlayer.setName(roleName);
        elementPlayer.setScore(score);
        elementPlayer.setUpdateTime(TimeUtil.getNowOfSeconds());

        dealRank();
        if (playerIds.contains(roleId)) {
            ERPersistManager.getInstance().update(new ElementUpdateThread.Update(roleId, ElementPlayer.UPDATE));
        } else {
            playerIds.add(roleId);
            ERPersistManager.getInstance().update(new ElementUpdateThread.Update(roleId, ElementPlayer.INSERT));
        }
        log.info("元素副本数据更新 玩家：{} {} obtainScore:{} totalScore:{}", roleId, roleName, score, elementPlayer.getScore());
    }

    private void dealRank() {
        rankList.clear();
        rankList.addAll(rankMap.values());
        rankList.sort((o1, o2) -> {
            if (o1.getScore() < o2.getScore()) {
                return 1;
            } else if (o1.getScore() == o2.getScore()) {
                return Integer.compare(o1.getUpdateTime(), o2.getUpdateTime());
            } else {
                return -1;
            }
        });
        int size = rankList.size();
        for (int index = size - 1; index > RANK_LIMIT - 1; index--) {
            ElementPlayer removePlayer = rankList.remove(index);
            rankMap.remove(removePlayer.getId());
        }
    }

    public void reqPRCElementRank(Session session, long roleId) {
        ResPRCElementRankMessage msg = new ResPRCElementRankMessage();
        msg.setRoleId(roleId);
        List<ElementRankBean> list = msg.getRankList();
        int rank = 1;
        boolean hasMyRank = false;
        for (ElementPlayer player : rankList) {
            if (rank <= SHOW_RANK_LIMIT) {
                list.add(toBean(player, rank));
            }

            if (roleId == player.getId()) {
                msg.setMyRank(rank);
                hasMyRank = true;
            }
            rank++;

            if (rank > SHOW_RANK_LIMIT && hasMyRank) {
                break;
            }
        }
        session.sendMessage(msg);
    }

    private ElementRankBean toBean(ElementPlayer player, int rank) {
        ElementRankBean bean = new ElementRankBean();
        bean.setRoleId(player.getId());
        bean.setRoleName(player.getName());
        bean.setScore(player.getScore());
        bean.setRank(rank);
        return bean;
    }

    public ElementPlayer getPlayer(long roleId) {
        return rankMap.get(roleId);
    }

    public boolean rankSettleTime() {
        int week = rankRewardDate.getKey();
        int rewardMinutes = rankRewardDate.getValue();
        LocalDateTime time = LocalDateTime.now();
        int todayWeek = time.getDayOfWeek().getValue();
        int todayOfMinutes = TimeUtil.getTodayOfMinute();
        return todayWeek == week && todayOfMinutes == rewardMinutes;
    }

/*    public boolean clearRankTime() {
        int week = rankRewardDate.getKey();
        LocalDateTime time = LocalDateTime.now();
        int todayWeek = time.getDayOfWeek().getValue();
        int todayOfMinutes = TimeUtil.getTodayOfMinute();
        int clearMinutes = endMinutes - 1;
        return todayWeek == week && todayOfMinutes == clearMinutes;
    }*/

    public void elementRankEndAward() {
        log.info("元素副本积分排行结算开始......");
        int rank = 1;
        int count = 0;
        List<ElementRankBean> list = Lists.newArrayList();
        for (ElementPlayer elementPlayer : rankList) {
            list.add(toBean(elementPlayer, rank));
            count++;
            if (count >= 200) {
                ResPRCElementRankEndAwardMessage msg = new ResPRCElementRankEndAwardMessage();
                msg.getRankList().addAll(list);
                for (RemoteHost host : RemoteHostManager.getInstance().getAddressMap(GameConst.RemoteType.CROSS).values()) {
                    host.getClient().sendMsg(msg);
                }
                list.clear();
                count = 0;
            }
            log.info("元素副本积分排行发奖 -> rank:{}, role:{} {}, score:{}, updateTime:{}", rank, elementPlayer.getId(), elementPlayer.getName(), elementPlayer.getScore(), elementPlayer.getUpdateTime());
            rank++;
        }

        if (count > 0) {
            ResPRCElementRankEndAwardMessage msg = new ResPRCElementRankEndAwardMessage();
            msg.getRankList().addAll(list);
            for (RemoteHost host : RemoteHostManager.getInstance().getAddressMap(GameConst.RemoteType.CROSS).values()) {
                host.getClient().sendMsg(msg);
            }
        }
        log.info("元素副本积分排行结算结束......");

        clearRankData();
    }

    public void clearRankData() {
        log.info("清空元素副本积分排行数据......");
        rankMap.clear();
        rankList.clear();
        playerIds.clear();
        ERPersistManager.getInstance().update(new ElementUpdateThread.Update(0, ElementPlayer.TRUNCATE));
    }
}
