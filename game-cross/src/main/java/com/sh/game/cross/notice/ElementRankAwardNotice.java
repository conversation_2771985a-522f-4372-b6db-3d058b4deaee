package com.sh.game.cross.notice;

import com.sh.common.jdbc.SerializerUtil;
import com.sh.game.common.communication.notice.ProcessNotice;
import com.sh.game.common.constant.NoticeConstant;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/3/12
 * @Desc : to do anything
 */
public class ElementRankAwardNotice extends ProcessNotice {
    @Override
    public int id() {
        return NoticeConstant.NoticeId.ElementRankAwardNotice;
    }

    @Override
    public byte[] encode() {
        return SerializerUtil.encode(this, ElementRankAwardNotice.class);
    }

    @Override
    public void decode(byte[] bytes) {
        SerializerUtil.decode(bytes, ElementRankAwardNotice.class, this);
    }
}
