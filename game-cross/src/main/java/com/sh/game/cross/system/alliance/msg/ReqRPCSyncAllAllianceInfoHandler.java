package com.sh.game.cross.system.alliance.msg;

import com.sh.game.common.communication.msg.system.alliance.ReqRPCSyncAllAllianceInfoMessage;
import com.sh.game.cross.system.alliance.CrossAllianceManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求同步所有联盟信息</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-12-16 17:36:38
 */
public class ReqRPCSyncAllAllianceInfoHandler extends AbstractHandler<ReqRPCSyncAllAllianceInfoMessage> {

    @Override
    public void doAction(ReqRPCSyncAllAllianceInfoMessage msg) {
        CrossAllianceManager.getInstance().syncGameAllianceInfos(msg.getHostId(), msg.getOperate(), msg.getAlliancesList(), msg.getHasAllianceUnion(), msg.getNoHasAllianceUnion());
    }

}
