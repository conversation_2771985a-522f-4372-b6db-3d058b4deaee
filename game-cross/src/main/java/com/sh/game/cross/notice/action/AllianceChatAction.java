package com.sh.game.cross.notice.action;

import com.sh.game.common.communication.notice.NoticeAction;
import com.sh.game.common.communication.notice.map.AllianceChatNotice;
import com.sh.game.cross.system.alliance.CrossAllianceManager;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2020/2/22
 * @Desc : to do anything
 */
public class AllianceChatAction  extends NoticeAction<AllianceChatNotice> {
    @Override
    public void doAction(AllianceChatNotice notice) {
        CrossAllianceManager.getInstance().allianceChat(notice.getAllianceId(), notice.getHasSendUnionId(), notice.getType(), notice.getContent(), notice.getSourceBean());
    }
}
