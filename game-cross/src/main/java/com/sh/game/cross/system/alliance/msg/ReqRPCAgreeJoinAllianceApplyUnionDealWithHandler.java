package com.sh.game.cross.system.alliance.msg;

import com.sh.game.common.communication.msg.system.alliance.ReqRPCAgreeJoinAllianceApplyUnionDealWithMessage;
import com.sh.game.cross.system.alliance.CrossAllianceManager;
import com.sh.server.AbstractHandler;

/**
 * <p>申请帮会联盟盟主同意后处理结果返回</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-12-17 13:59:53
 */
public class ReqRPCAgreeJoinAllianceApplyUnionDealWithHandler extends AbstractHandler<ReqRPCAgreeJoinAllianceApplyUnionDealWithMessage> {

    @Override
    public void doAction(ReqRPCAgreeJoinAllianceApplyUnionDealWithMessage msg) {
        CrossAllianceManager.getInstance().reqRPCAgreeJoinAllianceApplyUnionDealWith(msg.getSession(), msg.getRet(), msg.getTips(), msg.getAllianceId(), msg.getUnionInfoBean());
    }

}
