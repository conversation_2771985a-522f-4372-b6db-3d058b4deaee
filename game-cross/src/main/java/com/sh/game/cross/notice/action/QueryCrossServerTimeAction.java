package com.sh.game.cross.notice.action;

import com.sh.game.common.communication.msg.map.remote.ResQueryCrossServerTimeMessage;
import com.sh.game.common.communication.notice.NoticeAction;
import com.sh.game.common.communication.notice.map.QueryCrossServerTimeNotice;
import com.sh.game.map.startup.HostManager;
import com.sh.game.map.startup.RemoteHost;
import com.sh.game.common.util.TimeUtil;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/6/16
 * @Desc : to do anything
 */
public class QueryCrossServerTimeAction extends NoticeAction<QueryCrossServerTimeNotice> {
    @Override
    public void doAction(QueryCrossServerTimeNotice notice) {
        ResQueryCrossServerTimeMessage msg = new ResQueryCrossServerTimeMessage();
        msg.setRid(notice.getRid());
        msg.setTimeStr(TimeUtil.timeFormat(TimeUtil.getNowOfMills(), TimeUtil.DEFAULT_FORMAT));
        for (RemoteHost host : HostManager.getInstance().getHostMap(0).values()) {
            host.getClient().sendMsg(msg);
        }
    }
}
