package com.sh.game.pay;

import java.util.concurrent.TimeUnit;

import org.apache.http.conn.HttpClientConnectionManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class HttpIdleConnectionMonitorTask implements Runnable{
	
	private static final Logger LOGGER = LoggerFactory.getLogger(HttpIdleConnectionMonitorTask.class);
	
	private final HttpClientConnectionManager connMgr;

	public HttpIdleConnectionMonitorTask(HttpClientConnectionManager connMgr) {
		super();
		this.connMgr = connMgr;
	}

	@Override
	public void run() {
		try {
			// 关闭失效的连接
			connMgr.closeExpiredConnections();
			// 关闭超过空闲30秒的连接
			connMgr.closeIdleConnections(30, TimeUnit.SECONDS);
		} catch (Exception e) {
			LOGGER.error("关闭Http连接失效线程失败", e);
		}
	}
}
