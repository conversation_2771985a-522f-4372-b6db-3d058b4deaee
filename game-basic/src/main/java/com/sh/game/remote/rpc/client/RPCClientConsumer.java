package com.sh.game.remote.rpc.client;

import com.sh.game.remote.rpc.server.ModuleCommandRouter;
import com.sh.game.server.AbstractHandlerPool;
import com.sh.game.server.AttributeUtil;
import com.sh.game.server.ChannelAttrKey;
import com.sh.game.server.CommandRouter;
import com.sh.net.Message;
import com.sh.net.NetworkConsumer;
import com.sh.server.AbstractHandler;
import com.sh.server.AbstractMessage;
import com.sh.server.Session;
import io.netty.channel.Channel;

/**
 * RPCClientConsumer
 *
 * <AUTHOR>
 * @date 2020/8/27 14:09
 */
public class RPCClientConsumer implements NetworkConsumer {

    private AbstractHandlerPool handlerPool;

    private CommandRouter commandRouter = new ModuleCommandRouter();


    public RPCClientConsumer(ModuleClient moduleClient) {
        ClientMessageHandler clientMessageHandler =  new ClientMessageHandler(moduleClient);
        this.handlerPool = new ClientHandlerPool(clientMessageHandler);
    }

    @Override
    public void consume(Channel channel, Message msg) {
        Session session = AttributeUtil.get(channel, ChannelAttrKey.SESSION);
        if (session == null) {
            return;
        }
        AbstractMessage absMsg = (AbstractMessage) msg;
        absMsg.setSession(session);

        AbstractHandler<? extends AbstractMessage> handler = handlerPool.getHandler(msg.getId());

        handler.setMsg(msg);

        byte processorId = handlerPool.getProcessorId(msg.getId());

        //将消息分发到指定的队列中，该队列有可能在同一个进程，也有可能不在同一个进程

        commandRouter.process(processorId, handler);
    }
}
