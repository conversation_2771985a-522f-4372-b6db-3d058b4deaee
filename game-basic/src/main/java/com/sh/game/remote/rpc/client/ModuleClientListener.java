package com.sh.game.remote.rpc.client;

import com.sh.game.remote.rpc.RPCConnection;

/**
 * AfterClientStartedListener
 *
 * <AUTHOR>
 * @date 2020/8/27 11:30
 */
public interface ModuleClientListener {

    void afterLogin(ModuleClient client, RPCConnection connection);

    void serverUnavailable(ModuleClient client, RPCConnection connection);

    void serverDestroy(ModuleClient client, RPCConnection connection);
}
