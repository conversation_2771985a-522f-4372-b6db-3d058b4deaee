package com.sh.game.server;

import com.sh.net.Message;
import com.sh.net.NetworkConsumer;
import com.sh.server.AbstractHandler;
import com.sh.server.AbstractMessage;
import com.sh.server.Session;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;

/**
 * 网络消息消费者
 * <AUTHOR>
 * @date 2018/12/14 17:43
 */
@Slf4j
public class MessageConsumer implements NetworkConsumer {

    /**
     * 消息池
     */
    private AbstractHandlerPool handlerPool;

    /**
     * 命令路由
     */
    private final CommandRouter router;

    public MessageConsumer(CommandRouter router, AbstractHandlerPool handlerPool) {
        this.router = router;
        this.handlerPool = handlerPool;
    }

    @Override
    public void consume(Channel channel, Message msg) {

        //这个channel是本地的channel，所以，Attribute拿到的是对端的Session

        Session session = AttributeUtil.get(channel, ChannelAttrKey.SESSION);
        if (session == null) {
            return;
        }
        AbstractMessage absMsg = (AbstractMessage) msg;
        absMsg.setSession(session);

        AbstractHandler<? extends AbstractMessage> handler = handlerPool.getHandler(msg.getId());

        handler.setMsg(msg);

        byte processorId = handlerPool.getProcessorId(msg.getId());

        //将消息分发到指定的队列中，该队列有可能在同一个进程，也有可能不在同一个进程

        router.process(processorId, handler);
    }
}
