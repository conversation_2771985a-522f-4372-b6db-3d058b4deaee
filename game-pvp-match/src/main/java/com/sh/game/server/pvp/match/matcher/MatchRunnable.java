package com.sh.game.server.pvp.match.matcher;


import com.sh.concurrent.AbstractCommand;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.server.pvp.match.bean.Battle;
import com.sh.game.server.pvp.match.MatchContext;
import com.sh.game.server.pvp.match.MatchManager;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/15 13:17
 */
@Slf4j
public class MatchRunnable implements Runnable {

    private PVPMatcher matcher;

    private AbstractCommand innerCommand = new InnerCommand();

    public MatchRunnable(PVPMatcher matcher) {
        this.matcher = matcher;
    }

    @Override
    public void run() {

        MatchContext.getServer().getRouter().process(ProcessorId._12_MATCH, innerCommand, 0L);


    }

    class InnerCommand extends AbstractCommand {
        @Override
        public void doAction() {
            try {

                //一次匹配
                matcher.match();

                //获取匹配结果
                List<Battle> battleList = matcher.out();
                //创建战斗
                MatchManager.getInstance().tryCreateBattle(battleList);
            } catch (Throwable throwable) {
                log.error("匹配线程报错", throwable);
            }
        }
    }


}
