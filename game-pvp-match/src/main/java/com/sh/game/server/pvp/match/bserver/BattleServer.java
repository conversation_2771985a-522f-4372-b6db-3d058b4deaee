package com.sh.game.server.pvp.match.bserver;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/2 10:58
 */
@Getter
@Setter
public class BattleServer extends ServerInfo {

    private List<Integer> pvpTypeList;

    private int battleCount;

    private int playerCount;

    private int selected;

    private int lastSelectTime;

    @Override
    public String toString() {
        return "BattleServer{" +
                "battleCount=" + battleCount +
                ", playerCount=" + playerCount +
                ", selected=" + selected +
                ", lastSelectTime=" + lastSelectTime +
                ", hostId=" + hostId +
                '}';
    }
}
