package com.sh.game.server.pvp.match.notice.action;

import com.sh.game.common.communication.notice.battle.BattleMapToMatchNotice;
import com.sh.game.common.communication.notice.battle.BattleRegisterToMatchNotice;
import com.sh.game.common.communication.notice.battle.DFLSMapCreateSuccessNotice;
import com.sh.game.common.communication.notice.battle.DFLSMapEndNotice;
import com.sh.game.common.communication.notice.match.BattleHeartNotice;
import com.sh.game.notice.NoticeAction;
import com.sh.game.server.pvp.match.BattleServerManager;
import com.sh.game.server.pvp.match.MatchManager;
import com.sh.game.server.pvp.match.dfls.DFLSMatchManager;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/8/12.
 */
@NoticeAction
@Slf4j
public class BattleNAction {

    /**
     * 战斗服注册
     * @param notice
     */
    public void register(BattleRegisterToMatchNotice notice) {
        BattleServerManager.getInstance().register(notice);
    }

    /**
     * 战斗服创建地图成功
     */
    public void mapCreateSuccess(BattleMapToMatchNotice notice) {
        MatchManager.getInstance().afterCreateBattle(notice);

    }

    public void BattleHeartNotice(BattleHeartNotice notice) {
        BattleServerManager.getInstance().battleHeart(notice);
    }


    public void dlfsMapCreateSuccess(DFLSMapCreateSuccessNotice notice) {
        DFLSMatchManager.getInstance().battleMapCreateSuccess(notice);
    }

    public void dflsMapEnd(DFLSMapEndNotice notice) {
        DFLSMatchManager.getInstance().dflsMapEnd(notice);
    }
}
