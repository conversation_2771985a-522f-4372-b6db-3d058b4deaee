package com.sh.game.server.pvp.match.matcher;

import com.sh.commons.tuple.TwoTuple;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.server.pvp.match.*;
import com.sh.game.server.pvp.match.bean.Battle;
import com.sh.game.server.pvp.match.bean.MatchGroup;
import com.sh.game.server.pvp.match.bean.MatchTeam;
import com.sh.game.server.pvp.match.bean.Player;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 一个一个顺序匹配器
 * <AUTHOR>
 * @date 2019/8/2 14:41
 */
@Slf4j
public class OneByOnePVPMatcher implements PVPMatcher {

    private int groupPlayerCount;

    //pvp类型
    private int pvpType;

    //pvp地图id
    private int mapId;

    //组队战斗力匹配上边界
    private int groupUpOffset = 1000000;
    //组队战斗力匹配下边界
    private int groupDownOffset = 1000000;
    //组队每秒时间加权
    private int groupTimeWeight = 100000;

    //对战战斗力匹配上边界
    private int matchUpOffset = 200000;
    //对战战斗力匹配下边界
    private int matchDownOffset = 200000;

    //对战每秒时间加权
    private int matchTimeWeight = 100000;


    //每个玩家的原生组
    private Map<Long, MatchGroup> playerGroupMap= new HashMap<>();


    //所有队伍
    private Map<Long, MatchTeam> teamMap = new HashMap<>();
    //已组满队伍
    private List<MatchTeam> fullTeamList = new ArrayList<>();
    //未组满队伍
    private List<MatchTeam> notTeamGroupList = new ArrayList<>();

    private List<Battle> battleList = new ArrayList<>();


    public OneByOnePVPMatcher(int groupPlayerCount,int pvpType, int mapId,
                              int groupUpOffset, int groupDownOffset, int groupTimeWeight,
                              int matchUpOffset, int matchDownOffset, int matchTimeWeight
                              ) {
        this.groupPlayerCount = groupPlayerCount;
        this.groupUpOffset = groupUpOffset;
        this.groupDownOffset = groupDownOffset;
        this.groupTimeWeight = groupTimeWeight;
        this.matchUpOffset = matchUpOffset;
        this.matchDownOffset = matchDownOffset;
        this.matchTimeWeight = matchTimeWeight;
        this.pvpType = pvpType;
        this.mapId = mapId;
    }

    public OneByOnePVPMatcher(int groupPlayerCount,int pvpType, int mapId ) {
        this.groupPlayerCount = groupPlayerCount;
        this.pvpType = pvpType;
        this.mapId = mapId;
    }

    @Override
    public void in(MatchGroup group) {

        MatchTeam team = new MatchTeam();
        team.getGroupList().add(group);
        team.getPlayerList().addAll(group.getPlayerList());
        team.setPvpType(this.pvpType);
        team.setFightPower(group.getFightPower());
        team.setStartTime(group.getStartTime());

        //借用一下id
        team.setId(group.getId());

        //设置自己组
        for (Player player : group.getPlayerList()) {
            playerGroupMap.put(player.getId(), group);
        }
        group.setTeam(team);

        //放入匹配队列
        teamMap.put(team.getId(), team);
    }

    @Override
    public void match() {
        fullTeamList.clear();
        notTeamGroupList.clear();
        battleList.clear();
        for (MatchTeam team : teamMap.values()) {
            if (team.isFull(groupPlayerCount)) {
                fullTeamList.add(team);
            } else {
                notTeamGroupList.add(team);
            }
        }

        long time = TimeUtil.getNowOfMills();
        //填充队伍
        boolean filled = !notTeamGroupList.isEmpty();
        //每次循环填充一次，都会改变队伍组合，如果发生了填充，队伍的战斗等属性发生变化，需要进行第二次填充
        //因为队伍可能有 3 2、3 1 1、2 1 1 1 等各种组合
        while (filled) {
            filled = fillGroup(time);
        }
        //匹配队伍
        matchGroup(time);

        if(teamMap.size() > 0) {
            log.info("本次匹配剩余组数：{}", teamMap.size());
        }

    }

    /**
     * 检查是否有重复之类的逻辑，以新的为主，发下重复的team直接取消匹配
     * @param group
     * @return
     */
    @Override
    public TwoTuple<Boolean, String> check(MatchGroup group) {

        //重找重复匹配的玩家的组
        List<MatchGroup> repeatedList = new ArrayList<>();
        for (Player player : group.getPlayerList()) {
            MatchGroup repeatedGroup = playerGroupMap.get(player.getId());
            if (repeatedGroup != null) {
                log.info("发现重复匹配：playerId:{}, groupId:{}", player.getId(), group.getId());
                repeatedList.add(repeatedGroup);
            }
        }

        //取消重复匹配的组
        if (!repeatedList.isEmpty()) {
            for (MatchGroup repeatedGroup : repeatedList) {
                MatchTeam team = repeatedGroup.getTeam();
                team.removeSrc(repeatedGroup);
                if (team.getGroupList().isEmpty()) {
                    //空了就移除
                    teamMap.remove(team.getId());
                }
            }
        }
        return new TwoTuple<>(true, "开始匹配.");
    }

    /**
     * 填充队伍
     */
    private boolean fillGroup(long time) {


        //是否发生了队伍填充
        boolean filled = false;
        for (int i = 0; i < notTeamGroupList.size(); i++) {
            MatchTeam team = notTeamGroupList.get(i);
            if (team.isMatched()) {
                continue;
            }
            TwoTuple<Integer, Integer> range = groupFightPowerRange(team, time);
            //只需要匹配自己后面的，前面已经在之前循环中做过比较
            List<MatchTeam> mergedTeamList = new ArrayList<>();
            for (int j = i + 1; j < notTeamGroupList.size(); j++) {
                MatchTeam target = notTeamGroupList.get(j);
                if (target.isMatched()) {
                    continue;
                }

                //队伍人数比较，以及其他条件检查
                if (!canMerge(team, target)) {
                    continue;
                }

                //战力比较
                int targetFightPower = target.getAvgFightPower();
                if (targetFightPower < range.first || targetFightPower > range.second) {
                    continue;
                }

                //合并队伍
                team.mergeGroup(target);

                //移除已合并的队伍
                teamMap.remove(target.getId());

                mergedTeamList.add(target);

                filled = true;
                if(team.isFull(groupPlayerCount)) {
                    fullTeamList.add(team);
                    int waitTime = (int) ((TimeUtil.getNowOfMills() - team.getStartTime()) / 1000);
                    StringBuilder str = new StringBuilder();
                    str.append("组成队伍时间:")
                            .append(waitTime)
                            .append("秒。")
                            .append("玩家-->")
                            .append(team.getPlayerList())
                            .append("-->")
                            .append(target.getPlayerList());
                    log.info(str.toString());
                    team.setFullGroupTime(TimeUtil.getNowOfMills() / 1000);
                    break;
                }
            }

            //移除已合并的队伍
            for (MatchTeam mergedTeam : mergedTeamList) {
                mergedTeam.setMatched(true);
            }

        }
        return filled;
    }

    /**
     * 满组队伍匹配战斗
     */
    public void matchGroup(long time) {
        for (int i = 0; i < fullTeamList.size(); i++) {
            MatchTeam current = fullTeamList.get(i);
            if (current.isMatched()) {
                //已匹配成功
                continue;
            }
            TwoTuple<Integer, Integer> range = matchFightPowerRange(current, time);

            List<MatchTeam> targetList = new ArrayList<>();
            for (int j = 0; j < fullTeamList.size(); j++) {
                MatchTeam target = fullTeamList.get(j);
                if (current == target) {
                    continue;
                }
                if (target.isMatched()) {
                    //已匹配成功
                    continue;
                }

                int targetFightPower = target.getFightPower();
                if (targetFightPower < range.first || targetFightPower > range.second) {
                    continue;
                }

                targetList.add(target);
            }

            if (targetList.isEmpty()) {
                continue;
            }

            FightPowerOrdering ordering = new FightPowerOrdering(current.getAvgFightPower());
            MatchTeam target = ordering.min(targetList);
            //不移除，采用标志跳过
            current.setMatched(true);
            target.setMatched(true);

            //移除已匹配成功的队伍
            teamMap.remove(current.getId());
            teamMap.remove(target.getId());

            //移除玩家的组
            target.getPlayerList().forEach(v->playerGroupMap.remove(v.getId()));
            current.getPlayerList().forEach(v->playerGroupMap.remove(v.getId()));

            Battle battle = new Battle();
            battle.setId(MatchManager.getInstance().getBattleId());
            battle.setA(current);
            battle.setB(target);
            battle.setMapCfgId(this.mapId);
            battle.setPvpType(this.pvpType);
            long curTime = TimeUtil.getNowOfMills() / 1000;
            StringBuilder str = new StringBuilder();
            str.append("匹配成功:--->")
                    .append("playerList:")
                    .append(current.getPlayerList())
                    .append("vs")
                    .append(target.getPlayerList())
                    .append("最大用时:")
                    .append(current.getFullGroupTime() > target.getFullGroupTime() ? curTime - target.getFullGroupTime() : curTime - current.getFullGroupTime())
                    .append("秒！");
            log.info("匹配成功:battleId->{},{}vs{}",battle.getId(), current.info(), target.info());
            log.info(str.toString());
            battleList.add(battle);
        }
    }


    /**
     * 组队战力上下边界（组成一个队伍）
     * @param time
     * @return
     */
    @SuppressWarnings("Duplicates")
    public TwoTuple<Integer, Integer> groupFightPowerRange(MatchTeam team, long time) {


        int waitTime = (int) ((time - team.getStartTime()) / 1000);

        int curGroupTimeWeight;
        if(waitTime <= 10) {
            curGroupTimeWeight = 100000;
        } else if (waitTime <= 20) {
            curGroupTimeWeight = 300000;
        } else if (waitTime <= 30) {
            curGroupTimeWeight = 500000;
        } else {
            curGroupTimeWeight = 800000;
        }
        int up = Math.min(team.getAvgFightPower() + groupUpOffset + waitTime * curGroupTimeWeight, Integer.MAX_VALUE);
        int down = Math.max(team.getAvgFightPower() - groupDownOffset - waitTime * curGroupTimeWeight, 0);
        if (down < 0) {
            down = 0;
        }

        return new TwoTuple<>(down, up);
    }

    /**
     * 战斗匹配上下边界（组成比赛的双方）
     * @param team
     * @param time
     * @return
     */
    @SuppressWarnings("Duplicates")
    public TwoTuple<Integer, Integer> matchFightPowerRange(MatchTeam team, long time) {

        int waitTime = (int) ((time - team.getStartTime()) / 1000);

        int curGroupTimeWeight;
        if(waitTime <= 10) {
            curGroupTimeWeight = 100000;
        } else if (waitTime <= 20) {
            curGroupTimeWeight = 300000;
        } else if (waitTime <= 30) {
            curGroupTimeWeight = 500000;
        } else {
            curGroupTimeWeight = 1000000;
        }

        int up = Math.min(team.getFightPower() + matchUpOffset + waitTime * curGroupTimeWeight, Integer.MAX_VALUE);
        int down = Math.max(team.getFightPower() - matchDownOffset - waitTime * curGroupTimeWeight, 0);
        if (down < 0) {
            down = 0;
        }
        return new TwoTuple<>(down, up);
    }

    /**
     * 两个group是否可以组队
     * @param t1
     * @param t2
     * @return
     */
    public boolean canMerge(MatchTeam t1, MatchTeam t2) {
        if (t1.getPlayerList().size() + t2.getPlayerList().size() > groupPlayerCount) {
            return false;
        }
        return true;
    }


    @Override
    public List<Battle> out() {
        return battleList;
    }

    @Override
    public boolean cancel(MatchGroup group) {
        MatchTeam team = group.getTeam();
        team.removeSrc(group);
        if (team.getGroupList().isEmpty()) {
            teamMap.remove(team.getId());
        }
        for (Player player : group.getPlayerList()) {
            playerGroupMap.remove(player.getId());
        }
        return true;
    }

    @Override
    public void clean(int hostId) {
        Map<Long, MatchGroup> groups = new HashMap<>();
        for (MatchGroup group : playerGroupMap.values()){
            if (groups.containsKey(group.getId())){
                continue;
            }
            if (group.isMatched()){
                continue;
            }
            if (group.getHostId() == hostId){
                groups.put(group.getId(), group);
            }
        }

        for (MatchGroup group : groups.values()){
            cancel(group);
        }
    }
}
