package com.sh.game.auth.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sh.client.Client;
import com.sh.concurrent.AbstractCommand;
import com.sh.game.GameContext;
import com.sh.game.auth.AbstractApiAuth;
import com.sh.game.common.constant.ChatConst;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.constant.RechargeCode;
import com.sh.game.common.constant.RoleConst;
import com.sh.game.common.entity.User;
import com.sh.game.common.entity.user.LoginContext;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.HttpUtil;
import com.sh.game.common.util.Md5Util;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.server.SessionManager;
import com.sh.game.server.SessionUtil;
import lombok.extern.slf4j.Slf4j;

import java.time.ZoneOffset;
import java.util.*;

/**
 * 贪玩
 */
@Slf4j
public class AuthController4TANWAN extends AbstractApiAuth {
    private static final int appId = 4280;
    private static final String appKey = "yWpx3hWQHFhSnTCj#4280#6KuRKuaAjLJ5sYRy";
    private static final String payKey = "8b67f12428631b8288ce1220cd0730cd";
    private static final String url = "http://api.sdk.tanwan.com/user/verifyAccount/";

    private static final int FAILED = -1;   // 失败
    private static final int SUCCESS = 1;    // 成功
    private static final int ERROR_ACCOUNT = 2;    // 账号错误
    private static final int ERROR_SIGN = 3;    // md5验证失败
    private static final int ERROR_REPEATED = 4;    // 订单重复
    private static final int ERROR_MONEY = 5;    // 充值金额有误
    private static final int ERROR_IP = 6;    // IP限制


    @Override
    public String getChannel() {
        return "102";
    }

    @Override
    public String getName() {
        return "贪玩";
    }


    @Override
    public void loginApi(LoginContext context, AbstractCommand callback) {
        executor.submit(new AbstractCommand() {
            @Override
            public void doAction() {
                Map<String, Object> params = new HashMap<>();
                params.put("appid", appId);
                params.put("userID", context.getUid());
                params.put("token", context.getToken());
                params.put("sign", Md5Util.md5(String.format("userID=%stoken=%s%s", context.getUid(), context.getToken(), appKey)));

                log.info(">>>> LoginAuth: {} {}", url, params);
                long reqTime = TimeUtil.getNowOfMills();
                String ret = HttpUtil.get(url, params);
                log.info("<<<< LoginAuth: {} {}ms", ret, TimeUtil.getNowOfMills() - reqTime);
                try {
                    JSONObject retJson = JSON.parseObject(ret);
                    Integer status = retJson.getInteger("state");
                    if (status == 1) {
                        String username = retJson.getJSONObject("data").getString("username");
                        if (username != null) {
                            context.setUsername(username);
                        }
                    } else {
                        log.error("auth failed: {}", context.getUid());
                        return;
                    }
                } catch (Exception e) {
                    log.error("auth failed: {} {}", context.getUid(), e.getMessage());
                    return;
                }

                GameContext.getGameServer().getRouter().process(ProcessorId.SERVER_AUTH, callback, 0L);
            }
        });
    }

    @Override
    protected Object rechargeApi(Map<String, String> parameters, Client client) {
        String sign = parameters.get("flag");
        if (sign == null) {
            log.error("sign is empty");
            return ERROR_SIGN;
        }

        StringBuilder builder = new StringBuilder();
        builder
                .append(parameters.get("uid"))
                .append(parameters.get("money"))
                .append(parameters.get("time"))
                .append(parameters.get("sid"))
                .append(parameters.get("orderid"))
                .append(parameters.get("ext"))
                .append(payKey);
        String md5 = Md5Util.md5(builder.toString());
        if (!sign.equals(md5)) {
            log.error("sign not match {} / {}", sign, md5);
            return ERROR_SIGN;
        }

        RechargeCode code = doRecharge(
                client,
                parameters,
                parameters.get("uid"),
                Integer.parseInt(parameters.get("money")),
                parameters.get("orderid")
        );
        if (code == null) {
            return FAILED;
        }

        if (code != RechargeCode.SUCCESS) {
            log.error("error: {}", code.getMsg());
        }
        switch (code) {
            case SUCCESS:
                return SUCCESS;
            case FAILED:
                return FAILED;
            case ERROR_USER:
            case ERROR_ROLE:
                return ERROR_ACCOUNT;
            case ERROR_MONEY:
                return ERROR_MONEY;
            case ERROR_ORDER:
                return ERROR_REPEATED;
        }

        return FAILED;
    }


    @Override
    protected String chatApi(Role role, int channel, long target, String content) {
        Map<String, Object> params = new HashMap<>();
        params.put("platform_id", 1);
        params.put("game_type_id", 11);
        params.put("base_game_id", 480);
        params.put("game_id", 4306);
        params.put("game_server_id", GameContext.getServerId());
        params.put("game_server_name", GameContext.getServerId());
        params.put("game_server_open", GameContext.getOpenTime().toEpochSecond(ZoneOffset.of("+8")));

        User user = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(role.getId()));
        if (user != null) {
            params.put("uid", user.getAccount());
            params.put("username", user.getUsername());
            params.put("ip", user.getIp());
        }
        params.put("role_id", role.getId());
        params.put("role_name", role.getName());
        params.put("role_occupation", RoleConst.CAREER_NAMES[role.getCareer()]);
        params.put("role_level", role.getLevel());
        params.put("role_create_time", role.getRoleLogin().getCreateTime());
        params.put("role_pay", role.getRecharge().getRechargedTotal() / 100.0);

        if (channel == ChatConst.ChatType.PERSON) {
            Role receiver = SessionManager.getInstance().getRole(target);
            if (receiver != null) {
                User receiverUser = SessionUtil.getUser(SessionManager.getInstance().getRoleSession(receiver.getId()));
                if (receiverUser != null) {
                    params.put("to_uid", receiverUser.getAccount());
                    params.put("to_username", receiverUser.getUsername());
                }
                params.put("to_role_id", receiver.getId());
                params.put("to_role_name", receiver.getName());
                params.put("to_role_pay", receiver.getRecharge().getRechargedTotal());
            }
        }
        int content_type = 9;
        switch (channel) {
            case ChatConst.ChatType.PERSON:
                content_type = 1;
                break;
            case ChatConst.ChatType.WORLD:
                content_type = 4;
                break;
            case ChatConst.ChatType.UNION:
                content_type = 6;
                break;
            case ChatConst.ChatType.GROUP:
                content_type = 7;
                break;
            case ChatConst.ChatType.NEAR:
                content_type = 8;
                break;
        }
        params.put("content_type", content_type);
        params.put("content", content);
        int now = TimeUtil.getNowOfSeconds();
        params.put("send_time", now);
        params.put("time", now);

        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);
        StringBuilder builder = new StringBuilder();
        for (String key : keys) {
            builder.append(key);
            builder.append("=");
            builder.append(params.get(key));
            builder.append("&");
        }
        String sign = Md5Util.md5(builder.toString().substring(0, builder.length() - 1) + "-" + "FKLAJSDfdksaj78ASFjJFADVSDFS");
        params.put("sign", sign);

        String ret = HttpUtil.post("http://gamechat.tanwan.com/supervisory/judge", params, 3000);
        if (ret != null) {
            try {
                JSONObject retJson = JSON.parseObject(ret);
                if (retJson.getInteger("code") != 10009) {
                    log.error("聊天发送失败：{}", ret);
                    TipUtil.show(role, CommonTips.服务_聊天内容包含敏感信息);
                    return null;
                }
            } catch (Exception e) {
                log.error("访问运营方聊天接口失败: {}", e.getMessage());
            }
        }

        return content;
    }
}
