package com.sh.game.auth.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sh.client.Client;
import com.sh.concurrent.AbstractCommand;
import com.sh.game.GameContext;
import com.sh.game.auth.AbstractApiAuth;
import com.sh.game.auth.InteriorCheck;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.constant.RechargeCode;
import com.sh.game.common.entity.user.LoginContext;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.HttpUtil;
import com.sh.game.common.util.Md5Util;
import com.sh.game.common.util.TimeUtil;
import lombok.extern.log4j.Log4j2;

import java.util.HashMap;
import java.util.Map;

/**
 * ATO：yumo<br>;
 * 时间：2021/1/23 14:54<br>;
 * 版本：1.0<br>;
 * 描述：39互娱
 */
@Log4j2
public class AuthController<PERSON><PERSON><PERSON><PERSON><PERSON> extends AbstractApiAuth {

    /**
     * 应用编号
     */
    protected static final int appId = 285;

    /**
     * 分配的验证key
     */
    protected static final String payKey = "799cdff9a974f8b891b2d758d8e6e26f";

    /**
     * 登录token验证地址
     */
    protected static final String login_url = "https://juhesdk.3975ad.com/api/oauth/verify_token";

    /**
     * 充值验证地址
     */
    protected static final String recharge_url = "https://juhesdk.3975ad.com/api/order/query";

    protected static final String FAILED = "错误充值信息";
    protected static final String SUCCESS = "success";
    protected static final String ERROR_ACCOUNT = "账号错误";
    protected static final String ERROR_SIGN = "md5验证失败";
    protected static final String ERROR_MONEY = "充值金额有误";
    protected static final String WECHAT_RECHARGE = "2#";

    @Override
    public String getChannel() {
        return "39";
    }

    @Override
    public String getName() {
        return "39互娱";
    }

    @Override
    public boolean isDefault() {
        return true;
    }


    @Override
    public void loginApi(LoginContext context, AbstractCommand callback) {
        executor.submit(new AbstractCommand() {
            @Override
            public void doAction() {
                boolean check = InteriorCheck.check(getChannel(), context.getIp(), context.getToken());
                if (!check) {
                    Map<String, Object> params = new HashMap<>();
                    params.put("juhe_userid", context.getUid());
                    params.put("juhe_token", context.getToken());
                    params.put("channel_userid", "");
                    params.put("app_id", appId);

                    StringBuffer buffer = new StringBuffer();
                    buffer.append("app_id=").append(appId).append("&");
                    buffer.append("channel_userid=&");
                    buffer.append("juhe_token=").append(context.getToken()).append("&");
                    buffer.append("juhe_userid=").append(context.getUid()).append(payKey);
                    String sign = Md5Util.md5(buffer.toString()).toUpperCase();
                    params.put("sign", sign);
                    try {
                        String ret = HttpUtil.post(login_url, params, 1000);
                        JSONObject retJson = JSON.parseObject(ret);
                        boolean status = retJson.getBooleanValue("success");
                        if (!status) {
                            log.error("auth failed: {} {}", context.getUid(), retJson.getString("message"));
                            return;
                        }
                    } catch (Exception e) {
                        log.error("auth failed: {} {}", context.getUid(), e.getMessage());
                        return;
                    }
                }
                GameContext.getGameServer().getRouter().process(ProcessorId.SERVER_AUTH, callback, 0);
            }

        });
    }

    @Override
    protected Object rechargeApi(Map<String, String> parameters, Client client) {
        String sign = parameters.getOrDefault("sign", "");
        StringBuffer buffer = new StringBuffer();
        buffer.append("channel_userid").append(parameters.getOrDefault("channel_userid", ""));
        String cp_order_id = parameters.getOrDefault("cp_order_id", "");
        buffer.append("cp_order_id").append(cp_order_id);
        String ext = parameters.getOrDefault("ext", "");
        buffer.append("ext").append(ext);
        String order_id = parameters.getOrDefault("juhe_order_id", "");
        buffer.append("juhe_order_id").append(order_id);
        int money = Integer.parseInt(parameters.getOrDefault("money", "0"));
        buffer.append("money").append(money);
        buffer.append("package_id").append(parameters.getOrDefault("package_id", ""));
        buffer.append("unix_name").append(parameters.getOrDefault("unix_name", ""));
        buffer.append(payKey);
        String md5 = Md5Util.md5(buffer.toString()).toUpperCase();
        if (!sign.equals(md5)) {
//            log.error("sign not match {} / {}", sign, md5);
//            return ERROR_SIGN;
        }
        Map<String, Object> params = new HashMap<>();
        params.put("app_id", appId);
        params.put("cp_order_id", cp_order_id);
        params.put("order_id", order_id);

        StringBuffer checkBuffer = new StringBuffer();
        checkBuffer.append("app_id=").append(appId).append("&");
        checkBuffer.append("cp_order_id=").append(cp_order_id).append("&");
        checkBuffer.append("order_id=").append(order_id).append(payKey);
        params.put("sign", Md5Util.md5(checkBuffer.toString()).toUpperCase());
        long retTime = TimeUtil.getNowOfMills();
        log.info("<<<< Order check params=" + params);
//        String ret = HttpUtil.post(recharge_url, params, 1000);
//        log.info("<<<< LoginAuth: {} {}ms", ret, TimeUtil.getNowOfMills() - retTime);
//        JSONObject retJson = JSON.parseObject(ret);
//        boolean success = retJson.getBooleanValue("success");
//        if (!success) {
//            return retJson.getString("message");
//        }
//        int order_status = retJson.getJSONObject("data").getIntValue("order_status");
//        if (order_status < 1 || order_status > 3) {
//            return "订单渠道验证失败";
//        }
        if (ext.startsWith(WECHAT_RECHARGE)) {
            parameters.put("rechargeWay", "1");
        }
        RechargeCode code = doRecharge(client, parameters, parameters.get("user_id"), money, order_id);
        if (code == null) {
            return FAILED;
        }

        if (code != RechargeCode.SUCCESS) {
            log.error("error: {}", code.getMsg());
        }
        switch (code) {
            case SUCCESS:
            case ERROR_ORDER:
                return SUCCESS;
            case ERROR_USER:
            case ERROR_ROLE:
                return ERROR_ACCOUNT;
            case ERROR_MONEY:
                return ERROR_MONEY;
            default:
                return FAILED;
        }
    }

    @Override
    protected String chatApi(Role role, int channel, long target, String content) {
        return null;
    }
}
