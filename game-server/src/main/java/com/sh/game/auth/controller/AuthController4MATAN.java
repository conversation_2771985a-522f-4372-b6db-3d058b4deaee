package com.sh.game.auth.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sh.client.Client;
import com.sh.concurrent.AbstractCommand;
import com.sh.game.GameContext;
import com.sh.game.auth.AbstractApiAuth;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.constant.RechargeCode;
import com.sh.game.common.entity.user.LoginContext;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.HttpUtil;
import com.sh.game.common.util.Md5Util;
import com.sh.game.common.util.TimeUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/**
 * 9z - 麻探
 */
@Slf4j
public class AuthController4MATAN extends AbstractApiAuth {
    private static final String appKey = "bdf4f00f17344a6991bb454aa650ce68";
    private static final String payKey = "75f09832b7c94fbfbf93b7923296aa80";
    private static final String url = "https://api.matangame.com/cp/v1/user/auth";

    private static final int SUCCESS = 0;
    private static final int FAILED = 1;

    @Override
    public String getChannel() {
        return "10009";
    }

    @Override
    public String getName() {
        return "麻探";
    }


    @Override
    public void loginApi(LoginContext context, AbstractCommand callback) {
        executor.submit(new AbstractCommand() {
            @Override
            public void doAction() {
                Map<String, Object> params = new HashMap<>();
                params.put("slug", context.getUid());
                Map<String, String> header = new HashMap<>();
                header.put("Accept", "application/json");
                header.put("Authorization", context.getToken());

                log.info(">>>> LoginAuth: {} {}", url, params);
                long retTime = TimeUtil.getNowOfMills();
                String ret = HttpUtil.post(url, params, header, 10000);
                log.info("<<<< LoginAuth: {} {}ms", ret, TimeUtil.getNowOfMills() - retTime);
                try {
                    JSONObject retJson = JSON.parseObject(ret);
                    Integer status = retJson.getInteger("code");
                    if (status != 0) {
                        log.error("auth failed: {} {}", context.getUid(), retJson.getString("message"));
                        return;
                    }
                } catch (Exception e) {
                    log.error("auth failed: {} {}", context.getUid(), e.getMessage());
                    return;
                }

                GameContext.getGameServer().getRouter().process(ProcessorId.SERVER_AUTH, callback, 0);
            }
        });
    }


    @Override
    protected Object rechargeApi(Map<String, String> parameters, Client client) {
        Map<String, Object> map = new HashMap<>();

        String sign = parameters.get("sign");
        if (sign == null) {
            log.error("sign is empty");
            map.put("code", FAILED);
            map.put("message", "sign error");
            return map;
        }

        StringBuilder builder = new StringBuilder();
        builder
                .append(parameters.get("order_number_cp"))
                .append(payKey)
                .append(parameters.get("money"));
        String md5 = Md5Util.md5(builder.toString());
        if (!sign.equals(md5)) {
            log.error("sign not match {} / {}", sign, md5);
            map.put("code", FAILED);
            map.put("message", "sign error");
            return map;
        }

        RechargeCode code = doRecharge(
                client,
                parameters,
                parameters.get("user"),
                Integer.parseInt(parameters.get("money")) / 100,
                parameters.get("order_number")
        );
        if (code == null) {
            map.put("code", FAILED);
            map.put("message", "失败");
            return map;
        }

        map.put("code", code == RechargeCode.ERROR_ORDER ? SUCCESS : code.getCode());
        map.put("message", code.getMsg());
        return map;

    }

    @Override
    protected String chatApi(Role role, int channel, long target, String content) {
        return null;
    }
}
