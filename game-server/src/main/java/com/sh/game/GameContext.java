package com.sh.game;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sh.commons.tuple.Tuple;
import com.sh.commons.tuple.TwoTuple;
import com.sh.game.back.BackServer;
import com.sh.game.common.constant.ServerOpenState;
import com.sh.game.common.entity.sys.OtherData;
import com.sh.game.common.util.HttpUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnServerStartUpScript;
import com.sh.game.option.ServerOption;
import com.sh.game.server.ModuleRouter;
import com.sh.script.ScriptEngine;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoField;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
public class GameContext {

    private static final Logger LOGGER = LoggerFactory.getLogger(GameContext.class);

    private static ModuleRouter moduleRouter;

    private static int serverId;

    private static int serverType;

    private static boolean ready;

    /**
     * 开服日期
     */
    private static LocalDateTime openTime;

    /**
     * 开服当天凌晨0点时间戳
     */
    private static long openDayZeroTime;

    /**
     * 合服日期
     */
    private static LocalDateTime combineTime;

    /**
     * 合服日期当天凌晨0点时间戳
     */
    private static long combineDayZeroTime;

    /**
     * 是否已经合服
     */
    private static boolean combined = false;

    /**
     * 是否开启全服双倍经验
     */
    private static int expDouble = 1;

    private static ServerOption option;

    private static GameServer gameServer;

    private static BackServer backServer;

    private static boolean isDebug;

    /**
     * 服务器关闭逻辑已经是否已经执行
     */
    private static boolean serverCloseLogicExecuted;

    /**
     * 游戏服务器关闭
     */
    private static boolean closed;

    /**
     * 是否是防沉迷
     */
    private static boolean fcm = false;

    private static String queryCreateRoleUrl = "http://ht.zlzz.app.9125flying.com/extapi?action=forbidcreate";

    public static void init(ServerOption option) {
        GameContext.option = option;
        serverId = option.getServerId();
        serverType = option.getServerType();
        openTime = option.getOpenTime();
        isDebug = option.isDebug();

        long openTimeMills = openTime.atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
        openDayZeroTime = TimeUtil.dayZeroMillsFromTime(openTimeMills);


        LOGGER.info("开服时间：【{}】", openTime);
        LOGGER.info("开服当天凌晨0点时间戳：【{}】", openDayZeroTime);
        LOGGER.info("开服距离开服当天凌晨：【{}】", (openTimeMills - openDayZeroTime));

        combineTime = option.getCombineTime();
        if (combineTime != null) {
            long combineTimeMills = combineTime.atZone(ZoneId.systemDefault()).toEpochSecond() * 1000;
            combineDayZeroTime = TimeUtil.dayZeroMillsFromTime(combineTimeMills);
            if (combineTimeMills <= openTimeMills) {
                LOGGER.error("开服与合服时间配置错误，合服时间早于或等于开服时间....");
                throw new RuntimeException("开服与合服时间配置错误，合服时间早于或等于开服时间....");
            }

            if(TimeUtil.getNowOfMills() >= combineTimeMills) {
                combined = true;
            }

            LOGGER.info("合服时间：【{}】", combineTime);
            LOGGER.info("合服当天凌晨0点时间戳：【{}】", combineDayZeroTime);
            LOGGER.info("合服距离开服当天凌晨：【{}】", (combineTimeMills - combineDayZeroTime));
        }

    }

    public static GameServer createGameServer() {
        try {

            ModuleRouter moduleRouter = new ModuleRouter();
            GameContext.setModuleRouter(moduleRouter);

            gameServer = new GameServer(option);
            gameServer.load();



            ready = true;
            return gameServer;
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public static BackServer createBackServer() {
        try {
            backServer = new BackServer(option);
            return backServer;
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

    public static int getHostId() {
        return option.getServerId() * 1000 + option.getPlatformId();
    }

    public static boolean isStarted() {
        return gameServer != null && gameServer.isOpen();
    }

    public static int getExpDouble() {
        return expDouble;
    }

    public static void setExpDouble(int expDouble) {
        GameContext.expDouble = expDouble;
    }

    public static LocalDateTime getOpenTime() {
        return openTime;
    }

    public static void setOpenTime(LocalDateTime openTime) {
        GameContext.openTime = openTime;
    }

    public static long getOpenDayZeroTime() {
        return openDayZeroTime;
    }

    public static void setOpenDayZeroTime(long openDayZeroTime) {
        GameContext.openDayZeroTime = openDayZeroTime;
    }

    public static LocalDateTime getCombineTime() {
        return combineTime;
    }

    public static void setCombineTime(LocalDateTime combineTime) {
        GameContext.combineTime = combineTime;
    }

    public static long getCombineDayZeroTime() {
        return combineDayZeroTime;
    }

    public static void setCombineDayZeroTime(long combineDayZeroTime) {
        GameContext.combineDayZeroTime = combineDayZeroTime;
    }

    public static boolean isCombined() {
        return combined;
    }

    public static void setCombined(boolean combined) {
        GameContext.combined = combined;
    }

    public static ServerOption getOption() {
        return option;
    }


    public static void setOption(ServerOption option) {
        GameContext.option = option;
    }

    public static GameServer getGameServer() {
        return gameServer;
    }

    public static BackServer getBackServer() {
        return backServer;
    }

    public static int getServerId() {
        return serverId;
    }

    public static int getServerType() {
        return serverType;
    }

    public static boolean isServerCloseLogicExecuted() {
        return serverCloseLogicExecuted;
    }

    public static void setServerCloseLogicExecuted(boolean serverCloseLogicExecuted) {
        GameContext.serverCloseLogicExecuted = serverCloseLogicExecuted;
    }

    public static boolean isClosed(String loginName, String ip) {
        if(closed) {
            return true;
        }
        ServerOpenState serverOpenState = getServerState();
        return serverOpenState == ServerOpenState.CLOSED;
    }

    public static void setClosed(boolean closed) {
        GameContext.closed = closed;
    }

    public static boolean isDebug() {
        return isDebug;
    }

    public static void setDebug(boolean debug) {
        isDebug = debug;
    }

    public static boolean isFcm() {
        return fcm;
    }

    public static void setFcm(boolean fcm) {
        GameContext.fcm = fcm;
    }

    public static boolean isReady() {
        return ready;
    }

    public static void setReady(boolean ready) {
        GameContext.ready = ready;
    }

    public static void setServerId(int serverId) {
        GameContext.serverId = serverId;
    }

    public static void setServerType(int serverType) {
        GameContext.serverType = serverType;
    }

    public static ServerOpenState getServerState() {
        return GameContext.getGameServer().getServerOpenState();
    }

    public static ServerOpenState setServerState(int state) {
        getGameServer().setServerOpenState(ServerOpenState.valueOf(state));
        return getServerState();
    }
    /**
     * 获取开服天数,开服首日算作第一天
     *
     * @return
     */
    public static int getOpenServerDay() {

        return (int) (LocalDateTime.now().getLong(ChronoField.EPOCH_DAY) - GameContext.getOpenTime().getLong(ChronoField.EPOCH_DAY) + 1);
    }

    /**
     * 获取合服天数，合服首日算作第一天
     *
     * @return
     */
    public static int getCombineServerDay() {
        if (!GameContext.isCombined()) {
            return 0;
        }
        return (int) (LocalDateTime.now().getLong(ChronoField.EPOCH_DAY) - GameContext.getCombineTime().getLong(ChronoField.EPOCH_DAY) + 1);
    }


    public static TwoTuple<Integer, String> getCreateRoleLimitData() {
        OtherData otherData = DataCenter.getOtherData();
        if(!otherData.isHasSetCreateRoleLimit()) {
            Map<String, Object> paramMap = Maps.newHashMap();
            paramMap.put("pid", GameContext.getOption().getPlatformId());
            try {
                String post = HttpUtil.post(queryCreateRoleUrl, paramMap, 3000);
                log.info("请求创角限制结果：{}", post);
                JSONObject jsonObject = JSON.parseObject(post);
                int createRoleLimitDay = jsonObject.getInteger("opentime");
                String createRoleLimitDescribe = jsonObject.getString("str");

                otherData.setCreateRoleLimitDay(createRoleLimitDay);
                otherData.setCreateRoleLimitDescribe(createRoleLimitDescribe);
                otherData.setHasSetCreateRoleLimit(true);
                DataCenter.updateData(otherData);
            } catch (Exception e) {
                log.error("请求创角限制信息失败：{}", e);
            }
        }
         return Tuple.tuple(otherData.getCreateRoleLimitDay(), otherData.getCreateRoleLimitDescribe());
    }

    public static ModuleRouter getModuleRouter() {
        return moduleRouter;
    }

    public static void setModuleRouter(ModuleRouter moduleRouter) {
        GameContext.moduleRouter = moduleRouter;
    }
}
