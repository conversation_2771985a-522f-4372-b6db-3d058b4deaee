package com.sh.game.data.persist.dao;

import com.sh.common.jdbc.JdbcTemplate;
import com.sh.common.jdbc.RowMapper;
import com.sh.common.persist.Persistable;
import com.sh.game.common.persist.PersistDao;
import com.sh.game.data.mysql.mapper.OrderMapper;
import com.sh.game.system.recharge.entity.Order;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/7/8.
 */
public class PersistRechargeOrderDao extends PersistDao {
    public PersistRechargeOrderDao(JdbcTemplate jdbcTemplate, Class<? extends Persistable> clazz) {
        super(jdbcTemplate, clazz);
    }


    @Override
    public String buildInsterSql() {
        return "INSERT INTO " + tableName + " (id, thridOrderSn, channel, platform,  sid, uid, rid, status, money, goodsId, createTime,successTime,deliverTime) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?)";
    }

    @Override
    public String buildUpdateSql() {
        return "UPDATE  " + tableName + "  SET thridOrderSn = ?,channel = ?, platform = ?, sid = ?, uid = ?,rid = ? ,status=? ,money = ? ,goodsId = ?,createTime = ?,successTime = ?,deliverTime = ? WHERE id = ?";

    }

    @Override
    public String buildDeleteSql() {
        return null;
    }

    @Override
    public String buildSelectByIdSql() {
        return "select * from " + this.tableName + " where id=?";
    }

    @Override
    public void ddlCreateTable() {

        String sqlStr = "CREATE TABLE IF NOT EXISTS `" + this.tableName + "` (\n" +
                "  `id` bigint(20) NOT NULL DEFAULT '0',\n" +
                "  `thridOrderSn` varchar(255) DEFAULT NULL,\n" +
                "  `channel` varchar(20) DEFAULT NULL,\n" +
                "  `platform` int(11) DEFAULT '0',\n" +
                "  `sid` int(11) DEFAULT '0',\n" +
                "  `uid` bigint(20) DEFAULT '0',\n" +
                "  `rid` bigint(20) DEFAULT '0',\n" +
                "  `status` int(3) DEFAULT NULL,\n" +
                "  `money` int(11) DEFAULT '0',\n" +
                "  `goodsId` int(11) DEFAULT '0',\n" +
                "  `createTime` varchar(15) DEFAULT '0',\n" +
                "  `successTime` varchar(15) DEFAULT '0',\n" +
                "  `deliverTime` varchar(15) DEFAULT '0',\n" +
                "  PRIMARY KEY (`id`)\n" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n";
        jdbcTemplate.update(sqlStr);
    }

    @Override
    public Object[] createInsertParameters(Persistable persistable) {
        Order order = (Order) persistable;
        return new Object[]{
                order.getId(),
                order.getThridOrderSn(),
                order.getChannel(),
                order.getPlatform(),
                order.getSid(),
                order.getUid(),
                order.getRid(),
                order.getStatus(),
                order.getMoney(),
                order.getGoodsId(),
                order.getCreateTime(),
                order.getSuccessTime(),
                order.getDeliverTime(),
        };
    }


    @Override
    public Object[] createUpdateParameters(Persistable persistable) {
        Order order = (Order) persistable;
        return new Object[]{
                order.getThridOrderSn(),
                order.getChannel(),
                order.getPlatform(),
                order.getSid(),
                order.getUid(),
                order.getRid(),
                order.getStatus(),
                order.getMoney(),
                order.getGoodsId(),
                order.getCreateTime(),
                order.getSuccessTime(),
                order.getDeliverTime(),
                order.getId(),
        };
    }

    @Override
    public Object[] createDeleteParameters(Persistable persistable) {
        return new Object[0];
    }

    @Override
    public <T extends Persistable> RowMapper<T> selectRowMapper() {
        return (RowMapper<T>) new OrderMapper();
    }

}
