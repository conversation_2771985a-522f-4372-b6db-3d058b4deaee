package com.sh.game.data.mysql.factory;

import com.sh.common.persist.Persistable;
import com.sh.common.persist.SimpleProtostuffPersistFactory;
import com.sh.game.common.constant.DataType;
import com.sh.game.data.mysql.PersistAutoCTable;
import lombok.extern.slf4j.Slf4j;

/** 
 * 系统数据持久化工具
 * <AUTHOR>
 * 2017年6月6日 下午9:30:40   
 */
@Slf4j
public class SysDataPersistFactory extends SimpleProtostuffPersistFactory implements PersistAutoCTable {

	private static final String INSERT = "insert into s_data (id, data) values (?, ?)";

	private static final String UPDATE = "update s_data set data = ? where id = ?";

	private static final String DELETE = "delete from s_data where id = ?";

	@Override
	public String name() {
		return null;
	}
	

	@Override
	public int dataType() {
		return DataType.SYS;
	}


	@Override
	public String createInsertSql() {
		return INSERT;
	}

	@Override
	public String createUpdateSql() {
		return UPDATE;
	}

	@Override
	public String createDeleteSql() {
		return DELETE;
	}

	@Override
	public Object[] createDeleteParameters(Persistable obj) {
		return new Object[]{ obj.getId() };
	}

	@Override
	public long taskPeriod() {
		return 60 * 1000;
	}

	@Override
	public String createTableSql() {
		return "CREATE TABLE IF NOT EXISTS  `s_data`(" +
				"  `id` bigint(20) NOT NULL," +
				"  `data` mediumblob" +
				", PRIMARY KEY (`id`)" +
				") COMMENT='基于自动建表' ;";
	}
}
