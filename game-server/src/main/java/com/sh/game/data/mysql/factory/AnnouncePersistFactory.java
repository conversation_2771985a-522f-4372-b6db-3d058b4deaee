package com.sh.game.data.mysql.factory;

import com.sh.common.persist.PersistFactory;
import com.sh.common.persist.Persistable;
import com.sh.game.back.entity.Announce;
import com.sh.game.common.constant.DataType;
import com.sh.game.data.mysql.PersistAutoCTable;

/**
 * 用户数据持久化工厂
 *
 * <AUTHOR>
 * 2017年6月6日 下午9:31:10
 */
public class AnnouncePersistFactory implements PersistFactory, PersistAutoCTable {

    private static final String INSERT = "INSERT INTO s_announce (id, uniqueId,startTime, endTime, period, type,content) VALUES (?, ?, ?, ?, ?, ?, ?)";

    private static final String UPDATE = "UPDATE  s_announce SET uniqueId = ?,startTime = ?, endTime = ?, period = ?, type = ?,content = ? WHERE id = ?";

    private static final String DELETE = "DELETE FROM s_announce WHERE id = ?";

    @Override
    public String name() {
        return "定时公告";
    }

    @Override
    public int dataType() {
        return DataType.ANNOUNCE;
    }

    @Override
    public String createInsertSql() {
        return INSERT;
    }

    @Override
    public String createUpdateSql() {
        return UPDATE;
    }

    @Override
    public String createDeleteSql() {
        return DELETE;
    }

    @Override
    public Object[] createInsertParameters(Persistable obj) {
        Announce announce = (Announce) obj;
        return new Object[]{announce.getId(), announce.getUniqueId(), announce.getStarTime(), announce.getEndTime(), announce.getPeriod(), announce.getType(), announce.getContent()};
    }

    @Override
    public Object[] createUpdateParameters(Persistable obj) {
        Announce announce = (Announce) obj;
        return new Object[]{announce.getUniqueId(), announce.getStarTime(), announce.getEndTime(), announce.getPeriod(), announce.getType(), announce.getContent(), announce.getId()};
    }

    @Override
    public Object[] createDeleteParameters(Persistable obj) {
        return new Object[]{obj.getId()};
    }

    @Override
    public long taskPeriod() {
        return 60 * 1000;
    }

    @Override
    public String createTableSql() {
        return "CREATE TABLE  IF NOT EXISTS `s_announce` (\n" +
                "  `id` bigint(20) NOT NULL,\n" +
                "  `uniqueId` int(11) DEFAULT NULL,\n" +
                "  `startTime` int(11) DEFAULT NULL,\n" +
                "  `endTime` int(11) DEFAULT NULL,\n" +
                "  `period` int(11) DEFAULT NULL,\n" +
                "  `type` int(11) DEFAULT NULL,\n" +
                "  `content` varchar(1000) NOT NULL,\n" +
                "  PRIMARY KEY (`id`)\n" +
                ") ENGINE=MyISAM DEFAULT CHARSET=utf8";
    }
}
