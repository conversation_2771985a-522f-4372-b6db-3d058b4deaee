package com.sh.game.data.mysql.factory;

import com.sh.game.common.constant.DataType;
import com.sh.game.common.entity.usr.RoleDaily;
import com.sh.game.data.mysql.PersistAutoCTable;
import com.sh.game.data.mysql.factory.abs.CGlibProxyPersistFactory;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RoleDailyPersistFactory extends CGlibProxyPersistFactory<RoleDaily> implements PersistAutoCTable {

    private static final String INSERT = "insert into p_daily (id, data) values (?, ?)";

    private static final String UPDATE = "update p_daily set data = ? where id = ?";

    private static final String DELETE = "delete from p_daily where id = ?";

    @Override
    public String name() {
        return null;
    }

    @Override
    public int dataType() {
        return DataType.DAILYDATA;
    }

    @Override
    public String createInsertSql() {
        return INSERT;
    }

    @Override
    public String createUpdateSql() {
        return UPDATE;
    }

    @Override
    public String createDeleteSql() {
        return DELETE;
    }

    @Override
    public long taskPeriod() {
        return 60 * 1000;
    }

    @Override
    public String createTableSql() {
        return "CREATE TABLE IF NOT EXISTS  `p_daily`(" +
                "  `id` bigint(20) NOT NULL," +
                "  `data` mediumblob" +
                ", PRIMARY KEY (`id`)" +
                ") COMMENT='基于自动建表' ;";
    }
}
