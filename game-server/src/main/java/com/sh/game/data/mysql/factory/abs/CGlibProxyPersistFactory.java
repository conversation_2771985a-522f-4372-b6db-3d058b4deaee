package com.sh.game.data.mysql.factory.abs;

import com.sh.common.jdbc.SerializerUtil;
import com.sh.common.persist.Persistable;
import com.sh.common.persist.SimpleProtostuffPersistFactory;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.ParameterizedType;

/**
 * CGlibProxyPersistFactory 主要提供给 <b>使用了CGLib代理的实体</b> 使用
 * <p>
 * 由于外层使用cglib动态代理，Protostuff解析的时候，代理类的属性不符合要求，校验通不过。<br/>
 * 于是这里需要提供一个泛型，用于指定实际被代理的类型。
 * </p>
 * <AUTHOR>
 * @date 2020/12/21 13:12
 */
@Slf4j
public abstract class CGlibProxyPersistFactory<P extends Persistable> extends SimpleProtostuffPersistFactory {


    public Class<P> clazz;

    public CGlibProxyPersistFactory() {

        clazz = (Class<P>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];

    }

    @Override
    protected <T extends Persistable> byte[] serialize(T obj) {
        P persistable = (P) obj;
        byte[] bytes = null;
        for (int retry = 1; retry <= 10; retry ++) {
            try {
                bytes = SerializerUtil.encode(persistable, this.clazz);
                break;
            } catch (Throwable e) {
                log.error(String.format("数据 %d/%d 入库序列化失败，进行重试 -> %d ...", obj.dataType(), obj.getId(), retry), e);
            }
        }
        if (bytes == null) {
            log.error("Protostuff编码错误,data:{}/{}", obj.dataType(), obj.getId());
        }

        return bytes;
    }
}
