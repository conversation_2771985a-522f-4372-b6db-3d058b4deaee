package com.sh.game.log.entity;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.log.entity.abs.BaseRoleLog;
import lombok.Getter;
import lombok.Setter;

/**
 * description: 倒计时剧情活动埋点
 * date: 2024/7/19
 * author: chenBin
 */
@Getter
@Setter
public class RoleCountDownLog extends BaseRoleLog {
    /**
     * 倒计时开始时间
     */
    private int startTime;

    /**
     * 领取生效奖励时间，失效奖励不算
     */
    private int endTime;

    /**
     * 领奖状态 0-未领取 1-已领取
     */
    private int rewardStatus;

    /**
     * 活动ID
     */
    private int activityId;

    /**
     * 配置表id
     */
    private int storyCountId;

    public RoleCountDownLog(){}

    public RoleCountDownLog(Role role) {
        super(role);
    }
}
