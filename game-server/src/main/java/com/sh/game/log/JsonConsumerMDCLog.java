package com.sh.game.log;

import com.sh.engine.log.ILog;
import com.sh.engine.log.ILogConsumer;
import com.sh.engine.log.JsonLog;
import com.sh.game.log.entity.abs.BaseLog;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

/**
 * 带MDC的日志消费者
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2022/9/13.
 */
public class JsonConsumerMDCLog implements ILogConsumer<JsonLog> {

    /**
     * 总日志
     */
    private Logger logger = LoggerFactory.getLogger("game.log.json");



    public JsonConsumerMDCLog() {
    }

    public void init(ILog iLog) {
    }

    public void consume(JsonLog jsonLog) {

        if (jsonLog instanceof BaseLog) {
            MDC.put("logtype", ((BaseLog) jsonLog).getLogtype());
        }

        this.logger.info(jsonLog.toJSON());
        MDC.clear();

    }

}
