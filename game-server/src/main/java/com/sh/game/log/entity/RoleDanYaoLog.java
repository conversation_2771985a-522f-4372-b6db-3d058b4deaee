package com.sh.game.log.entity;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.log.entity.abs.BaseRoleLog;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class RoleDanYaoLog extends BaseRoleLog {

    /**
     * 玩家已学习的丹药天赋
     */
    private Map<Integer, Integer> danYaoPointsMap = new HashMap<>();

    /**
     * 剩余丹药天赋点
     */
    private int remainingPoints;

    /**
     * 当日使用丹药次数
     */
    private int usageCount;

    public RoleDanYaoLog(){}

    public RoleDanYaoLog(Role role){
        super(role);
    }

}
