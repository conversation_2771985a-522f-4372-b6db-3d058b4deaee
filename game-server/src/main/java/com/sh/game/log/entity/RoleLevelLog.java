package com.sh.game.log.entity;


import com.sh.game.common.entity.usr.Role;
import com.sh.game.log.entity.abs.BaseRoleLog;
import lombok.Getter;
import lombok.Setter;

/**
 * 角色等级
 */
@Getter
@Setter
public class RoleLevelLog extends BaseRoleLog {

    public RoleLevelLog(){

    }
    /**
     * 原等级
     */
    private int oldLevel;

    /**
     * 是否英雄
     */
    private int hero;

    /**
     * 新等级
     */
    private int newLevel;

    /**
     * 账号
     */
    private long userId;

    /**
     * 角色名
     */
    private String roleName;

    /**
     * 升级时间
     */
    private int levelTime;

    /**
     * 账户
     */
    private String account;

    public RoleLevelLog(Role role) {
        super(role);

    }
}
