package com.sh.game;

import com.sh.common.config.ConfigDataManager;
import com.sh.common.config.annotation.ConfigDataCustomization;
import com.sh.engine.log.LogBuilder;
import com.sh.engine.log.LogService;
import com.sh.game.auth.AuthFactory;
import com.sh.game.common.communication.msg.AllMessagePool;
import com.sh.game.common.config.check.ConfigLoadChecker;
import com.sh.game.common.constant.ServerOpenState;
import com.sh.game.common.env.AppContext;
import com.sh.game.common.msg.AutoRegisterRpcService;
import com.sh.game.common.util.IDUtil;
import com.sh.game.common.util.ProtostuffTagUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.filter.FilterUtil;
import com.sh.game.log.JsonConsumerMDCLog;
import com.sh.game.map.SceneModule;
import com.sh.game.notice.GameNoticeTransformer;
import com.sh.game.notice.NoticeCenter;
import com.sh.game.notice.NoticePool;
import com.sh.game.option.ServerOption;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.server.*;
import com.sh.game.system.merge.MergeManager;
import com.sh.game.system.schedule.ScheduleManager;
import com.sh.game.system.user.NameManager;
import com.sh.net.ClientPackageMonitor;
import com.sh.net.NetworkService;
import com.sh.net.NetworkServiceBuilder;
import com.sh.script.ScriptEngine;
import io.netty.channel.WriteBufferWaterMark;

/**
 * <AUTHOR>
 */
public class GameServer {

    /**
     * 游戏服
     */
    public static String GAME_DB = "GAME_DB";
    private NetworkService netWork;
    private boolean state = false;

    private ServerOpenState serverOpenState;

    private CommandRouter router;

    private ServerOption option;

    private AllMessagePool msgPool;

    private GameNoticeTransformer gameNoticeTransformer = new GameNoticeTransformer();

    private NoticeCenter noticeCenter;

    private SceneModule sceneModule;

    private NoticePool noticePool;

    public GameServer(ServerOption option) throws Exception {

        if (!ProtostuffTagUtil.check()) {
            throw new RuntimeException("Protostuff Tag重复，请检查...");
        }

        this.option = option;


        //创建各种消息、notice等组件
        this.noticeCenter = new NoticeCenter();
        this.msgPool = AllMessagePool.newInstance(ModuleConst.MODULE_STR_TO_INT);
        this.router = new LogicCommandRouter();

        this.noticePool = new NoticePool("com.sh.game.notice.action");

        // 初始化脚本系统
        ScriptEngine.load("com.sh.game.script");
        int bossLoopGroupCount = 4;
        int workerLoopGroupCount = Math.max(Runtime.getRuntime().availableProcessors(), 12);
        NetworkServiceBuilder builder = new NetworkServiceBuilder();
        builder.setMsgPool(msgPool);
        builder.setBossLoopGroupCount(bossLoopGroupCount);
        builder.setWorkerLoopGroupCount(workerLoopGroupCount);
        builder.setNetworkEventlistener(new GameNetworkEventListener());
        builder.setPort(option.getGameServerPort());
        builder.setSsl(option.isSsl());
        builder.setSslKeyCertChainFile(option.getSslKeyCertChainFile());
        builder.setSslKeyFile(option.getSslKeyFile());
        //高低水位
        builder.setWriteBufferWaterMark(new WriteBufferWaterMark(512 * 1024, 1024 * 1024));
        //builder.setIdleMaxTime(5 * 60 * 1000);
        //不设置该参数，默认是关闭功能
        builder.setClientPackageMonitorOption(new ClientPackageMonitor.DefaultOpenMonitorOption() {
            public int rateMax() {
                //每秒多少个，默认是30，这里面可以写自己的，比如说这里写了15
                return 50;
            }
        });
        builder.setConsumer(GameContext.getModuleRouter());
        builder.setWebSocket(true);
        // 创建网络服务
        netWork = builder.createService();

        // 初始化AppContext
        AppContext.setContext(LogicContext.getInstance());
        //初始化ID工具类
        IDUtil.init(option.getServerId(), option.getPlatformId());

        //初始化配置中心
        ConfigDataManager.getInstance().init(
                ConfigDataCustomization.newInstance()
                        .setPkg("com.sh.game.common.config")
                        .setSkipLine(2)
                        .setChecker(ConfigLoadChecker.getInstance())
                        .setPath(option.getConfigDataPath())
        );

        //初始化数据中心
        SysDataProvider.init();

        //启动日志服务
        LogService.start(LogBuilder.builder().setLogPackagePath("com.sh.game.log")
                .registerConsumer(new JsonConsumerMDCLog()));

        // 初始化AppContext
//        AppContext.setContext(LogicContext.getInstance());
    }

    public CommandRouter getRouter() {
        return this.router;
    }

    public void load() throws Exception {
        //敏感词
        FilterUtil.init();

        DataCenter.init(option);

        //合服初始化
        MergeManager.getInstance().init();


        //加载玩家昵称
        NameManager.getInstance().loadName();

        //初始化代理地图
        MapProxyManager.getInstance().init();

        //logic模块注册
        LogicModule logicModule = new LogicModule(router);
        AutoRegisterRpcService.initHandler(logicModule.getHandlerPool(), "com.sh.game");

        GameContext.getModuleRouter().addModule(logicModule);

        AuthFactory.init();

        //开启计划任务
        ScheduleManager.getInstance().start();
    }


    public void start() {
        netWork.start();
        if (netWork.isRunning()) {
            state = true;
        }
        serverOpenState = ServerOpenState.valueOf(DataCenter.getOtherData().getServerState());
    }

    public boolean isOpen() {
        return state;
    }

    public void stop() {
        netWork.stop();
        state = false;
    }

    public ServerOption getOption() {
        return option;
    }

    public ServerOpenState getServerOpenState() {
        return serverOpenState;
    }

    public void setServerOpenState(ServerOpenState serverOpenState) {
        this.serverOpenState = serverOpenState;
    }

    public AllMessagePool getMsgPool() {
        return msgPool;
    }

    public void setMsgPool(AllMessagePool msgPool) {
        this.msgPool = msgPool;
    }

    public NoticePool getNoticePool() {
        return noticePool;
    }

    public GameNoticeTransformer getGameNoticeTransformer() {
        return gameNoticeTransformer;
    }

    public void setGameNoticeTransformer(GameNoticeTransformer gameNoticeTransformer) {
        this.gameNoticeTransformer = gameNoticeTransformer;
    }

    public NoticeCenter getNoticeCenter() {
        return noticeCenter;
    }

    public void setNoticeCenter(NoticeCenter noticeCenter) {
        this.noticeCenter = noticeCenter;
    }

    public SceneModule getSceneModule() {
        return sceneModule;
    }

    public void setSceneModule(SceneModule sceneModule) {
        this.sceneModule = sceneModule;
    }
}
