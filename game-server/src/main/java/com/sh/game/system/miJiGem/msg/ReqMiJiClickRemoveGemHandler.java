package com.sh.game.system.miJiGem.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.MiJiGemProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.miJiGem.MiJiGemManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.miJiGem.ReqMiJiClickRemoveGemMessage;

/**
 * <p>请求一键卸下宝石</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqMiJiClickRemoveGemHandler extends AbstractHandler<ReqMiJiClickRemoveGemMessage> {

    @Override
    public void doAction(ReqMiJiClickRemoveGemMessage msg) {
        MiJiGemProtos.ReqMiJiClickRemoveGem miJiClickRemoveGem = msg.getProto();
        MiJiGemManager.getInstance().reqMiJiOneClickRemoveGem(SessionUtil.getRole(msg), miJiClickRemoveGem.getEquipIndex());
    }

}
