package com.sh.game.system.trade.msg;

import com.sh.game.common.communication.msg.system.trade.ReqTradeCancelMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.trade.TradeManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求取消交易</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_SOCIAL)
public class ReqTradeCancelHandler extends AbstractHandler<ReqTradeCancelMessage> {

    @Override
    public void doAction(ReqTradeCancelMessage msg) {
        TradeManager.getInstance().reqTradeCancel(SessionUtil.getRole(msg.getSession()).getId());
    }

}
