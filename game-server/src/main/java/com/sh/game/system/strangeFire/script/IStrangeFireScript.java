package com.sh.game.system.strangeFire.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

public interface IStrangeFireScript extends IScript {

    /**
     * 异火升级
     * @param role
     */
    void reqStrangeFireUp(Role role, int type);

    /**
     * 异火升阶
     * @param role
     * @param type
     */
    void reqStrangeFireShengJie(Role role, int type);

    void reqStrangeFireInfo(Role role);

    /**
     * 请求进入异火塔
     *
     * @param role
     */
    void reqIntoFireTowerDuplicate(Role role);

    /**
     * 请求购买异火塔次数
     *
     * @param role
     */
    void reqBuyFireTowerCount(Role role);
}
