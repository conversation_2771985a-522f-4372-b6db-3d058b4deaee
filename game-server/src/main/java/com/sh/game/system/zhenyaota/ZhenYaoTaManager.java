package com.sh.game.system.zhenyaota;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.zhenyaota.script.IZhenYaoTaScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2023/12/13
 */
public class ZhenYaoTaManager {

    private static final ZhenYaoTaManager INSTANCE = new ZhenYaoTaManager();

    public static ZhenYaoTaManager getInstance() {
        return INSTANCE;
    }

    /**
     * 请求镇妖塔信息
     */
    public void reqZhenYaoTaInfo(Role role) {
        ScriptEngine.invoke1t1(IZhenYaoTaScript.class, s -> s.reqZhenYaoTaInfo(role));
    }

    /**
     * 请求镇妖塔战斗
     */
    public void reqZhenYaoTaBattle(Role role) {
        ScriptEngine.invoke1t1(IZhenYaoTaScript.class, s -> s.reqZhenYaoTaBattle(role));
    }

    /**
     * 请求镇妖塔扫荡
     */
    public void reqZhenYaoTaSweep(Role role) {
        ScriptEngine.invoke1t1(IZhenYaoTaScript.class, s -> s.reqZhenYaoTaSweep(role));
    }

    /**
     * 请求镇妖塔buff信息
     */
    public void reqZhenYaoTaBuffSelectInfo(Role role, int barrierId) {
        ScriptEngine.invoke1t1(IZhenYaoTaScript.class, s -> s.reqZhenYaoTaBuffSelectInfo(role, barrierId));
    }

    /**
     * 请求镇妖塔buff选择
     */
    public void reqZhenYaoTaBuffSelect(Role role, int buffId, int replaceBuffId) {
        ScriptEngine.invoke1t1(IZhenYaoTaScript.class, s -> s.reqZhenYaoTaBuffSelect(role, buffId, replaceBuffId));
    }

    /**
     * 请求镇妖塔排行榜信息
     */
    public void reqZhenYaoTaRankInfo(Role role) {
        ScriptEngine.invoke1t1(IZhenYaoTaScript.class, s -> s.reqZhenYaoTaRankInfo(role));
    }
}
