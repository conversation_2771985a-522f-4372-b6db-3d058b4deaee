package com.sh.game.system.daily.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.daily.impl.DailyBossChallengeManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.daily.ReqDailyBossChallengeInfoMessage;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqDailyBossChallengeInfoHandler extends AbstractHandler<ReqDailyBossChallengeInfoMessage> {

    @Override
    public void doAction(ReqDailyBossChallengeInfoMessage msg) {
        DailyBossChallengeManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()));
    }

}
