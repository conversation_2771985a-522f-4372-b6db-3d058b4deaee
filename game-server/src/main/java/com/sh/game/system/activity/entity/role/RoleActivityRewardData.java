package com.sh.game.system.activity.entity.role;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色活动奖励领取数据
 *
 * <AUTHOR>
 * @since 2022/5/10 15:55
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleActivityRewardData extends AbstractRoleEntity {

    /**
     * 角色id
     */
    @Tag(1)
    private long id;

    /**
     * 已领取列表
     */
    @Tag(2)
    private Map<Integer, List<Integer>> rewardDataMap = new HashMap<>();
}
