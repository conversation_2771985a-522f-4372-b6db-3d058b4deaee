package com.sh.game.system.zhenYan.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.zhenYan.ZhenYanManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.zhenYan.ReqZhenYanTowerInfoMessage;

/**
 * <p>请求真言塔信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqZhenYanTowerInfoHandler extends AbstractHandler<ReqZhenYanTowerInfoMessage> {

    @Override
    public void doAction(ReqZhenYanTowerInfoMessage msg) {
        ZhenYanManager.getInstance().reqZhenYanTowerInfo(SessionUtil.getRole(msg));
    }

}
