package com.sh.game.system.role.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.role.RoleManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.role.ReqRoleGoldCountInfoMessage;

/**
 * <p>请求刀刀元宝当日已获取数信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqRoleGoldCountInfoHandler extends AbstractHandler<ReqRoleGoldCountInfoMessage> {

    @Override
    public void doAction(ReqRoleGoldCountInfoMessage msg) {
        RoleManager.getInstance().reqGoldCountInfo(SessionUtil.getRole(msg));
    }

}
