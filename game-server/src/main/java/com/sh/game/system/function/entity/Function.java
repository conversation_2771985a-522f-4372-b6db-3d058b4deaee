package com.sh.game.system.function.entity;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @data 2020/10/27 16:32
 */
@Getter
@Setter
public class Function {
    @Tag(1)
    private int count;

    @Tag(2)
    private List<Integer> list = new ArrayList<>();

    public Function(int count, List<Integer> list) {
        this.count = count;
        this.list = list;
    }

    public Function() {
    }
}
