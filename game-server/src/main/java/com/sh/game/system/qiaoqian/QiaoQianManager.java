package com.sh.game.system.qiaoqian;

import com.sh.game.common.config.model.QiaoQianShouGouConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.data.DataCenter;
import com.sh.game.system.qiaoqian.entity.RoleQiaoQian;
import com.sh.game.system.qiaoqian.script.IQiaoQianScript;
import com.sh.script.ScriptEngine;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/12/3 15:13
 */
public class QiaoQianManager {
    private static final QiaoQianManager INSTANCE = new QiaoQianManager();
    public static QiaoQianManager getInstance() {
        return INSTANCE;
    }
    private QiaoQianManager() {
    }
    public RoleQiaoQian findQiaoQian(long roleId) {
        RoleQiaoQian roleQiaoQian = DataCenter.get(RoleQiaoQian.class, roleId);
        if (roleQiaoQian == null) {
            roleQiaoQian = new RoleQiaoQian();
            roleQiaoQian.setId(roleId);
            DataCenter.insertData(roleQiaoQian,true);
        }
        return roleQiaoQian;
    }

    public void reqQiaoQianInfo(Role role) {
        ScriptEngine.invoke1t1(IQiaoQianScript.class, script -> script.reqQiaoQianInfo(role));
    }

    public void reqQiaoQianShouGou(Role role, long targetId) {
        ScriptEngine.invoke1t1(IQiaoQianScript.class, script -> script.reqQiaoQianShouGou(role, targetId));
    }

    public void reqQiaoQianView(Role role, int type) {
        ScriptEngine.invoke1t1(IQiaoQianScript.class, script -> script.reqQiaoQianView(role, type));
    }

    public void reqQiaoQianLog(Role role) {
        ScriptEngine.invoke1t1(IQiaoQianScript.class, script -> script.reqQiaoQianLog(role));
    }

    public void reqQiaoQianDianZan(Role role, int type) {
        ScriptEngine.invoke1t1(IQiaoQianScript.class, script -> script.reqQiaoQianDianZan(role, type));
    }



    public QiaoQianShouGouConfig getQiaoQianRank(long roleId) {
        return ScriptEngine.invoke1t1WithRet(IQiaoQianScript.class, script -> script.getQiaoQianRank(roleId));
    }
}
