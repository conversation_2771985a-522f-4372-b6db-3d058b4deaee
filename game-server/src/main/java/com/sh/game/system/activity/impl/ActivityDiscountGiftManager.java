package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityDiscountGiftScript;
import com.sh.script.ScriptEngine;

/**
 * 限时特惠礼包
 */
public class ActivityDiscountGiftManager {
    private static final ActivityDiscountGiftManager INSTANCE = new ActivityDiscountGiftManager();

    private ActivityDiscountGiftManager() {

    }

    public static ActivityDiscountGiftManager getInstance() {
        return INSTANCE;
    }


    public void reqInfo(Role role) {
        ScriptEngine.invoke1t1(IActivityDiscountGiftScript.class, script -> script.reqInfo(role));
    }

    public void reqAcquire(Role role, int cid) {
        ScriptEngine.invoke1t1(IActivityDiscountGiftScript.class, script -> script.reqAcquire(role, cid));
    }
}
