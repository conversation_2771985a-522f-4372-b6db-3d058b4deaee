package com.sh.game.system.mainbatt.msg;

import com.sh.game.common.communication.msg.system.mainbatt.ReqMainBattInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.mainbatt.MainBattManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求打怪信息/p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqMainBattInfoHandler extends AbstractHandler<ReqMainBattInfoMessage> {
    @Override
    public void doAction(ReqMainBattInfoMessage msg) {
        MainBattManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()));
    }

}
