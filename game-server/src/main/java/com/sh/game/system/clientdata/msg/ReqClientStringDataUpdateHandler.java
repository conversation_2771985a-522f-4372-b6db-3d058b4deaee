package com.sh.game.system.clientdata.msg;

import com.sh.game.common.communication.msg.system.clientdata.ReqClientStringDataUpdateMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.clientdata.ClientDataManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqClientStringDataUpdateHandler extends AbstractHandler<ReqClientStringDataUpdateMessage> {

    @Override
    public void doAction(ReqClientStringDataUpdateMessage msg) {
        ClientDataManager.getInstance().reqClientStringDataUpdate(SessionUtil.getRole(msg), msg.getProto().getClientStringDataBeanList());
    }

}
