package com.sh.game.system.count.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.constant.CountConst;
import com.sh.game.common.util.ActionCountLimitUtil;
import com.sh.script.IScript;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 *
 * @author: xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2018/3/21 16:35
 * desc  :
 * Copyright(©) 2017 by xiaomo.
 */
public interface ICountScript extends IScript {

    int getCount(Role role, CountConst.CountType countType, int key);

    int count(Role role, CountConst.CountType countType, int key, int add);

    void setCount(Role role, CountConst.CountType countType, int key, int value);

	int verifyRemainderCount(Role role, int count, ActionCountLimitUtil.Action action);

    void renameCount(Role role);
}
