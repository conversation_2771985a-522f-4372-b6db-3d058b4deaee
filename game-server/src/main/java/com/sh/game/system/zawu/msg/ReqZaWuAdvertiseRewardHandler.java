package com.sh.game.system.zawu.msg;

import com.sh.game.common.communication.msg.system.zawu.ReqZaWuAdvertiseRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.zawu.ZaWuManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求广告通关奖励翻倍</p>
* <p>Created by MessageUtil</p>
* @date 2024-09-24 下午4:39:51
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqZaWuAdvertiseRewardHandler extends AbstractHandler<ReqZaWuAdvertiseRewardMessage> {

    @Override
    public void doAction(ReqZaWuAdvertiseRewardMessage msg) {
        ZaWuManager.getInstance().reqZaWuAdvertiseReward(SessionUtil.getRole(msg), msg.getProto().getStage());
    }
}