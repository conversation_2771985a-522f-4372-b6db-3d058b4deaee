package com.sh.game.system.imperialedictchallenge.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.imperialedictchallenge.EdictChallengeManage;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.imperialedictchallenge.ReqAbandonEdictMessage;

/**
 * <p>请求放弃挑战</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqAbandonEdictHandler extends AbstractHandler<ReqAbandonEdictMessage> {

    @Override
    public void doAction(ReqAbandonEdictMessage msg) {
        EdictChallengeManage.getInstance().abandonEdict(SessionUtil.getRole(msg));
    }

}
