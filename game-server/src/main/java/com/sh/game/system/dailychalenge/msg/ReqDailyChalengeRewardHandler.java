package com.sh.game.system.dailychalenge.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.DailychalengeProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.dailychalenge.DailyChalengeManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.dailychalenge.ReqDailyChalengeRewardMessage;

/**
 * <p>请求领取奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqDailyChalengeRewardHandler extends AbstractHandler<ReqDailyChalengeRewardMessage> {

    @Override
    public void doAction(ReqDailyChalengeRewardMessage msg) {
        DailychalengeProtos.ReqDailyChalengeReward dailyChalenge = msg.getProto();
        DailyChalengeManager.getInstance().reqReward(SessionUtil.getRole(msg.getSession()), dailyChalenge.getMul());
    }

}
