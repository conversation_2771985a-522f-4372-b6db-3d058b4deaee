package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityCrossRankManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqActivityCrossRankInfoMessage;

/**
 * <p>请求跨服活动排行榜信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqActivityCrossRankInfoHandler extends AbstractHandler<ReqActivityCrossRankInfoMessage> {

    @Override
    public void doAction(ReqActivityCrossRankInfoMessage msg) {
        ActivityCrossRankManager.getInstance().findRanks(SessionUtil.getRole(msg));
    }

}
