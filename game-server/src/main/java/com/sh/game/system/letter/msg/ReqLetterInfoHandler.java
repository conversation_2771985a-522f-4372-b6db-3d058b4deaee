package com.sh.game.system.letter.msg;

import com.sh.game.common.communication.msg.system.letter.ReqLetterInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.letter.LetterManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求来信信息</p>
* <p>Created by MessageUtil</p>
* @date 2024-11-06 上午11:37:44
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqLetterInfoHandler extends AbstractHandler<ReqLetterInfoMessage> {

    @Override
    public void doAction(ReqLetterInfoMessage msg) {
        LetterManager.getInstance().reqLetterList(SessionUtil.getRole(msg));
    }
}