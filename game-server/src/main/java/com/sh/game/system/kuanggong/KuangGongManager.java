package com.sh.game.system.kuanggong;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.kuanggong.script.IKuangGongScript;
import com.sh.script.ScriptEngine;

/**
 * 矿工管理类
 *
 * <AUTHOR>
 * @since 2024-09-13
 */
public class KuangGongManager {

    private static final KuangGongManager INSTANCE = new KuangGongManager();

    public static KuangGongManager getInstance() {
        return INSTANCE;
    }

    public void reqWaKuang(Role role, int cid) {
        ScriptEngine.invoke1t1(IKuangGongScript.class, s -> s.req<PERSON><PERSON><PERSON><PERSON>(role, cid));
    }

    public void reqSettle(Role role, int rate, int fail) {
        ScriptEngine.invoke1t1(IKuangGongScript.class, s -> s.reqSettle(role, rate, fail));
    }

    public void cleanData(Role role) {
        ScriptEngine.invoke1t1(IKuangGongScript.class, s -> s.cleanData(role));
    }

    public void reqWaKuangLimit(Role role) {
        ScriptEngine.invoke1t1(IKuangGongScript.class, s -> s.reqWaKuangLimit(role));
    }
}
