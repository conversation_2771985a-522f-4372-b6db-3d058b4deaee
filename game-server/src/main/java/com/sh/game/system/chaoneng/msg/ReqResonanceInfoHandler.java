package com.sh.game.system.chaoneng.msg;

import com.sh.game.common.communication.msg.system.chaoneng.ReqResonanceInfo;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.chaoneng.ChaoNengManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqResonanceInfoHandler extends AbstractHandler<ReqResonanceInfo> {
    @Override
    public void doAction(ReqResonanceInfo msg) {
        ChaoNengManager.getInstance().sendResonanceInfo(SessionUtil.getRole(msg));
    }
}
