package com.sh.game.system.school.msg;

import com.sh.game.common.communication.msg.system.school.ReqServerGraduateInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.school.SchoolManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqServerGraduateInfoMessageHandler extends AbstractHandler<ReqServerGraduateInfoMessage> {
    @Override
    public void doAction(ReqServerGraduateInfoMessage msg) {
        SchoolManager.getInstance().sendServerGraduateMsg(SessionUtil.getRole(msg.getSession()));
    }
}
