package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqActivityPurchasePresentAcquireDaysMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityPurchasePresentManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求领取天数累计奖励</p>
 * <p>Created by MessageUtil</p>
 * @date 2020-08-18 19:57:38
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqActivityPurchasePresentAcquireDaysHandler extends AbstractHandler<ReqActivityPurchasePresentAcquireDaysMessage> {

    @Override
    public void doAction(ReqActivityPurchasePresentAcquireDaysMessage msg) {
        ActivityPurchasePresentManager.getInstance().reqAcquireDays(SessionUtil.getRole(msg.getSession()));
    }

}
