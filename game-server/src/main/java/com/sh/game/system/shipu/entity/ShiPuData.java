package com.sh.game.system.shipu.entity;

import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/16 15:26
 */
@Getter
@Setter
public class ShiPuData {
    @Tag(71)
    private Map<Integer, Integer> levelMap = new HashMap<>();

    //广告临时变量
    @Exclude
    private int levelAdvertise;
}
