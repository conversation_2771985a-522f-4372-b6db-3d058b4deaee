package com.sh.game.system.newbarrier;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.newbarrier.script.INewBarrierScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2023/9/19
 */
public class NewBarrierManager {

    private static final NewBarrierManager INSTANCE = new NewBarrierManager();

    public static NewBarrierManager getInstance() {
        return INSTANCE;
    }

    public void reqBarrierBattle(Role role) {
        ScriptEngine.invoke1t1(INewBarrierScript.class, s -> s.reqBarrierBattle(role));
    }

    public void reqBarrierBattleInfo(Role role) {
        ScriptEngine.invoke1t1(INewBarrierScript.class, s -> s.reqBarrierBattleInfo(role));
    }
}
