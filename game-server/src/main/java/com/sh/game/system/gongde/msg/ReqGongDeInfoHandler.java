package com.sh.game.system.gongde.msg;

import com.sh.game.common.communication.msg.system.gongde.ReqGongDeInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.gongde.GongDeManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求装备或者替换符文</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqGongDeInfoHandler extends AbstractHandler<ReqGongDeInfoMessage> {

    @Override
    public void doAction(ReqGongDeInfoMessage msg) {
        GongDeManager.getInstance().info(SessionUtil.getRole(msg.getSession()));
    }

}
