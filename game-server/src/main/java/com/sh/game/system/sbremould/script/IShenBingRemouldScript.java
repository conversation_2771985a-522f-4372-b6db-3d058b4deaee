package com.sh.game.system.sbremould.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.sbremould.entity.RoleShenBingRemould;
import com.sh.script.IScript;

/**
 * 神兵打造
 *
 * <AUTHOR>
 * @date 2022/09/20 21:39
 */
public interface IShenBingRemouldScript extends IScript {

    /**
     * 根据角色id查询神兵打造信息
     *
     * @param roleId 角色id
     * @return RoleShenBingRemould 神兵打造信息
     */
    RoleShenBingRemould find(long roleId);

    /**
     * 请求打造信息
     *
     * @param role 角色
     */
    void reqInfo(Role role);

    /**
     * 请求打造
     *
     * @param role  角色
     * @param pos   部位
     */
    void reqRemould(Role role, int pos);
}
