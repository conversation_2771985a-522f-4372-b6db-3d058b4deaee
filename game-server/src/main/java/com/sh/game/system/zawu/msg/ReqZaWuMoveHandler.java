package com.sh.game.system.zawu.msg;

import com.sh.game.common.communication.msg.system.zawu.ReqZaWuMoveMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ZaWuProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.zawu.ZaWuManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求杂屋移动</p>
* <p>Created by MessageUtil</p>
* @date 2024-09-24 下午4:39:51
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqZaWuMoveHandler extends AbstractHandler<ReqZaWuMoveMessage> {

    @Override
    public void doAction(ReqZaWuMoveMessage msg) {
        ZaWuProtos.ReqZaWuMoveMessage proto = msg.getProto();
        ZaWuManager.getInstance().reqZaWuMove(SessionUtil.getRole(msg), proto.getFromIndex(), proto.getToIndex());
    }
}