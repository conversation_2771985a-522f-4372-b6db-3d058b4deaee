package com.sh.game.system.equip.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.EquipProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equip.EquipManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.equip.ReqEquipChaoticInlayMessage;

/**
 * <p>请求混沌装备附魂</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqEquipChaoticInlayHandler extends AbstractHandler<ReqEquipChaoticInlayMessage> {

    @Override
    public void doAction(ReqEquipChaoticInlayMessage msg) {
        EquipProtos.ReqEquipChaoticInlay equipChaoticInlay = msg.getProto();
        EquipManager.getInstance().reqChaoticInlay(SessionUtil.getRole(msg), equipChaoticInlay.getEquipId(),
                equipChaoticInlay.getChaoticGemId());
    }

}
