package com.sh.game.system.team.msg;

import com.sh.game.common.communication.msg.system.team.ReqReplyInvitedMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.TeamProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.team.TeamManager;
import com.sh.server.AbstractHandler;

/**
 * <p>答复邀请请求</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_SOCIAL)
public class ReqReplyInvitedHandler extends AbstractHandler<ReqReplyInvitedMessage> {

    @Override
    public void doAction(ReqReplyInvitedMessage msg) {
        TeamProtos.ReqReplyInvited proto = msg.getProto();
        TeamManager.getInstance().reqReplyInvited(SessionUtil.getRole(msg), proto.getRid(), proto.getState());
    }

}
