package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityZhiZunPackScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2022/4/26 17:46
 */
public class ActivityZhiZunPackManager {

    private static final ActivityZhiZunPackManager INSTANCE = new ActivityZhiZunPackManager();

    public static ActivityZhiZunPackManager getInstance() {
        return INSTANCE;
    }

    public void gainReward(Role role, int cfgId) {
        ScriptEngine.invoke1t1(IActivityZhiZunPackScript.class, s -> s.gainReward(role, cfgId));
    }
}
