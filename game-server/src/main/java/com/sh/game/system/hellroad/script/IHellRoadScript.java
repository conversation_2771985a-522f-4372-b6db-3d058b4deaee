package com.sh.game.system.hellroad.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

import java.util.Map;

/**
 * 地狱之路
 *
 * <AUTHOR>
 * @date 2022/09/13 10:05
 */
public interface IHellRoadScript extends IScript {

    /**
     * 请求地狱之路信息
     *
     * @param role 角色
     */
    void reqInfo(Role role);

    /**
     * 请求击杀数领奖
     *
     * @param role      角色
     * @param configId  奖励配置id
     */
    void reqKillReward(Role role, int configId);

    /**
     * 同步地狱之路信息
     *
     * @param role      角色
     * @param addScore  增加的积分
     * @param killCount 击杀数
     */
    void syncInfo(Role role, int addScore, int killCount);

    /**
     * 发送结算榜单奖励
     *
     * @param type  榜单类型
     * @param rank  排行榜数据
     */
    void sendRankReward(int type, Map<Long, Integer> rank);
}
