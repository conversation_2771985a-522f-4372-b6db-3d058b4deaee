package com.sh.game.system.union.msg;

import com.sh.game.common.communication.msg.system.union.ReqKickOutMemberMessage;
import com.sh.game.protos.UnionProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求踢出玩家</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 15:51:41
 */
public class ReqKickOutMemberHandler extends AbstractHandler<ReqKickOutMemberMessage> {

    @Override
    public void doAction(ReqKickOutMemberMessage msg) {
        UnionProtos.ReqKickOutMember proto = msg.getProto();
        UnionManager.getInstance().removeMember(SessionUtil.getRole(msg.getSession()), proto.getMemberId());

    }

}
