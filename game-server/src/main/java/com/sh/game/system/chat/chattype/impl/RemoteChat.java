package com.sh.game.system.chat.chattype.impl;

import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.system.chat.ResChatMessage;
import com.sh.game.common.communication.notice.RemoteChatNotice;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.remote.rpc.RPCConnection;
import com.sh.game.remote.rpc.client.ModuleClient;
import com.sh.game.server.ConnectionConst;
import com.sh.game.system.chat.chattype.IChat;

import java.util.List;

public class RemoteChat implements IChat {
    @Override
    public void chat(Role role, long target, ResChatMessage msg, int banFlag) {
        msg.getProto().getChat().toBuilder().setHostId(role.getSid());
        ModuleClient client = GameContext.getGameServer().getSceneModule().getClient();
        List<RPCConnection> connList = client.getConnectionListByType(ConnectionConst.TYPE.CROSS);
        RemoteChatNotice notice = new RemoteChatNotice();
        notice.setMsg(msg);
        connList.forEach(connection -> {
            notice.addHost(connection.getHostId());
        });
        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.MAP_COMMON, notice, 0);
    }

}
