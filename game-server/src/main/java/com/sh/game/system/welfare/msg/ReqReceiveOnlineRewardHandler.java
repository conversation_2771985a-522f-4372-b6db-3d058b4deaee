package com.sh.game.system.welfare.msg;

import com.sh.game.protos.WelfareProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.welfare.OnlineRewardManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.welfare.ReqReceiveOnlineRewardMessage;

/**
 * <p>领取在线奖励</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-31 20:13:03
 */
public class ReqReceiveOnlineRewardHandler extends AbstractHandler<ReqReceiveOnlineRewardMessage> {

    @Override
    public void doAction(ReqReceiveOnlineRewardMessage msg) {
        WelfareProtos.ReqReceiveOnlineReward proto = msg.getProto();
        OnlineRewardManager.getInstance().receiveReward(SessionUtil.getRole(msg.getSession()), proto.getRewardId());
    }

}
