package com.sh.game.system.friend.msg;

import com.sh.game.common.communication.msg.system.friend.ReqFriendUnlockMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.FriendsProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.friend.FriendManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求当前封号信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqFriendUnlockHandler extends AbstractHandler<ReqFriendUnlockMessage> {

    @Override
    public void doAction(ReqFriendUnlockMessage msg) {
        FriendsProtos.ReqFriendUnlock proto = msg.getProto();
        FriendManager.getInstance().unlockFriend(SessionUtil.getRole(msg.getSession()), proto.getCfgId());
    }


}
