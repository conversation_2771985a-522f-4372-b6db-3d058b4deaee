package com.sh.game.system.specialRing.msg;

import com.sh.game.common.communication.msg.system.specialRing.ReqSplitRingMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.SpecialRingProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.specialRing.SpecialRingManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求拆分</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqSplitRingHandler extends AbstractHandler<ReqSplitRingMessage> {

    @Override
    public void doAction(ReqSplitRingMessage msg) {
        SpecialRingProtos.ReqSplitRing proto = msg.getProto();
        SpecialRingManager.getInstance().reqSplitRing(SessionUtil.getRole(msg.getSession()), proto.getEquipId(), proto.getType(), proto.getSplitType());
    }

}
