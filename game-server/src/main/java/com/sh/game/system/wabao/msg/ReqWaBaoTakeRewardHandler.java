package com.sh.game.system.wabao.msg;

import com.sh.game.common.communication.msg.system.wabao.ReqWaBaoTakeRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityWaBaoManager;
import com.sh.server.AbstractHandler;

/**
 * <p>全部取出</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqWaBaoTakeRewardHandler extends AbstractHandler<ReqWaBaoTakeRewardMessage> {

    @Override
    public void doAction(ReqWaBaoTakeRewardMessage msg) {
        ActivityWaBaoManager.getInstance().reqTake(SessionUtil.getRole(msg.getSession()));
    }

}
