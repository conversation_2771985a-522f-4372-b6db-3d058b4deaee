package com.sh.game.system.school.msg;

import com.sh.game.common.communication.msg.system.school.ReqStudentApplyInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.school.SchoolManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqStudentApplyInfoMessageHandler extends AbstractHandler<ReqStudentApplyInfoMessage> {
    @Override
    public void doAction(ReqStudentApplyInfoMessage msg) {
        SchoolManager.getInstance().sendStudentApplyMsg(SessionUtil.getRole(msg.getSession()), msg.getProto().getType());
    }
}
