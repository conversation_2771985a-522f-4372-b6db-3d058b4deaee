package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.RechargeConfig;
import com.sh.game.common.constant.RechargeConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.recharge.RechargeManager;
import com.sh.game.system.using.ItemUsage;
import lombok.extern.log4j.Log4j2;

import java.util.List;

@Log4j2
public class ItemUsageForRecharge extends ItemUsage {

    @Override
    public int useCount(Role role, Item item, ItemConfig config, int count) {
        return 1;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int id = config.getUseParam()[0][0];
        log.info("玩家{} {}apply充值,getRechargeConfigId={}", role.getId(), role.getName(), id);
        RechargeConfig rechargeConfig = null;
        for (RechargeConfig cfg : ConfigDataManager.getInstance().getList(RechargeConfig.class)) {
            if (cfg.getId() == id) {
                rechargeConfig = cfg;
                break;
            }
        }
        if (rechargeConfig == null) {
            log.error("recharge config not found: {}", config.getUseParam()[0][0]);
            return false;
        }

        RechargeManager.getInstance().recharge(role, rechargeConfig, RechargeConst.source.card, 0);
        return true;
    }

    @Override
    public int getUsedType() {
        return 150;
    }
}
