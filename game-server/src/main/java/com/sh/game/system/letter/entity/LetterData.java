package com.sh.game.system.letter.entity;

import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/11/6 10:58
 */
@Data
public class LetterData {
    /** letterCfgId-List<letterStoryId> */
    @Tag(30)
    private Map<Integer, List<Integer>> storys = new HashMap<>();
    @Tag(31)
    private List<Integer> rewardAdvertises = new ArrayList<>();
    @Exclude
    private int advertiseStoryId;
}
