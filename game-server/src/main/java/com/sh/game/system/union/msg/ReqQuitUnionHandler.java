package com.sh.game.system.union.msg;

import com.sh.game.common.communication.msg.system.union.ReqQuitUnionMessage;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求退出帮会信息</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 15:51:41
 */
public class ReqQuitUnionHandler extends AbstractHandler<ReqQuitUnionMessage> {

    @Override
    public void doAction(ReqQuitUnionMessage msg) {
        UnionManager.getInstance().quitUnion(SessionUtil.getRole(msg.getSession()));

    }

}
