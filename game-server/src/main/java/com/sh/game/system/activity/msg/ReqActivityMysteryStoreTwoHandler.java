package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityMysteryStoresManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqActivityMysteryStoreTwoMessage;

/**
 * <p>请求神秘商店2</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqActivityMysteryStoreTwoHandler extends AbstractHandler<ReqActivityMysteryStoreTwoMessage> {

    @Override
    public void doAction(ReqActivityMysteryStoreTwoMessage msg) {
        ActivityMysteryStoresManager.getInstance().reqInfo(SessionUtil.getRole(msg));
    }

}
