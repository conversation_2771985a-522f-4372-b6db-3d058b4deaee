package com.sh.game.system.jiutian.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @date 2022/8/3 13:07
 */
public interface IJiuTianScript extends IScript {

    /**
     * 请求进入九天之巅地图
     *
     * @param role
     * @param cfgId jiutian表id
     */
    void reqEnterJiuTianMap(Role role, int cfgId);

    /**
     * 请求九天之巅杀人数奖励
     *
     * @param role
     * @param cfgId jiutian表id
     */
    void reqJiuTianKillPlayerReward(Role role, int cfgId);

    /**
     * 增加九天积分
     * @param role
     * @param jiFen
     */
    void addJiuTianJiFen(Role role, int jiFen);

    /**
     * 设置九天杀人数
     * @param role
     * @param count
     */
    void setKillCount(Role role,int count);

    /**
     * 请求个人九天详情
     * @param role
     */
    void reqJiuTianIn<PERSON>(Role role);

}
