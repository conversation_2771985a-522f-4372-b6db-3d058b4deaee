package com.sh.game.system.chat.chattype.impl;

import com.sh.game.common.communication.msg.system.chat.ResChatMessage;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.system.chat.chattype.IChat;
import com.sh.game.system.union.UnionManager;

public class UnionChat implements IChat {

    /**
     * 行会聊天
     *
     * @param role   发送者
     * @param target 接收者
     * @param msg    消息内容
     */
    @Override
    public void chat(Role role, long target, ResChatMessage msg, int banFlag) {
        if (target == 0 && role != null) {
            target = role.getUnion().getId();
        }
        // 获取行会信息
        Union union = UnionManager.getInstance().getUnion(target);
        if (union == null) {
            return;
        }
        if (banFlag == 1) {
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        for (long rid : union.getMemberInfos().keySet()) {
            MessageUtil.sendMsg(msg, rid);
        }
    }

}
