package com.sh.game.system.union.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionDianFenLianSaiManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.union.ReqDFLSTaoTaiBattleInfoMessage;

/**
 * <p>请求淘汰赛信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqDFLSTaoTaiBattleInfoHandler extends AbstractHandler<ReqDFLSTaoTaiBattleInfoMessage> {

    @Override
    public void doAction(ReqDFLSTaoTaiBattleInfoMessage msg) {
        UnionDianFenLianSaiManager.getInstance().reqTaoTaiBattleInfo(SessionUtil.getRole(msg));
    }

}
