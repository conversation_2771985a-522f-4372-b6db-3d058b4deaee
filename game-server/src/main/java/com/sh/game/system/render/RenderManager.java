package com.sh.game.system.render;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.protos.AbcProtos;
import com.sh.game.system.render.entity.RenderData;
import com.sh.game.system.render.script.IRenderScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
public class RenderManager {
    private static final RenderManager INSTANCE = new RenderManager();

    public static RenderManager getInstance() {
        return INSTANCE;
    }

    public void clickIncome(Role role, int secondCount) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.reqClickIncome(role, secondCount));
    }

    public void secondIncome(Role role) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.reqSecondIncome(role));
    }

    public void reqEnergyBox(Role role) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.reqEnergyBox(role));
    }

    public void reqRoleLevel(Role role) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.reqRoleLevel(role));
    }

    public void reqRoleLevelNotCost(Role role) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.reqRoleLevelNotCost(role));
    }

    public void reqQuickPractice(Role role) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.quickPractice(role));
    }

    public void lianDian(Role role, int rate) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.lianDian(role, rate));
    }

    public void multiple(Role role) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.multiple(role));
    }

    public void reqSettle2048(Role role, List<AbcProtos.CommonKeyValueBean> items, int rate) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.reqSettle2048(role, items, rate));
    }

    public void fromZhuanSheng(Role role, List<int[]> incomes, boolean onRoleCreate) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.fromZhuanSheng(role, incomes, onRoleCreate));
    }

    public void qiaoQianClickIncomeUpdate(Role role) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.qiaoQianClickIncomeUpdate(role));
    }

    public void quickKanChai(Role role) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.quickKanChai(role));
    }

    public void reqLiXianRewards(Role role) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.reqLiXianRewards(role));
    }

    /**
     * 点击收益(赚钱)/每秒收益(修为) 计算
     *
     * @param renderData            RenderData
     * @param click                 点击收益(赚钱)
     * @param second                每秒收益(修为)
     * @param resIncomeInfoMessage  是否推送客户端收益msg
     */
    public void calRenderIncome(Role role, RenderData renderData, boolean click, boolean second, boolean resIncomeInfoMessage) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.calRenderIncome(role, renderData, click, second, resIncomeInfoMessage));
    }

    public void useItemEnergy(Role role, int value) {
        ScriptEngine.invoke1t1(IRenderScript.class, script -> script.useItemEnergy(role, value));
    }
}
