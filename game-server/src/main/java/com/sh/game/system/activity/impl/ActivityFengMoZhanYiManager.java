package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityFengMoZhanYiScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2021/12/7 9:44
 */
public class ActivityFengMoZhanYiManager {

    private static final ActivityFengMoZhanYiManager INSTANCE = new ActivityFengMoZhanYiManager();

    public static ActivityFengMoZhanYiManager getInstance() {
        return INSTANCE;
    }

    public void buyZhanYi(Role role,int count) {
        ScriptEngine.invoke1t1(IActivityFengMoZhanYiScript.class, script -> script.buyZhanYi(role,count));
    }

    public void reqSelfInfo(Role role) {
        ScriptEngine.invoke1t1(IActivityFengMoZhanYiScript.class, script -> script.reqSelfInfo(role));
    }

    public void reqRankInfo(Role role) {
        ScriptEngine.invoke1t1(IActivityFengMoZhanYiScript.class, script -> script.reqRankInfo(role));
    }

    public void gearsReward(Role role, int cfgId) {
        ScriptEngine.invoke1t1(IActivityFengMoZhanYiScript.class, script -> script.gearsReward(role, cfgId));
    }
}
