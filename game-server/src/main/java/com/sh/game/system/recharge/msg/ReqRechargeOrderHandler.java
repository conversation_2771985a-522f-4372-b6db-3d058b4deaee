package com.sh.game.system.recharge.msg;

import com.sh.game.common.communication.msg.system.recharge.ReqRechargeOrderIdMessage;
import com.sh.game.common.communication.msg.system.recharge.ResRechargeOrderIdMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.protos.RechargeProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.recharge.RechargeManager;
import com.sh.server.AbstractHandler;


/**
 * 玩家获取充值订单id
 */

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqRechargeOrderHandler extends AbstractHandler<ReqRechargeOrderIdMessage> {

    @Override
    public void doAction(ReqRechargeOrderIdMessage msg) {
        Role role = SessionUtil.getRole(msg.getSession());
        RechargeProtos.ReqRechargeOrderId proto = msg.getProto();
        long orderId = RechargeManager.getInstance().createOrder(role.getRoleId(), proto.getProductId());
        ResRechargeOrderIdMessage rechargeOrderIdMessage = new ResRechargeOrderIdMessage();
        //发送给前端消息
        rechargeOrderIdMessage.setProto(RechargeProtos.ResRechargeOrderId.newBuilder()
                .setOrderNo(String.valueOf(orderId))
                .setProductId(proto.getProductId())
                .build());
        MessageUtil.sendMsg(rechargeOrderIdMessage, role.getId());
    }

}
