package com.sh.game.system.schedule.script;

import com.sh.script.IScript;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 *
 * @author: xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2018/3/22 20:52
 * desc  :
 * Copyright(©) 2017 by xiaomo.
 */
public interface IScheduleScript extends IScript {

    void start();
}
