package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * 礼包回馈
 *
 * <AUTHOR>
 * @date 2022/08/15 20:17
 */
public interface IActivityFeedbackGiftScript extends IScript {

    /**
     * 请求获取礼包回馈进度信息
     *
     * @param role 角色
     */
    void reqFeedbackInfo(Role role);

    /**
     * 请求获取礼包回馈奖励
     *
     * @param role      角色
     * @param configId  奖励配置id
     */
    void reqFeedbackReward(Role role, int configId);
}
