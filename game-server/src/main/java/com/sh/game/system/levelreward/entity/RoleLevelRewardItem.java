package com.sh.game.system.levelreward.entity;

import com.sh.game.common.config.model.LevelRewardConfig;
import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 等级奖励
 *
 * <AUTHOR>
 * @since 2022-04-18 16:50
 **/
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleLevelRewardItem extends AbstractRoleEntity {

    /**
     * 角色ID
     */
    @Tag(1)
    private long id;

    /**
     * 已领取奖励id列表
     * @see LevelRewardConfig#getId() 
     */
    @Tag(2)
    private List<Integer> rewardIdList = new ArrayList<>();
}
