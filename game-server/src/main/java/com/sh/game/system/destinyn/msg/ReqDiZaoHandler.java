package com.sh.game.system.destinyn.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.EquipProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.destinyn.DestinynManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.equip.ReqDiZaoMessage;

/**
 * <p>请求缔造</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqDiZaoHandler extends AbstractHandler<ReqDiZaoMessage> {

    @Override
    public void doAction(ReqDiZaoMessage msg) {
        EquipProtos.ReqDiZao diZao = msg.getProto();
        DestinynManager.getInstance().diZao(SessionUtil.getRole(msg), diZao.getUid(), diZao.getCfgId());
    }

}
