package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqZhenYingTaskRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityZhenYingTaskManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求阵营任务奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqZhenYingTaskRewardHandler extends AbstractHandler<ReqZhenYingTaskRewardMessage> {

    @Override
    public void doAction(ReqZhenYingTaskRewardMessage msg) {
        ActivityProtos.ReqZhenYingTaskReward proto = msg.getProto();
        ActivityZhenYingTaskManager.getInstance().reqReward(SessionUtil.getRole(msg), proto.getCidList(), proto.getActivityId());
    }

}
