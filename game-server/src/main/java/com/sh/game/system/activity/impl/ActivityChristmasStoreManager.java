package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityChristmasStoreScript;
import com.sh.script.ScriptEngine;

public class ActivityChristmasStoreManager {
    private static final ActivityChristmasStoreManager INSTANCE = new ActivityChristmasStoreManager();

    private ActivityChristmasStoreManager() {

    }

    public static ActivityChristmasStoreManager getInstance() {
        return INSTANCE;
    }

    /**
     * 请求抽取打折
     *
     * @param role
     */
    public void reqRandomDiscount(Role role) {
        ScriptEngine.invoke1t1(IActivityChristmasStoreScript.class, s -> s.reqRandomDiscount(role));
    }

    /**
     * 请求确认打折
     *
     * @param role
     */
    public void reqSubmitDiscount(Role role) {
        ScriptEngine.invoke1t1(IActivityChristmasStoreScript.class, s -> s.reqSubmitDiscount(role));
    }

    /**
     * 请求圣诞商店信息
     *
     * @param role
     */
    public void reqChristmasStoreInfo(Role role) {
        ScriptEngine.invoke1t1(IActivityChristmasStoreScript.class, s -> s.reqChristmasStoreInfo(role));
    }

    /**
     * 请求购买商品
     *
     * @param role
     * @param cfgId
     */
    public void reqBuyStoreGoods(Role role, int cfgId) {
        ScriptEngine.invoke1t1(IActivityChristmasStoreScript.class, s -> s.reqBuyStoreGoods(role, cfgId));
    }

    /**
     * 请求刷新商品
     * @param role
     */
    public void reqRefreshStoreGoods(Role role) {
        ScriptEngine.invoke1t1(IActivityChristmasStoreScript.class, s -> s.reqRefreshStoreGoods(role));
    }

}
