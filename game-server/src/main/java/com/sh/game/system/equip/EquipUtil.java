package com.sh.game.system.equip;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.EquipSuitConfig;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.EquipData;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.system.goal.GoalManager;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/10 16:46<br>;
 * 版本：1.0<br>;
 * 描述：
 */
@Slf4j
public class EquipUtil {

    public final static int LOOP_DEPTH = 30;

    /**
     * 获取装备套装
     *
     * @param storage
     * @return
     */
    public static Set<Integer> getEquipSuits(IAvatar avatar, Storage storage) {
        Role cast = null;
        if (avatar instanceof Role) {
            cast = (Role) avatar;
        }
        Role role = cast;
        Set<Integer> equipSuits = new HashSet<>();

        Map<Integer, Set<Integer>> suitPosMap = findSuitPosMap(storage);
        suitPosMap.forEach((id, equips) -> {
            EquipSuitConfig suitConfig = ConfigDataManager.getInstance().getById(EquipSuitConfig.class, id);
            if (suitConfig == null) {
                return;
            }
            boolean available = true;
            for (int required : suitConfig.getNeed_position()) {
                if (!equips.contains(required)) {
                    available = false;
                    break;
                }
            }
            if (!available) {
                return;
            }

            List<Integer> typeList = GlobalUtil.findJingHaoList(GameConst.GlobalId.SUIT_LEVEL_TYPE);
            if (typeList != null && typeList.contains(suitConfig.getType())) {
                if (getEquipLevel(role, equips) < suitConfig.getNum()) {
                    return;
                }
            } else {
                if (equips.size() < suitConfig.getNum()) {
                    return;
                }
            }

            if (!ConditionUtil.validate(avatar, suitConfig.getCondition())) {
                // 套装条件校验
                return;
            }

            equipSuits.add(suitConfig.getId());

            if (role != null) {
                GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.EQUIP_SUIT, suitConfig.getId());
            }

            // 大套装生效时小套装失效（最多移除5层）
            EquipSuitConfig subSuitConfig = ConfigDataManager.getInstance().getById(EquipSuitConfig.class, suitConfig.getSubClass());
            for (int i = 1; i <= LOOP_DEPTH; i++) {
                // 子套装
                if (subSuitConfig == null) {
                    break;
                }
                equipSuits.remove(suitConfig.getSubClass());
                subSuitConfig = ConfigDataManager.getInstance().getById(EquipSuitConfig.class, subSuitConfig.getSubClass());

                if (i == LOOP_DEPTH) {
                    log.error("EquipSuitConfig配置id:{}，子套转嵌套深度超出上限:{}", suitConfig.getId(), LOOP_DEPTH);
                }
            }

        });
        return equipSuits;
    }

    /**
     * 获取装备总等级
     *
     * @param equips 部位列表
     * @return int 总等级
     */
    private static int getEquipLevel(Role role, Set<Integer> equips) {
        Storage storage = role.getBackpack().fetchStorage(BackpackConst.Place.EQUIP);
        Map<Integer, Item> storageData = storage.getData();
        int level = 0;
        for (Integer index : equips) {
            Item item = storageData.get(index);
            if (item == null) {
                continue;
            }
            ItemConfig itemConfig = item.findItemConfig();
            if (itemConfig == null) {
                continue;
            }
            level += itemConfig.getLevel();
        }
        return level;
    }


    /**
     * 身上装备的套装cid和槽位汇总
     *
     * @param storage 装备背包
     * @return key:套装表cfgId  value:装备槽位置
     */
    private static Map<Integer, Set<Integer>> findSuitPosMap(Storage storage) {
        Map<Integer, Set<Integer>> suitPosMap = new HashMap<>();
        Map<Integer, Set<Integer>> suitItemMap = new HashMap<>();
        Map<Integer, Set<Integer>> suitTypeMap = new HashMap<>();
        storage.getData().forEach((index, item) -> {
            if (item == null) {
                return;
            }
            if (item.findItemConfig() == null || item.findItemConfig().getSuitID() == null) {
                return;
            }

            // 汇总套装id
            EquipData equipData = item.getEquipData();

            Set<Integer> equipDataSuit = findEquipDataSuit(equipData);
            Set<Integer> suitIdSet = new HashSet<>(equipDataSuit);

            for (int suitId : item.findItemConfig().getSuitID()) {
                suitIdSet.add(suitId);
            }

            if (suitIdSet.isEmpty()) {
                return;
            }

            EquipSuitConfig suitConfig;
            // 装备拥有的套装id
            for (int suitId : suitIdSet) {
                suitConfig = ConfigDataManager.getInstance().getById(EquipSuitConfig.class, suitId);
                if (suitConfig == null) {
                    continue;
                }
                // 添加套装的子套装效果（最多套娃5层）
                for (int i = 1; i <= LOOP_DEPTH; i++) {

                    int cfgId = item.getCfgId();
                    if (suitConfig.getItemCheck() == 1) {
                        List<Integer> suitItem = suitConfig.getSuitItem();
                        if (suitItem != null && !suitItem.contains(cfgId) && equipData != null && suitItem.contains(equipData.getGemId())) {
                            cfgId = equipData.getGemId();
                        }
                    }

                    Set<Integer> itemSet = suitItemMap.computeIfAbsent(suitConfig.getId(), k -> new HashSet<>());
                    Set<Integer> indexSet = suitPosMap.computeIfAbsent(suitConfig.getId(), k -> new HashSet<>());
                    Set<Integer> typeSet = suitTypeMap.computeIfAbsent(suitConfig.getId(), k -> new HashSet<>());
                    if (suitConfig.getItemCheck() != 1) {
                        indexSet.add(index);
                    } else if (!itemSet.contains(cfgId) && !typeSet.contains(item.findItemConfig().getType())) {
                        indexSet.add(index);
                    }
                    // 当前套装需要检查重复装备
                    if (suitConfig.getItemCheck() == 1) {
                        itemSet.add(cfgId);
                        typeSet.add(item.findItemConfig().getType());
                    }

                    // 子套装
                    suitConfig = ConfigDataManager.getInstance().getById(EquipSuitConfig.class, suitConfig.getSubClass());
                    if (suitConfig == null) {
                        break;
                    }

                    if (i == LOOP_DEPTH) {
                        log.error("EquipSuitConfig配置id:{}，子套转嵌套深度超出上限:{}", suitId, LOOP_DEPTH);
                    }
                }

            }
        });

        Map<Integer, Set<Integer>> result = new LinkedHashMap<>();
        suitPosMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEachOrdered(x -> result.put(x.getKey(), x.getValue()));

        return result;
    }

    private static Set<Integer> findEquipDataSuit(EquipData equipData) {
        if (equipData == null) {
            return new HashSet<>(0);
        }
        Set<Integer> suitIdSet = new HashSet<>(equipData.getExtendSuitIdList());
        ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, equipData.getGemId());
        if (config == null) {
            return suitIdSet;
        }
        for (int suitId : config.getSuitID()) {
            suitIdSet.add(suitId);
        }
        return suitIdSet;
    }
}
