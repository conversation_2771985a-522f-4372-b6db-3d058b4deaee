package com.sh.game.system.equipfirstgain.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.equipfirstgain.entity.RoleEquipFirstGain;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @since 2022-04-20 19:52
 **/
public interface IEquipFirstGainScript extends IScript {

    /**
     * 请求一键获取个人装备首爆奖励
     *
     * @param role 角色
     */
    void reqGainAllReward(Role role);

    /**
     * 请求个人装备首爆奖励领取信息
     *
     * @param role 角色
     */
    void reqPersonalEquipFirstGainInfo(Role role);

    /**
     * 获取个人装备首爆
     *
     * @param roleId 角色Id
     * @return {@link RoleEquipFirstGain} 个人装备首爆信息
     */
    RoleEquipFirstGain find(long roleId);
}
