package com.sh.game.system.recharge.entity;

import com.google.common.collect.Sets;
import com.sh.game.common.util.TimeUtil;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.Set;

@Getter
@Setter
public class InvestPlanRecharge {
    /**
     * 活动id
     */
    @Tag(1)
    private int activityId;
    /**
     * 参与时间
     */
    @Tag(2)
    private int beginTime;
    /**
     * 奖励天数
     */
    @Tag(3)
    private int rewardTotalDay;
    /**
     * 领取记录
     */
    @Tag(4)
    private Set<Integer> hasDrawList = Sets.newHashSet();

    /**
     * 是否已全部领取
     */
    @Tag(5)
    private boolean hasDrawAll = false;

    public boolean isExpired() {
        int now = TimeUtil.getNowOfSeconds();
        int endTime = getEndTime();
        return now > endTime;
    }

    public int getEndTime() {
        return (int) (beginTime + rewardTotalDay * TimeUtil.ONE_DAY_IN_SECONDS);
    }

    public boolean needSendEmail() {
        return isExpired() && !hasDrawAll;
    }

    public Set<Integer> getNotHasDrawDayNos() {
        Set<Integer> notHasDrawSets = Sets.newHashSet();
        for (int dayNo = 1; dayNo <= rewardTotalDay; dayNo++) {
            if (!hasDrawList.contains(dayNo)) {
                notHasDrawSets.add(dayNo);
            }
        }
        return notHasDrawSets;
    }
}
