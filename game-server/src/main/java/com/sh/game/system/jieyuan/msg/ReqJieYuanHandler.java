package com.sh.game.system.jieyuan.msg;

import com.sh.game.common.communication.msg.system.jieyuan.ReqJieYuanMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.jieyuan.JieYuanManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求结缘</p>
* <p>Created by MessageUtil</p>
* @date 2025-04-08 上午11:43:49
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqJieYuanHandler extends AbstractHandler<ReqJieYuanMessage> {

    @Override
    public void doAction(ReqJieYuanMessage msg) {
        JieYuanManager.getInstance().reqJieYuan(SessionUtil.getRole(msg.getSession()), msg.getProto().getCount());
    }
}