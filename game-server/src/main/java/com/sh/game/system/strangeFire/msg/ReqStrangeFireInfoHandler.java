package com.sh.game.system.strangeFire.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.strangeFire.StrangeFireManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.strangeFire.ReqStrangeFireInfoMessage;

/**
 * <p>请求异火信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqStrangeFireInfoHandler extends AbstractHandler<ReqStrangeFireInfoMessage> {

    @Override
    public void doAction(ReqStrangeFireInfoMessage msg) {
        StrangeFireManager.getInstance().reqStrangeFireInfo(SessionUtil.getRole(msg));
    }

}
