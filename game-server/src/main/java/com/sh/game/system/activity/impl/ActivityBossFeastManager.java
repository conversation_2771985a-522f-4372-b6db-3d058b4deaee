package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityBossFeastScript;
import com.sh.script.ScriptEngine;

/**
 * Boss盛宴
 *
 * <AUTHOR>
 * @date 2022/6/27/027 11:11
 */
public class ActivityBossFeastManager {

    private static final ActivityBossFeastManager INSTANCE = new ActivityBossFeastManager();

    private ActivityBossFeastManager() {}

    public static ActivityBossFeastManager getInstance() {
        return INSTANCE;
    }

    /**
     * 请求Boss盛宴信息
     *
     * @param role 角色
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1tn(IActivityBossFeastScript.class, script -> script.reqInfo(role));
    }

    /**
     * 请求领奖
     *
     * @param role      角色
     * @param configId  Boss盛宴表配置id
     */
    public void reqReward(Role role, int configId) {
        ScriptEngine.invoke1tn(IActivityBossFeastScript.class, script -> script.reqReward(role, configId));
    }
}
