package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityCompeteAtkScript;
import com.sh.script.ScriptEngine;


public class ActivityCompeteAtkManager {
    private static final ActivityCompeteAtkManager INSTANCE = new ActivityCompeteAtkManager();

    private ActivityCompeteAtkManager() {}

    public static ActivityCompeteAtkManager getInstance() {
        return INSTANCE;
    }


    public void reqInfo(Role role, int type) {
        ScriptEngine.invoke1t1(IActivityCompeteAtkScript.class, script -> script.reqInfo(role, type));
    }

    public void reqAcquire(Role role, int cid) {
        ScriptEngine.invoke1t1(IActivityCompeteAtkScript.class, script -> script.reqAcquire(role, cid));
    }
}
