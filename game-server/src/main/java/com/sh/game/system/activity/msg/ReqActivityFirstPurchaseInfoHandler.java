package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqActivityFirstPurchaseInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityFirstPurchaseManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求玩家首充数据</p>
 * <p>Created by MessageUtil</p>
 * @date 2020-08-18 19:57:38
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqActivityFirstPurchaseInfoHandler extends AbstractHandler<ReqActivityFirstPurchaseInfoMessage> {

    @Override
    public void doAction(ReqActivityFirstPurchaseInfoMessage msg) {
        ActivityFirstPurchaseManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()));
    }

}
