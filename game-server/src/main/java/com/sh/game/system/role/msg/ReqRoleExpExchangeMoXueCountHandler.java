package com.sh.game.system.role.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.role.RoleManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.role.ReqRoleExpExchangeMoXueCountMessage;

/**
 * <p>请求兑换次数信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqRoleExpExchangeMoXueCountHandler extends AbstractHandler<ReqRoleExpExchangeMoXueCountMessage> {

    @Override
    public void doAction(ReqRoleExpExchangeMoXueCountMessage msg) {
        RoleManager.getInstance().reqMoXueCount(SessionUtil.getRole(msg));
    }

}
