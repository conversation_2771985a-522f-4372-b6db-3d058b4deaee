package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.BoxUtil;
import com.sh.game.system.using.ItemUsage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * description: 随机宝箱
 * date: 2024/4/24
 * author: chenBin
 */
@Slf4j
public class ItemUsageForSuiJiBox extends ItemUsage {
    @Override
    public int getUsedType() {
        return 2301;
    }

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int useParam = config.getUseParam()[0][0];
        // 1=神魔，2=伙伴
        if (useParam != 1 && useParam != 2) {
            log.error("神魔随机宝箱使用参数异常, 道具ID:{},useParam:{}", config.getId(), useParam);
            return false;
        }
        return true;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int useParam = config.getUseParam()[0][0];
        List<int[]> items = BoxUtil.openBox(role, config.getId(), count).stream().map(x -> new int[]{x.getCfgId(),
                (int) x.getCount()}).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(items)) {
            return false;
        }
        log.info("神魔随机宝箱使用成功, 道具ID:{}， useParam:{}", config.getId(), useParam);
        return sendBoxReward(role, stash, useParam, items);

    }
}
