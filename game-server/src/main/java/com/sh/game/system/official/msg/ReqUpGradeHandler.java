package com.sh.game.system.official.msg;

import com.sh.game.common.communication.msg.system.official.ReqUpGradeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.official.OfficialManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求官职升阶</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqUpGradeHandler extends AbstractHandler<ReqUpGradeMessage> {

    @Override
    public void doAction(ReqUpGradeMessage msg) {
        OfficialManager.getInstance().upGrade(SessionUtil.getRole(msg.getSession()));
    }

}
