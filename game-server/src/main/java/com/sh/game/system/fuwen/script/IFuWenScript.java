package com.sh.game.system.fuwen.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

public interface IFuWenScript extends IScript {

    void reqHuoLongLevel(Role role, long itemId);

    void reqEquipFuWen(Role role, int index, long itemId);

    void reqOffFuWen(Role role, int index);

    void reqFuWenEqual(Role role, int itemId, int count);

    void reqHuoLongStrong(Role role, int index);

    void equipHuoLong(Role role);

    void reqFuWenCompose(Role role, int itemId);
}
