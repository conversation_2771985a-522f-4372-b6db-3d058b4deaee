package com.sh.game.system.cross.msg;

import com.sh.game.common.communication.msg.system.cross.ReqCrossInfoMessage;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.cross.CrossManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求跨服列表</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 15:51:39
 */
public class ReqCrossInfoHandler extends AbstractHandler<ReqCrossInfoMessage> {

    @Override
    public void doAction(ReqCrossInfoMessage msg) {
        CrossManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()));
    }
}
