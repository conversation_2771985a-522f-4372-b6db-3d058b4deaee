package com.sh.game.system.fishpond.msg;

import com.sh.game.common.communication.msg.system.fishpond.ReqFishPondResultOperMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.fishpond.FishPondManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求鱼塘钓鱼结果处理</p>
* <p>Created by MessageUtil</p>
* @date 2024-09-13 下午4:35:20
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqFishPondResultOperHandler extends AbstractHandler<ReqFishPondResultOperMessage> {

    @Override
    public void doAction(ReqFishPondResultOperMessage msg) {
        FishPondManager.getInstance().reqFishPondResultOper(SessionUtil.getRole(msg), msg.getProto().getType());
    }
}