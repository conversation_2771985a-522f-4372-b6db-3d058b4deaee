package com.sh.game.system.xianfa.msg;

import com.sh.game.common.communication.msg.system.xianfa.ReqBeiZhanInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.xianfa.XianFaManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求备战仙友</p>
* <p>Created by MessageUtil</p>
* @date 2025-03-10 下午5:52:54
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqBeiZhanInfoHandler extends AbstractHandler<ReqBeiZhanInfoMessage> {

    @Override
    public void doAction(ReqBeiZhanInfoMessage msg) {
        XianFaManager.getInstance().reqBeiZhan(SessionUtil.getRole(msg.getSession()), msg.getProto().getBeiZhanType());
    }
}