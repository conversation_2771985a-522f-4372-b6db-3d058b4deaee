package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

/**
 * ATO：yumo<br>;
 * 时间：2021/1/16 11:46<br>;
 * 版本：1.0<br>;
 * 描述：地图消耗
 */
public class ItemUsageMapKeep extends ItemUsage {

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        return true;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
        if (player == null) {
            return true;
        }
        int[][] useParam = config.getUseParam();
        if (useParam == null || useParam.length == 0) {
            return false;
        }
        int[] ints = useParam[0];
        if (ints.length < 2) {
            return false;
        }
        stash.increase(ints[0], ints[1] * count);
        stash.commit(role, LogAction.MAP_KEEP_TIME_TIME);

        player.buyKeepItem(role, config.getUseParam()[0][0]);
        return true;
    }

    @Override
    public int getUsedType() {
        return 170;
    }
}
