package com.sh.game.system.school.msg;

import com.sh.game.common.communication.msg.system.school.ReqRefuseAllMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.school.SchoolManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求一键拒绝</p>
* <p>Created by MessageUtil</p>
* @date 2025-02-20 下午9:14:28
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqRefuseAllHandler extends AbstractHandler<ReqRefuseAllMessage> {

    @Override
    public void doAction(ReqRefuseAllMessage msg) {
        SchoolManager.getInstance().refuseAll(SessionUtil.getRole(msg));
    }
}