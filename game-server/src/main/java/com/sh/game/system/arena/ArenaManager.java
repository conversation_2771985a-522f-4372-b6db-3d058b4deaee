package com.sh.game.system.arena;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.arena.script.IArenaScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2023/11/8
 */
public class ArenaManager {

    private static final ArenaManager INSTANCE = new ArenaManager();

    public static ArenaManager getInstance() {
        return INSTANCE;
    }

    public void reqArenaInfo(Role role) {
        ScriptEngine.invoke1t1(IArenaScript.class, s -> s.reqArenaInfo(role));
    }

    public void reqArenaBattle(Role role, long roleId, long battleId) {
        ScriptEngine.invoke1t1(IArenaScript.class, s -> s.reqArenaBattle(role, roleId, battleId));
    }

    public void reqArenaRivalInfo(Role role) {
        ScriptEngine.invoke1t1(IArenaScript.class, s -> s.reqArenaRivalInfo(role));
    }

    public void reqArenaRivalRefresh(Role role) {
        ScriptEngine.invoke1t1(IArenaScript.class, s -> s.reqArenaRivalRefresh(role));
    }

    public void reqArenaLogInfo(Role role) {
        ScriptEngine.invoke1t1(IArenaScript.class, s -> s.reqArenaLogInfo(role));
    }
}
