package com.sh.game.system.store.listener;

import com.sh.game.event.EventType;
import com.sh.game.event.IListener;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR> xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2017/9/2 13:13
 * desc  :
 * Copyright(©) 2017 by xiaomo.
 * <AUTHOR>
 */
public class StoreLoginListener implements IListener {
    @Override
    public void update(EventType type, Object param) {

    }
}
