package com.sh.game.system.zongmen;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.zongmen.script.IZongMenScript;
import com.sh.script.ScriptEngine;

import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2025/2/14 10:35
 */
public class ZongMenManager {
    private static final ZongMenManager INSTANCE = new ZongMenManager();

    public static ZongMenManager getInstance() {
        return INSTANCE;
    }

    private ZongMenManager() {
    }

    public void reqZongMenInfoList(Role role) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqZongMenInfoList(role));
    }

    public void reqZongMenFight(Role role, int zongMenCfgId) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqZongMenFight(role, zongMenCfgId));
    }

    public void reqZongMenFightMotivate(Role role, int zongMenCfgId, boolean costItem) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqZongMenFightMotivate(role, zongMenCfgId, costItem));
    }

    public void reqZongMenLevel(Role role, int zongMenCfgId) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqZongMenLevel(role, zongMenCfgId));
    }

    public void reqZongMenSeatSecretaryBind(Role role, int zongMenCfgId, int seat, int secretaryCfgId) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqZongMenSeatSecretaryBind(role, zongMenCfgId, seat, secretaryCfgId));
    }

    public void reqZongMenDiZiBuy(Role role, int zongMenCfgId, boolean ten) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqZongMenDiZiBuy(role, zongMenCfgId, ten));
    }

    public void calZongMenIncome(Role role) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.calZongMenIncome(role, null));
    }

    public void calZongMenTotalIncome(Role role, int secretaryCfgId) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.calZongMenTotalIncome(role, secretaryCfgId));
    }

    public Map<Integer, Long> calZongMenLiXianTotalIncome(Role role, int liXianSecond) {
        return ScriptEngine.invoke1t1WithRet(IZongMenScript.class, script -> script.calZongMenLiXianTotalIncome(role, liXianSecond));
    }

    public void reqXuanBaInfo(Role role) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqXuanBaInfo(role));
    }

    public void reqXuanBa(Role role, boolean isAdvert, int type) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqXuanBa(role, isAdvert, type));
    }

    public void reqModifyName(Role role, String name) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqModifyName(role, name));
    }

    public void reqFirstZongMen(Role role) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqFirstZongMen(role));
    }

    public void reqXuanBaCountForItem(Role role, int type) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqXuanBaCountForItem(role, type));
    }

    public void reqZongMenEventReward(Role role, int ) {
        ScriptEngine.invoke1t1(IZongMenScript.class, script -> script.reqZongMenEventReward(role));
    }
}
