package com.sh.game.system.secretary.msg;

import com.sh.game.common.communication.msg.system.secretary.ReqSecretaryZiZhiUpMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.secretary.Secretary<PERSON>anager;
import com.sh.server.AbstractHandler;

/**
* <p>请求秘书资质升级</p>
* <p>Created by MessageUtil</p>
* @date 2024-10-18 上午11:10:08
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqSecretaryZiZhiUpHandler extends AbstractHandler<ReqSecretaryZiZhiUpMessage> {

    @Override
    public void doAction(ReqSecretaryZiZhiUpMessage msg) {
        SecretaryManager.getInstance().reqSecretaryZiZhiUp(SessionUtil.getRole(msg.getSession()), msg.getProto().getSecretaryCfgId());
    }
}