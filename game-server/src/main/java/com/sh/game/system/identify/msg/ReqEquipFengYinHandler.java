package com.sh.game.system.identify.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.IdentifyProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.identify.IdentifyManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.identify.ReqEquipFengYinMessage;

/**
 * <p>请求封印装备</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqEquipFengYinHandler extends AbstractHandler<ReqEquipFengYinMessage> {

    @Override
    public void doAction(ReqEquipFengYinMessage msg) {
        IdentifyProtos.ReqEquipFengYin equipFengYin = msg.getProto();
        IdentifyManager.getInstance().unlockFengyin(SessionUtil.getRole(msg), equipFengYin.getItemUid());
    }

}
