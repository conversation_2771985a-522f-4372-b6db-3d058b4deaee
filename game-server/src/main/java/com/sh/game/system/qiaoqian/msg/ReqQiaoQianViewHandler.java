package com.sh.game.system.qiaoqian.msg;

import com.sh.game.common.communication.msg.system.qiaoqian.ReqQiaoQianViewMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.qiaoqian.QiaoQianManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求乔迁场景</p>
* <p>Created by MessageUtil</p>
* @date 2024-12-18 下午8:08:39
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqQiaoQianViewHandler extends AbstractHandler<ReqQiaoQianViewMessage> {

    @Override
    public void doAction(ReqQiaoQianViewMessage msg) {
        QiaoQianManager.getInstance().reqQiaoQianView(SessionUtil.getRole(msg), msg.getProto().getType());
    }
}