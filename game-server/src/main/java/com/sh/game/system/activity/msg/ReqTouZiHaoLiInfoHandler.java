package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityTouZiHaoLiManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqTouZiHaoLiInfoMessage;

/**
 * <p>请求投资豪礼活动信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqTouZiHaoLiInfoHandler extends AbstractHandler<ReqTouZiHaoLiInfoMessage> {

    @Override
    public void doAction(ReqTouZiHaoLiInfoMessage msg) {
        ActivityTouZiHaoLiManager.getInstance().reqTouZiHaoLiInfo(SessionUtil.getRole(msg));
    }

}
