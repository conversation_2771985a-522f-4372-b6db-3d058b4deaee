package com.sh.game.system.guide.msg;

import com.sh.game.common.communication.msg.system.guide.ReqGuideListMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.guide.GuideManager;
import com.sh.server.AbstractHandler;

/**
 * description: 存储引导内容
 * date: 2024/7/4
 * author: chenBin
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqGuideListHandler extends AbstractHandler<ReqGuideListMessage> {
    @Override
    public void doAction(ReqGuideListMessage msg) {
        GuideManager.getInstance().reqGuide(SessionUtil.getRole(msg.getSession()), msg.getProto().getDataList());
    }
}
