package com.sh.game.system.daily.impl;

import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.daily.script.IDailyBossChallengeScript;
import com.sh.script.ScriptEngine;

import java.util.Optional;

public class DailyBossChallengeManager {

    private static final DailyBossChallengeManager INSTANCE = new DailyBossChallengeManager();

    private DailyBossChallengeManager() {

    }

    public static DailyBossChallengeManager getInstance() {
        return INSTANCE;
    }


    /**
     * 个人boss信息
     *
     * @param role
     */
    public void reqInfo(Role role) {
        Optional.ofNullable(ScriptEngine.get1t1(IDailyBossChallengeScript.class)).ifPresent(script -> script.reqInfo(role));
    }

    /**
     * 更新副本已用时间
     *
     * @param role        角色
     * @param config      副本表记录
     * @param duplicateCreateTime 副本创建时间
     */
    public void duplicateElapsedTime(Role role, DuplicateConfig config, long duplicateCreateTime) {
        Optional.ofNullable(ScriptEngine.get1t1(IDailyBossChallengeScript.class)).ifPresent(script -> script.duplicateElapsedTime(role, config, duplicateCreateTime));
    }

}
