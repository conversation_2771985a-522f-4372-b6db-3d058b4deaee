package com.sh.game.system.appearance.msg;

import com.sh.game.common.communication.msg.system.appearance.ReqBuyAppearanceMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.appearance.AppearanceManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求购买装扮</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqBuyAppearanceHandler extends AbstractHandler<ReqBuyAppearanceMessage> {

    @Override
    public void doAction(ReqBuyAppearanceMessage msg) {
        AppearanceManager.getInstance().buyAppearance(SessionUtil.getRole(msg.getSession()), msg.getProto().getFashionId());
    }

}
