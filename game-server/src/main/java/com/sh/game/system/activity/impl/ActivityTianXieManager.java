package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityTianXieScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2021/11/12 15:09
 */
public class ActivityTianXieManager {

    private static final ActivityTianXieManager INSTANCE = new ActivityTianXieManager();

    public static ActivityTianXieManager getInstance() {
        return INSTANCE;
    }

    public void reqTaskInfo(Role role) {
        ScriptEngine.invoke1t1(IActivityTianXieScript.class, script -> script.reqTaskInfo(role));
    }

    public void completeTask(Role role, int cfgId) {
        ScriptEngine.invoke1t1(IActivityTianXieScript.class, script -> script.completeTask(role, cfgId));
    }
}
