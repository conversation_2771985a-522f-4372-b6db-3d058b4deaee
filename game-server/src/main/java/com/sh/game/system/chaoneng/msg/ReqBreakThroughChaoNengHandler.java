package com.sh.game.system.chaoneng.msg;

import com.sh.game.common.communication.msg.system.chaoneng.ReqBreakThroughChaoNeng;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ChaoNengProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.chaoneng.ChaoNengManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqBreakThroughChaoNengHandler extends AbstractHandler<ReqBreakThroughChaoNeng> {
    @Override
    public void doAction(ReqBreakThroughChaoNeng msg) {
        ChaoNengProtos.ReqBreakThroughChaoNeng proto = msg.getProto();
        ChaoNengManager.getInstance().breakThrough(SessionUtil.getRole(msg), proto.getChaoNengId<PERSON>ist(), proto.getIndex(), proto.getOneKey());
    }
}
