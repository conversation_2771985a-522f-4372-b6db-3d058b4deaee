package com.sh.game.system.user.command;

import com.sh.concurrent.AbstractCommand;
import com.sh.game.GameContext;
import com.sh.game.common.entity.User;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.server.SessionManager;
import com.sh.game.server.SessionUtil;
import com.sh.game.server.SessionValue;
import com.sh.game.system.user.UserManager;
import com.sh.server.Session;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019/6/27 14:43
 */
@Slf4j
public class ClientActiveCheckTask extends AbstractCommand {


    @Override
    public void doAction() {
        if(GameContext.isDebug()) {
            return;
        }

        try {
            Session[] sessions = SessionManager.getInstance().sessions();
            long curTime = TimeUtil.getNowOfMills();
            for (Session session : sessions) {
                SessionValue sv = (SessionValue) session.getValue();
                if (sv.isDeadClient()) {
                    continue;
                }
                Long heartTime = sv.getLastHeartTime();

                int maxIdleTime = 15000;

                if (!SessionUtil.isEnter(session)) {
                    //未进入游戏的情况下，超过10分钟没收到心跳才断开
                    maxIdleTime = 600000;
                }
                if (curTime - heartTime > maxIdleTime) {
                    Role role = SessionUtil.getRole(session);
                    if(role != null && role.getMemory().getReqOrderIdTime() != 0 && curTime - role.getMemory().getReqOrderIdTime() < 10 * 60 * 1000) {
                        continue;
                    }

                    log.info("curTime - heartTime: {},heartTime: {}, maxIdleTime: {}", (curTime - heartTime ), heartTime ,maxIdleTime);
                    //关闭
                    sv.setDeadClient(true);
                    User user = SessionUtil.getUser(session);
                    if(user == null || !UserManager.getInstance().isLocal(user.getIp())) {
                        log.info("长时间未收到心跳，连接闲置，主动断开连接, channel:[{}],玩家[{}]-> [{}]", session.getChannel(),
                                user == null ? "" : user.getAccount(), role == null ? "" : role.getName());
                        session.close();
                    }
                }
            }

        } catch (Throwable e) {
            log.error("客户端活性检查发生错误...", e);
        }

    }
}
