package com.sh.game.system.fabao.entity;

import com.sh.game.common.config.model.FaBaoConfig;
import com.sh.game.common.constant.IDConst;
import com.sh.game.common.util.IDUtil;
import io.protostuff.Tag;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/11/22 17:29
 */
@Data
public class FaBao {
    @Tag(20)
    private long id;
    @Tag(21)
    private int cfgId;
    @Tag(22)
    private int level;
    @Tag(23)
    private int zizhi;
    @Tag(24)
    private int secretaryCfgId;
    @Tag(25)
    private List<FaBaoSkill> skills = new ArrayList<>();

    public static FaBao valueOf(FaBaoConfig faBaoConfig) {
        FaBao data = new FaBao();
        data.setId(IDUtil.getId(IDConst.MAP));
        data.setCfgId(faBaoConfig.getId());
        data.setZizhi(faBaoConfig.getZizhi());
        return data;
    }
}
