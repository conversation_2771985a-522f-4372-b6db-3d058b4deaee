package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.GongDeConfig;
import com.sh.game.common.config.model.HuoBanConfig;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleHuoBan;
import com.sh.game.common.entity.usr.RoleNormal;
import com.sh.game.system.gongde.GongDeManager;
import com.sh.game.system.gongde.entity.GongDeData;
import com.sh.game.system.huoban.HuoBanManager;
import com.sh.game.system.huoban.entity.HuoBan;
import com.sh.game.system.using.ItemUsage;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

@Slf4j
public class ItemUsageForHouBan extends ItemUsage {

    /**
     * 验证是否可用
     *
     * @param role
     * @param item
     * @param config
     * @param count
     * @param params
     * @param stash
     * @return
     */
    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        RoleHuoBan huoBan = role.findHuoBan();
        Map<Integer, HuoBan> huoBanBag = huoBan.getHuoBanBag();
        if (huoBanBag.size() + count > huoBan.getMaxSize()) {
            return false;
        }
        int[][] useParam = config.getUseParam();
        if (useParam.length < 1 || useParam[0].length < 1) {
            return false;
        }
        int huoBanId = useParam[0][0];
        HuoBanConfig huoBanConfig = ConfigDataManager.getInstance().getById(HuoBanConfig.class, huoBanId);
        return huoBanConfig != null;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int[][] useParam = config.getUseParam();
        int huoBanId = useParam[0][0];
        for (int i = 0; i < count; i++) {
            HuoBanManager.getInstance().huoBanItemJiHuo(role, huoBanId);
        }
        return true;
    }

    @Override
    public int getUsedType() {
        return 2101;
    }
}
