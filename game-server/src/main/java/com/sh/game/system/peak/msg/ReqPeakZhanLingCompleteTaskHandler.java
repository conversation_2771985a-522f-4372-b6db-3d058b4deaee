package com.sh.game.system.peak.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.PeakProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.peak.PeakZhanLingManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.peak.ReqPeakZhanLingCompleteTaskMessage;

/**
 * <p>请求巅峰战令任务领取完成</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqPeakZhanLingCompleteTaskHandler extends AbstractHandler<ReqPeakZhanLingCompleteTaskMessage> {

    @Override
    public void doAction(ReqPeakZhanLingCompleteTaskMessage msg) {
        PeakProtos.ReqPeakZhanLingCompleteTask peakZhanLingCompleteTask = msg.getProto();
        PeakZhanLingManager.getInstance().reqCompleteTask(SessionUtil.getRole(msg), peakZhanLingCompleteTask.getConfigId());
    }

}
