package com.sh.game.system.kuanggong.msg;

import com.sh.game.common.communication.msg.system.kuanggong.ReqCLeanDataMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.kuanggong.KuangGongManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求清理旧数据</p>
* <p>Created by MessageUtil</p>
* @date 2024-09-13 17:48:15
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqCLeanDataHandler extends AbstractHandler<ReqCLeanDataMessage> {

    @Override
    public void doAction(ReqCLeanDataMessage msg) {
        KuangGongManager.getInstance().cleanData(SessionUtil.getRole(msg));
    }
}