package com.sh.game.system.suitExtend.msg;

import com.sh.game.common.communication.msg.system.suitExtend.ReqSuitExtendUpgradeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.suitExtend.SuitAttrExtendManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求专属继承套装升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqSuitExtendUpgradeHandler extends AbstractHandler<ReqSuitExtendUpgradeMessage> {

    @Override
    public void doAction(ReqSuitExtendUpgradeMessage msg) {
//        SuitAttrExtendManager.getInstance().equipSuitAttrUpgrade(SessionUtil.getRole(msg), msg.getItemUid());
    }

}
