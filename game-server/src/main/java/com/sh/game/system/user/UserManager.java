package com.sh.game.system.user;

import com.sh.game.GameContext;
import com.sh.game.common.communication.msg.system.user.ResRoleKillMessage;
import com.sh.game.common.communication.msg.system.user.ResServerEnvirMessage;
import com.sh.game.common.communication.notice.QuitGameNotice;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.user.LoginContext;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnRoleDeleteScript;
import com.sh.game.protos.UserProtos;
import com.sh.game.system.user.command.LogoutCommand;
import com.sh.game.system.user.script.IUserScript;
import com.sh.script.ScriptEngine;
import com.sh.server.Session;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 管理玩家的登录等业务
 *
 * <AUTHOR>
 * 2017年6月6日 下午10:00:22
 */
@Slf4j
public class UserManager {
    private static final UserManager INSTANCE = new UserManager();

    private static final String LOCAL_IP_127 = "127.0.0.1";
    private static final String LOCAL_IP_00 = "10.0";
    private static final String LOCAL_IP_39 = "10.39";
    private static final String LOCAL_IP_40 = "10.40";
    private static final String LOCAL_IP_41 = "10.41";
    private static final String LOCAL_IP_42 = "10.42";
    private static final String LOCAL_IP_43 = "10.43";
    private static final String LOCAL_IP_44 = "10.44";
    private static final String LOCAL_IP_COMPANY = "*************";
    private static final String LOCAL_IP_COMPANY_2 = "***************";
    public static final int NAME_MAX_LENGTH = 7;

    private final Map<Long, Long> userOfflineTimeMap = new ConcurrentHashMap<>();

    public static UserManager getInstance() {
        return INSTANCE;
    }

    public Map<Long, Long> getUserOfflineTimeMap() {
        return userOfflineTimeMap;
    }

    private Optional<IUserScript> getScript() {
        return Optional.ofNullable(ScriptEngine.get1t1(IUserScript.class));
    }

    public boolean isLocal(String ip) {
        return ip.contains(LOCAL_IP_00) ||
                ip.contains(LOCAL_IP_39) ||
                ip.contains(LOCAL_IP_40) ||
                ip.contains(LOCAL_IP_41) ||
                ip.contains(LOCAL_IP_42) ||
                ip.contains(LOCAL_IP_COMPANY) ||
                ip.contains(LOCAL_IP_COMPANY_2) ||
                ip.contains(LOCAL_IP_43) ||
                ip.contains(LOCAL_IP_44) ||
                ip.contains(LOCAL_IP_127);
    }

    /**
     * 心跳
     *
     * @param session
     */
    public void clientHeartbeat(Session session) {
        ScriptEngine.invoke1t1(IUserScript.class, script -> script.clientHeartbeat(session));
    }


    /**
     * 账号登录 未认证
     *
     * @param session
     * @param context
     */
    public void login(Session session, LoginContext context) {
        ScriptEngine.invoke1t1(IUserScript.class, script -> script.login(session, context));
    }

    /**
     * 账号登录 已认证
     *
     * @param context
     */
    public void login(LoginContext context) {
        ScriptEngine.invoke1t1(IUserScript.class, script -> script.login(context));
    }

    /**
     * 创建角色
     *
     * @param session  session
     * @param roleName roleName
     * @param sex      sex
     * @param career   career
     */
    public void createRole(Session session, String roleName, int sex, int career, int hair, String activeCode, boolean oldMsg) {
        getScript().ifPresent(iUserScript -> iUserScript.createRole(
                session,
                roleName,
                sex,
                career,
                hair,
                activeCode,
                oldMsg
        ));
    }

    /**
     * 随机名字
     *
     * @param session
     * @param sex
     */
    public void randomName(Session session, int sex) {
        getScript().ifPresent(iUserScript -> iUserScript.randomName(session, sex));
    }

    /**
     * delete role
     */
    public void deleteRole(Session session, long rid) {
        getScript().ifPresent(iUserScript -> iUserScript.deleteRole(session, rid));
    }

    public void deleteRole(long uid, long rid) {
        getScript().ifPresent(iUserScript -> iUserScript.deleteRole(uid, rid));
    }

    /**
     * 进入游戏
     *
     * @param session
     */
    public void enterGame(Session session, long rid) {
        getScript().ifPresent(iUserScript -> iUserScript.enterGame(session, rid));
    }

    /**
     * 断线重连
     *
     * @param session
     */
    public void disconnectLogin(Session session, long rid, String tokenString) {
        getScript().ifPresent(iUserScript -> iUserScript.disConnectionLogin(session, rid, tokenString));
    }

    /**
     * 退出游戏
     *
     * @param session
     */
    public void quitGame(Session session) {
        ScriptEngine.invoke1t1(IUserScript.class, script -> script.leaveGame(session));
    }

    /**
     * 玩家改名
     *
     * @param session
     * @param rid
     * @param newName
     */
    public void changeRoleName(Session session, long rid, String newName, int isRole) {
        ScriptEngine.invoke1t1(IUserScript.class, script -> script.changeRoleNameMessage(session, rid, newName, isRole));
    }

    public void reqServerEnvironment(Session session) {
        ResServerEnvirMessage msg = new ResServerEnvirMessage();

        int openTime = (int) (GameContext.getOpenDayZeroTime() / 1000);
        int mergeTime = (int) (GameContext.getCombineDayZeroTime() / 1000);
        if (openTime < 0) openTime = Integer.MAX_VALUE;
        if (mergeTime < 0) mergeTime = Integer.MAX_VALUE;

        UserProtos.ResServerEnvir.Builder resSeverEnvir = UserProtos.ResServerEnvir.newBuilder();
        resSeverEnvir.setServerStartTime(openTime);
        resSeverEnvir.setServerMergeTime(mergeTime);
        msg.setProto(resSeverEnvir.build());
        session.sendMessage(msg);
    }

    // ---------------------------------------------------------

    public void logout(Session session, LogoutCommand.Reason reason) {
        //发送玩家掉线原因
        ResRoleKillMessage resRoleKillMessage = new ResRoleKillMessage();
        UserProtos.ResRoleKill.Builder resRoleKill = UserProtos.ResRoleKill.newBuilder();
        resRoleKill.setType(reason.getType());
        resRoleKillMessage.setProto(resRoleKill.build());
        session.sendMessage(resRoleKillMessage);

        getScript().ifPresent(iUserScript -> iUserScript.logout(session, reason));

        QuitGameNotice notice = new QuitGameNotice();
        notice.setRoleId(session.getId());
        GameContext.getGameServer().getGameNoticeTransformer().sendNotice(ProcessorId.SERVER_COMMON, notice, session.getId());
    }

    /**
     * 记录登录时间
     *
     * @param role role
     */
    public void updateLoginTime(Role role) {
        getScript().ifPresent(iUserScript -> iUserScript.updateLoginTime(role));
    }

    /**
     * 踢被封ip的账号
     *
     * @param timeFormat
     */
    public void kickBanIp(String timeFormat, String ip) {
        ScriptEngine.invoke1t1(IUserScript.class, script -> script.kickBanIp(timeFormat, ip));
    }

    public void onRoleDelete(long rid) {
        Role role = DataCenter.getRole(rid);
        if (role == null) {
            return;
        }

        ScriptEngine.get1tn(IEventOnRoleDeleteScript.class).forEach(script -> {
            try {
                script.onRoleDelete(role);
            } catch (Exception e) {
                log.error("execute failed:", e);
            }
        });
    }

    /**
     * 检测邀请码是否可用
     *
     * @param code 邀请码
     */
    public void checkInvitationCode(Session session, String code) {
        ScriptEngine.invoke1t1(IUserScript.class, script -> script.checkInvitationCode(session, code));
    }

    /**
     * 检测名字是否可用
     */
    public void checkName(Session session, String name) {
        ScriptEngine.invoke1t1(IUserScript.class, script -> script.checkName(session, name));
    }


    /**
     * 更新登录天数与时间
     *
     * @param role role
     */
    public void updateRoleLoginDays(Role role, long updateTime) {
        getScript().ifPresent(iUserScript -> iUserScript.updateRoleLoginDays(role, updateTime));
    }

}

