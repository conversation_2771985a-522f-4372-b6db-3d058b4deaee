package com.sh.game.system.banquet.msg;

import com.sh.game.common.communication.msg.system.banquet.ReqBanquetListMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.BanquetProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.banquet.BanquetManager;
import com.sh.server.AbstractHandler;

import java.util.ArrayList;

/**
* <p>请求宴会列表</p>
* <p>Created by MessageUtil</p>
* @date 2024-11-28 下午3:21:27
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqBanquetListHandler extends AbstractHandler<ReqBanquetListMessage> {

    @Override
    public void doAction(ReqBanquetListMessage msg) {
        BanquetProtos.ReqBanquetListMessage proto = msg.getProto();
        BanquetManager.getInstance().reqBanquetList(SessionUtil.getRole(msg.getSession()), proto.getTypesList(), proto.getUnion());
    }
}