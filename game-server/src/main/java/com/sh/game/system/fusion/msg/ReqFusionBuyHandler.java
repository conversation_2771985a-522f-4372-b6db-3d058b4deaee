package com.sh.game.system.fusion.msg;

import com.sh.game.protos.FusionProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.fusion.FusionManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.fusion.ReqFusionBuyMessage;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */

public class ReqFusionBuyHandler extends AbstractHandler<ReqFusionBuyMessage> {

    @Override
    public void doAction(ReqFusionBuyMessage msg) {
        FusionProtos.ReqFusionBuy fusionBuy = msg.getProto();
        FusionManager.getInstance().reqBuy(SessionUtil.getRole(msg.getSession()), fusionBuy.getUid());
    }

}
