package com.sh.game.system.houyuanwork.entity;

import com.sh.game.common.util.TimeUtil;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RoleWork {
    @Tag(1)
    private int cid;

    /**
     * 下一级cid
     */
    @Tag(2)
    private int nextCid;

    /**
     * 正常结束时间戳，单位秒
     */
    @Tag(3)
    private int endTime;

    /**
     * 协助+道具 缩短的时间 单位秒
     */
    @Tag(4)
    private int reduceTime;

    /**
     * 当前建筑id
     *
     */
    public int getCurrentCid() {
        if (endTime - reduceTime > TimeUtil.getNowOfSeconds()) {
            return cid;
        } else {
            return nextCid;
        }
    }

    /**
     *
     * @return 真正结束时间戳
     */
    public int getEndTimeStamp() {
        return endTime - reduceTime;
    }
}
