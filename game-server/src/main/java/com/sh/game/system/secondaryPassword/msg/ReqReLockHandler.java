package com.sh.game.system.secondaryPassword.msg;

import com.sh.game.common.communication.msg.system.secondaryPassword.ReqReLockMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.secondaryPassword.SecondaryPasswordManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求上锁</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqReLockHandler extends AbstractHandler<ReqReLockMessage> {

    @Override
    public void doAction(ReqReLockMessage msg) {
        SecondaryPasswordManager.getInstance().reqReLock(SessionUtil.getRole(msg.getSession()));
    }

}