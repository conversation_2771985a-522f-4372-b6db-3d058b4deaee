package com.sh.game.system.using.impl;

import com.sh.game.common.communication.msg.system.backpack.ResExchangeExpMessage;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.BagConst;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.protos.BackPackProtos;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

import static com.sh.game.common.constant.CountConst.CountType.ITEM_USE_NUM;

public class ItemUsageForJuHun extends ItemUsage {

    @Override
    public int useCount(Role role, Item item, ItemConfig config, int count) {
        return super.useCount(role, item, config, count);
    }

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int limitExp = config.getUseParam()[0][0];
        int myExp = item.getParams().get(0);
        // 聚魂丹中的经验小于限制条件
        if (myExp < limitExp) {
            return false;
        }
        return true;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int addExp = item.getParams().get(0) * count;
        int addExpId = BagConst.ItemId.ROLE_EXP;
        int targetId = config.getBind() > 0 ? config.getBind() : config.getId();
        stash.increase(addExpId, addExp);
        // 更新已使用数量
        int useCount = CountManager.getInstance().count(role, ITEM_USE_NUM, targetId, count);
        // 提交经验变化
        stash.commit(role, LogAction.USE_JINGYAN_DAN, true);

        // 通知经验变化
        ResExchangeExpMessage message = new ResExchangeExpMessage();
        BackPackProtos.ResExchangeExp.Builder exChangeExp = BackPackProtos.ResExchangeExp.newBuilder();
        exChangeExp.setItemId(item.getCfgId());
        exChangeExp.setCount(useCount);
        message.setProto(exChangeExp.build());
        MessageUtil.sendMsg(message, role.getId());

        return true;
    }

    @Override
    public int getUsedType() {
        return 164;
    }
}
