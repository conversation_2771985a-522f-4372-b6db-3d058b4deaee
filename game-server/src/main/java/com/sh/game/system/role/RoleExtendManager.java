package com.sh.game.system.role;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleExtend;
import com.sh.game.data.DataCenter;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/16 14:29
 */
public class RoleExtendManager {
    private static final RoleExtendManager INSTANCE = new RoleExtendManager();
    public static RoleExtendManager getInstance() {
        return INSTANCE;
    }
    private RoleExtendManager() {
    }

    /**
     * 角色扩展功能类, 存储玩家其它扩展功能
     * @param roleId
     * @return
     */
    public RoleExtend getRoleExtend(long roleId) {
        RoleExtend data = DataCenter.get(RoleExtend.class, roleId);
        if (data == null){
            data = new RoleExtend();
            data.setId(roleId);
            DataCenter.insertData(data, true);
        }
        return data;
    }
}
