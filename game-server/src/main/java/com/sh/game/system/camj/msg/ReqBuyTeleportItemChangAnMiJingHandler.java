package com.sh.game.system.camj.msg;

import com.sh.game.common.communication.msg.system.camj.ReqBuyTeleportItemChangAnMiJingMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.camj.ChangAnMiJingManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求购买随机传送道具</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqBuyTeleportItemChangAnMiJingHandler extends AbstractHandler<ReqBuyTeleportItemChangAnMiJingMessage> {

    @Override
    public void doAction(ReqBuyTeleportItemChangAnMiJingMessage msg) {
        ChangAnMiJingManager.getInstance().buyTeleportItem(SessionUtil.getRole(msg), msg.getBuyCount());
    }

}
