package com.sh.game.system.destinyn;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.destinyn.script.IDestinynScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2021/12/9 19:32
 */
public class DestinynManager {

    private static final DestinynManager INSTANCE = new DestinynManager();

    public static DestinynManager getInstance() {
        return INSTANCE;
    }

    public void diZao(Role role, long uid, int cfgId) {
        ScriptEngine.invoke1t1(IDestinynScript.class, script -> script.diZao(role, uid, cfgId));
    }

    public void ning<PERSON>ian(Role role, long uid) {
        ScriptEngine.invoke1t1(IDestinynScript.class, script -> script.ning<PERSON>ian(role, uid));
    }

    public void xingMing(Role role, long uid) {
        ScriptEngine.invoke1t1(IDestinynScript.class, script -> script.xingMing(role, uid));
    }

    public void chuanCheng(Role role, long source, long target) {
        ScriptEngine.invoke1t1(IDestinynScript.class, script -> script.chuanCheng(role, source, target));
    }
}
