package com.sh.game.system.battlegd.msg;

import com.sh.game.common.communication.msg.system.battlegd.ReqBattleGdFightMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.BattleGdProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.battlegd.BattleGdManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求战役战斗fight</p>
* <p>Created by MessageUtil</p>
* @date 2024-08-14 上午11:20:31
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqBattleGdFightHandler extends AbstractHandler<ReqBattleGdFightMessage> {

    @Override
    public void doAction(ReqBattleGdFightMessage msg) {
        BattleGdProtos.ReqBattleGdFightMessage proto = msg.getProto();
        BattleGdManager.getInstance().reqBattleFight(SessionUtil.getRole(msg.getSession()), proto.getBattleType());
    }
}