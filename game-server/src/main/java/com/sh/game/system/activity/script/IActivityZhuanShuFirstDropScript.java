package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.entity.role.RoleZhuanshuActivity;
import com.sh.script.IScript;

import java.util.Map;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-08-30
 **/
public interface IActivityZhuanShuFirstDropScript extends IScript {

    RoleZhuanshuActivity find(long rid);

    /**
     * 请求专属信息
     *
     * @param role
     */
    void reqInfo(Role role, int area);

    /**
     * 请求专属首爆奖励
     *
     * @param role
     * @param cfgId
     */
    void reqFirstAcquire(Role role, int cfgId);

    /**
     * 请求专属宝箱奖励
     *
     * @param role
     * @param cfgId
     */
    void reqBoxAcquire(Role role, int cfgId);

    /**
     * 查找全服首爆数量
     */
    Map<Integer, Integer> findTotalCount();

    /**
     * 增加全服首爆数量
     *
     * @param cfgId 全服首爆表cfgId
     * @param count 数量
     */
    void addTotalCount(int cfgId, int count);

}
