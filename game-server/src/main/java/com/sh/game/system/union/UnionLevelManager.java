package com.sh.game.system.union;

import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.event.IEventOnUnionUpgradeScript;
import com.sh.game.system.union.script.IUnionLevelScript;
import com.sh.script.ScriptEngine;
import lombok.extern.slf4j.Slf4j;

/**
 * 行会升级
 */
@Slf4j
public class UnionLevelManager {
    private static final UnionLevelManager INSTANCE = new UnionLevelManager();

    private UnionLevelManager() {

    }

    public static UnionLevelManager getInstance() {
        return INSTANCE;
    }


    /**
     * 行会捐献
     *
     * @param role
     * @param index
     */
    public void reqDonate(Role role, int index) {
        ScriptEngine.invoke1t1(IUnionLevelScript.class, script -> script.reqDonate(role, index));
    }

    /**
     * 增加行会资金
     *
     * @param unionId
     * @param count
     * @param rid
     */
    public void addFund(long unionId, int count, long rid) {
        ScriptEngine.invoke1t1(IUnionLevelScript.class, script -> script.addFund(unionId, count, rid));
    }

    /**
     * 行会等级变更
     *
     * @param union
     */
    public void onUnionUpgrade(Union union) {
        ScriptEngine.invoke1tn(IEventOnUnionUpgradeScript.class, script -> script.onUnionUpgrade(union));
    }

}
