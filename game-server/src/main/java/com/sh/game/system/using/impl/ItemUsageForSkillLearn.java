package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.config.model.SkillConfig;
import com.sh.game.common.constant.SkillConst;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.intf.IAvatar;
import com.sh.game.common.util.condition.ConditionUtil;
import com.sh.game.system.role.entity.Hero;
import com.sh.game.system.skill.SkillManager;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

/**
 * 技能升级
 */
public class ItemUsageForSkillLearn extends ItemUsage {

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int[][] useParam = config.getUseParam();
        int skillId = useParam[0][0];
        int level = useParam[0][1];
        int type = 0;
        if (useParam[0].length > 2) {
            type = useParam[0][2];
        }
        SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, skillId);
        if (skillConfig == null) {
            return false;
        }
        IAvatar avatar;
        if (type != 0) {
            avatar = type == 1 ? role : role.getHero();
        } else {
            avatar = role.getIAvatarByItemWhere(item.getWhere());
        }
        if (avatar == null) {
            return false;
        }
//        if (!ConditionUtil.validate(avatar, config.getNeed())) {
//            return false;
//        }
        return SkillManager.getInstance().canLevelUpSkill(role, avatar.getId(), skillId, level);
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int[][] useParam = config.getUseParam();
        int skillId = useParam[0][0];
        int level = useParam[0][1];
        int type = 0;
        if (useParam[0].length > 2) {
            type = useParam[0][2];
        }
        IAvatar avatar;
        if (type != 0) {
            avatar = type == 1 ? role : role.getHero();
        } else {
            avatar = role.getIAvatarByItemWhere(item.getWhere());
        }
        if (avatar == null) {
            return false;
        }
        SkillConfig skillConfig = ConfigDataManager.getInstance().getById(SkillConfig.class, skillId);
        if (skillConfig == null) {
            return false;
        }
        //合击技能判断主角跟英雄
        if (skillConfig.getCls() == SkillConst.SkillCls.COMB_SKILL.getCls()) {
            Hero hero = role.getHero();
            if (hero == null) {
                return false;
            }
            SkillManager.getInstance().levelUpSkill(role, role.getId(), skillId, level);
            SkillManager.getInstance().levelUpSkill(role, hero.getId(), skillId, level);
        } else {
            SkillManager.getInstance().levelUpSkill(role, avatar.getId(), skillId, level);
        }
        return true;
    }

    @Override
    public int getUsedType() {
        return 140;
    }
}
