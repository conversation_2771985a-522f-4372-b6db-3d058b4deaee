package com.sh.game.system.barriergd;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleBarrierGd;
import com.sh.game.data.DataCenter;
import com.sh.game.system.barriergd.script.IBarrierGdScript;
import com.sh.script.ScriptEngine;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/13 13:22
 */
public class BarrierGdManager {
    private static final BarrierGdManager INSTANCE = new BarrierGdManager();
    private BarrierGdManager() {
    }
    public static BarrierGdManager getInstance() {
        return INSTANCE;
    }

    public RoleBarrierGd findBarrier(long roleId) {
        RoleBarrierGd roleBarrier = DataCenter.get(RoleBarrierGd.class, roleId);
        if (roleBarrier == null) {
            roleBarrier = new RoleBarrierGd();
            roleBarrier.setId(roleId);
            DataCenter.insertData(roleBarrier,true);
        }
        return roleBarrier;
    }

    public void reqBarrierInfo(Role role) {
        ScriptEngine.invoke1t1(IBarrierGdScript.class, s -> s.reqBarrierInfo(role));
    }

    public void reqBarrierReward(Role role, int barrierId) {
        ScriptEngine.invoke1t1(IBarrierGdScript.class, s -> s.reqBarrierReward(role, barrierId));
    }
}
