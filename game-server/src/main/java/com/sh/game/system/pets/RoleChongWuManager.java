package com.sh.game.system.pets;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.pets.script.IRoleChongWuScript;
import com.sh.script.ScriptEngine;

public class RoleChongWuManager {

    private static final RoleChongWuManager INSTANCE = new RoleChongWuManager();

    private RoleChongWuManager(){}

    public static RoleChongWuManager getInstance(){
        return INSTANCE;
    }

    /**
     * 请求宠物升级
     * @param role
     */
    public void reqRoleChongWuUp(Role role){
        ScriptEngine.invoke1t1(IRoleChongWuScript.class, iRoleChongWuScript -> iRoleChongWuScript.reqRoleChongWuUp(role));
    }

    /**
     * 解锁宠物
     * @param role
     * @param cfgId
     */
    public void unlockRoleChongWu(Role role, int cfgId){
        ScriptEngine.invoke1t1(IRoleChongWuScript.class, iRoleChongWuScript -> iRoleChongWuScript.unlockRoleChongWu(role, cfgId));
    }

    /**
     * 重新选择宠物
     * @param role
     * @param cfgId
     */
    public void reqReselectRoleChongWu(Role role, int cfgId){
        ScriptEngine.invoke1t1(IRoleChongWuScript.class, iRoleChongWuScript -> iRoleChongWuScript.reqReselectRoleChongWu(role, cfgId));
    }

    /**
     * 请求宠物信息
     * @param role
     */
    public void reqRoleChongWuInfo(Role role){
        ScriptEngine.invoke1t1(IRoleChongWuScript.class, iRoleChongWuScript -> iRoleChongWuScript.reqRoleChongWuInfo(role));
    }


    public void chongWuAttributeChange(Role role) {
        ScriptEngine.invoke1t1(IRoleChongWuScript.class, iRoleChongWuScript -> iRoleChongWuScript.chongWuAttributeChange(role));
    }

}
