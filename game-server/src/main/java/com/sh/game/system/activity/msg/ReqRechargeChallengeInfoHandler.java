package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityRechargeChallengeManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqRechargeChallengeInfoMessage;

/**
 * <p>请求充值挑战信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqRechargeChallengeInfoHandler extends AbstractHandler<ReqRechargeChallengeInfoMessage> {

    @Override
    public void doAction(ReqRechargeChallengeInfoMessage msg) {
        ActivityRechargeChallengeManager.getInstance().reqRechargeChallengeInfo(SessionUtil.getRole(msg));
    }

}
