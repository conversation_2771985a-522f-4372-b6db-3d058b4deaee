package com.sh.game.system.summon;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.abc.bean.RoleBriefBean;
import com.sh.game.common.communication.msg.system.summon.ResAssistMessage;
import com.sh.game.common.communication.msg.system.summon.ResSummonTipMessage;
import com.sh.game.common.communication.notice.GetPlayerPositionRetNotice;
import com.sh.game.common.config.model.DuplicateConfig;
import com.sh.game.common.constant.IDConst;
import com.sh.game.common.constant.SummonConst;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.IDUtil;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.notice.NoticeCallback;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.SummonProtos;
import com.sh.game.scene.MapProxy;
import com.sh.game.scene.MapProxyManager;
import com.sh.game.system.summon.entity.Summon;
import com.sh.game.system.team.TeamManager;
import com.sh.game.system.team.entity.TeamInfo;
import com.sh.game.system.teleport.TeleportManager;
import com.sh.game.system.union.UnionManager;

import java.util.HashMap;
import java.util.Map;

public class SummonManager {
    private static final SummonManager INSTANCE = new SummonManager();
    public static final Map<Long, Summon> summons = new HashMap<>();

    private SummonManager() {
    }

    public static SummonManager getInstance() {
        return INSTANCE;
    }


    public void summon(Role role, int summonType) {
        role.proxyCall(proxy -> proxy.reqPosition(new NoticeCallback<GetPlayerPositionRetNotice>() {
            @Override
            public void callback(GetPlayerPositionRetNotice notice) {
                Union union = null;
                TeamInfo team = null;
                switch (summonType) {
                    case SummonConst.SummonType.TEAM:
                        team = TeamManager.getInstance().getTeamByRid(role.getId());
                        break;
                    case SummonConst.SummonType.UNION:
                        union = UnionManager.getInstance().getUnion(role);
                        break;
                }
                if (union == null && team == null) {
                    return;
                }

                MapProxy mapProxy = MapProxyManager.getInstance().getMap(notice.getMapId());
                if (mapProxy == null) {
                    return;
                }

                long summonId = IDUtil.getId(IDConst.MAP);
                Summon summon = new Summon(summonType, role.getId(), role.getName(), mapProxy.getCfgId(), mapProxy.getId(), notice.getX(), notice.getY());
                SummonManager.summons.put(summonId, summon);

                ResSummonTipMessage msg = new ResSummonTipMessage();
                RoleBriefBean roleBriefBean = new RoleBriefBean();
                roleBriefBean.setUid(role.getId());
                roleBriefBean.setName(role.getName());
                msg.setProto(SummonProtos.ResSummonTip.newBuilder()
                        .setSummoner(AbcProtos.RoleBriefBean.newBuilder()
                                .setUid(role.getId())
                                .setName(role.getName())
                                .build())
                        .setSummonId(summonId)
                        .setType(summon.getType())
                        .setMap(summon.getMapCfgId())
                        .setExpire(summon.getExpire())
                        .build());
                if (union != null) {
                    MessageUtil.sendToUnion(union, msg);
                }
                if (team != null) {
                    team.sendMessage(msg, 0);
                }
            }
        }));
    }

    public void reqAnswer(Role role, long summonId) {
        Summon summon = summons.get(summonId);
        if (summon == null || summon.getExpire() < TimeUtil.getNowOfSeconds()) {
            return;
        }

        switch (summon.getType()) {
            case SummonConst.SummonType.TEAM:
                TeamInfo team = TeamManager.getInstance().getTeamByRid(summon.getSummonerId());
                if (team == null) {
                    TipUtil.show(role, CommonTips.服务_没有相同的队伍);
                    return;
                }
                break;
            case SummonConst.SummonType.UNION:
                Union union = UnionManager.getInstance().getUnion(DataCenter.getRole(summon.getSummonerId()));
                if (union == null || union.getId() != role.getUnion().getId()) {
                    TipUtil.show(role, CommonTips.服务_没有相同的队伍);
                    return;
                }
                break;
        }

        TeleportManager.getInstance().teleport(role, summon.getMapCfgId(), summon.getMapId(), summon.getX(), summon.getY(),summon.getMapId());
    }

    /**
     * 请求协助
     *
     * @param role role
     */
    public void reqAssist(Role role) {
        TeamInfo team = TeamManager.getInstance().getTeamByRid(role.getId());
        if (team == null || team.getCaptain() != role.getId()) {
            return;
        }

        role.proxyCall(proxy -> proxy.reqPosition(new NoticeCallback<GetPlayerPositionRetNotice>() {
            @Override
            public void callback(GetPlayerPositionRetNotice notice) {

                MapProxy mapProxy = MapProxyManager.getInstance().getMap(notice.getMapId());
                if (mapProxy == null) {
                    return;
                }
                DuplicateConfig duplicateConfig = ConfigDataManager.getInstance().getById(DuplicateConfig.class, mapProxy.getCfgId());
                if (duplicateConfig == null) {
                    return;
                }
                ResAssistMessage msg = new ResAssistMessage();
                RoleBriefBean roleBriefBean = new RoleBriefBean();
                roleBriefBean.setUid(role.getId());
                roleBriefBean.setName(role.getName());

                msg.setProto(SummonProtos.ResAssist.newBuilder()
                        .setSummoner(AbcProtos.RoleBriefBean.newBuilder()
                                .setUid(role.getId())
                                .setName(role.getName())
                                .build())
                        .setTeleportId(duplicateConfig.getDeliver())
                        .setMap(mapProxy.getCfgId())
                        .setExpire(TimeUtil.getNowOfSeconds() + 30)
                        .build());
                team.sendMessage(msg, role.getId());
            }
        }));
    }
}
