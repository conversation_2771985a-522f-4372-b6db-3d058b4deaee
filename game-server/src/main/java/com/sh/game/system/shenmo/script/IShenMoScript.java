package com.sh.game.system.shenmo.script;

import com.sh.game.common.entity.attr.Attribute;
import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/10/17
 */
public interface IShenMoScript extends IScript {
    void reqShenMoInfo(Role role);

    void reqReqShenMoLevelUp(Role role, int shenMoId);

    void reqShenMoResonanceLevelUp(Role role, int resonanceId);

    void reqShenMoGoBattle(Role role, List<Integer> shenMoIdList);

    void shenMoAttr(Role role, Attribute attribute);
}
