package com.sh.game.system.shenWeiTuPo;


import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.shenWeiTuPo.script.IShenWeiScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @Description: 神威突破
 * @Date 2022-05-06 13:32
 **/
public class ShenWeiManager {

    private static final ShenWeiManager INSTANCE = new ShenWeiManager();

    private ShenWeiManager() {
    }

    public static ShenWeiManager getInstance() {
        return INSTANCE;
    }

    /**
     * 请求神威突破升级
     *
     * @param role
     * @param type
     */
    public void reqShenWeiUp(Role role, int type) {
        ScriptEngine.invoke1t1(IShenWeiScript.class, script -> script.reqShenWeiUp(role, type));
    }

    /**
     * 请求神威信息
     *
     * @param role
     */
    public void reqShenWeiInfo(Role role) {
        ScriptEngine.invoke1t1(IShenWeiScript.class, script -> script.reqShenWeiInfo(role));
    }


}
