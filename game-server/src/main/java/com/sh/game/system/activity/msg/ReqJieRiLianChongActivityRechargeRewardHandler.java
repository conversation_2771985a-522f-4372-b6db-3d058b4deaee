package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqJieRiLianChongActivityRechargeRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityJieRiLianChongManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求节日连充连充奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqJieRiLianChongActivityRechargeRewardHandler extends AbstractHandler<ReqJieRiLianChongActivityRechargeRewardMessage> {

    @Override
    public void doAction(ReqJieRiLianChongActivityRechargeRewardMessage msg) {
        ActivityProtos.ReqJieRiLianChongActivityRechargeReward proto = msg.getProto();
        ActivityJieRiLianChongManager.getInstance().reqActivityRechargeReward(SessionUtil.getRole(msg), proto.getCfgId());
    }

}
