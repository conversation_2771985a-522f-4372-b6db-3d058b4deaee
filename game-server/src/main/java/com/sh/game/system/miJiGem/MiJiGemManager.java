package com.sh.game.system.miJiGem;


import com.sh.game.common.communication.msg.abc.bean.EquipPosGemBean;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.protos.AbcProtos;
import com.sh.game.system.miJiGem.script.IMiJiGemScript;
import com.sh.script.ScriptEngine;

import java.util.List;

public class MiJiGemManager {
    private static final MiJiGemManager INSTANCE = new MiJiGemManager();

    private MiJiGemManager() {
    }

    public static MiJiGemManager getInstance() {
        return INSTANCE;
    }

    public void reqMiJiRingStoneInfo(Role role) {
        ScriptEngine.invoke1t1(IMiJiGemScript.class, script -> script.reqMiJiRingStoneInfo(role));
    }

    /**
     * 镶嵌宝石
     *
     * @param role
     * @param itemId     宝石id
     * @param equipIndex 装备位pos
     */
    public void reqMiJiInlaidGem(Role role, int itemId, int equipIndex) {
        ScriptEngine.invoke1t1(IMiJiGemScript.class, script -> script.reqMiJiInlaidGem(role, itemId, equipIndex));
    }

    /**
     * 宝石合成
     *
     * @param role
     * @param itemId     材料id
     * @param equipIndex 装备位pos
     * @param backpack   是否是已镶嵌宝石合成
     */
    public void reqMiJiGemCompound(Role role, int itemId, int equipIndex, int backpack) {
        ScriptEngine.invoke1t1(IMiJiGemScript.class, script -> script.reqMiJiGemCompound(role, itemId, equipIndex, backpack));
    }

    /**
     * 一键镶嵌宝石
     *
     * @param role
     * @param equipIndex 装备位pos
     */
    public void reqMiJiOneClickCompletionGem(Role role, int equipIndex) {
        ScriptEngine.invoke1t1(IMiJiGemScript.class, script -> script.reqMiJiOneClickCompletionGem(role, equipIndex));
    }

    /**
     * 卸下宝石
     *
     * @param role
     * @param index
     */
    public void reqMiJiRemoveGem(Role role, int equipIndex, int index) {
        ScriptEngine.invoke1t1(IMiJiGemScript.class, script -> script.reqMiJiRemoveGem(role, equipIndex, index));
    }

    /**
     * 一键卸下
     *
     * @param role
     */
    public void reqMiJiOneClickRemoveGem(Role role, int equipIndex) {
        ScriptEngine.invoke1t1(IMiJiGemScript.class, script -> script.reqMiJiOneClickRemoveGem(role, equipIndex));
    }

    /**
     * 查询玩家秘籍灵石bean信息
     *
     * @param role
     * @return
     */
    public List<AbcProtos.EquipPosGemBean> findRingGemBean(Role role) {
        return ScriptEngine.invoke1t1WithRet(IMiJiGemScript.class, script -> script.findGemBean(role));
    }

}
