package com.sh.game.system.cross.msg;

import com.sh.game.common.communication.msg.map.duplicate.ReqWorldFirstKempInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.cross.CrossManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求天下第一冠军信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqWorldFirstKempInfoHandler extends AbstractHandler<ReqWorldFirstKempInfoMessage> {

    @Override
    public void doAction(ReqWorldFirstKempInfoMessage msg) {
        CrossManager.getInstance().ReqWorldFirstKempInfo(SessionUtil.getRole(msg.getSession()));
    }

}
