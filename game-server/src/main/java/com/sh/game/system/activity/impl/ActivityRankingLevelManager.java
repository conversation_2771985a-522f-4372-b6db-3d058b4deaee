package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityRankingLevelScript;
import com.sh.script.ScriptEngine;

import java.util.Optional;

public class ActivityRankingLevelManager {
    private static final ActivityRankingLevelManager INSTANCE = new ActivityRankingLevelManager();

    private ActivityRankingLevelManager() {

    }

    public static ActivityRankingLevelManager getInstance() {
        return INSTANCE;
    }


    public void reqRanking(Role role, int type) {
        Optional.ofNullable(ScriptEngine.get1t1(IActivityRankingLevelScript.class)).ifPresent(script -> script.reqRanking(role, type));
    }


}
