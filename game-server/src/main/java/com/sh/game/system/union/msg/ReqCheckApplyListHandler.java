package com.sh.game.system.union.msg;

import com.sh.game.common.communication.msg.system.union.ReqCheckApplyListMessage;
import com.sh.game.protos.UnionProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求处理申请列表信息</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 15:51:41
 */
public class ReqCheckApplyListHandler extends AbstractHandler<ReqCheckApplyListMessage> {

    @Override
    public void doAction(ReqCheckApplyListMessage msg) {
        UnionProtos.ReqCheckApplyList proto = msg.getProto();
        UnionManager.getInstance().handleApplication(SessionUtil.getRole(msg.getSession()), proto.getListList(), proto.getCheckState());

    }

}
