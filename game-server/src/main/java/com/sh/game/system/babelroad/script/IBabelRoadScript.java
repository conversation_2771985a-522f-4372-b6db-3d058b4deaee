package com.sh.game.system.babelroad.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.babelroad.entity.RoleBabelRoadData;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @date 2022/6/6 21:13
 */
public interface IBabelRoadScript extends IScript {

    /**
     * 发送通天之路信息
     *
     * @param role 角色
     */
    void sendBabelRoadInfo(Role role);

    /**
     * 进入通天之路副本地图
     *
     * @param role 角色
     */
    void enterDuplicate(Role role);

    /**
     * 领取通天之路每日奖励
     *
     * @param role 角色
     */
    void gainBabelRoadDailyReward(Role role);

    /**
     * 获取角色通天之路数据
     *
     * @param role 角色
     * @return RoleBabelRoadData 通天之路数据
     */
    RoleBabelRoadData find(Role role);
}
