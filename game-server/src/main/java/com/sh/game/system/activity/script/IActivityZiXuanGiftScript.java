package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

import java.util.List;

public interface IActivityZiXuanGiftScript extends IScript {

    void reqInfo(Role role);

    /**
     * 选择礼包必备奖励
     *
     * @param role
     * @param cfgId       礼包配置id
     * @param rewardIndex 自选奖励下标
     */
    void reqSelectRewardByGiftId(Role role, int cfgId, List<Integer> gridIndex, List<Integer> rewardIndex);

    void freeZiXuanPack(Role role, int cfgId);
}
