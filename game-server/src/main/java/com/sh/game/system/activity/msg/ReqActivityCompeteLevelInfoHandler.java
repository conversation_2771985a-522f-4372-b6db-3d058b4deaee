package com.sh.game.system.activity.msg;

import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityCompeteLevelManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqActivityCompeteLevelInfoMessage;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivityCompeteLevelInfoHandler extends AbstractHandler<ReqActivityCompeteLevelInfoMessage> {

    @Override
    public void doAction(ReqActivityCompeteLevelInfoMessage msg) {
        ActivityProtos.ReqActivityCompeteLevelInfo proto = msg.getProto();
        ActivityCompeteLevelManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()), proto.getType());
    }

}
