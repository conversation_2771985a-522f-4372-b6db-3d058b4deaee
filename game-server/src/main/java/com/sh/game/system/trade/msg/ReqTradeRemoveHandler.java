package com.sh.game.system.trade.msg;

import com.sh.game.common.communication.msg.system.trade.ReqTradeRemoveMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.TradeProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.trade.TradeManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求移除交易物品</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_SOCIAL)
public class ReqTradeRemoveHandler extends AbstractHandler<ReqTradeRemoveMessage> {

    @Override
    public void doAction(ReqTradeRemoveMessage msg) {
        TradeProtos.ReqTradeRemove tradeRemove = msg.getProto();
        TradeManager.getInstance().reqTradeRemove(SessionUtil.getRole(msg.getSession()), tradeRemove.getGrid());
    }

}
