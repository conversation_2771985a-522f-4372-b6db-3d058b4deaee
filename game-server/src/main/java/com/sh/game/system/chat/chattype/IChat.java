package com.sh.game.system.chat.chattype;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.communication.msg.system.chat.ResChatMessage;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 * <p>
 *
 * <AUTHOR> xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2017/7/29 16:42
 * desc  : 聊天接口
 * Copyright(©) 2017 by xiaomo.
 */
public interface IChat {

    void chat(Role role, long target, ResChatMessage msg, int banFlag);
}
