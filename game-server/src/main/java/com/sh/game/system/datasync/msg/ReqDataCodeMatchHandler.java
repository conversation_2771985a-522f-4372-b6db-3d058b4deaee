package com.sh.game.system.datasync.msg;

import com.sh.game.protos.DatasyncProtos;
import com.sh.game.system.datasync.DataSyncManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.datasync.ReqDataCodeMatchMessage;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>发送当前配置版本信息列表</p>
 * <p>Created by MessageUtil</p>
 * @date 2020-08-05 09:30:56
 */
public class ReqDataCodeMatchHandler extends AbstractHandler<ReqDataCodeMatchMessage> {

    @Override
    public void doAction(ReqDataCodeMatchMessage msg) {
        Map<String, String> versions = new HashMap<>();
        for (DatasyncProtos.DataVersionBean bean: msg.getProto().getVersionsList()) {
            versions.put(bean.getName(), bean.getVersion());
        }
        DataSyncManager.getInstance().reqDataCodeMatch(msg.getSession(), versions);
    }

}
