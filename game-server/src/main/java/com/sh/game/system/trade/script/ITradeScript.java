package com.sh.game.system.trade.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

public interface ITradeScript extends IScript {

    /**
     * 请求交易
     *
     * @param role  发起者
     * @param oppId 接收者
     */
    void reqTradeTrading(Role role, long oppId);

    /**
     * 同意交易
     *
     * @param role role
     */
    void reqTradeAccept(Role role);

    /**
     * 请求拒绝交易
     *
     * @param role   role
     * @param roleId 被拒绝者id
     * @param type   1主动拒绝 2未响应 3不在附近
     */
    void reqTradeRefuse(Role role, long roleId, int type);

    /**
     * 请求添加交易道具
     *
     * @param role  role
     * @param where 道具来源
     * @param index 来源中的位置
     * @param count 数量
     * @param grid  交易列表位置
     */
    void reqTradeAppend(Role role, int where, int index, int count, int grid);

    /**
     * 请求移除交易道具
     *
     * @param role role
     * @param grid 交易列表位置
     */
    void reqTradeRemove(Role role, int grid);

    /**
     * 请求交易上锁
     *
     * @param role role
     */
    void reqTradeLock(Role role);

    /**
     * 请求交易解锁
     *
     * @param role role
     */
    void reqTradeUnLock(Role role);

    /**
     * 请求取消交易
     *
     * @param rid 玩家id
     */
    void reqTradeCancel(long rid);

    /**
     * 请求完成交易
     *
     * @param role role
     */
    void reqTradeDeal(Role role);

}
