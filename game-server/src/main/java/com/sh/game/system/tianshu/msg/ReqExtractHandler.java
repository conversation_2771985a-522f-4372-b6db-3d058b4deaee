package com.sh.game.system.tianshu.msg;

import com.sh.game.common.communication.msg.system.tianshu.ReqExtractMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.TianshuProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.tianshu.TianShuManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求萃取</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqExtractHandler extends AbstractHandler<ReqExtractMessage> {

    @Override
    public void doAction(ReqExtractMessage msg) {
        TianshuProtos.ReqExtract proto = msg.getProto();
        TianShuManager.getInstance().reqExtract(SessionUtil.getRole(msg.getSession()), proto.getEquipUidListList());
    }

}
