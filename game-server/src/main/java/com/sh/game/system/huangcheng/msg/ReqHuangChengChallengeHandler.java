package com.sh.game.system.huangcheng.msg;

import com.sh.game.common.communication.msg.system.huangcheng.ReqHuangChengChallengeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.huangcheng.HuangChengManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求皇城挑战列表</p>
* <p>Created by MessageUtil</p>
* @date 2024-12-30 下午5:05:25
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqHuangChengChallengeHandler extends AbstractHandler<ReqHuangChengChallengeMessage> {

    @Override
    public void doAction(ReqHuangChengChallengeMessage msg) {
        HuangChengManager.getInstance().reqChallenge(SessionUtil.getRole(msg));
    }
}