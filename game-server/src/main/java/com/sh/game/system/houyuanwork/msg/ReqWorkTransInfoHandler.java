package com.sh.game.system.houyuanwork.msg;

import com.sh.game.common.communication.msg.system.work.ReqWorkTransInfo;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.houyuanwork.HouYuanWorkManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求建筑生产详细信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqWorkTransInfoHandler extends AbstractHandler<ReqWorkTransInfo> {

    @Override
    public void doAction(ReqWorkTransInfo msg) {
        HouYuanWorkManager.getInstance().sendTransferMsg(SessionUtil.getRole(msg));
    }

}
