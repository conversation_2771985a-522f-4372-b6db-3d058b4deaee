package com.sh.game.system.militaryRank.msg;

import com.sh.game.common.communication.msg.system.militaryRank.ReqMilitaryRankInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.militaryRank.MilitaryRankManger;
import com.sh.server.AbstractHandler;

/**
 * <p>请求军衔信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqMilitaryRankInfoHandler extends AbstractHandler<ReqMilitaryRankInfoMessage> {

    @Override
    public void doAction(ReqMilitaryRankInfoMessage msg) {
        MilitaryRankManger.getInstance().sendMilitaryRankInfo(SessionUtil.getRole(msg.getSession()));
    }

}
