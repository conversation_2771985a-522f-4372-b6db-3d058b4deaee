package com.sh.game.system.magicweapon.entity;

import com.sh.game.common.entity.backpack.item.MagicWeapon;
import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.magiccircle.entity.MagicCircleInfo;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色法宝开孔信息
 *
 * <AUTHOR>
 * @date 2022/6/23 23:00
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleMagicWeaponSlot extends AbstractRoleEntity {

    /**
     * 角色ID
     */
    @Tag(1)
    private long id;

    /**
     * 开孔id信息列表
     * cfg_equip_fabaokong表id
     */
    @Tag(2)
    private List<Integer> slotIdList = new ArrayList<>();

    /**
     * 法器融合列表
     * key:     开孔id, cfg_equip_fabaokong表id
     * value:   法宝
     */
    @Tag(3)
    private Map<Integer, MagicWeapon> magicWeaponMap = new HashMap<>();
}
