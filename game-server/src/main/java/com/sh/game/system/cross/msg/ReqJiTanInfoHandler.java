package com.sh.game.system.cross.msg;

import com.sh.game.common.communication.msg.map.cross.ReqJiTanInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.system.cross.CrossManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求祭坛总信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_COMMON)
public class ReqJiTanInfoHandler extends AbstractHandler<ReqJiTanInfoMessage> {

    @Override
    public void doAction(ReqJiTanInfoMessage msg) {
        CrossManager.getInstance().reqJiTanInfo(msg.getSession().getId());
    }

}
