package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.entity.role.RoleZhanPan;
import com.sh.script.IScript;

/**
 * 如意转盘
 *
 * <AUTHOR>
 * @<PERSON>ail <EMAIL>
 * @since 2022-06-27
 **/
public interface IActivityRuYiZhuanPanScript extends IScript {

    RoleZhanPan find(long rid);

    void reqInfo(Role role);

    void reqRaffle(Role role, int group);

    void reqRecordInfo(Role role);

}
