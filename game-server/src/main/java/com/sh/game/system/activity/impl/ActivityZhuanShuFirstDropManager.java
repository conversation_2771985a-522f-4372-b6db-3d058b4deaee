package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.entity.role.RoleZhuanshuActivity;
import com.sh.game.system.activity.script.IActivityZhuanShuFirstDropScript;
import com.sh.script.ScriptEngine;

import java.util.Map;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-08-30
 **/
public class ActivityZhuanShuFirstDropManager {

    private static final ActivityZhuanShuFirstDropManager INSTANCE = new ActivityZhuanShuFirstDropManager();

    private ActivityZhuanShuFirstDropManager() {
    }

    public static ActivityZhuanShuFirstDropManager getInstance() {
        return INSTANCE;
    }

    /**
     * 获取专属首爆数据
     *
     * @param rid
     * @return
     */
    public RoleZhuanshuActivity find(long rid) {
        return ScriptEngine.invoke1t1WithRet(IActivityZhuanShuFirstDropScript.class, script -> script.find(rid));
    }

    /**
     * 请求专属信息
     *
     * @param role
     */
    public void reqInfo(Role role, int area) {
        ScriptEngine.invoke1t1(IActivityZhuanShuFirstDropScript.class, script -> script.reqInfo(role, area));
    }

    /**
     * 请求获取专属收集奖励
     *
     * @param role
     * @param cfgId
     */
    public void reqFirstAcquire(Role role, int cfgId) {
        ScriptEngine.invoke1t1(IActivityZhuanShuFirstDropScript.class, script -> script.reqFirstAcquire(role, cfgId));
    }

    /**
     * 请求获取专属宝箱奖励
     *
     * @param role
     * @param cfgId
     */
    public void reqBoxAcquire(Role role, int cfgId) {
        ScriptEngine.invoke1t1(IActivityZhuanShuFirstDropScript.class, script -> script.reqBoxAcquire(role, cfgId));
    }


    /**
     * 查找全服首爆数量
     */
    public Map<Integer, Integer> findTotalCount() {
        return ScriptEngine.invoke1t1WithRet(IActivityZhuanShuFirstDropScript.class, script -> script.findTotalCount());
    }

    /**
     * 增加全服首爆数量
     *
     * @param cfgId 全服首爆表cfgId
     * @param count 数量
     */
    public void addTotalCount(int cfgId, int count) {
        ScriptEngine.invoke1t1(IActivityZhuanShuFirstDropScript.class, script -> script.addTotalCount(cfgId, count));
    }

}
