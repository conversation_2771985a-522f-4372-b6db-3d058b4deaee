package com.sh.game.system.scene.msg;

import com.sh.game.common.communication.msg.system.scene.ReqCleanPkvalueMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.SceneProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.scene.SceneManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求清洗红名</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqCleanPkvalueHandler extends AbstractHandler<ReqCleanPkvalueMessage> {

    @Override
    public void doAction(ReqCleanPkvalueMessage msg) {
        SceneProtos.ReqCleanPkvalue proto = msg.getProto();
        SceneManager.getInstance().reqCleanPkvalue(SessionUtil.getRole(msg), proto.getItemID());
    }

}
