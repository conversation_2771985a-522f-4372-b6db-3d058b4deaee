package com.sh.game.system.render.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.protos.AbcProtos;
import com.sh.game.system.render.entity.RenderData;
import com.sh.script.IScript;

import java.util.List;

/**
 * <p>
 * 摆摊功能
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
public interface IRenderScript extends IScript {

    void reqClickIncome(Role role, int secondCount);

    void reqSecondIncome(Role role);

    void reqEnergyBox(Role role);

    void reqRoleLevel(Role role);

    void reqRoleLevelNotCost(Role role);

    void quickPractice(Role role);

    void lianDian(Role role, int rate);

    void multiple(Role role);

    void reqSettle2048(Role role, List<AbcProtos.CommonKeyValueBean> items, int rate);

    void fromZhuanSheng(Role role, List<int[]> incomes, boolean onRoleCreate);

    void qiaoQianClickIncomeUpdate(Role role);

    void quickKan<PERSON><PERSON>(Role role);

    void reqLi<PERSON>ian<PERSON><PERSON><PERSON>(Role role);

    /**
     * 点击收益(赚钱)/每秒收益(修为) 计算
     *
     * @param renderData            RenderData
     * @param click                 点击收益(赚钱)
     * @param second                每秒收益(修为)
     * @param resIncomeInfoMessage  是否推送客户端收益msg
     */
    void calRenderIncome(Role role, RenderData renderData, boolean click, boolean second, boolean resIncomeInfoMessage);

    void useItemEnergy(Role role, int value);
}
