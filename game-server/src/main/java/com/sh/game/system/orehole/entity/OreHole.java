package com.sh.game.system.orehole.entity;

import com.sh.game.protos.OreholeProtos;
import com.sh.game.system.user.NameManager;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ATO：lrf;
 * 时间：2020/12/9 14:12;
 * 版本：1.0;
 * 描述：记录挖矿信息
 */
@Getter
@Setter
public class OreHole {
    /**
     * 挖矿开始时间 秒
     */
    @Tag(1)
    private int startTime;

    /**
     * 奖励
     */
    @Tag(2)
    private Map<Integer, Integer> reward = new ConcurrentHashMap<>();

    /**
     * 坐标
     */
    @Tag(3)
    private int pointIndex;

    @Tag(4)
    private long roleId;

    /**
     * 更新领奖时间
     */
    @Tag(5)
    private int rewardTime;

    @Tag(6)
    private long minerId;

    /**
     * 挖矿结束时间 秒
     */
    @Tag(8)
    private int endTime;

    /**
     * 自动出售
     */
    @Tag(9)
    private boolean autoSale;


    /**
     * 出售获得得奖励
     */
    @Tag(10)
    private int saleReward;

    public OreholeProtos.MinerBean toMinerBean() {
        OreholeProtos.MinerBean.Builder bean = OreholeProtos.MinerBean.newBuilder();
        bean.setPositionId(pointIndex);
        bean.setRoleId(minerId);
        bean.setRoleName(NameManager.getInstance().getNameByRid(roleId));
        return bean.build();
    }
}
