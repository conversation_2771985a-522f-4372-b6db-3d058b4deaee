package com.sh.game.system.faqihuiyuan;

import com.sh.game.common.config.model.FengHaoConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.faqihuiyuan.script.IFaQiHuiYuanScript;
import com.sh.script.ScriptEngine;

import java.util.Optional;

public class FaQiHuiYuanManager {
    private static final FaQiHuiYuanManager INSTANCE = new FaQiHuiYuanManager();
    private FaQiHuiYuanManager(){

    }

    public static FaQiHuiYuanManager getInstance(){
        return INSTANCE;
    }

    private Optional<IFaQiHuiYuanScript> getScript() {
        return Optional.ofNullable(ScriptEngine.get1t1(IFaQiHuiYuanScript.class));
    }

    /**
     * 升级(必须一级组往上升)
     * @param role
     */
    public void upgrade(Role role){
        getScript().ifPresent(IFaQiHuiYuanScript -> IFaQiHuiYuanScript.upgrade(role));
    }

    /**
     * 修改封号等级
     */
    public boolean changeLv(Role role, int nLevel){
        return ScriptEngine.invoke1t1WithRet(IFaQiHuiYuanScript.class, script -> script.changeLv(role, nLevel));
    }
    public boolean checkIsCanChangeLv(Role role, int nLevel){
        return ScriptEngine.invoke1t1WithRet(IFaQiHuiYuanScript.class, script -> script.checkIsCanChangeLv(role, nLevel));
    }
    /**
     * 查询状态
     */
    public void queryFaQiHuiYuan(Role role){
        getScript().ifPresent(IFaQiHuiYuanScript -> IFaQiHuiYuanScript.queryFaQiHuiYuan(role));
    }


    /**
     * 轮回塔扫荡
     */
    public void lunHuiTaReward(Role role){
        getScript().ifPresent(IFaQiHuiYuanScript -> IFaQiHuiYuanScript.lunHuiTaReward(role));

    }

    /**
     * 领取每日工资
     */
   public void getDailyReward(Role role){
       getScript().ifPresent(IFaQiHuiYuanScript -> IFaQiHuiYuanScript.getDailyReward(role));
   }

}
