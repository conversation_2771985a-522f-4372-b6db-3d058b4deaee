package com.sh.game.system.monster.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.monster.MonsterManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.monster.ReqMonsterWorldBossInfoMessage;

/**
 * <p>返回世界Boss复活时间信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_COMMON)
public class ReqMonsterWorldBossInfoHandler extends AbstractHandler<ReqMonsterWorldBossInfoMessage> {

    @Override
    public void doAction(ReqMonsterWorldBossInfoMessage msg) {
        MonsterManager.getInstance().reqWordBossInfo(SessionUtil.getRole(msg.getSession()));
    }

}
