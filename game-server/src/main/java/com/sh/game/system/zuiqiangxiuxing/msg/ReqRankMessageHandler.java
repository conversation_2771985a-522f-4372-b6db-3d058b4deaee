package com.sh.game.system.zuiqiangxiuxing.msg;

import com.sh.game.common.communication.msg.system.zuiqiangxiuxing.ReqRankMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.zuiqiangxiuxing.ZuiQiangXiuXingManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqRankMessageHandler extends AbstractHandler<ReqRankMessage> {
    @Override
    public void doAction(ReqRankMessage msg) {
        ZuiQiangXiuXingManager.getInstance().sendRankMessage(SessionUtil.getRole(msg), msg.getProto().getType());
    }
}
