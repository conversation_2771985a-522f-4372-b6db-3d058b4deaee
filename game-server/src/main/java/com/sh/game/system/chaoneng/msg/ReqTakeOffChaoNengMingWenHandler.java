package com.sh.game.system.chaoneng.msg;

import com.sh.game.common.communication.msg.system.chaoneng.ReqTakeOffChaoNengMingWen;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ChaoNengProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.chaoneng.ChaoNengManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqTakeOffChaoNengMingWenHandler extends AbstractHandler<ReqTakeOffChaoNengMingWen> {
    @Override
    public void doAction(ReqTakeOffChaoNengMingWen msg) {
        ChaoNengProtos.ReqTakeOffChaoNengMingWen proto = msg.getProto();
        ChaoNengManager.getInstance().takeOffMingWen(SessionUtil.getRole(msg), proto.getChaoNengId(), proto.getIndex(), proto.getLineUpindex());
    }
}
