package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityRankingRechargeScript;
import com.sh.script.ScriptEngine;

import java.util.Optional;

public class ActivityRankingRechargeManager {
    private static final ActivityRankingRechargeManager INSTANCE = new ActivityRankingRechargeManager();

    private ActivityRankingRechargeManager() {

    }

    public static ActivityRankingRechargeManager getInstance() {
        return INSTANCE;
    }


    public void reqRanking(Role role) {
        Optional.ofNullable(ScriptEngine.get1t1(IActivityRankingRechargeScript.class)).ifPresent(script -> script.reqRanking(role));
    }

    public void reqRankingTop(Role role){
        Optional.ofNullable(ScriptEngine.get1t1(IActivityRankingRechargeScript.class)).ifPresent(script -> script.reqRankingTop(role));
    }
}
