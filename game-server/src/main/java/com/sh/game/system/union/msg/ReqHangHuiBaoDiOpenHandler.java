package com.sh.game.system.union.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionHangHuiBaoDiManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.union.ReqHangHuiBaoDiOpenMessage;

/**
 * <p>请求开启行会宝地</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqHangHuiBaoDiOpenHandler extends AbstractHandler<ReqHangHuiBaoDiOpenMessage> {

    @Override
    public void doAction(ReqHangHuiBaoDiOpenMessage msg) {
        UnionHangHuiBaoDiManager.getInstance().reqOpenHangHuiBaoDi(SessionUtil.getRole(msg));
    }

}
