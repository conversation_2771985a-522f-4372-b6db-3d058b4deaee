package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityWaBaoRankingScript;
import com.sh.script.ScriptEngine;

import java.util.Optional;

public class ActivityWaBaoRankingManager {
    private static final ActivityWaBaoRankingManager INSTANCE = new ActivityWaBaoRankingManager();

    private ActivityWaBaoRankingManager() {

    }

    public static ActivityWaBaoRankingManager getInstance() {
        return INSTANCE;
    }


    public void reqRanking(Role role) {
        Optional.ofNullable(ScriptEngine.get1t1(IActivityWaBaoRankingScript.class)).ifPresent(script -> script.reqRanking(role));
    }

    public void reqRankingTop(Role role){
        Optional.ofNullable(ScriptEngine.get1t1(IActivityWaBaoRankingScript.class)).ifPresent(script -> script.reqRankingTop(role));
    }
}
