package com.sh.game.system.backpack.msg;

import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.backpack.BackpackManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.backpack.ReqBuyPhysicalPowerMessage;

/**
 * <p>请求领取体力</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(3)
public class ReqBuyPhysicalPowerHandler extends AbstractHandler<ReqBuyPhysicalPowerMessage> {

    @Override
    public void doAction(ReqBuyPhysicalPowerMessage msg) {
        BackpackManager.getInstance().reqDailyPhysicalPower(SessionUtil.getRole(msg));
    }

}
