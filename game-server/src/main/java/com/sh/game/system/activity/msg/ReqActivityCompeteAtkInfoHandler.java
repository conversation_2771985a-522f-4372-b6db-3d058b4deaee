package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqActivityCompeteAtkInfoMessage;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityCompeteAtkManager;
import com.sh.server.AbstractHandler;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivityCompeteAtkInfoHandler extends AbstractHandler<ReqActivityCompeteAtkInfoMessage> {

    @Override
    public void doAction(ReqActivityCompeteAtkInfoMessage msg) {
        ActivityProtos.ReqActivityCompeteAtkInfo proto = msg.getProto();
        ActivityCompeteAtkManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()), proto.getType());
    }

}
