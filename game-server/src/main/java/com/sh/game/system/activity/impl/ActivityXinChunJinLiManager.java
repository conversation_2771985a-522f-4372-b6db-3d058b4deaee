package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityXinChunJinLiScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2022/12/27 16:45
 */
public class ActivityXinChunJinLiManager {

    private static final ActivityXinChunJinLiManager INSTANCE = new ActivityXinChunJinLiManager();

    public static ActivityXinChunJinLiManager getInstance() {
        return INSTANCE;
    }

    public void info(Role role) {
        ScriptEngine.invoke1t1(IActivityXinChunJinLiScript.class, s -> s.info(role));
    }
}
