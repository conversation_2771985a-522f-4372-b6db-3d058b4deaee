package com.sh.game.system.chengzhangjijin;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.chengzhangjijin.script.IActivityChengZhangJiJinScript;
import com.sh.script.ScriptEngine;

import java.util.Optional;

/**
 * <AUTHOR>
 * Created by Silence on 2022/4/19.
 */
public class ChengZhangJiJinManager {
    private static final ChengZhangJiJinManager instance = new ChengZhangJiJinManager();

    private ChengZhangJiJinManager() {
    }

    public static ChengZhangJiJinManager getInstance() {
        return instance;
    }

    private Optional<IActivityChengZhangJiJinScript> getScript() {
        return Optional.ofNullable(ScriptEngine.get1t1(IActivityChengZhangJiJinScript.class));
    }


    public void queryChengZhangJiJinInfo(Role role){
        getScript().ifPresent(IActivityChengZhangJiJinScript -> IActivityChengZhangJiJinScript.queryChengZhangJiJinInfo(role));
    }

    public void receiveChengZhangeJiJinReward(Role role, int idx) {
        getScript().ifPresent(IActivityChengZhangJiJinScript -> IActivityChengZhangJiJinScript.receiveChengZhangeJiJinReward(role, idx));
    }


}
