package com.sh.game.system.backpack;

import com.sh.game.common.communication.notice.BackpackStashCommitRetNotice;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.backpack.Backpack;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.constant.BackpackConst;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.backpack.storage.ItemChange;
import com.sh.game.common.entity.backpack.storage.Storage;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.event.IEventOnRoleCoinChangedScript;
import com.sh.game.notice.NoticeCallback;
import com.sh.game.system.backpack.script.IBackpackScript;
import com.sh.script.ScriptEngine;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;


@Slf4j
public class BackpackManager {
    private static final BackpackManager INSTANCE = new BackpackManager();

    private BackpackManager() {
    }

    public static BackpackManager getInstance() {
        return INSTANCE;
    }

    /**
     * 获取背包货币信息
     *
     * @param role
     */
    public void reqCoin(Role role) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.reqCoin(role));
    }

    /**
     * 请求当前类型背包的道具信息
     *
     * @param role
     * @param where 背包类型
     */
    public void reqGrid(Role role, int where) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.reqGrid(role, where));
    }

    /**
     * 解锁格子
     *
     * @param role
     * @param where
     * @param count
     */
    public void reqUnlock(Role role, int where, int count) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.reqUnlock(role, where, count));
    }

    /**
     * 整理背包
     *
     * @param role
     * @param where
     */
    public void reqSort(Role role, int where) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.reqSort(role, where));
    }

    /**
     * 穿脱装备处理类 比如脱装备是从类型0的装备挪到11的背包中
     *
     * @param role
     * @param sourceWhere
     * @param sourceIndex
     * @param targetWhere
     * @param targetIndex
     * @param count
     * @see BackpackConst.Place
     */
    public void reqMerge(Role role, int sourceWhere, int sourceIndex, int targetWhere, int targetIndex, int count, int logAction) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.reqMerge(role, sourceWhere, sourceIndex, targetWhere, targetIndex, count, logAction));
    }

    /**
     * 执行merge动作,
     * @param role
     * @param backpack
     * @param sourceStorage
     * @param sourceConfig
     * @param sourceItem
     * @param sourceWhere
     * @param sourceIndex
     * @param targetStorage
     * @param targetItem
     * @param targetWhere
     * @param targetIndex
     * @param count
     * @param logAction
     * @return
     */
    public boolean executeMerge(Role role, Backpack backpack, Storage sourceStorage, ItemConfig sourceConfig, Item sourceItem, int sourceWhere, int sourceIndex, Storage targetStorage, Item targetItem, int targetWhere , int targetIndex, int count, int logAction){
        return ScriptEngine.invoke1t1WithRet(IBackpackScript.class, s -> s.executeMerge(role, backpack, sourceStorage, sourceConfig, sourceItem, sourceWhere, sourceIndex, targetStorage, targetItem, targetWhere, targetIndex, count, logAction));
    }

    /**
     * 道具丢弃
     *
     * @param role
     * @param where
     * @param index
     * @param count
     */
    public void reqDiscard(Role role, int where, int index, int count) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.reqDiscard(role, where, index, count));
    }

    /**
     * 道具变化 涉及装备变化
     *
     * @param role
     * @param changes
     */
    public void onUpdate(Role role, List<ItemChange> changes) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.onUpdate(role, changes));
    }

    /**
     * 货币变化
     *
     * @param role
     * @param changes
     * @param action
     */
    public void onUpdate(Role role, Map<Integer, Long> changes, int action) {
        ScriptEngine.invoke1tn(IEventOnRoleCoinChangedScript.class, script -> script.onRoleCoinChanged(role, changes, action));
    }

    public void fetchTreasure(Role role, List<Long> lidList) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.fetchTreasure(role, lidList));
    }

    /**
     * 请求兑换经验
     *
     * @param role
     * @param itemId
     * @param actorId
     */
    public void reqExchangeExp(Role role, int itemId, long actorId) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.reqExchangeExp(role, itemId, actorId));
    }

    /**
     * 请求所有的信息
     *
     * @param role
     */
    public void reqExchangeExpInfo(Role role) {
        ScriptEngine.invoke1t1(IBackpackScript.class, s -> s.reqExchangeExpInfo(role));
    }


    /**
     * 消耗道具 注意这里是指定多个背包一起扣得 其他得消耗可以走之前的
     *
     * @param role
     * @param costItems
     * @param logAction
     * @param callback
     * @param places
     * @return
     */
    public boolean costItem(Role role, List<int[]> costItems, LogAction logAction, NoticeCallback<BackpackStashCommitRetNotice> callback, BackpackConst.Place... places) {
        return ScriptEngine.invoke1t1WithRet(IBackpackScript.class,
                script -> script.costItem(role, costItems, logAction, callback, places));
    }

    /**
     * 消耗道具 注意这里是指定多个背包一起扣得 其他得消耗可以走之前的 注意统一使用上面的接口 这个主要是notice调用
     *
     * @param role
     * @param costItems
     * @param logAction
     * @param places
     * @return
     */
    public boolean doCostItem(Role role, List<int[]> costItems, LogAction logAction, BackpackConst.Place... places) {
        return ScriptEngine.invoke1t1WithRet(IBackpackScript.class,
                script -> script.doCostItem(role, costItems, logAction, places));
    }

    /**
     * 添加道具
     *
     * @param role
     * @param addItems
     * @param logAction
     * @param tip
     * @param callback
     * @param place     有些需求需要指定加道具到指定背包
     * @return 玩家线程才能获取到正常结果 非玩家线程会返回false 正确使用方法是非玩家线程设置callback处理成功失败逻辑
     */
    public boolean addItem(Role role, List<int[]> addItems, LogAction logAction, boolean tip, NoticeCallback<BackpackStashCommitRetNotice> callback, BackpackConst.Place place) {
        return ScriptEngine.invoke1t1WithRet(IBackpackScript.class,
                script -> script.addItem(role, addItems, logAction, tip, callback, place));
    }

    /**
     * 道具日志处理
     *
     * @param role
     * @param changes
     * @param logAction
     */
    public void itemLog(Role role, List<ItemChange> changes, LogAction logAction) {
        ScriptEngine.invoke1t1(IBackpackScript.class,
                script -> script.itemLog(role, changes, logAction));
    }

    /**
     * 货币日志处理
     *
     * @param role
     * @param changes
     * @param oldValues
     * @param action
     */
    public void coinLog(Role role, Map<Integer, Long> changes, Map<Integer, Long> oldValues, int action) {
        ScriptEngine.invoke1t1(IBackpackScript.class,
                script -> script.coinLog(role, changes, oldValues, action));
    }

    /**
     * 购买祭坛次数
     *
     * @param role
     */
    public void reqBuyJiTan(Role role) {
        ScriptEngine.invoke1t1(IBackpackScript.class, script -> script.reqBuyJiTan(role));
    }

    /**
     * 请求领取每日体力
     *
     * @param role 角色
     */
    public void reqDailyPhysicalPower(Role role) {
        ScriptEngine.invoke1t1(IBackpackScript.class, script -> script.reqDailyPhysicalPower(role));
    }

    /**
     * 请求每日体力信息
     *
     * @param role 角色
     */
    public void reqPhysicalPowerInfo(Role role) {
        ScriptEngine.invoke1t1(IBackpackScript.class, script -> script.reqPhysicalPowerInfo(role));
    }

    public Item findItemByUniqueId(Role role, long uniqueId) {
        return ScriptEngine.invoke1t1WithRet(IBackpackScript.class, script -> script.findItemByUniqueId(role, uniqueId));
    }

    /**
     * 缴械
     *
     * @param role  角色
     * @param pos   部位
     * @param time  时间
     */
    public void disarmEquip(Role role, int pos, long time) {
        ScriptEngine.invoke1t1(IBackpackScript.class, script -> script.disarmEquip(role, pos, time));
    }
}