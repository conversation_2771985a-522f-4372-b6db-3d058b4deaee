package com.sh.game.system.activity.entity.role;


import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ActivityLingFuWheelData {

    /**
     * 可用转盘次数
     */
    @Tag(1)
    private int count;

    /**
     * 已转盘次数
     */
    @Tag(2)
    private int accumulateCount;

    /**
     * 此活动已获得的灵符数量
     */
    @Tag(3)
    private long reward;

    /**
     * 活动期间充值
     */
    @Tag(4)
    private long rechargeNum;

    /**
     * 轮次（暂未使用
     */
    @Tag(5)
    private int round;

    /**
     * 已充值的挡位
     */
    @Tag(6)
    private List<Integer> rechargeList = new ArrayList<>();
}
