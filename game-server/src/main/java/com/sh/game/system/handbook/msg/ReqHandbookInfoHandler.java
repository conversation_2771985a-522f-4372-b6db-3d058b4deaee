package com.sh.game.system.handbook.msg;

import com.sh.game.common.communication.msg.system.handbook.ReqHandbookInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.handbook.HandbookManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求专属图鉴信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqHandbookInfoHandler extends AbstractHandler<ReqHandbookInfoMessage> {

    @Override
    public void doAction(ReqHandbookInfoMessage msg) {
        HandbookManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()));
    }

}
