package com.sh.game.system.recycle.msg;

import com.sh.game.common.communication.msg.system.recycle.ReqRMBRecycleMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.RecycleProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.recycle.RecycleManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求人民币回收</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqRMBRecycleHandler extends AbstractHandler<ReqRMBRecycleMessage> {


    @Override
    public void doAction(ReqRMBRecycleMessage msg) {
        RecycleProtos.ReqRMBRecycle proto = msg.getProto();
        RecycleManager.getInstance().reqRMBRecycle(SessionUtil.getRole(msg.getSession()), proto.getRecycleId());
    }
}
