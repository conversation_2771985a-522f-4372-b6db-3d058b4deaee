package com.sh.game.system.xianfa.msg;

import com.sh.game.common.communication.msg.system.xianfa.ReqNoticeRecordMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.xianfa.XianFaManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求公告详情</p>
* <p>Created by MessageUtil</p>
* @date 2025-03-24 上午11:47:00
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqNoticeRecordHandler extends AbstractHandler<ReqNoticeRecordMessage> {

    @Override
    public void doAction(ReqNoticeRecordMessage msg) {
        XianFaManager.getInstance().reqNoticeRecord(SessionUtil.getRole(msg.getSession()));
    }
}