package com.sh.game.system.union.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.union.ReqCanUseUnionNameMessage;

/**
 * <p>请求可使用的行会随机名</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqCanUseUnionNameHandler extends AbstractHandler<ReqCanUseUnionNameMessage> {

    @Override
    public void doAction(ReqCanUseUnionNameMessage msg) {
        UnionManager.getInstance().checkRandomUnionName(SessionUtil.getRole(msg));
    }

}
