package com.sh.game.system.tower.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.tower.TowerManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.tower.ReqTowerMessage;

/**
 * <p>请求玩家开天塔信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqTowerHandler extends AbstractHandler<ReqTowerMessage> {

    @Override
    public void doAction(ReqTowerMessage msg) {
        TowerManager.getInstance().sendTowerInfo(SessionUtil.getRole(msg.getSession()));
    }
}
