package com.sh.game.system.trade.event;

import com.sh.game.event.EventType;
import com.sh.game.event.IListener;

public class TradeListenOnQuitMapEventListener implements IListener {

    @Override
    public void update(EventType type, Object param) {
        //TODO 错误依赖场景代码
        /*QuitMapEvent event = (QuitMapEvent) param;
        PlayerActor actor = event.getActor();
        if (actor == null) {
            return;
        }
        TradeManager.getInstance().reqTradeCancel(actor.getRid());*/
    }
}
