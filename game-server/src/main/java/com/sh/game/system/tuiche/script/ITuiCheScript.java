package com.sh.game.system.tuiche.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/9/20 15:25
 */
public interface ITuiCheScript extends IScript {

    void reqTuiCheStart(Role role);

    void reqTuiCheReward(Role role, int stage,  int rewardCount);

    void reqTuiCheInfo(Role role);

    void tuiCheAdvertiseGameTimeCallBack(Role role);

}
