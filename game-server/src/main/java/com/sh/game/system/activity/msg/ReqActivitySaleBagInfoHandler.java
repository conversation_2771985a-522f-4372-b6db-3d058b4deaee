package com.sh.game.system.activity.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivitySaleBagManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqActivitySaleBagInfoMessage;

/**
 * <p>请求信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivitySaleBagInfoHandler extends AbstractHandler<ReqActivitySaleBagInfoMessage> {

    @Override
    public void doAction(ReqActivitySaleBagInfoMessage msg) {
        ActivitySaleBagManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()));
    }

}
