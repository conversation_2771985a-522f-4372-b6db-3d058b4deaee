package com.sh.game.system.activity.entity.role;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class LuckyDrawData {

    @Tag(1)
    private Map<Integer,Integer> lukyDrawReward = new HashMap<>();

    @Tag(2)
    private int resetCount = 0;

//    @Tag(3)
//    private List<CommonLog> rechargeDrawLog = new ArrayList<>();
}
