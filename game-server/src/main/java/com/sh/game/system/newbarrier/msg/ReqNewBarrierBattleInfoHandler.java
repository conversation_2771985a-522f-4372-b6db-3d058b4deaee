package com.sh.game.system.newbarrier.msg;

import com.sh.game.common.communication.msg.system.newbarrier.ReqNewBarrierBattleInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.newbarrier.NewBarrierManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqNewBarrierBattleInfoHandler extends AbstractHandler<ReqNewBarrierBattleInfoMessage> {

    @Override
    public void doAction(ReqNewBarrierBattleInfoMessage msg) {
        NewBarrierManager.getInstance().reqBarrierBattleInfo(SessionUtil.getRole(msg));
    }

}
