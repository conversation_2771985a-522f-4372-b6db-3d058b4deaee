package com.sh.game.system.mainbatt.entity;

import com.sh.commons.tuple.TwoTuple;
import io.protostuff.Tag;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * description:
 * date: 2024/6/27
 * author: chenBin
 */
@Data
public class MainBatt {
    @Tag(1)
    private int mainBattCount;

    @Tag(2)
    private Map<Integer, TwoTuple<Integer, int[]>> rewardMap = new HashMap<>();

    @Tag(3)
    private int lastResetTime;

    @Tag(4)
    private int cycleStartTime;

    @Tag(5)
    private int lobbyId;

}
