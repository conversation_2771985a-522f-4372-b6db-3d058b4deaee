package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityWorldCupLoginScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/2 9:33
 */
public class ActivityWorldCupLoginManager {

    private static final ActivityWorldCupLoginManager INSTANCE = new ActivityWorldCupLoginManager();

    public static ActivityWorldCupLoginManager getInstance() {
        return INSTANCE;
    }

    public void info(Role role) {
        ScriptEngine.invoke1t1(IActivityWorldCupLoginScript.class,s -> s.info(role));
    }

    public void reqAcquireFree(Role role, int cid) {
        ScriptEngine.invoke1t1(IActivityWorldCupLoginScript.class,s -> s.reqAcquireFree(role, cid));
    }

    public void reqAcquirePay(Role role, int cid) {
        ScriptEngine.invoke1t1(IActivityWorldCupLoginScript.class,s -> s.reqAcquirePay(role, cid));
    }

    public void reqAcquireLeiJi(Role role, List<Integer> idList) {
        ScriptEngine.invoke1t1(IActivityWorldCupLoginScript.class,s -> s.reqAcquireLeiJi(role, idList));
    }

    public void reqBuQian(Role role, int cid) {
        ScriptEngine.invoke1t1(IActivityWorldCupLoginScript.class,s -> s.reqBuQian(role, cid));
    }
}
