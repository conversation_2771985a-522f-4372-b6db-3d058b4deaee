package com.sh.game.system.auction;

import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.auction.script.IAuctionScript;
import com.sh.script.ScriptEngine;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;


public class AuctionManager {
    private static final AuctionManager INSTANCE = new AuctionManager();

    private static final Set<Long> auctionSet = new HashSet<>();

    private AuctionManager() {

    }

    public static AuctionManager getInstance() {
        return INSTANCE;
    }

    public static Set<Long> getSet() {
        return auctionSet;
    }

    /**
     * 请求拍卖行商品列表
     *
     * @param role role
     * @param type 拍卖行类别1钻石拍卖2元宝拍卖3世界拍卖4行会拍卖
     */
    public void reqAuctionList(Role role, int type) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqAuctionListInfo(role, type));
    }

    /**
     * 请求上架商品
     *
     * @param role      role
     * @param uniqueId  道具唯一id
     * @param itemId    道具id
     * @param itemCount 道具数量
     * @param moneyType 商品价格类型
     * @param bidMoney  竞拍价
     * @param money     一口价
     */
    public void reqAuctionPutOn(Role role, long uniqueId, int itemId, int itemCount, int moneyType, int bidMoney, int money) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqAuctionPutOn(role, uniqueId, itemId, itemCount, moneyType, bidMoney, money));
    }

    /**
     * 请求下架商品
     *
     * @param role    role
     * @param storeId 商品唯一id
     */
    public void reqAuctionPutOff(Role role, long storeId) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqAuctionPutOff(role, storeId));
    }

    /**
     * 请求上架列表
     *
     * @param role role
     */
    public void reqAuctionPutOnList(Role role) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqAuctionPutOnList(role));
    }

    /**
     * 请求竞价
     *
     * @param role      role
     * @param storeId   商品id
     * @param addPrice  竞价后的价格
     * @param type      1钻石2元宝3世界4行会5我的竞价列表
     * @param moneyType 价格类型
     */
    public void reqAuctionBid(Role role, long storeId, int addPrice, int type, int moneyType) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqAuctionBid(role, storeId, addPrice, type, moneyType));

    }

    /**
     * 请求购买商品
     *
     * @param role      role
     * @param storeId   商品唯一id
     * @param type      1钻石2元宝3世界4行会5我的竞价列表
     * @param moneyType 价格类型
     */
    public void reqAuctionBuy(Role role, long storeId, int type, int moneyType) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqAuctionBuy(role, storeId, type, moneyType));
    }

    /**
     * 请求拍卖行个人出售记录
     *
     * @param role role
     */
    public void reqAuctionDealList(Role role) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqAuctionDealList(role));
    }

    /**
     * 请求拍卖行个人竞价列表
     *
     * @param role role
     */
    public void reqAuctionBidList(Role role) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqAuctionBidList(role));
    }

    /**
     * 行会拍卖上架道具
     *
     * @param union
     * @param playerScores 积分
     * @param type         1沙巴克 2其他
     */
    public void reqUnionAuction(Union union, Map<Long, Long> playerScores, int type) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqUnionAuction(union, playerScores, type));
    }

    /**
     * 打开拍卖行
     *
     * @param role role
     */
    public void reqOpenAuction(Role role) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqOpenAuction(role));
    }

    /**
     * 关闭拍卖行
     *
     * @param role role
     */
    public void reqCloseAuction(Role role) {
        ScriptEngine.invoke1t1(IAuctionScript.class, script -> script.reqCloseAuction(role));
    }


    /**
     * 处理过期竞拍
     */
    public void dealTimeOutAuction() {
        ScriptEngine.invoke1t1(IAuctionScript.class, IAuctionScript::dealTimeOutAuction);
    }
}
