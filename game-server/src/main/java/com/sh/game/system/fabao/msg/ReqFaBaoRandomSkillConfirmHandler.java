package com.sh.game.system.fabao.msg;

import com.sh.game.common.communication.msg.system.fabao.ReqFaBaoRandomSkillConfirmMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.FaBaoProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.fabao.FaBaoManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求法宝技能随机结果确认</p>
* <p>Created by MessageUtil</p>
* @date 2024-11-25 上午11:41:29
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqFaBaoRandomSkillConfirmHandler extends AbstractHandler<ReqFaBaoRandomSkillConfirmMessage> {

    @Override
    public void doAction(ReqFaBaoRandomSkillConfirmMessage msg) {
        FaBaoProtos.ReqFaBaoRandomSkillConfirmMessage proto = msg.getProto();
        FaBaoManager.getInstance().reqFaBaoRandomSkillConfirm(SessionUtil.getRole(msg.getSession()), proto.getFaBaoId(), proto.getSkillIndex());
    }
}