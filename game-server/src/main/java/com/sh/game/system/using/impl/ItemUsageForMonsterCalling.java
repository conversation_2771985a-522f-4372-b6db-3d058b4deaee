package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

public class ItemUsageForMonsterCalling extends ItemUsage {

    @Override
    public boolean needMapVerify(Role role, Item item, ItemConfig config) {
        return true;
    }

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(role.getId());
        return player != null;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        int monsterId = config.getUseParam()[0][0];
        role.proxyCall(proxy -> proxy.createMonster(monsterId, 1, false, true, true));
        return true;
    }

    @Override
    public int getUsedType() {
        return 163;
    }
}
