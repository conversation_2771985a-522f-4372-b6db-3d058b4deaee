package com.sh.game.system.bore.msg;

import com.sh.game.protos.BoreProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.bore.BoreManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.bore.ReqBoreMessage;

/**
 * <p>请求打孔</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqBoreHandler extends AbstractHandler<ReqBoreMessage> {

    @Override
    public void doAction(ReqBoreMessage msg) {
        BoreProtos.ReqBore bore = msg.getProto();
        BoreManager.getInstance().bore(SessionUtil.getRole(msg.getSession()), bore.getIndex());
    }
}
