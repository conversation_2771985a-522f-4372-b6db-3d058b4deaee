package com.sh.game.system.user.msg;

import com.sh.game.common.entity.user.LoginContext;
import com.sh.game.protos.UserProtos.ReqLogin;
import com.sh.game.system.user.UserManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.user.ReqLoginMessage;

/**
 * <p>请求登录</p>
 * <p>Created by MessageUtil</p>
 *
 * @date 2020-07-31 14:48:10
 */
public class ReqLoginHandler extends AbstractHandler<ReqLoginMessage> {

    @Override
    public void doAction(ReqLoginMessage msg) {
        ReqLogin loginInfo = msg.getProto();
        LoginContext context = new LoginContext(msg.getSession(), loginInfo.getUid(), loginInfo.getSid(), loginInfo.getPlatform(), loginInfo.getChannel());
        context.setVersion(loginInfo.getVersion());
        context.setToken(loginInfo.getToken());
        if (loginInfo.getPkg().isEmpty()) {
            context.setPkg(loginInfo.getPkg());
        }
        if (loginInfo.getDevice().isEmpty()) {
            context.setDevice(loginInfo.getDevice());
        }

        /**
         * 设置扩展参数
         */
        if (!loginInfo.getValue1().isEmpty()) {
            context.setValue1(loginInfo.getValue1());
        }
        if (!loginInfo.getValue2().isEmpty()) {
            context.setValue2(loginInfo.getValue2());
        }
        if (!loginInfo.getValue3().isEmpty()) {
            context.setValue3(loginInfo.getValue3());
        }

        UserManager.getInstance().login(context.getSession(), context);
    }

}
