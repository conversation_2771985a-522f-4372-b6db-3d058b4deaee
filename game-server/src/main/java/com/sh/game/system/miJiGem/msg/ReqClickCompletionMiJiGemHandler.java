package com.sh.game.system.miJiGem.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.MiJiGemProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.miJiGem.MiJiGemManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.miJiGem.ReqClickCompletionMiJiGemMessage;

/**
 * <p>请求一键镶嵌</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqClickCompletionMiJiGemHandler extends AbstractHandler<ReqClickCompletionMiJiGemMessage> {

    @Override
    public void doAction(ReqClickCompletionMiJiGemMessage msg) {
        MiJiGemProtos.ReqClickCompletionMiJiGem clickCompletionMiJiGem = msg.getProto();
        MiJiGemManager.getInstance().reqMiJiOneClickCompletionGem(SessionUtil.getRole(msg), clickCompletionMiJiGem.getEquipIndex());
    }

}
