package com.sh.game.system.equip.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ShoujueSysProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equip.EquipManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.equip.ReqQiangHuaShouJueMessage;

/**
 * <p>请求强化兽决</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqQiangHuaShouJueHandler extends AbstractHandler<ReqQiangHuaShouJueMessage> {

    @Override
    public void doAction(ReqQiangHuaShouJueMessage msg) {
        ShoujueSysProtos.ReqQiangHuaShouJue proto = msg.getProto();
        EquipManager.getInstance().reqQiangHuaShouJueEquip(SessionUtil.getRole(msg), proto.getEquipId());
    }

}
