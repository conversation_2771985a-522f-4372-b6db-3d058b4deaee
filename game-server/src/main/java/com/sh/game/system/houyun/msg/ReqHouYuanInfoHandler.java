package com.sh.game.system.houyun.msg;

import com.sh.game.common.communication.msg.system.houyuan.ReqHouYuanInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.houyun.HouYuanManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求地狱之路信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqHouYuanInfoHandler extends AbstractHandler<ReqHouYuanInfoMessage> {

    @Override
    public void doAction(ReqHouYuanInfoMessage msg) {
        HouYuanManager.getInstance().info(SessionUtil.getRole(msg));
    }

}
