package com.sh.game.system.chaijia;

import com.sh.game.common.entity.backpack.item.EquipData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.chaijia.script.IChaiJiaScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2023/9/7 13:22
 */
public class ChaiJiaManager {

    private static final ChaiJiaManager INSTANCE = new ChaiJiaManager();

    public static ChaiJiaManager getInstance() {
        return INSTANCE;
    }

    public void info(Role role) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.info(role));
    }

    public void chaiJia(Role role, int rate) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.chaiJia(role, rate));
    }

    public void takeItem(Role role, long itemUid, int type, int index, boolean recycle) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.takeItem(role, itemUid, type, index, recycle));
    }

    public void recycleItem(Role role, long itemUid, int type) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.recycleItem(role, itemUid, type));
    }

    public void upGradeChaiJia(Role role) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.upGradeChaiJia(role));
    }

    public void upGradeFree(Role role) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.upGradeFree(role));
    }

    public void chaiJiaRate(Role role, int rate) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.chaiJiaRate(role, rate));
    }

    public void checkChaiJiaUp(Role role) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.checkChaiJiaUp(role));
    }

    public void energyDrinkLevelUp(Role role) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.energyDrinkLevelUp(role));
    }

    public void energyDrinkInfo(Role role) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.energyDrinkInfo(role));
    }

    public void calChaiJiaAttr(Role role, EquipData data, int quality, int equipLocation) {
        ScriptEngine.invoke1t1(IChaiJiaScript.class, s -> s.calChaiJiaAttr(role, data, quality, equipLocation));
    }
}
