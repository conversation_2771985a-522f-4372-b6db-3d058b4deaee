package com.sh.game.system.zhenYan.msg;

import com.sh.game.common.communication.msg.system.zhenYan.ReqZhenYanCompoundMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ZhenYanProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.zhenYan.ZhenYanManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求真言合成</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqZhenYanCompoundHandler extends AbstractHandler<ReqZhenYanCompoundMessage> {

    @Override
    public void doAction(ReqZhenYanCompoundMessage msg) {
        ZhenYanProtos.ReqZhenYanCompound proto = msg.getProto();
        ZhenYanManager.getInstance().reqZhenYanCompound(SessionUtil.getRole(msg), proto.getCommpoundBeanListList(), proto.getCfgId());
    }

}
