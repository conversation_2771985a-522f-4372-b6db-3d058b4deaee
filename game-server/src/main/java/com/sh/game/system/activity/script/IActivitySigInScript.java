package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleSignIn;
import com.sh.script.IScript;

/**
 * @description: 每日签到
 * @author: Wangbl
 * @create: 2022/02/24
 */
public interface IActivitySigInScript extends IScript {
    /**
     * 获取玩家签到数据
     * @param rid
     * @return
     */
    RoleSignIn find(long rid);

    /**
     * 请求玩家每日签到数据
     * @param role
     */
    void sendInfo(Role role);

    /**
     * 请求签到
     * @param role
     */
    void reqSigIn(Role role);

    /**
     * 请求补签
     * @param role
     * @param day
     */
    void reqSigned(Role role, int day);

    /**
     * 请求获得签到奖励
     * @param role
     * @param cid
     */
    void gainSigInReward(Role role, int cid);
}
