package com.sh.game.system.monthCard;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.MonthCardConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.system.vip.entity.RoleCard;

/**
 * ATO：yumo<br>;
 * 时间：2021/1/4 14:06<br>;
 * 版本：1.0<br>;
 * 描述：
 */
public class MonthCardUtil {

    private MonthCardUtil() {
    }

    /**
     * 月卡是否存在
     * 永久为-1
     *
     * @param card 月卡特权
     * @param role 角色
     * @return true:不可用  false:可用
     */
    public static boolean isOverdue(RoleCard card, Role role) {
        boolean flag = card == null || card.getCardId() < 1 || card.getTimeout() == 0;
        //不存在
        if (flag) {
            MonthCardManager.getInstance().sendMonthCardInfo(role);
        }
        return flag;
    }

    /**
     * 是否拥有随身回收特权
     *
     * @param role role
     * @return
     */
    public static boolean isRecovery(Role role) {
        for (RoleCard roleCard : role.getRoleAdvance().getMonthCard().values()) {
            MonthCardConfig config = ConfigDataManager.getInstance().getById(MonthCardConfig.class, roleCard.getCardId());
            if (config != null && config.getRecovery() == 1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否拥有随身仓库特权
     *
     * @param role role
     * @return
     */
    public static boolean isWarehouse(Role role) {
        for (RoleCard roleCard : role.getRoleAdvance().getMonthCard().values()) {
            MonthCardConfig config = ConfigDataManager.getInstance().getById(MonthCardConfig.class, roleCard.getCardId());
            if (config != null && config.getWarehouse() == 1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否拥有自动回收特权
     *
     * @param role role
     * @return
     */
    public static boolean isAutomaticRecovery(Role role) {
        for (RoleCard roleCard : role.getRoleAdvance().getMonthCard().values()) {
            MonthCardConfig config = ConfigDataManager.getInstance().getById(MonthCardConfig.class, roleCard.getCardId());
            if (config != null && config.getAutomaticrecovery() == 1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否拥有免费一键完成每日任务特权
     *
     * @param role role
     * @return
     */
    public static boolean isFreeFinish(Role role) {
        for (RoleCard roleCard : role.getRoleAdvance().getMonthCard().values()) {
            MonthCardConfig config = ConfigDataManager.getInstance().getById(MonthCardConfig.class, roleCard.getCardId());
            if (config != null && config.getFreefinish() == 1) {
                return true;
            }
        }
        return false;
    }
}
