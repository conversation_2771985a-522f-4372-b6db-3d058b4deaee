package com.sh.game.system.zhanling.msg;

import com.sh.game.common.communication.msg.system.zhanling.ReqExtraRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.zhanling.ActivityZhanLingManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqExtraRewardHandler extends AbstractHandler<ReqExtraRewardMessage> {

    @Override
    public void doAction(ReqExtraRewardMessage msg) {
        ActivityZhanLingManager.getInstance().extraReward(SessionUtil.getRole(msg.getSession()));
    }

}