package com.sh.game.system.secretary.entity;

import com.sh.commons.tuple.TwoTuple;
import io.protostuff.Exclude;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.math.BigInteger;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/10/17 20:33
 */
@Getter
@Setter
public class SecretaryData {
    /** 培训等级 */
    @Tag(1)
    private int training;

    /** 秘书列表 秘书-TwoTuple(lv-zizhiLv) */
    @Tag(2)
    private Map<Integer, TwoTuple<Integer, Integer>> secretaryMap = new HashMap<>();

    /** 秘书总收益 (计算后包含secretaryItemXiuWei) */
    @Tag(3)
    private Map<Integer, String> secretaryIncomeMap = new HashMap<>();

    /** 秘书技能列表 秘书-Map(skillId-lv) */
    @Tag(4)
    private Map<Integer, Map<Integer, Integer>> skillLevelMap = new HashMap<>();

    /** 总资质(包含法宝) */
    @Tag(5)
    private int totalZiZhi;

    /** 秘书资质(不包含法宝) */
    @Tag(6)
    private int secretaryZiZhi;

    /** 法宝资质 */
    @Tag(7)
    private int faBaoZiZhi;

    /**
     *  秘书(仙友)道具使用增加的 秒收益(修为)
     */
    @Tag(8)
    private Map<Integer, Map<Integer, Long>> secretaryItemXiuWei = new HashMap<>();

    /** 秘书-进阶 */
    @Tag(9)
    private Map<Integer, Integer> secretaryJinJieMap = new HashMap<>();

    /**
     *  秘书(仙友)仙法挑战增加的固定值 秒收益(修为)
     */
    @Tag(10)
    private Map<Integer, Map<Integer, Long>> secretaryXianFaXiuWei = new HashMap<>();

    /**
     *  秘书-仙游
     */
    @Tag(11)
    private Map<Integer, Integer> secretaryXianYouMap = new HashMap<>();

    /** 秘书列表 秘书-升级倒计时 */
    @Tag(12)
    private Map<Integer, Integer> secretaryLevelTime = new ConcurrentHashMap<>();

    /** 秘书-品质 */
    @Tag(13)
    private Map<Integer, Integer> secretaryQualityMap = new HashMap<>();

    //广告临时变量
    @Exclude
    private int levelAdvertise;

    public BigInteger getTotalIncome() {
        BigInteger bigInteger = BigInteger.ZERO;
        for(Map.Entry<Integer, String> entry : secretaryIncomeMap.entrySet()) {
            bigInteger = bigInteger.add(new BigInteger(entry.getValue()));
        }
        return  bigInteger;
    }

}
