package com.sh.game.system.union.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionDianFenLianSaiManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.union.ReqDFLSJiangJinMessage;

/**
 * <p>请求查看巅峰联赛奖金</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqDFLSJiangJinHandler extends AbstractHandler<ReqDFLSJiangJinMessage> {

    @Override
    public void doAction(ReqDFLSJiangJinMessage msg) {
        UnionDianFenLianSaiManager.getInstance().reqJiangJin(SessionUtil.getRole(msg));
    }

}
