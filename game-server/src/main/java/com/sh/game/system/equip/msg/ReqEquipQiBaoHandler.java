package com.sh.game.system.equip.msg;

import com.sh.game.common.communication.msg.system.equip.ReqEquipQiBaoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.EquipProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equip.EquipManager;
import com.sh.server.AbstractHandler;

/**
 * <p>弃保</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqEquipQiBaoHandler extends AbstractHandler<ReqEquipQiBaoMessage> {

    @Override
    public void doAction(ReqEquipQiBaoMessage msg) {
        EquipProtos.ReqEquipQiBao equipQiBao = msg.getProto();
        EquipManager.getInstance().qibao(SessionUtil.getRole(msg.getSession()), equipQiBao.getUid());
    }

}
