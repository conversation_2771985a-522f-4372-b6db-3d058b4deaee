package com.sh.game.system.task.msg;

import com.sh.game.common.communication.msg.system.task.ReqSubmitTaskMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.TaskProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.task.TaskManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求提交任务消息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqSubmitTaskHandler extends AbstractHandler<ReqSubmitTaskMessage> {

    @Override
    public void doAction(ReqSubmitTaskMessage msg) {
        TaskProtos.ReqSubmitTask submitTask = msg.getProto();
        TaskManager.getInstance().reqSubmitTask(SessionUtil.getRole(msg),submitTask.getTaskId(), submitTask.getMul());
    }

}
