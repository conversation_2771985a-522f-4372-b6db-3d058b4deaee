package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @date 2022/2/7 16:41
 */
public interface IActivityDailyTaskScript extends IScript {

    /**
     * 获取进度奖励
     *
     * @param role  role
     * @param cfgId DailyTaskRewardConfig的id
     */
    void processReward(Role role, int cfgId);

    /**
     * 请求信息
     *
     * @param role role
     */
    void info(Role role);

    /**
     * 快速完成任务
     * @param role
     * @param cfgId
     */
    void reqYiJianCompete(Role role, int cfgId);
}
