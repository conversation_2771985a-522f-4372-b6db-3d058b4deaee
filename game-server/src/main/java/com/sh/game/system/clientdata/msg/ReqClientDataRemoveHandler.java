package com.sh.game.system.clientdata.msg;

import com.sh.game.common.communication.msg.system.clientdata.ReqClientDataRemoveMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.clientdata.ClientDataManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqClientDataRemoveHandler extends AbstractHandler<ReqClientDataRemoveMessage> {

    @Override
    public void doAction(ReqClientDataRemoveMessage msg) {
        ClientDataManager.getInstance().reqClientDataRemove(SessionUtil.getRole(msg), msg.getProto().getKeyList());
    }

}
