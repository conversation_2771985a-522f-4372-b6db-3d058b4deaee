package com.sh.game.system.militaryRank;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.militaryRank.entity.RoleMilitaryRank;
import com.sh.game.system.militaryRank.script.IMilitaryRankScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-05-09
 **/
public class MilitaryRankManger {

    private static final MilitaryRankManger INSTANCE = new MilitaryRankManger();

    private MilitaryRankManger() {
    }

    public static MilitaryRankManger getInstance() {
        return INSTANCE;
    }

    /**
     * 获取军衔数据
     *
     * @param rid 角色 Id
     */
    public RoleMilitaryRank find(long rid) {
        return ScriptEngine.invoke1t1WithRet(IMilitaryRankScript.class, script -> script.find(rid));
    }

    /**
     * 发送军衔信息
     *
     * @param role 角色
     */
    public void sendMilitaryRankInfo(Role role) {
        ScriptEngine.invoke1t1(IMilitaryRankScript.class, script -> script.sendMilitaryRankInfo(role));
    }

    /**
     * 军衔升级
     *
     * @param role 角色
     */
    public void militaryRankUp(Role role) {
        ScriptEngine.invoke1t1(IMilitaryRankScript.class, script -> script.militaryRankUp(role));
    }

    /**
     * 领取军衔任务奖励
     *
     * @param role   角色
     * @param taskId 任务id
     */
    public void gainTaskReward(Role role, int taskId) {
        ScriptEngine.invoke1t1(IMilitaryRankScript.class, script -> script.gainTaskReward(role, taskId));
    }

    /**
     * 领取每日俸禄
     *
     * @param role 角色
     */
    public void gainDailyReward(Role role) {
        ScriptEngine.invoke1t1(IMilitaryRankScript.class, script -> script.gainDailyReward(role));
    }

}
