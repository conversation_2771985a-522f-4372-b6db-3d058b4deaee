package com.sh.game.system.shabake.msg;

import com.sh.game.common.communication.msg.system.shabake.ReqMonsterHpMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.shabake.ShaBaKeManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求城墙护卫血量</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_SOCIAL)
public class ReqMonsterHpHandler extends AbstractHandler<ReqMonsterHpMessage> {

    @Override
    public void doAction(ReqMonsterHpMessage msg) {
        ShaBaKeManager.getInstance().reqMonsterHp(SessionUtil.getRole(msg.getSession()));
    }

}
