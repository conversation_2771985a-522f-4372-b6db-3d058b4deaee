package com.sh.game.system.auction.msg;

import com.sh.game.common.communication.msg.system.auction.ReqAuctionPutOnListMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.auction.AuctionManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求上架商品列表</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqAuctionPutOnListHandler extends AbstractHandler<ReqAuctionPutOnListMessage> {

    @Override
    public void doAction(ReqAuctionPutOnListMessage msg) {
        AuctionManager.getInstance().reqAuctionPutOnList(SessionUtil.getRole(msg.getSession()));
    }

}
