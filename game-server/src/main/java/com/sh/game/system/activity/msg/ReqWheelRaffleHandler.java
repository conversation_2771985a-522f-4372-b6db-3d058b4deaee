package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityWheelManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqWheelRaffleMessage;

/**
 * <p>请求幸运转盘抽奖</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqWheelRaffleHandler extends AbstractHandler<ReqWheelRaffleMessage> {

    @Override
    public void doAction(ReqWheelRaffleMessage msg) {
        ActivityProtos.ReqWheelRaffle proto = msg.getProto();
        ActivityWheelManager.getInstance().wheelRaffle(SessionUtil.getRole(msg), proto.getActivityId());
    }

}
