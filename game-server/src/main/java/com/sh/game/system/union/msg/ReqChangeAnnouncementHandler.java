package com.sh.game.system.union.msg;

import com.sh.game.common.communication.msg.system.union.ReqChangeAnnouncementMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.UnionProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.union.UnionManager;
import com.sh.server.AbstractHandler;

/**
 * <p>申请修改公告</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 15:51:41
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqChangeAnnouncementHandler extends AbstractHandler<ReqChangeAnnouncementMessage> {

    @Override
    public void doAction(ReqChangeAnnouncementMessage msg) {
        UnionProtos.ReqChangeAnnouncement proto = msg.getProto();
        UnionManager.getInstance().changeAnnouncement(SessionUtil.getRole(msg.getSession()), proto.getAnnouncement(),proto.getType());

    }

}
