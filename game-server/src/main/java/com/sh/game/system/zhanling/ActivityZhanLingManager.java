package com.sh.game.system.zhanling;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleZhanLing;
import com.sh.game.system.activity.script.IActivityZhanLingScript;
import com.sh.game.system.activity.script.IActivityZhanLingTaskScript;
import com.sh.game.system.zhanling.script.IActivityZhanLingPackScript;
import com.sh.script.ScriptEngine;

/**
 * 战令
 *
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2022-01-13
 **/
public class ActivityZhanLingManager {

    private static final ActivityZhanLingManager INSTANCE = new ActivityZhanLingManager();

    public static ActivityZhanLingManager getInstance() {
        return INSTANCE;
    }

    private ActivityZhanLingManager() {

    }

    public RoleZhanLing find(long rid) {
        return ScriptEngine.invoke1t1WithRet(IActivityZhanLingScript.class, script -> script.find(rid));
    }

    public void extraReward(Role role) {
        ScriptEngine.invoke1t1(IActivityZhanLingPackScript.class, s -> s.extraReward(role));
    }

    /**
     * 请求战令信息
     *
     * @param role 角色
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1tn(IActivityZhanLingScript.class, script -> script.reqInfo(role));
    }

    /**
     * 请求战令任务信息
     *
     * @param role 角色
     */
    public void reqTaskInfo(Role role) {
        ScriptEngine.invoke1tn(IActivityZhanLingTaskScript.class, script -> script.reqInfo(role));
    }

    /**
     * 领取战令任务奖励
     *
     * @param role 角色
     * @param cid  战令任务表id
     */
    public void gainTaskReward(Role role, int cid) {
        ScriptEngine.invoke1tn(IActivityZhanLingTaskScript.class, script -> script.gainTaskReward(role, cid));
    }

    /**
     * 战令进度奖励
     *
     * @param role 角色
     * @param cid  战令进度表id
     */
    public void gainProgressReward(Role role, int cid) {
        ScriptEngine.invoke1tn(IActivityZhanLingScript.class, script -> script.gainZhanLingReward(role, cid));
    }
}
