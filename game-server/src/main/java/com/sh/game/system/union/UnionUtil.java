package com.sh.game.system.union;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.config.model.AppearanceConfig;
import com.sh.game.common.config.model.UnionGuideConfig;
import com.sh.game.common.constant.DailyConst;
import com.sh.game.common.constant.GameConst;
import com.sh.game.common.constant.UnionConst;
import com.sh.game.common.constant.UnionPositionConst;
import com.sh.game.common.entity.ServerJiangHuCamp;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.common.unionCamp.CrossCampData;
import com.sh.game.common.unionCamp.CrossUnionCamp;
import com.sh.game.common.util.ItemCoinUtil;
import com.sh.game.common.util.GlobalUtil;
import com.sh.game.common.util.TimeUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.map.ProxyPlayer;
import com.sh.game.player.PlayerProxyManager;
import com.sh.game.protos.UnionProtos;
import com.sh.game.server.SessionManager;
import com.sh.game.system.daily.DailyManager;
import com.sh.game.system.query.QueryManager;
import com.sh.game.system.render.entity.RenderData;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.union.comparator.UnionMemberComparator;
import com.sh.game.system.union.entity.MemberInfo;
import com.sh.game.system.union.entity.UnionEvent;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 行会工具类
 */
public class UnionUtil {


    public static List<UnionProtos.UnionMemberInfoBean> toUnionMemberBean(Union union) {
        List<UnionProtos.UnionMemberInfoBean> memberList = new ArrayList<>();

        for (MemberInfo info : union.getMemberInfos().values()) {
            UnionProtos.UnionMemberInfoBean bean = toUnionMemberBean(union, info.getMemberId());
            if (bean != null) {
                memberList.add(bean);
            }
        }

        if (union.getFakeUnion() > 0) {
            UnionProtos.UnionMemberInfoBean bean = toFakeUnionMemberInfoBean(union);
            if (bean != null) {
                memberList.add(bean);
            }
        }

        return memberList;
    }

    /**
     * 是否在某个行会活动期间
     *
     * @return
     */
    public static boolean checkUnionActivity(int type) {
        return DailyManager.getInstance().isInTimedPeriod(DailyConst.DailyType.DILAO);
    }

    /**
     * 查找转让会长id
     *
     * @param union
     * @return
     */
    public static MemberInfo findReplaceLeader(Union union) {
        Map<Long, MemberInfo> memberInfos = union.getMemberInfos();
        int nowOfSeconds = TimeUtil.getNowOfSeconds();
        long unionLeaderId = UnionUtil.getUnionLeaderId(union.getId());
        int day = GlobalUtil.getGlobalInt(GameConst.GlobalId.IMPEACH_DAY);
        List<MemberInfo> collect = memberInfos.values().stream().filter(memberInfo -> {
            RoleSummary summary = SummaryManager.getInstance().getSummary(memberInfo.getMemberId());
            if (summary == null) {
                return false;
            }
            if (summary.getId() == unionLeaderId) {
                return false;
            }
            if (summary.getOfflineTime() <= 0 || summary.getOfflineTime() + day * TimeUtil.ONE_DAY_IN_SECONDS > nowOfSeconds || summary.getLoginTime() >= summary.getOfflineTime()) {
                return true;
            }
            return false;
        }).sorted(UnionMemberComparator.COMPARATOR).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            return collect.get(0);
        }
        return null;
    }

    /**
     * 获取职位名称
     *
     * @param position
     * @return
     */
    public static String getPositionName(int position) {
        StringBuilder sb = new StringBuilder();
        switch (position) {
            case UnionPositionConst.Position.CHAIRMAN:
                sb.append("会长");
                break;
            case UnionPositionConst.Position.MATE:
                sb.append("副会长");
                break;
            case UnionPositionConst.Position.ELDER:
                sb.append("精英");
                break;
            case UnionPositionConst.Position.MEMBER:
                sb.append("普通成员");
                break;
        }
        return sb.toString();
    }

    public static UnionProtos.UnionMemberInfoBean toFakeUnionMemberInfoBean(Union union) {
        MemberInfo info = union.getMemberInfos().get(0L);
        if (info == null) {
            return null;
        }
        UnionGuideConfig config = ConfigDataManager.getInstance().getById(UnionGuideConfig.class, info.getFakeMember());
        if (config == null) {
            return null;
        }
        UnionProtos.UnionMemberInfoBean.Builder builder = UnionProtos.UnionMemberInfoBean.newBuilder()
                .setPosition(info.getPosition())
                .setCareer(config.getCareer())
                .setMemberName(config.getLeaderid())
                .setSex(config.getSex())
                .setMemberLevel(config.getLevel2())
                .setOfflineTime((int) (union.getCreateTime() / 1000))
                .setFightPower(info.getHistoryFightPower());
        Map<Integer, Integer> map = new HashMap<>();
        config.getFashion().forEach(id -> {
            AppearanceConfig appearanceConfig = ConfigDataManager.getInstance().getById(AppearanceConfig.class, id);
            if (appearanceConfig != null) {
                map.put(appearanceConfig.getType(), id);
            }
        });
        builder.addAllFashions(QueryManager.getInstance().fashionsToBean(map));
        return builder.build();
    }

    public static UnionProtos.UnionMemberInfoBean toUnionMemberBean(Union union, long rid) {
        RoleSummary summary = SummaryManager.getInstance().getSummary(rid);
        if (summary == null) {
            return null;
        }
        Role role = DataCenter.get(Role.class, rid);
        if (role == null) {
            return null;
        }
        UnionProtos.UnionMemberInfoBean.Builder bean = UnionProtos.UnionMemberInfoBean.newBuilder();
        bean.setId(summary.getId());
        bean.setCareer(summary.getCareer());
        bean.setMemberLevel(summary.getLevel());
        bean.setMemberName(summary.getName());
        MemberInfo memberInfo = union.getMemberInfos().get(summary.getId());
        bean.setPosition(memberInfo.getPosition());
        if (role.isOnline()) {
            bean.setFightPower(role.getAttribute().calculateFightPower(role.getCareer(), role.getLevel()));
        } else {
            bean.setFightPower(role.getFinalAttribute().calculateFightPower(role.getCareer(), role.getLevel()));
        }
        bean.setSex(summary.getSex());
        bean.setVipLevel(summary.getVipLevel());
        bean.setFightPower(memberInfo.getHistoryFightPower());
        bean.setDayGongXian(memberInfo.getDayDonate());
        bean.setTotalGongXian(memberInfo.getDonate());

        if (SessionManager.getInstance().isRoleOnline(summary.getId())) {
            bean.setOfflineTime(0);
        } else {
            bean.setOfflineTime(summary.getOfflineTime());
        }

        ProxyPlayer player = PlayerProxyManager.getInstance().getPlayer(rid);
        if (player != null && player.getMapCfgId() > 0) {
            bean.setMapId(player.getMapCfgId());
        }
        bean.setJoinTime((int) (memberInfo.getJoinTime() / 1000));
        bean.setZhuanShengId(role.getRoleAdvance().getZhuanshengId());

        bean.addAllFashions(QueryManager.getInstance().fashionsToBean(summary.getData().getFashions()));
        return bean.build();
    }


    public static UnionProtos.UnionInfoBean toUnionInfoBean(Role role, Union union) {
        UnionProtos.UnionInfoBean.Builder bean = UnionProtos.UnionInfoBean.newBuilder();
        if (role != null && union.getApplyList().containsKey(role.getId())) {
            bean.setApplyState(UnionConst.ApplyUnionState.APPLIED);
        } else {
            bean.setApplyState(UnionConst.ApplyUnionState.CAN_APPLY);
        }
        bean.setCreateTime(union.getCreateTime());
        String unionLeaderName = getUnionLeaderName(union);
        if (unionLeaderName == null) {
            unionLeaderName = "佚名";
        }
        bean.setLeaderName(unionLeaderName);
        bean.setUnionId(union.getId());
        bean.setUnionLevel(union.getUnionLevel());
        bean.setUnionName(union.getName());
        bean.setUnionNum(union.getMemberInfos().size());
        bean.setJoinCondition(union.getJoinCondition());
        if (SessionManager.getInstance().isRoleOnline(getUnionLeaderId(union.getId()))) {
            bean.setLeaderOnline(1);
        }

        bean.setUnionFund(union.getFund());
        bean.setAnnouncement(union.getAnnouncement());
        bean.setAutoJoin(union.getAutoJoin());
        bean.setBossRanking(union.getBossRanking());
        bean.setTime(union.getDissolveTime());
        bean.setCallPosition(union.getCallPosition());
        bean.setCampType(findCampTypeByUnion(union));
        bean.setExpendLevel(union.getUnionExpandLevel());
        bean.setXuanYan(union.getXuanYan());
        bean.setFlag(union.getFlag());
        bean.setFakeUnionConfigId(union.getFakeUnion());
        long fightPower = 0L;
        for (MemberInfo info : union.getMemberInfos().values()) {
            fightPower += info.getHistoryFightPower();
        }
        bean.setUnionFightPower(fightPower);
        return bean.build();
    }

    public static int findCampTypeByUnion(Union union) {
        CrossCampData campData = ServerJiangHuCamp.findCampData();
        if (campData == null) {
            return 0;
        }
        Optional<CrossUnionCamp> optional = campData.getCamp().values().stream()
                .flatMap(Collection::stream).filter(e -> e.getId() == union.getId()).findFirst();
        if (!optional.isPresent()) {
            return 0;
        }

        CrossUnionCamp unionCamp = optional.get();
        return unionCamp.getCampType();
    }

    public static UnionProtos.ApplyListInfoBean.Builder toApplyListInfoBean(Role role) {
        UnionProtos.ApplyListInfoBean.Builder bean = UnionProtos.ApplyListInfoBean.newBuilder();
        bean.setCareer(role.getCareer());
        bean.setId(role.getId());
        bean.setMemberLevel(role.getRoleAdvance().getLevel());
        bean.setMemberName(role.getName());
        bean.setFightValue(role.getFinalAttribute().calculateFightPower(role.getCareer(), role.getLevel()));
        bean.setSex(role.getSex());
        bean.setZhuanShengId(role.getRoleAdvance().getZhuanshengId());
        bean.setVipLevel(role.getRoleAdvance().getVipLevel());

        RoleSummary summary = SummaryManager.getInstance().getSummary(role.getId());
        if (summary != null) {
            bean.addAllFashions(QueryManager.getInstance().fashionsToBean(summary.getData().getFashions()));
        }

        RenderData renderData = role.findNormal().getRenderData();
        bean.setSecondIncome(ItemCoinUtil.getCoinBigInteger(renderData.getSecondIncome()).toString());

        return bean;
    }

    /**
     * 获取行会会长名
     *
     * @param union
     * @return
     */
    public static String getUnionLeaderName(Union union) {
        if (union.getFakeUnion() > 0) {
            UnionGuideConfig config = ConfigDataManager.getInstance().getById(UnionGuideConfig.class, union.getFakeUnion());
            if (config == null) {
                return null;
            }
            return config.getLeaderid();
        } else {
            long leaderId = getUnionLeaderId(union.getId());
            RoleSummary summary = SummaryManager.getInstance().getSummary(leaderId);
            return summary != null ? summary.getName() : null;
        }
    }

    /**
     * 获取行会会长id
     *
     * @param unionId
     * @return
     */
    public static long getUnionLeaderId(long unionId) {
        Union union = DataCenter.get(Union.class, unionId);
        if (union == null) {
            return 0;
        }

        for (MemberInfo info : union.getMemberInfos().values()) {
            if (info.getPosition() == UnionPositionConst.Position.CHAIRMAN) {
                return info.getMemberId();
            }
        }

        return 0;
    }

    public static UnionProtos.UnionEventBean toUnionEventBean(UnionEvent event) {
        UnionProtos.UnionEventBean.Builder bean = UnionProtos.UnionEventBean.newBuilder();
        bean.setTime(event.getTime());
        bean.setId(event.getId());
        for (String param : event.getParams()) {
            bean.addParams(param);
        }

        return bean.build();
    }

    /**
     * 添加事件
     *
     * @param union
     * @param recordId
     * @param params
     */
    public static void addEvent(Union union, int recordId, String... params) {
        UnionEvent event = new UnionEvent(recordId, params);

        List<UnionEvent> events = union.getEvents();
        int globalInt = GlobalUtil.getGlobalInt(GameConst.GlobalId.UNION_ENENT_NUM);
        if (events.size() >= globalInt) {
            events.remove(0);
        }
        events.add(event);
        DataCenter.updateData(union);
    }

    /**
     * 校验行会职位
     *
     * @param role 角色
     * @return
     */
    public static boolean checkUnionPosition(Role role, int... positions) {
        Union myUnion = UnionManager.getInstance().getUnion(role);
        if (myUnion == null) {
            return false;
        }
        MemberInfo memberInfo = myUnion.getMemberInfos().get(role.getRoleId());
        if (memberInfo == null) {
            return false;
        }
        if (positions == null) {
            return memberInfo.getPosition() == UnionPositionConst.Position.CHAIRMAN;
        }
        for (int position : positions) {
            if (memberInfo.getPosition() == position) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取行会id
     *
     * @param role 角色
     * @return 行会id
     */
    public static long findRoleUnionId(Role role) {
        Union myUnion = UnionManager.getInstance().getUnion(role);
        if (myUnion == null) {
            return 0;
        }
        MemberInfo memberInfo = myUnion.getMemberInfos().get(role.getRoleId());
        if (memberInfo == null) {
            return 0;
        }

        return myUnion.getId();
    }


    /**
     * 获取行会人数
     *
     * @param union 行会
     * @return 行会人数
     */
    public static int findUnionPopulation(Union union) {
        if (union == null) {
            return 0;
        }
        return union.getMemberInfos().size();
    }

}
