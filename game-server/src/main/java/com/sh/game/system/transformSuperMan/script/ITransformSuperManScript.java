package com.sh.game.system.transformSuperMan.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.transformSuperMan.entity.RoleToSuperMan;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-30
 **/
public interface ITransformSuperManScript extends IScript {

    RoleToSuperMan find(long rid);

    void reqInfo(Role role);

    void reqBuyTransformed(Role role);

    void reqOpenProtect(Role role, int isOpen);

    void dropKuang<PERSON><PERSON>(Role role);


}
