package com.sh.game.system.qianMing.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.qianMing.QianMingManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.qianMing.ReqQianMingInfoMessage;

/**
 * <p>请求签名系统信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqQianMingInfoHandler extends AbstractHandler<ReqQianMingInfoMessage> {

    @Override
    public void doAction(ReqQianMingInfoMessage msg) {
        QianMingManager.getInstance().reqQianMingInfo(SessionUtil.getRole(msg));
    }

}
