package com.sh.game.system.barrier.msg;

import com.sh.game.common.communication.msg.system.barrier.ReqBarrierBattleMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.barrier.BarrierManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqBarrierBattleHandler extends AbstractHandler<ReqBarrierBattleMessage> {

    @Override
    public void doAction(ReqBarrierBattleMessage msg) {
        BarrierManager.getInstance().reqBarrierBattle(SessionUtil.getRole(msg));
    }

}
