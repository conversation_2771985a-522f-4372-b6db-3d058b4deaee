package com.sh.game.system.activity.entity.role;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 限时累充
 *
 * <AUTHOR>
 * @date 2022/6/21 17:59
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleLimitedTimeRecharge extends AbstractRoleEntity {
    @Tag(1)
    private long id;

    /**
     * 活动期间充值金额
     * key: 活动id
     * value: 活动期间充值金额
     */
    @Tag(2)
    Map<Integer, Integer> recharge = new HashMap<>();


    /**
     * 活动期间领取记录
     * key: 活动id
     * value: 已领取的配表id
     */
    @Tag(3)
    Map<Integer, List<Integer>> requiredCids = new HashMap<>();

}