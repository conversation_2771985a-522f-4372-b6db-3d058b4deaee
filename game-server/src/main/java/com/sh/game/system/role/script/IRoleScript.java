package com.sh.game.system.role.script;

import com.sh.game.common.constant.LogAction;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.User;
import com.sh.script.IScript;
import com.sh.server.Session;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 *
 * @author: xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2018/3/22 20:09
 * desc  :
 * Copyright(©) 2017 by xiaomo.
 */
public interface IRoleScript extends IScript {

    Role createCharacter(Session session, User user, String roleName, int sex, int career, int hair, LogAction action);

    void levelChange(Role role, long actorId, int oLevel, int nLevel);

    void reqCreateHero(Role role, String roleName, int career, int sex);

    void exchangeMoxue(Role role);

    void reqMoXueCount(Role role);

    void reqShenBingExtraDailyExp(Role role);

    void roleKilledByMonster(Role role);

    /**
     * 请求获取角色多倍回收卡buff
     *
     * @param role 用户角色
     **/
    void getRecoveryCard(Role role);

    /**
     * 请求角色今日刀刀元宝获取数
     *
     * @param role 角色
     */
    void reqGoldCountInfo(Role role);

    void sendRoleOnlineTime(Role role);

}
