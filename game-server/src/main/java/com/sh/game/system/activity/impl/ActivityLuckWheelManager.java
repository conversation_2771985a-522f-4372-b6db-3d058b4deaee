package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.entity.role.RoleLuckWheelActivity;
import com.sh.game.system.activity.script.IActivityLuckWheelScript;
import com.sh.script.ScriptEngine;

/**
 * 幸运转盘活动
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-02
 **/
public class ActivityLuckWheelManager {

    private static final ActivityLuckWheelManager INSTANCE = new ActivityLuckWheelManager();

    private ActivityLuckWheelManager() {

    }

    public static ActivityLuckWheelManager getInstance() {
        return INSTANCE;
    }


    /**
     * 查找幸运转盘记录
     *
     * @param rid 角色id
     * @return 幸运转盘记录
     */
    public RoleLuckWheelActivity find(long rid) {
        return ScriptEngine.invoke1t1WithRet(IActivityLuckWheelScript.class, script -> script.find(rid));
    }

    /**
     * 请求幸运转盘信息
     *
     * @param role 角色
     */
    public void reqInfo(Role role, int activityType) {
        ScriptEngine.invoke1tn(IActivityLuckWheelScript.class, script -> script.reqInfo(role, activityType));
    }

    /**
     * 幸运转盘抽奖
     *
     * @param role 角色
     */
    public void wheelRaffle(Role role, int activityType) {
        ScriptEngine.invoke1tn(IActivityLuckWheelScript.class, script -> script.wheelRaffle(role, activityType));
    }

}
