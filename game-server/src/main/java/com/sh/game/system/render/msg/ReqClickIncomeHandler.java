package com.sh.game.system.render.msg;

import com.sh.game.common.communication.msg.system.render.ReqClickIncomeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.render.RenderManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求点击收益</p>
* <p>Created by MessageUtil</p>
* @date 2024-09-04 上午9:22:31
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqClickIncomeHandler extends AbstractHandler<ReqClickIncomeMessage> {

    @Override
    public void doAction(ReqClickIncomeMessage msg) {
        RenderManager.getInstance().clickIncome(SessionUtil.getRole(msg.getSession()), msg.getProto().getSecondCount());
    }
}