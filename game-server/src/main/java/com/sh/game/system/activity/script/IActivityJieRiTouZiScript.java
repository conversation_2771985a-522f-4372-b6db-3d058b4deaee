package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

public interface IActivityJieRiTouZiScript extends IScript {

    /**
     * 领取投资好礼奖励
     *
     * @param role
     */
    void reqReceiveReWard(Role role, int cfgId, int activityId);

    /**
     * 请求投资豪礼活动信息
     *
     * @param role
     */
    void reqTouZiHaoLiInfo(Role role, int activityId);
}
