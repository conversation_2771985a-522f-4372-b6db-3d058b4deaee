package com.sh.game.system.magicweapon.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.magicweapon.MagicWeaponManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.magicweapon.ReqMagicWeaponSlotInfoMessage;

/**
 * <p>请求法宝开孔信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqMagicWeaponSlotInfoHandler extends AbstractHandler<ReqMagicWeaponSlotInfoMessage> {

    @Override
    public void doAction(ReqMagicWeaponSlotInfoMessage msg) {
        MagicWeaponManager.getInstance().reqSlotInfo(SessionUtil.getRole(msg));
    }

}
