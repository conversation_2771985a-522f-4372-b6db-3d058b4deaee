package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityShenBingKuangHuanScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2022/6/29 17:45
 */
public class ActivityShenBingKuangHuanManager {

    private static final ActivityShenBingKuangHuanManager INSTANCE = new ActivityShenBingKuangHuanManager();

    public static ActivityShenBingKuangHuanManager getInstance() {
        return INSTANCE;
    }

    public void info(Role role) {
        ScriptEngine.invoke1t1(IActivityShenBingKuangHuanScript.class,s -> s.info(role));
    }

    public void reqReward(Role role, int cfgId) {
        ScriptEngine.invoke1t1(IActivityShenBingKuangHuanScript.class,s -> s.reqReward(role, cfgId));
    }
}
