package com.sh.game.system.activity.msg;

import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityTreasureMonsterManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqActivityTreasureMonsterCallMessage;

/**
 * <p>请求寻宝</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivityTreasureMonsterCallHandler extends AbstractHandler<ReqActivityTreasureMonsterCallMessage> {

    @Override
    public void doAction(ReqActivityTreasureMonsterCallMessage msg) {
        ActivityProtos.ReqActivityTreasureMonsterCall proto = msg.getProto();
        ActivityTreasureMonsterManager.getInstance().reqTreasureMonsterHunt(SessionUtil.getRole(msg.getSession()), proto.getCid());
    }

}
