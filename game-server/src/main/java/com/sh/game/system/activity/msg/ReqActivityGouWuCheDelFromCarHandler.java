package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityGouWuCheManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqActivityGouWuCheDelFromCarMessage;

/**
 * <p>从购物车删除商品</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqActivityGouWuCheDelFromCarHandler extends AbstractHandler<ReqActivityGouWuCheDelFromCarMessage> {

    @Override
    public void doAction(ReqActivityGouWuCheDelFromCarMessage msg) {
        ActivityProtos.ReqActivityGouWuCheDelFromCar proto = msg.getProto();
        ActivityGouWuCheManager.getInstance().delItem(proto.getActivityID(), SessionUtil.getRole(msg.getSession()), proto.getItemId());
    }

}
