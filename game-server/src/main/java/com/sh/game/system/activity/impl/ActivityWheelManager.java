package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.entity.role.RoleActivityWheel;
import com.sh.game.system.activity.entity.role.RoleLuckWheelActivity;
import com.sh.game.system.activity.script.IActivityLuckWheelScript;
import com.sh.game.system.activity.script.IActivityWheelScript;
import com.sh.script.ScriptEngine;

/**
 * 积分转盘活动
 *
 * <AUTHOR>
 * @date 2022/07/25 16:17
 */
public class ActivityWheelManager {

    private static final ActivityWheelManager INSTANCE = new ActivityWheelManager();

    private ActivityWheelManager() {

    }

    public static ActivityWheelManager getInstance() {
        return INSTANCE;
    }

    /**
     * 根据roleId获取积分转盘活动数据
     *
     * @param roleId 角色id
     * @return RoleActivityLuckWheel 角色转盘活动数据
     */
    public RoleActivityWheel find(long roleId) {
        return ScriptEngine.invoke1t1WithRet(IActivityWheelScript.class, script -> script.find(roleId));
    }

    /**
     * 请求积分转盘信息
     *
     * @param role 角色
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1tn(IActivityWheelScript.class, script -> script.reqInfo(role));
    }

    /**
     * 请求积分转盘抽奖
     *
     * @param role          角色
     * @param activityId    活动id
     */
    public void wheelRaffle(Role role, int activityId) {
        ScriptEngine.invoke1tn(IActivityWheelScript.class, script -> script.wheelRaffle(role, activityId));
    }
}
