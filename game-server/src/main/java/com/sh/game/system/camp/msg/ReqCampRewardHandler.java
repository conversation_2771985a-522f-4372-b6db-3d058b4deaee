package com.sh.game.system.camp.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.camp.CampManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.camp.ReqCampRewardMessage;

/**
 * <p>请求训练营奖励信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqCampRewardHandler extends AbstractHandler<ReqCampRewardMessage> {

    @Override
    public void doAction(ReqCampRewardMessage msg) {
        CampManager.getInstance().campReward(SessionUtil.getRole(msg.getSession()));
    }
}
