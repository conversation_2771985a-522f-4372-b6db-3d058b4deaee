package com.sh.game.system.activity.entity.role;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 个人沙城捐献数据
 */
@Getter
@Setter
public class RoleJuanXianData {

    /**
     * 捐献获得的称号
     * 类型< - >穿戴
     */
    @Tag(1)
    private Map<Integer, Integer> wears = new HashMap<>();

    /**
     * 捐献数量
     */
    @Tag(2)
    private int count = 0;

    /**
     * 上次的排名
     */
    @Tag(3)
    private int rank = 0;

    /**
     * 合服次数
     */
    @Tag(4)
    private int mergeCount;

    /**
     * 历史捐献数量
     */
    @Tag(5)
    private int historyCount;

    /**
     * 是否激活永久称号
     */
    @Tag(6)
    private boolean yongJiuFashion;

    /**
     * 2022.9.1更新兼容线上数据，检查是否前三，发斗笠
     */
    @Tag(7)
    private boolean checkFashion;

}
