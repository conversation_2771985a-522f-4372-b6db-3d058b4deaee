package com.sh.game.system.tuiche.msg;

import com.sh.game.common.communication.msg.system.tuiche.ReqTuiCheGameInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.tuiche.TuiCheManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求推车游戏关卡信息</p>
* <p>Created by MessageUtil</p>
* @date 2024-10-28 下午5:43:16
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqTuiCheGameInfoHandler extends AbstractHandler<ReqTuiCheGameInfoMessage> {

    @Override
    public void doAction(ReqTuiCheGameInfoMessage msg) {
        TuiCheManager.getInstance().reqTuiCheInfo(SessionUtil.getRole(msg.getSession()));
    }
}