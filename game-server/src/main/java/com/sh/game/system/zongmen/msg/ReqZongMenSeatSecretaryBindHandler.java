package com.sh.game.system.zongmen.msg;

import com.sh.game.common.communication.msg.system.zongmen.ReqZongMenSeatSecretaryBindMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ZongMenProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.zongmen.ZongMenManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求宗门仙友绑定</p>
* <p>Created by MessageUtil</p>
* @date 2025-02-24 下午4:40:45
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqZongMenSeatSecretaryBindHandler extends AbstractHandler<ReqZongMenSeatSecretaryBindMessage> {

    @Override
    public void doAction(ReqZongMenSeatSecretaryBindMessage msg) {
        ZongMenProtos.ReqZongMenSeatSecretaryBind proto = msg.getProto();
        ZongMenManager.getInstance().reqZongMenSeatSecretaryBind(SessionUtil.getRole(msg.getSession()), proto.getZongMenCfgId(), proto.getSeat(), proto.getSecretaryCfgId());
    }
}