package com.sh.game.system.activity.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityRechargeDrawManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqActivityRechargeDrawInfoMessage;

/**
 * <p>请求活动数据</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivityRechargeDrawInfoHandler extends AbstractHandler<ReqActivityRechargeDrawInfoMessage> {

    @Override
    public void doAction(ReqActivityRechargeDrawInfoMessage msg) {
        ActivityRechargeDrawManager.getInstance().reqInfo(SessionUtil.getRole(msg.getSession()));
    }

}
