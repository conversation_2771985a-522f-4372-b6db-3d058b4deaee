package com.sh.game.system.huoban.msg;

import com.sh.game.common.communication.msg.system.huoban.ReqHuoBanInfoMessage;
import com.sh.game.common.communication.msg.system.huoban.ReqHuoBanLevelMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.huoban.HuoBanManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求地狱之路信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqHuoBanLevelHandler extends AbstractHandler<ReqHuoBanLevelMessage> {

    @Override
    public void doAction(ReqHuoBanLevelMessage msg) {
        HuoBanManager.getInstance().huoBanLevelUp(SessionUtil.getRole(msg), msg.getProto().getConfigId());
    }

}
