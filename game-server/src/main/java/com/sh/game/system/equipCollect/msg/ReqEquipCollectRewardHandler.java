package com.sh.game.system.equipCollect.msg;

import com.sh.game.common.communication.msg.system.equipCollect.ReqEquipCollectRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.EquipCollectProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.equipCollect.EquipCollectManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求领取装备收集奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqEquipCollectRewardHandler extends AbstractHandler<ReqEquipCollectRewardMessage> {

    @Override
    public void doAction(ReqEquipCollectRewardMessage msg) {
        EquipCollectProtos.ReqEquipCollectReward equipCollectReward = msg.getProto();
        EquipCollectManager.getInstance().gainReward(SessionUtil.getRole(msg), equipCollectReward.getCid());
    }

}
