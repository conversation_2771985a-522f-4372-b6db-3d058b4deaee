package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityWorldCupDianQiuManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqWorldCupDianQiuKuangHuanMessage;

/**
 * <p>请求狂欢奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqWorldCupDianQiuKuangHuanHandler extends AbstractHandler<ReqWorldCupDianQiuKuangHuanMessage> {

    @Override
    public void doAction(ReqWorldCupDianQiuKuangHuanMessage msg) {
        ActivityProtos.ReqWorldCupDianQiuKuangHuan proto = msg.getProto();
        ActivityWorldCupDianQiuManager.getInstance().reqKuangHuanReward(SessionUtil.getRole(msg), proto.getIdListList());
    }

}
