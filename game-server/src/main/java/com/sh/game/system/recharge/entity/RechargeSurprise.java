package com.sh.game.system.recharge.entity;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

/**
 * 充值惊喜
 *
 * <AUTHOR> <<EMAIL>>
 * @version 创建时间：2018年06月12日 17:29
 */
@Getter
@Setter
public class RechargeSurprise {

    /**
     * 对应配置表id 0以下为没开启 cfg_xianshijingxi.csv
     */
    @Tag(1)
    private int nowCfgId;

    /**
     * 奖励领取状态0不可领1可领取2已领取
     */
    @Tag(2)
    private int state;

    /**
     * 修正值
     */
    @Tag(3)
    private int fix;

    /**
     * 上次计算时间（秒 计算修正值）
     */
    @Tag(4)
    private int lastTime;

    /**
     * 是否是第一次结算修正值0是1不是
     */
    @Tag(5)
    private int isFirstCalc = 0;
}
