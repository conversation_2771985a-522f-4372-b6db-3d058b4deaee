package com.sh.game.system.medal;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.medal.script.IMedalScript;
import com.sh.script.ScriptEngine;

/**
 * 修罗勋章
 *
 * <AUTHOR>
 * @date 2022/08/22 17:22
 */
public class MedalManager {

    private static MedalManager instance = new MedalManager();

    private MedalManager() {
    }

    public static MedalManager getInstance() {
        return instance;
    }

    /**
     * 请求勋章信息
     *
     * @param role 角色
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1t1(IMedalScript.class, script -> script.reqInfo(role));
    }

    /**
     * 请求勋章升级
     *
     * @param role 角色
     */
    public void reqUpgrade(Role role) {
        ScriptEngine.invoke1t1(IMedalScript.class, script -> script.reqUpgrade(role));
    }
}
