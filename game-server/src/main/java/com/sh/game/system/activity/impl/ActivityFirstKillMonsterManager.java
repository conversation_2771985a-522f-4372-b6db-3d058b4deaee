package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.entity.role.FirstKillMonster;
import com.sh.game.system.activity.script.IActivityFirstKillMonsterScript;
import com.sh.script.ScriptEngine;

/**
 * 怪物首杀活动
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2021-12-02
 **/
public class ActivityFirstKillMonsterManager {
    private static final ActivityFirstKillMonsterManager INSTANCE = new ActivityFirstKillMonsterManager();

    private ActivityFirstKillMonsterManager() {

    }

    public static ActivityFirstKillMonsterManager getInstance() {
        return INSTANCE;
    }

    /**
     * 查找怪物首杀信息
     *
     * @param rid 角色uid
     * @return 怪物首杀信息
     */
    public FirstKillMonster find(long rid) {
        return ScriptEngine.invoke1t1WithRet(IActivityFirstKillMonsterScript.class, script -> script.find(rid));
    }

    /**
     * 请求怪物首杀信息
     *
     * @param role     角色
     * @param areaType 区域类型
     */
    public void reqInfo(Role role, int areaType) {
        ScriptEngine.invoke1t1(IActivityFirstKillMonsterScript.class, script -> script.reqInfo(role, areaType));

    }

    /**
     * 领取怪物首杀奖励
     *
     * @param role 角色
     * @param cid  怪物首杀表id
     */
    public void requireFirstKillReward(Role role, int cid) {
        ScriptEngine.invoke1t1(IActivityFirstKillMonsterScript.class, script -> script.requireFirstKillReward(role, cid));
    }

    /**
     * 领取怪物首杀奖励
     *
     * @param role 角色
     * @param cid  怪物首杀进度表id
     */
    public void requireFirstKillBoxReward(Role role, int cid) {
        ScriptEngine.invoke1t1(IActivityFirstKillMonsterScript.class, script -> script.requireFirstKillBoxReward(role, cid));
    }
}
