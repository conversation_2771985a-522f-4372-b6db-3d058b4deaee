package com.sh.game.system.towns.msg;

import com.sh.game.common.communication.msg.system.towns.ReqTownsInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.towns.TownsManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求城镇信息</p>
* <p>Created by MessageUtil</p>
* @date 2024-10-18 13:42:36
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqTownsInfoHandler extends AbstractHandler<ReqTownsInfoMessage> {

    @Override
    public void doAction(ReqTownsInfoMessage msg) {
        TownsManager.getInstance().reqTownsInfo(SessionUtil.getRole(msg.getSession()));
    }
}