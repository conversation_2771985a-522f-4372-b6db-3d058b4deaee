package com.sh.game.system.activity.entity.role;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

/**
 * 幸运转盘进度
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-02
 **/
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleLuckWheelActivity extends AbstractRoleEntity {
    /**
     * 角色 ID
     */
    @Tag(1)
    private long id;

    /**
     * 活动期间充值进度
     */
    @Tag(2)
    private int rechargeProgress;


    /**
     * 已抽奖次数
     */
    @Tag(3)
    private int raffleCount;


    /**
     * 活动期间充值进度
     * key: 活动id
     * value：充值进度
     */
    @Tag(4)
    private Map<Integer, Integer> actRechargeProgress = new HashMap<>();

    /**
     * 已抽奖次数
     * key: 活动id
     * value：已抽奖次数
     */
    @Tag(5)
    private Map<Integer, Integer> actUsedCount = new HashMap<>();


}
