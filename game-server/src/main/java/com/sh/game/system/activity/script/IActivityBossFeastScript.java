package com.sh.game.system.activity.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * boss盛宴
 *
 * <AUTHOR>
 * @date 2022/6/27/027 11:29
 */
public interface IActivityBossFeastScript extends IScript {

    /**
     * 请求Boss盛宴信息
     *
     * @param role 角色
     */
    void reqInfo(Role role);

    /**
     * 请求领奖
     *
     * @param role      角色
     * @param configId  Boss盛宴表配置id
     */
    void reqReward(Role role, int configId);
}
