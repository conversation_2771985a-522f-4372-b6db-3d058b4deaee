package com.sh.game.system.shenmo.msg;

import com.sh.game.common.communication.msg.system.shenmo.ReqShenMoLevelUpMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.shenmo.ShenMoManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqShenMoLevelUpHandler extends AbstractHandler<ReqShenMoLevelUpMessage> {

    @Override
    public void doAction(ReqShenMoLevelUpMessage msg) {
        ShenMoManager.getInstance().reqReqShenMoLevelUp(SessionUtil.getRole(msg.getSession()), msg.getProto().getShenMoId());
    }

}
