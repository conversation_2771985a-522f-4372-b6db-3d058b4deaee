package com.sh.game.system.using.impl;

import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.entity.backpack.BackpackStash;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.using.ItemUsage;

import java.util.List;

public class ItemUsageForServantLevelUp extends ItemUsage {

    @Override
    public boolean needMapVerify(Role role, Item item, ItemConfig config) {
        return true;
    }

    @Override
    public boolean verify(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        return config.getUseParam().length >= 1 && config.getUseParam()[0].length >= 1;
    }

    @Override
    public Boolean apply(Role role, Item item, ItemConfig config, int count, List<Integer> params, BackpackStash stash) {
        return null;
    }

    @Override
    public int getUsedType() {
        return 135;
    }
}
