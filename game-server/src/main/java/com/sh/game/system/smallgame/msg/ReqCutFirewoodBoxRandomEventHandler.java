package com.sh.game.system.smallgame.msg;

import com.sh.game.common.communication.msg.system.smallgame.ReqCutFirewoodBoxRandomEventMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.SmallGameProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.smallgame.SmallGameManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求砍柴宝箱随机事件</p>
* <p>Created by MessageUtil</p>
* @date 2025-03-18 下午2:47:49
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqCutFirewoodBoxRandomEventHandler extends AbstractHandler<ReqCutFirewoodBoxRandomEventMessage> {

    @Override
    public void doAction(ReqCutFirewoodBoxRandomEventMessage msg) {
        SmallGameProtos.ReqCutFirewoodBoxRandomEvent proto = msg.getProto();
        SmallGameManager.getInstance().reqCutFirewoodBoxRandomEvent(SessionUtil.getRole(msg), proto.getEventId(), proto.getOption());
    }
}