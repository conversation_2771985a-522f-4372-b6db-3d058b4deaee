package com.sh.game.system.wabao.msg;

import com.sh.game.common.communication.msg.system.wabao.ReqWaBaoExchangeMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.WabaoProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityWaBaoManager;
import com.sh.server.AbstractHandler;

/**
 * <p>挖宝兑换</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqWaBaoExchangeHandler extends AbstractHandler<ReqWaBaoExchangeMessage> {

    @Override
    public void doAction(ReqWaBaoExchangeMessage msg) {
        WabaoProtos.ReqWaBaoExchange proto = msg.getProto();
        ActivityWaBaoManager.getInstance().exchange(SessionUtil.getRole(msg.getSession()), proto.getConfigId());
    }

}
