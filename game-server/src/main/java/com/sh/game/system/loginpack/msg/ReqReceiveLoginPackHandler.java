package com.sh.game.system.loginpack.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.loginpack.LoginPackManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqReceiveLoginPackMessage;

/**
 * <p>请求领取登录豪礼</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqReceiveLoginPackHandler extends AbstractHandler<ReqReceiveLoginPackMessage> {

    @Override
    public void doAction(ReqReceiveLoginPackMessage msg) {
        ActivityProtos.ReqReceiveLoginPack proto = msg.getProto();
        LoginPackManager.getInstance().findReward(SessionUtil.getRole(msg), proto.getDay());
    }

}
