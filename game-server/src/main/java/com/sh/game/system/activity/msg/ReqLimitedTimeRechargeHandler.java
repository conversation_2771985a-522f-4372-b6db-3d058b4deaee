package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityLimitedTimeRechargeManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqLimitedTimeRechargeMessage;

/**
 * <p>请求限时充值活动信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqLimitedTimeRechargeHandler extends AbstractHandler<ReqLimitedTimeRechargeMessage> {

    @Override
    public void doAction(ReqLimitedTimeRechargeMessage msg) {
        ActivityLimitedTimeRechargeManager.getInstance().reqInfo(SessionUtil.getRole(msg));
    }

}
