package com.sh.game.system.social.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

import java.util.List;

public interface ISocialScript extends IScript {

    void openSocialPanel(Role role, int type);

    void socialAdd(Role role, int type, String idOrName);

    void socialDelete(Role role, int type, long targetId);

    void friendApplyOp(Role role, int op, List<Long> targetIdList);

    void loginSendFriendApplyList(Role role);

    List<Long> getSocialListByType(Role role, int type);

    void killAdd(Role role, int mapId, long rid);
}
