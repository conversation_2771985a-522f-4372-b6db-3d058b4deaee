package com.sh.game.system.chaijia.script;

import com.sh.game.common.entity.backpack.item.EquipData;
import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @date 2023/9/7 13:25
 */
public interface IChaiJiaScript extends IScript {

    void info(Role role);

    void chaiJia(Role role, int rate);

    void chaiJiaNoCost(Role role, int rate);

    void takeItem(Role role, long itemUid, int type, int index, boolean recycle);

    void recycleItem(Role role, long itemUid, int type);

    void upGradeChaiJia(Role role);

    /**
     * 看广告免费减少拆家升级时间
     *
     * @param role
     */
    void upGradeFree(Role role);

    void chaiJiaRate(Role role, int rate);

    void checkChaiJiaUp(Role role);

    void energyDrinkLevelUp(Role role);

    void energyDrinkInfo(Role role);

    void calChaiJiaAttr(Role role, EquipData data, int quality, int equipLocation);
}
