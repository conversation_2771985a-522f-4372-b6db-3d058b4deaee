package com.sh.game.system.chat.chattype.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.chat.ResChatMessage;
import com.sh.game.common.config.model.TipsConfig;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.tips.CommonTips;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.common.util.TipUtil;
import com.sh.game.server.SessionManager;
import com.sh.game.system.chat.chattype.IChat;


public class PersonChat implements IChat {

    /**
     * 私聊
     *
     * @param role   发送者
     * @param target 接收者
     * @param msg    消息内容
     */
    @Override
    public void chat(Role role, long target, ResChatMessage msg, int banFlag) {
        if (!SessionManager.getInstance().isRoleOnline(target)) {
            TipUtil.show(role.getId(), CommonTips.服务_该玩家不在线);
            return;
        }
        if (banFlag == 1) {
            MessageUtil.sendMsg(msg, role.getId());
            return;
        }
        MessageUtil.sendMsg(msg, target);
        if (role != null) {
            MessageUtil.sendMsg(msg, role.getId());
        }
    }
}
