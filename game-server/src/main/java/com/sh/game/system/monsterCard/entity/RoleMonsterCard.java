package com.sh.game.system.monsterCard.entity;

import com.sh.game.common.entity.usr.AbstractRoleEntity;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2021-12-28
 **/

@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleMonsterCard extends AbstractRoleEntity {
    /**
     * 角色 ID
     */
    @Tag(1)
    private long id;

    /**
     * key: 怪物表id
     * value: 击杀数量（受图鉴限制）
     */
    @Tag(2)
    private Map<Integer, Integer> monsterCidByCount = new HashMap<>();

    /**
     * 已解锁的怪物图鉴表id
     */
    @Tag(3)
    private List<Integer> unlockedMonsterCardCids = new ArrayList<>();

    /**
     * 已领取的怪物图鉴进度表id
     */
    @Tag(4)
    private List<Integer> requiredRewardCids = new ArrayList<>();
}
