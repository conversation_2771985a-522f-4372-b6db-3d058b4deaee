package com.sh.game.system.lilian;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleLiLian;
import com.sh.game.data.DataCenter;
import com.sh.game.system.lilian.script.ILiLianScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/** 历练 */
public class LiLianManager {
    private static final LiLianManager INSTANCE = new LiLianManager();
    private LiLianManager() {
    }
    public static LiLianManager getInstance() {
        return INSTANCE;
    }

    public RoleLiLian findRoleLiLian(long roleId) {
        RoleLiLian roleLiLian = DataCenter.get(RoleLiLian.class, roleId);
        if (roleLiLian == null) {
            roleLiLian = new RoleLiLian();
            roleLiLian.setId(roleId);
            DataCenter.insertData(roleLiLian,true);
        }
        return roleLiLian;
    }

    public void reqLiLianInfo(Role role) {
        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.reqLiLianInfo(role));
    }

    public void reqLiLian(Role role) {
        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.reqLiLian(role));
    }

    public void reqLiLianRandomEvent(Role role, int eventId, int option) {
        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.reqLiLianRandomEventOper(role, eventId, option));
    }

    public void reqLiLianFightMotivate(Role role, boolean costItem) {
        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.reqLiLianFightMotivate(role, costItem));
    }

//    public void reqLiLianBossSecretaryFight(Role role, List<Integer> secretaryCfgIds) {
//        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.reqLiLianBossSecretaryFight(role, secretaryCfgIds));
//    }

    public void reqLiLianAutoFight(Role role, int motivateCoinCount, int motivateItemCount) {
        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.reqLiLianAutoFight(role, motivateCoinCount, motivateItemCount));
    }

    public void reqLiLianAutoFightOver(Role role) {
        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.reqLiLianAutoFightOver(role));
    }



    //历练-剧情
    public void reqTriggerStoryLiLian(Role role) {
        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.reqTriggerStoryLiLian(role));
    }

    public void reqTriggerStoryLiLianGame(Role role, int gameStepId) {
        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.reqTriggerStoryLiLianGame(role, gameStepId));
    }

    public void gmLiLian(Role role,  int id, int node) {
        ScriptEngine.invoke1t1(ILiLianScript.class, script -> script.gmLiLian(role,  id, node));
    }

    /** 历练-事件类型 */
    public interface LiLianRandomEventType {
        int NORMAL = 1;
        int OPTION = 2;
        int PIC_OPTION = 3;
        int DISPATCH = 4;
        int COMBO = 5;
        int CHASE = 6;
    }
}
