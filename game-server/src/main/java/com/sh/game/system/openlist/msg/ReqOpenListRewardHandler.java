package com.sh.game.system.openlist.msg;

import com.sh.game.common.communication.msg.system.openlist.ReqOpenListRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.openlist.OpenListManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqOpenListRewardHandler extends AbstractHandler<ReqOpenListRewardMessage> {

    @Override
    public void doAction(ReqOpenListRewardMessage msg) {
        OpenListManager.getInstance().reward(SessionUtil.getRole(msg), msg.getProto().getCfgId());
    }

}