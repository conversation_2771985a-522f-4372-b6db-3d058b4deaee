package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityPurchasePresentScript;
import com.sh.script.ScriptEngine;

/**
 * 限购礼包
 */
public class ActivityPurchasePresentManager {
    private static final ActivityPurchasePresentManager INSTANCE = new ActivityPurchasePresentManager();

    private ActivityPurchasePresentManager() {

    }

    public static ActivityPurchasePresentManager getInstance() {
        return INSTANCE;
    }

    /**
     * 限购礼包信息
     *
     * @param role
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1t1(IActivityPurchasePresentScript.class, script -> script.reqInfo(role));
    }

    /**
     * 领取限购礼包奖励
     *
     * @param role
     * @param id
     */
    public void reqAcquireDaily(Role role, int id) {
        ScriptEngine.invoke1t1(IActivityPurchasePresentScript.class, script -> script.reqAcquireDaily(role, id));
    }

    /**
     * 领取限购礼包天数奖励
     *
     * @param role
     */
    public void reqAcquireDays(Role role) {
        ScriptEngine.invoke1t1(IActivityPurchasePresentScript.class, script -> script.reqAcquireDays(role));
    }

    /**
     * 领取累计金额奖励
     *
     * @param role
     * @param id
     */
    public void reqAcquireRecharge(Role role, int id) {
        ScriptEngine.invoke1t1(IActivityPurchasePresentScript.class, script -> script.reqAcquireRecharge(role, id));
    }

    /**
     * 修复累充进度数据
     *
     * @param rid
     * @param fixValue 增量值
     */
    public void reqFixActivitySchedule(long rid, int fixValue) {
        ScriptEngine.invoke1t1(IActivityPurchasePresentScript.class, script -> script.reqFixActivitySchedule(rid, fixValue));
    }

}
