package com.sh.game.system.climbTower.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.script.IScript;

/**
 * <AUTHOR>
 * @date 2021/11/12
 */
public interface IClimbTowerScript extends IScript {
    void reqInfo(Role role);
    void start(Role role);
    void rollTower(Role role, int type, int val);
    void jumpTower(Role role);
    void reqTurntableInfo(Role role, int type);
    void rollTurntable(Role role, int type);
    void acquireTurntable(Role role, int type);
}
