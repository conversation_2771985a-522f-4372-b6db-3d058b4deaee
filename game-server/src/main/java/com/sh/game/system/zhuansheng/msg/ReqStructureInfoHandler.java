package com.sh.game.system.zhuansheng.msg;

import com.sh.game.common.communication.msg.system.zhuansheng.ReqStructureInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.zhuansheng.ZhuanShengManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求新转生信息</p>
* <p>Created by MessageUtil</p>
* @date 2025-06-04 15:17:03
*/
@MessageHandler(ProcessorId.SERVER_COMMON)
public class ReqStructureInfoHandler extends AbstractHandler<ReqStructureInfoMessage> {

    @Override
    public void doAction(ReqStructureInfoMessage msg) {
        ZhuanShengManager.getInstance().reqStructureInfo(SessionUtil.getRole(msg.getSession()));
    }
}