package com.sh.game.system.houyun.msg;

import com.sh.game.common.communication.msg.system.houyuan.ReqHouYuanRatAutoItemMessage;
import com.sh.game.common.communication.msg.system.houyuan.ReqHouYuanToHouYuanMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.HouYuanProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.houyun.HouYuanManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求地狱之路信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqHouYuanRatAutoItemHandler extends AbstractHandler<ReqHouYuanRatAutoItemMessage> {

    @Override
    public void doAction(ReqHouYuanRatAutoItemMessage msg) {
        HouYuanProtos.ReqHouYuanRatAutoItem proto = msg.getProto();
        HouYuanManager.getInstance().ratAutoItem(SessionUtil.getRole(msg), proto.getCidList());
    }

}
