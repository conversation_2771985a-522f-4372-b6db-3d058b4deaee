package com.sh.game.system.heaven;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.heaven.script.IHeavenScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2022/9/7 15:58
 */
public class HeavenManager {

    private static final HeavenManager INSTANCE = new HeavenManager();

    public static HeavenManager getInstance() {
        return INSTANCE;
    }

    public void reqReward(Role role, int cid) {
        ScriptEngine.invoke1t1(IHeavenScript.class,s -> s.reqReward(role, cid));
    }

    public void reqOpenMap(Role role, int cid) {
        ScriptEngine.invoke1t1(IHeavenScript.class,s -> s.reqOpenMap(role, cid));
    }

    public void reqEnterMap(Role role) {
        ScriptEngine.invoke1t1(IHeavenScript.class,s -> s.reqEnterMap(role));
    }

    public void reqInfo(Role role) {
        ScriptEngine.invoke1t1(IHeavenScript.class,s -> s.reqInfo(role));
    }
}
