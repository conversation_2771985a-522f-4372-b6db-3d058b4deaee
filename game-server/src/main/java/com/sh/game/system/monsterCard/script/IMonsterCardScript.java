package com.sh.game.system.monsterCard.script;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.monsterCard.entity.RoleMonsterCard;
import com.sh.script.IScript;

/**
 * 怪物图鉴
 *
 * <AUTHOR>
 * @<PERSON>ail <EMAIL>
 * @since 2021-12-28
 **/
public interface IMonsterCardScript extends IScript {

    RoleMonsterCard find(long rid);

    void reqInfo(Role role);

    void unlockMonsterCard(Role role, int cid, boolean extraCost);

    void requireProgressReward(Role role, int cid);

}
