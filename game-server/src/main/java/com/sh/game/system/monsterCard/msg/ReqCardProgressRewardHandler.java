package com.sh.game.system.monsterCard.msg;

import com.sh.game.common.communication.msg.system.monsterCard.ReqCardProgressRewardMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.MonsterCardProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>请求领取怪物图鉴进度奖励</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqCardProgressRewardHandler extends AbstractHandler<ReqCardProgressRewardMessage> {

    @Override
    public void doAction(ReqCardProgressRewardMessage msg) {
        MonsterCardProtos.ReqCardProgressReward cardProgressReward = msg.getProto();
//        MonsterCardManager.getInstance().requireProgressReward(SessionUtil.getRole(msg.getSession()), cardProgressReward.getCid());
    }

}
