package com.sh.game.system.tianmo;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.tianmo.script.ITianMoLaiXiScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2022/8/9 15:24
 */
public class TianMoLaiXiManager {

    private static final TianMoLaiXiManager INSTANCE = new TianMoLaiXiManager();

    public static TianMoLaiXiManager getInstance() {
        return INSTANCE;
    }

    public void reqTaskInfo(Role role) {
        ScriptEngine.invoke1t1(ITianMoLaiXiScript.class,s -> s.reqTaskInfo(role));
    }

    public void reqTaskReward(Role role, int cid) {
        ScriptEngine.invoke1t1(ITianMoLaiXiScript.class,s -> s.reqTaskReward(role, cid));
    }
}
