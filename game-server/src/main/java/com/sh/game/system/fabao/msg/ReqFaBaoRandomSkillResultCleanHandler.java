package com.sh.game.system.fabao.msg;

import com.sh.game.common.communication.msg.system.fabao.ReqFaBaoRandomSkillResultCleanMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.FaBaoProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.fabao.FaBaoManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求法宝技能随机结果清除</p>
* <p>Created by MessageUtil</p>
* @date 2024-12-02 上午10:08:49
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqFaBaoRandomSkillResultCleanHandler extends AbstractHandler<ReqFaBaoRandomSkillResultCleanMessage> {

    @Override
    public void doAction(ReqFaBaoRandomSkillResultCleanMessage msg) {
        FaBaoProtos.ReqFaBaoRandomSkillResultCleanMessage proto = msg.getProto();
        FaBaoManager.getInstance().reqFaBaoRandomSkillResultClean(SessionUtil.getRole(msg.getSession()), proto.getFaBaoId(), proto.getSkillIndex());
    }
}