package com.sh.game.system.dailyDuplicate.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.DailyProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.dailyDuplicate.DailyDuplicateManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.daily.ReqShaoDangDailyDuplicateMessage;
import com.sh.server.Session;

/**
 * <p>扫荡藏经阁</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqShaoDangDailyDuplicateHandler extends AbstractHandler<ReqShaoDangDailyDuplicateMessage> {

    @Override
    public void doAction(ReqShaoDangDailyDuplicateMessage msg) {
        Role role = SessionUtil.getRole(msg.getSession());
        DailyProtos.ReqShaoDangDailyDuplicate shaoDangDailyDuplicate = msg.getProto();
        DailyDuplicateManager.getInstance().faQiHuiYuanShaoDang(role, shaoDangDailyDuplicate.getDuplicateId());
    }

}
