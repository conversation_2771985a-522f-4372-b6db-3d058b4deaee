package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityLingFuWheelManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqLingFuZhuanPanRewardMessage;

/**
 * <p>请求灵符转盘玩法</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqLingFuZhuanPanRewardHandler extends AbstractHandler<ReqLingFuZhuanPanRewardMessage> {

    @Override
    public void doAction(ReqLingFuZhuanPanRewardMessage msg) {
        ActivityLingFuWheelManager.getInstance().reqWheelRaffle(SessionUtil.getRole(msg));
    }

}
