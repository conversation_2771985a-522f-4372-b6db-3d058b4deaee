package com.sh.game.system.compound.msg;

import com.sh.game.common.communication.msg.system.compound.ReqCompoundSpecialItemInfosMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.compound.CompoundManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求特殊道具合成信息</p>
 * <p>Created by MessageUtil</p>
 * @date 2020-06-13 11:09:05
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqCompoundSpecialItemInfosHandler extends AbstractHandler<ReqCompoundSpecialItemInfosMessage> {

    @Override
    public void doAction(ReqCompoundSpecialItemInfosMessage msg) {
        CompoundManager.getInstance().reqCompoundSpecialItemInfos(SessionUtil.getRole(msg));
    }

}
