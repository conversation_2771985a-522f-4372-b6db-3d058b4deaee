package com.sh.game.system.rank.msg;

import com.sh.game.common.communication.msg.system.rank.ReqRankSimpleMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.RankProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.rank.RankManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求查看排行榜对象外观</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqRankSimpleHandler extends AbstractHandler<ReqRankSimpleMessage> {

    @Override
    public void doAction(ReqRankSimpleMessage msg) {
        RankProtos.ReqRankSimple reqRankSimple = msg.getProto();
        RankManager.getInstance().reqRankSimple(SessionUtil.getRole(msg), reqRankSimple.getRoleId());
    }

}
