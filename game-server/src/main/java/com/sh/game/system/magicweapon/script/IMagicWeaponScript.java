package com.sh.game.system.magicweapon.script;

import com.sh.game.common.entity.backpack.item.MagicWeapon;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.protos.MagicWeaponProtos;
import com.sh.game.system.magicweapon.entity.RoleMagicWeaponSlot;
import com.sh.script.IScript;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/6/22 15:58
 */
public interface IMagicWeaponScript extends IScript {

    /**
     * 请求打造法宝
     *
     * @param role      角色
     * @param configId  cfg_fabaohecheng表id
     */
    void compound(Role role, int configId);

    /**
     * 请求开孔
     *
     * @param role      角色
     * @param configId  equip_fabaokong槽位id
     */
    void reqSlot(Role role, int configId);

    /**
     * 获取角色法宝开孔信息
     *
     * @param role 角色
     * @return RoleMagicWeaponSlot 角色法宝开孔信息
     */
    RoleMagicWeaponSlot findSlot(Role role);

    /**
     * 请求发送开孔信息
     *
     * @param role 角色
     */
    void reqSlotInfo(Role role);

    /**
     * 请求法器镶嵌
     *
     * @param role         角色
     * @param weaponList  镶嵌法器列表
     */
    void reqWeaponInlay(Role role, List<MagicWeaponProtos.MagicWeaponBean> weaponList);

    /**
     * 请求法器分离
     *
     * @param role      角色
     * @param slotConfigIdList 孔位配置id列表
     */
    void reqSeparate(Role role, List<Integer> slotConfigIdList);

    /**
     * 获取角色法宝开孔信息
     *
     * @param role  角色
     * @param where 背包类型
     * @param pos   部位
     * @return RoleMagicWeaponSlot 角色法宝开孔信息
     */
    Map<Integer, MagicWeapon> find(Role role, int where, int pos);
}
