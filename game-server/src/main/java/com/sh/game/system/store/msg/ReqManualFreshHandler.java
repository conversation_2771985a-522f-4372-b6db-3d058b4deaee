package com.sh.game.system.store.msg;

import com.sh.game.common.communication.msg.system.store.ReqManualFreshMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.StoreProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.store.StoreManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求手动刷新</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqManualFreshHandler extends AbstractHandler<ReqManualFreshMessage> {

    @Override
    public void doAction(ReqManualFreshMessage msg) {
        StoreProtos.ReqManualFresh proto = msg.getProto();
        StoreManager.getInstance().reqManualFresh(SessionUtil.getRole(msg.getSession()), proto.getStoreClassId());
    }

}
