package com.sh.game.system.shenWeiTuPo.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ShenWeiTuPoProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.shenWeiTuPo.ShenWeiManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.shenWeiTuPo.ReqShenWeiUpMessage;

/**
 * <p>请求神威升级</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqShenWeiUpHandler extends AbstractHandler<ReqShenWeiUpMessage> {

    @Override
    public void doAction(ReqShenWeiUpMessage msg) {
        ShenWeiTuPoProtos.ReqShenWeiUp proto = msg.getProto();
        ShenWeiManager.getInstance().reqShenWeiUp(SessionUtil.getRole(msg), proto.getType());
    }

}
