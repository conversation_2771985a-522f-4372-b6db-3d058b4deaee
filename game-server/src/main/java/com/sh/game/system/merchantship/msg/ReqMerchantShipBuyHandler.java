package com.sh.game.system.merchantship.msg;

import com.sh.game.common.communication.msg.system.merchantship.ReqMerchantShipBuyMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.merchantship.MerchantShipManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求商船游戏道具购买</p>
* <p>Created by MessageUtil</p>
* @date 2024-09-30 下午8:00:26
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqMerchantShipBuyHandler extends AbstractHandler<ReqMerchantShipBuyMessage> {

    @Override
    public void doAction(ReqMerchantShipBuyMessage msg) {
        MerchantShipManager.getInstance().reqMerchantShipBuy(SessionUtil.getRole(msg));
    }
}