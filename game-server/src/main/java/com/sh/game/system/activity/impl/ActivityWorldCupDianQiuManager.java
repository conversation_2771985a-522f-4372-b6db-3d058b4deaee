package com.sh.game.system.activity.impl;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.activity.script.IActivityWorldCupDianQiuScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/2 18:47
 */
public class ActivityWorldCupDianQiuManager {

    private static final ActivityWorldCupDianQiuManager INSTANCE = new ActivityWorldCupDianQiuManager();

    public static ActivityWorldCupDianQiuManager getInstance() {
        return INSTANCE;
    }

    public void info(Role role) {
        ScriptEngine.invoke1t1(IActivityWorldCupDianQiuScript.class,s -> s.info(role));
    }

    public void reqDianQiu(Role role) {
        ScriptEngine.invoke1t1(IActivityWorldCupDianQiuScript.class,s -> s.reqDianQiu(role));
    }

    public void reqKuangHuanReward(Role role, List<Integer> idList) {
        ScriptEngine.invoke1t1(IActivityWorldCupDianQiuScript.class,s -> s.req<PERSON><PERSON><PERSON>uanReward(role, idList));
    }
}
