package com.sh.game.system.banquet.msg;

import com.sh.game.common.communication.msg.system.banquet.ReqBanquetCreateLogMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.banquet.BanquetManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求宴会创建记录</p>
* <p>Created by MessageUtil</p>
* @date 2024-11-28 下午3:21:27
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqBanquetCreateLogHandler extends AbstractHandler<ReqBanquetCreateLogMessage> {

    @Override
    public void doAction(ReqBanquetCreateLogMessage msg) {
        BanquetManager.getInstance().reqBanquetCreateLog(SessionUtil.getRole(msg.getSession()));
    }
}