package com.sh.game.system.union;

import com.sh.game.common.communication.msg.system.union.ResDiLaoLayerMessage;
import com.sh.game.common.communication.msg.system.union.ResSendQuitUnionSuccessMessage;
import com.sh.game.common.constant.GoalConst;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.avatar.AvatarUnion;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.event.IEventOnReqUnionInfoScript;
import com.sh.game.event.IEventOnRoleUnionUpdateScript;
import com.sh.game.protos.UnionProtos;
import com.sh.game.server.SessionManager;
import com.sh.game.system.attr.script.IAttributeScript;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.summary.SummaryManager;
import com.sh.game.system.union.entity.MemberInfo;
import com.sh.game.system.union.script.IUnionScript;
import com.sh.script.ScriptEngine;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Optional;

/**
 * 行会系统
 */
@Slf4j
public class UnionManager {
    private static final UnionManager INSTANCE = new UnionManager();

    private UnionManager() {
    }

    public static UnionManager getInstance() {
        return INSTANCE;
    }

    /** 1允许自动加入 */
    public static final int AUTO_JOIN = 1;

    public Union getUnion(Role role) {
        if (role == null) {
            return null;
        }
        return DataCenter.get(Union.class,role.getUnion().getId());
    }

    public Union getUnion(long unionUID) {
        return DataCenter.get(Union.class,unionUID);
    }

    /**
     * 获取家族名称
     *
     * @param unionID
     * @return
     */
    public String getUnionName(long unionID) {
        Union union = DataCenter.get(Union.class, unionID);
        if (union == null) {
            return null;
        }
        return union.getName();
    }

    private Optional<IUnionScript> getScript() {
        return Optional.ofNullable(ScriptEngine.get1t1(IUnionScript.class));
    }

    /**
     * 请求获取所有帮会
     *
     * @param role
     */
    public void reqAllUnions(Role role) {
        getScript().ifPresent(script -> script.reqAllUnions(role));
    }

    /**
     * 请求获取帮会
     *
     * @param role
     */
    public void reqUnionInfo(Role role) {
        getScript().ifPresent(script -> script.reqUnionInfo(role));

        ScriptEngine.invoke1tn(IEventOnReqUnionInfoScript.class, script -> script.onReqUnionInfo(role));
    }

    /**
     * 创建行会
     *
     * @param role
     * @param unionName
     * @param way
     * @param joinCondition
     * @param autoJoin
     */
    public void createUnion(Role role, String unionName, int way, String joinCondition, int autoJoin, String gongGao, String xuanYan, int flag) {
        getScript().ifPresent(script -> script.createUnion(role, unionName, way, joinCondition, autoJoin, gongGao, xuanYan, flag));
    }

    /**
     * 解散行会
     *
     * @param role
     * @param dissolve true解散false取消解散
     */
    public void dissolveUnion(Role role, boolean dissolve) {
        getScript().ifPresent(script -> script.dissolveUnion(role, dissolve));
    }

    /**
     * 改变设置
     *
     * @param role
     * @param joinCondition
     * @param autoJoin
     */
    public void changeSettings(Role role, String joinCondition, int autoJoin) {
        getScript().ifPresent(script -> script.changeSettings(role, joinCondition, autoJoin));
    }

    /**
     * 修改公告
     *
     * @param role
     * @param announcement
     */
    public void changeAnnouncement(Role role, String announcement, int type) {
        getScript().ifPresent(script -> script.changeAnnouncement(role, announcement, type));
    }

    /**
     * 邀请玩家
     *
     * @param role
     * @param roleId
     */
    public void inviteMember(Role role, long roleId) {
        getScript().ifPresent(script -> script.inviteMember(role, roleId));
    }

    /**
     * 接受邀请
     *
     * @param role
     * @param unionId
     */
    public void acceptInvitation(Role role, long unionId) {
        getScript().ifPresent(script -> script.acceptInvitation(role, unionId));
    }

    /**
     * 同意加入
     *
     * @param role
     * @param unionId
     */
    public void applyJoin(Role role, long unionId) {
        getScript().ifPresent(script -> script.applyJoin(role, unionId));
    }

    /**
     * 同意加入所有行会
     *
     * @param role
     */
    public void applyJoinAll(Role role) {
        getScript().ifPresent(script -> script.applyJoinAll(role));
    }

    /**
     * 请求获取申请入会列表
     *
     * @param role
     */
    public void reqApplications(Role role) {
        getScript().ifPresent(script -> script.reqApplications(role));
    }

    /**
     * 处理申请列表
     *
     * @param role
     * @param list
     * @param way
     */
    public void handleApplication(Role role, List<Long> list, int way) {
        getScript().ifPresent(script -> script.handleApplication(role, list, way));
    }

    /**
     * 改变职位
     *
     * @param role
     * @param memberId
     * @param position
     */
    public void changePosition(Role role, long memberId, int position) {
        getScript().ifPresent(script -> script.changePosition(role, memberId, position));
    }

    /**
     * 移除成员
     *
     * @param role
     * @param memberId
     */
    public void removeMember(Role role, long memberId) {
        getScript().ifPresent(script -> script.removeMember(role, memberId));
    }

    /**
     * 退出行会
     *
     * @param role
     */
    public void quitUnion(Role role) {
        getScript().ifPresent(script -> script.quitUnion(role));
    }

    /**
     * 获取行会事件
     *
     * @param role
     */
    public void reqUnionEvents(Role role) {
        getScript().ifPresent(script -> script.reqUnionEvents(role));
    }

    /**
     * 玩家家族变化
     *
     * @param role
     * @param isChangeUnion
     */
    public void onRoleUnionUpdate(Role role, boolean isChangeUnion) {
        AvatarUnion roleUnion = role.getUnion();
        MemberInfo info = null;
        Union union = UnionManager.getInstance().getUnion(role);
        if (union != null) {
            info = union.getMemberInfos().get(role.getId());
        }
        if (info == null) {
            roleUnion.setId(0);
            roleUnion.setName("");
            roleUnion.setPosition(0);
            roleUnion.getAllies().clear();
            roleUnion.getEnemies().clear();
        } else {
            roleUnion.setId(union.getId());
            roleUnion.setName(union.getName());
            roleUnion.setPosition(info.getPosition());
            roleUnion.setAllies(union.getAllies());
            roleUnion.setEnemies(union.getEnemies());
            long fightPower = ScriptEngine.invoke1t1WithRet(IAttributeScript.class, script -> script.getCurFightPower(role));
            info.setHistoryFightPower(fightPower);
        }
        role.setUnion(roleUnion);
        DataCenter.updateData(role);
        SummaryManager.getInstance().updateUnion(role.getId(), roleUnion.getId(), roleUnion.getName());

        if (roleUnion.getId() != 0) {
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.JOIN_UNION);
        }

        if (isChangeUnion && SessionManager.getInstance().isRoleOnline(role.getId())) {
            if (roleUnion.getId() != 0) {
                reqUnionInfo(role);
            } else {
                ResSendQuitUnionSuccessMessage msg = new ResSendQuitUnionSuccessMessage();
                msg.setProto(UnionProtos.ResSendQuitUnionSuccess.newBuilder().build());
                MessageUtil.sendMsg(msg, role.getId());
            }

            ScriptEngine.invoke1tn(IEventOnRoleUnionUpdateScript.class, script -> script.onRoleUnionUpdate(role));
        }
    }

    /**
     * 修改使用召唤令职位条件
     *
     * @param role
     * @param callPoition
     */
    public void changeUnionCall(Role role, int callPoition) {
        getScript().ifPresent(script -> script.changeUnionCall(role, callPoition));
    }

    /**
     * 请求行会地牢打到哪一层了
     *
     * @param role
     */
    public void reqDiLaoLayer(Role role) {
        Union union = UnionManager.getInstance().getUnion(role);
        if (union == null) {
            return;
        }
        ResDiLaoLayerMessage msg = new ResDiLaoLayerMessage();
        UnionProtos.ResDiLaoLayer proto = UnionProtos.ResDiLaoLayer.newBuilder()
                .setMapId(union.getLayerMapId())
                .build();
        msg.setProto(proto);
        MessageUtil.sendMsg(msg, role.getId());
    }

    /**
     * 后台执行解散行会
     *
     * @param unionId
     */
    public void deleteUnion(long unionId) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.deleteUnionForBack(unionId));
    }

    public void reqUnionExpand(Role role) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.reqUnionExpand(role));
    }

    public void reqUnionExpandLevel(Role role) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.reqUnionExpandLevel(role));
    }

    public int getPosition(Role role, Union union) {
        return ScriptEngine.invoke1t1WithRet(IUnionScript.class, s -> s.getPosition(role, union));
    }

    public void checkRandomUnionName(Role role) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.checkRandomUnionName(role));
    }

    public void changeUnionFlag(Role role, int cid) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.changeUnionFlag(role, cid));
    }

    public void reqUnionInfos(Role role, long unionId) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.reqUnionInfos(role, unionId));
    }

    public void xieZhuInfo(Role role) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.sendXieZhuInfo(role));
    }

    public void reqXieZhu(Role role) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.reqXieZhu(role));
    }

    public void xieZhu(Role role, long rid) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.xieZhu(role, rid));
    }

    public void setLeaderVX(Role role, String vx) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.setLeaderVX(role, vx));
    }

    public void changeUnionName(Role role, String name) {
        ScriptEngine.invoke1tn(IUnionScript.class, script -> script.changeUnionName(role, name));
    }

    public boolean checkMemberJoinTime(Role role) {
        return ScriptEngine.invoke1t1WithRet(IUnionScript.class, script -> script.checkMemberJoinTime(role));
    }

    public void randomJoin(Role role) {
        ScriptEngine.invoke1t1(IUnionScript.class, script -> script.randomJoin(role));
    }
}


