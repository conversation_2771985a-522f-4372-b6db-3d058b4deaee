package com.sh.game.system.record;

import com.sh.game.protos.CompoundProtos;
import com.sh.game.system.compound.entity.CompoundSpecialItemRecord;
import com.sh.game.system.record.script.ISystemRecordScript;
import com.sh.script.ScriptEngine;

import java.util.List;

/**
 * 系统合成相关记录
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by SuSu on 2021/10/27.
 */
public class RecordManager {

    private static final RecordManager instance = new RecordManager();

    public static RecordManager getInstance() {
        return instance;
    }

    /**
     * 合成记录
     */
    public void addCompoundRecord(CompoundSpecialItemRecord record) {
        ScriptEngine.invoke1t1(ISystemRecordScript.class, script -> script.addCompoundRecord(record));
    }

    public List<CompoundSpecialItemRecord> findCompoundRecords() {
        return ScriptEngine.invoke1t1WithRet(ISystemRecordScript.class, ISystemRecordScript::findCompoundRecords);
    }
}
