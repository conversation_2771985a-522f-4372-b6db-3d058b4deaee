package com.sh.game.system.storygd.msg;

import com.sh.game.common.communication.msg.system.storygd.ReqStoryMainInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.storygd.StoryGdManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求剧情主线信息</p>
* <p>Created by MessageUtil</p>
* @date 2024-08-28 下午2:27:56
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqStoryMainInfoHandler extends AbstractHandler<ReqStoryMainInfoMessage> {

    @Override
    public void doAction(ReqStoryMainInfoMessage msg) {
        StoryGdManager.getInstance().reqStoryMainInfo(SessionUtil.getRole(msg));
    }
}