package com.sh.game.system.equipCollect;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.equipCollect.entity.RoleEquipCollect;
import com.sh.game.system.equipCollect.script.IActivityEquipCollectScript;
import com.sh.script.ScriptEngine;

/**
 * 装备收集
 *
 * <AUTHOR>
 * @Email <EMAIL>
 * @since 2022-03-31
 **/
public class EquipCollectManager {
    private static final EquipCollectManager INSTANCE = new EquipCollectManager();

    private EquipCollectManager() {
    }

    public static EquipCollectManager getInstance() {
        return INSTANCE;
    }


    public RoleEquipCollect find(long rid) {
        return ScriptEngine.invoke1t1WithRet(IActivityEquipCollectScript.class, script -> script.find(rid));
    }

    /**
     * 请求装备收集信息
     *
     * @param role
     */
    public void reqInfo(Role role) {
        ScriptEngine.invoke1tn(IActivityEquipCollectScript.class, script -> script.reqInfo(role));
    }

    /**
     * 领取装备收集奖励
     *
     * @param role 角色
     * @param cid  装备收集表id
     */
    public void gainReward(Role role, int cid) {
        ScriptEngine.invoke1tn(IActivityEquipCollectScript.class, script -> script.gainReward(role, cid));
    }

}