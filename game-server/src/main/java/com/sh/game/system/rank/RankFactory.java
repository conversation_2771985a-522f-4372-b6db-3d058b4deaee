package com.sh.game.system.rank;

import com.sh.common.config.ConfigDataManager;
import com.sh.commons.util.ClassUtil;
import com.sh.game.common.config.model.RankConfig;
import com.sh.game.common.constant.RankConst;
import com.sh.game.common.constant.RoleConst;
import com.sh.game.system.rank.entity.RankData;
import com.sh.game.system.rank.type.AbstractRank;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/25 14:03<br>;
 * 版本：1.0<br>;
 * 描述：排行榜工厂类
 */
public class RankFactory {

    /**
     * 所有排行榜字典
     */
    private static final Map<Integer, AbstractRank> RANK_HASH_MAP = new HashMap<>();

    /**
     * 初始化排行榜
     */
    public static final void initAllRank(){
        Set<Class<?>> clzs = ClassUtil.findClassWithSuperClass("com.sh.game.system.rank.type.impl", AbstractRank.class);
        AbstractRank rank;
        for (Class<?> clz : clzs) {
            try {
                rank = (AbstractRank) clz.newInstance();
                RANK_HASH_MAP.put(rank.getRankType(),rank);
            } catch (Exception e){
                e.printStackTrace();
            }
        }
        for (RankConfig config : ConfigDataManager.getInstance().getList(RankConfig.class)) {
            rank = RANK_HASH_MAP.get(config.getId());
            if (rank == null) {
                continue;
            }
            rank.setCondition(config.getCondition());
            rank.setLimit(config.getNum());
            rank.setFirstFashion(config.getReward());
        }

    }

    /**
     * 获取指定类型排行榜
     *
     * @param type
     * @return
     */
    public static AbstractRank getHandler(int type) {
        return RANK_HASH_MAP.get(type);
    }

    /**
     * 获取所有排行榜
     *
     * @return
     */
    public static Map<Integer, AbstractRank> getAllRankHandler(){
        return RANK_HASH_MAP;
    }

    /**
     * 获取玩家攻击排行榜名次
     *
     * @param career 职业
     * @return
     */
    public static int getRoleAtkRank(int career, long rid) {
        int type = 0;
        switch (career) {
            case RoleConst.Career.ZHAN:
                type = RankConst.RankType.ROLE_ATK_ZHASHI;
                break;
            case RoleConst.Career.FA:
                type = RankConst.RankType.ROLE_ATK_FASHI;
                break;
            case RoleConst.Career.DAO:
                type = RankConst.RankType.ROLE_ATK_DAOSHI;
                break;
            default:
                break;
        }
        AbstractRank abstractRank = RANK_HASH_MAP.get(type);
        if (abstractRank == null) {
            return 0;
        }
        return abstractRank.getRankList().stream().filter(e -> e.getRoleID() == rid)
                .map(RankData::getRank).findFirst().orElse(0);
    }

}
