package com.sh.game.system.using.impl;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.using.ResUsingCutFirewoodBoxSuccessMessage;
import com.sh.game.common.config.model.BoxConfig;
import com.sh.game.common.config.model.ItemConfig;
import com.sh.game.common.constant.*;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.usr.RoleSmallGame;
import com.sh.game.common.util.*;
import com.sh.game.protos.AbcProtos;
import com.sh.game.protos.UsingProtos;
import com.sh.game.system.count.CountManager;
import com.sh.game.system.goal.GoalManager;
import com.sh.game.system.smallgame.SmallGameManager;
import com.sh.game.system.smallgame.entity.CutFirewoodBoxEventData;
import com.sh.game.system.using.ItemUsage;
import org.apache.commons.collections.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 砍柴宝箱
 * <AUTHOR>
 * @Date 2025/1/15 19:49
 */
public class ItemUsageForCutFirewoodBox extends ItemUsage {
    @Override
    public int getUsedType() {
        return BagConst.UseType.GD_CUT_FIREWOOD_BOX;
    }

    public void using(Role role, List<AbcProtos.CommonKeyValueBean> usingItemsList) {
        if(CollectionUtils.isEmpty(usingItemsList)) {
            return;
        }

        RoleSmallGame roleSmallGame = SmallGameManager.getInstance().getRoleSmallGame(role);
        CutFirewoodBoxEventData cutFirewoodBoxEventData = roleSmallGame.getCutFirewoodBoxEventData();
        if(cutFirewoodBoxEventData.getCurFightEventId() > 0) {
            return;
        }
        if(cutFirewoodBoxEventData.getCurRandomEventIds().size() >= GlobalUtil.getGlobalInt(GameConst.GlobalId.CUTFIREWOODBOX_EVENT_RANDOM_MAX)) {
            return;
        }

        Map<Integer, Long> costMap = new HashMap<>();
        for (AbcProtos.CommonKeyValueBean itemMod : usingItemsList) {
            ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, itemMod.getKey());
            if(config == null || config.getUseType() != getUsedType() || !check(config)) {
                return;
            }
            long count = itemMod.getValue();
            costMap.merge(itemMod.getKey(), count, Long::sum);
            if(CollectionUtils.isNotEmpty(config.getGodCost())) {
                config.getGodCost().forEach(ints-> {
                    costMap.merge(ints[0], ints[1] * count, Long::sum);
                });
            }
        }
        ItemCoinUtil.refreshCoinBigNumCarry(costMap);
        if(!BackPackStashUtil.decrease(role, costMap, 1, LogAction.ITEM_USE)) {
            return;
        }

        int totalCount = 0;
        UsingProtos.ResUsingCutFirewoodBoxSuccess.Builder builder = UsingProtos.ResUsingCutFirewoodBoxSuccess.newBuilder();
        Map<Integer, Long> itemMap = new HashMap<>();
        for (AbcProtos.CommonKeyValueBean itemMod : usingItemsList) {
            ItemConfig config = ConfigDataManager.getInstance().getById(ItemConfig.class, itemMod.getKey());
            int paramItemId = config.getUseParam()[0][0];
            List<Item> items = BoxUtil.openBox(role, paramItemId, itemMod.getValue());
            items.forEach(tempItem-> {
                itemMap.merge(tempItem.getCfgId(), tempItem.getCount(), Long::sum);
                builder.addRewards(AbcProtos.CommonKeyValueBean.newBuilder().setKey(tempItem.getCfgId()).setValue((int)tempItem.getCount()));
            });
            addUseCount(role, config, itemMod.getValue());
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.ITEM_USE_TIMES, config.getId(), itemMod.getValue());
            CountManager.getInstance().count(role, CountConst.CountType.USE_QUALITY_CUT_FIREWOOD_BOX_TOTAL_COUNT, 0, itemMod.getValue());
            CountManager.getInstance().count(role, CountConst.CountType.USE_QUALITY_CUT_FIREWOOD_BOX_TOTAL_COUNT, config.getQuality(), itemMod.getValue());
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.USE_QUALITY_CUT_FIREWOOD_BOX_COUNT, config.getQuality(), itemMod.getValue());
            GoalManager.getInstance().updateGoal(role, GoalConst.GoalType.USE_QUALITY_CUT_FIREWOOD_BOX_TOTAL_COUNT, itemMod.getValue());
            totalCount += itemMod.getValue();
        }

        ItemCoinUtil.itemOnlyLimit(role, itemMap);
        BackPackStashUtil.increase(role, itemMap, LogAction.ITEM_USE, false);

        SmallGameManager.getInstance().usageForCutFirewoodBox(role, totalCount);

        //推送奖励
        ResUsingCutFirewoodBoxSuccessMessage message = new ResUsingCutFirewoodBoxSuccessMessage();
        message.setProto(builder.build());
        MessageUtil.sendMsg(message, role);
    }

    private boolean check(ItemConfig config) {
        if (config.getUseParam().length < 1 || config.getUseParam()[0].length < 1) {
            return false;
        }
        BoxConfig boxConfig = ConfigDataManager.getInstance().getById(BoxConfig.class, config.getUseParam()[0][0]);
        return boxConfig != null;
    }
}
