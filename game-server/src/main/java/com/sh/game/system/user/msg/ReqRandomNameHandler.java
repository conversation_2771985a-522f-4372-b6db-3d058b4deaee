package com.sh.game.system.user.msg;

import com.sh.game.protos.UserProtos;
import com.sh.game.system.user.UserManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.user.ReqRandomNameMessage;

/**
 * <p>随机名字</p>
 * <p>Created by MessageUtil</p>
 * @date 2020-07-31 14:48:10
 */
public class ReqRandomNameHandler extends AbstractHandler<ReqRandomNameMessage> {

    @Override
    public void doAction(ReqRandomNameMessage msg) {
        UserProtos.ReqRandomName reqRandomName  = msg.getProto();
        UserManager.getInstance().randomName(
                msg.getSession(),
                reqRandomName.getSex()
        );
    }

}
