package com.sh.game.system.chaijia.msg;

import com.sh.game.common.communication.msg.system.chaijia.ReqChaiJiaInfoMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.chaijia.ChaiJiaManager;
import com.sh.server.AbstractHandler;

@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqChaiJiaInfoHandler extends AbstractHandler<ReqChaiJiaInfoMessage> {

    @Override
    public void doAction(ReqChaiJiaInfoMessage msg) {
        ChaiJiaManager.getInstance().info(SessionUtil.getRole(msg));
    }

}