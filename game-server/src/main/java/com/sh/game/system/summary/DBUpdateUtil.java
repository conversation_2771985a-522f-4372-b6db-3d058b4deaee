package com.sh.game.system.summary;

import com.sh.game.data.DelayQueryThread;
import com.sh.game.common.util.JdbcUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2019/1/2 10:46
 */
public class DBUpdateUtil {

    private static AtomicInteger THREAD_INDEX = new AtomicInteger(0);

    private static final Logger LOGGER = LoggerFactory.getLogger(DelayQueryThread.class);

    public static final ExecutorService EXECUTOR = Executors.newSingleThreadExecutor(r->
        new Thread(r, "数据延迟写入线程（线程池）" + THREAD_INDEX.incrementAndGet())
    );

    public static void update(String sql, Object[] param) {
        Update update = new Update();
        update.setParam(param);
        update.setSql(sql);
        EXECUTOR.execute(update);
    }

    public static void updateRightNow(String sql, Object[] param) {
        Update update = new Update();
        update.setParam(param);
        update.setSql(sql);
        JdbcUtil.update(sql, param);
    }

    public static void stop() throws InterruptedException{
        EXECUTOR.shutdown();
        int total = 60;
        while(total > 0 && !EXECUTOR.awaitTermination(1, TimeUnit.SECONDS)){
            LOGGER.error("正在关闭延迟入库线程....");
        }
    }

    public static class Update implements Runnable{
        String sql;

        Object[] param;

        @Override
        public void run() {
            JdbcUtil.update(sql, param);
        }


        public String getSql() {
            return sql;
        }

        public void setSql(String sql) {
            this.sql = sql;
        }

        public Object[] getParam() {
            return param;
        }

        public void setParam(Object[] param) {
            this.param = param;
        }


    }
}
