package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityPurchaseDailyManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqPurchaseDailyRewardMessage;

/**
 * <p>请求每日累充领奖</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqPurchaseDailyRewardHandler extends AbstractHandler<ReqPurchaseDailyRewardMessage> {

    @Override
    public void doAction(ReqPurchaseDailyRewardMessage msg) {
        ActivityProtos.ReqPurchaseDailyReward proto = msg.getProto();
        ActivityPurchaseDailyManager.getInstance().reqReward(SessionUtil.getRole(msg), proto.getConfigId());
    }

}
