package com.sh.game.system.activity.msg;

import com.sh.game.common.communication.msg.system.activity.ReqActivityGemGoalAcquireMessage;
import com.sh.game.protos.ActivityProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityGemGoalManager;
import com.sh.server.AbstractHandler;

/**
 * <p></p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqActivityGemGoalAcquireHandler extends AbstractHandler<ReqActivityGemGoalAcquireMessage> {

    @Override
    public void doAction(ReqActivityGemGoalAcquireMessage msg) {
        ActivityProtos.ReqActivityGemGoalAcquire proto = msg.getProto();
        ActivityGemGoalManager.getInstance().reqAcquire(SessionUtil.getRole(msg.getSession()), proto.getCid());
    }

}
