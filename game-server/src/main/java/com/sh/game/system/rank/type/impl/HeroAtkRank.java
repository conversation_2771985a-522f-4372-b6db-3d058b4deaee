package com.sh.game.system.rank.type.impl;

import com.sh.game.common.constant.RankConst;
import com.sh.game.common.entity.usr.RoleSummary;
import com.sh.game.system.rank.entity.RankData;
import com.sh.game.system.rank.type.AbstractRank;

/**
 * ATO：yumo<br>;
 * 时间：2020/12/25 15:46<br>;
 * 版本：1.0<br>;
 * 描述：英雄攻击力排行榜
 */
public class HeroAtkRank extends AbstractRank {
    /**
     * 排行榜查询SQL语句
     */
    private String queryRankSql;

    @Override
    protected RankData buildRankData(RoleSummary summary, int rank) {
        RankData data = new RankData();
        data.setRank(rank);
        data.setName(summary.getName());
        data.setRoleID(summary.getId());
        data.setParameter(summary.getCareer());
        data.setRankValue(summary.getHeroAtk());
        return data;
    }

    @Override
    public String getQuerySql() {
        if (queryRankSql == null) {
            queryRankSql = "select * from p_summary where banChat < 1 and heroAtk > 0 and level > " + condition + " order by heroAtk desc, level desc limit 0," + limit;
        }
        return queryRankSql;
    }

    @Override
    public int getRankType() {
        return RankConst.RankType.HERO_ATK;
    }
}
