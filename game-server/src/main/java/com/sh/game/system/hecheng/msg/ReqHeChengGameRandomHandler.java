package com.sh.game.system.hecheng.msg;

import com.sh.game.common.communication.msg.system.hecheng.ReqHeChengGameRandomMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.hecheng.HeChengManager;
import com.sh.server.AbstractHandler;

/**
* <p>请求合成游戏步骤随机</p>
* <p>Created by MessageUtil</p>
* @date 2024-10-29 下午5:11:48
*/
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqHeChengGameRandomHandler extends AbstractHandler<ReqHeChengGameRandomMessage> {

    @Override
    public void doAction(ReqHeChengGameRandomMessage msg) {
        HeChengManager.getInstance().reqHeChengRandom(SessionUtil.getRole(msg));
    }
}