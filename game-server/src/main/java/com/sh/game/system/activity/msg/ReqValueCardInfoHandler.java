package com.sh.game.system.activity.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.activity.impl.ActivityValueCardManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.activity.ReqValueCardInfoMessage;

/**
 * <p>请求超值双卡信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqValueCardInfoHandler extends AbstractHandler<ReqValueCardInfoMessage> {

    @Override
    public void doAction(ReqValueCardInfoMessage msg) {
        ActivityValueCardManager.getInstance().reqInfo(SessionUtil.getRole(msg));
    }

}
