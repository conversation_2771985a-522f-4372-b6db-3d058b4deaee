package com.sh.game.system.mingwang;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.mingwang.script.IMingWangScript;
import com.sh.script.ScriptEngine;

/**
 * <AUTHOR>
 * @date 2021/12/28 10:16
 */
public class MingWangManager {

    private static final MingWangManager INSTANCE = new MingWangManager();

    public static MingWangManager getInstance() {
        return INSTANCE;
    }

    public void upLevel(Role role) {
        ScriptEngine.invoke1t1(IMingWangScript.class, script -> script.upLevel(role));
    }
}
