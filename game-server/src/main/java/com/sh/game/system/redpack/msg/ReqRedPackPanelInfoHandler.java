package com.sh.game.system.redpack.msg;

import com.sh.game.server.SessionUtil;
import com.sh.game.system.redpack.RedPackManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.union.ReqRedPackPanelInfoMessage;

/**
 * <p>请求红包面板</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqRedPackPanelInfoHandler extends AbstractHandler<ReqRedPackPanelInfoMessage> {

    @Override
    public void doAction(ReqRedPackPanelInfoMessage msg) {
        RedPackManager.getInstance().sendRedPackPanelInfo(SessionUtil.getRole(msg.getSession()));
    }

}
