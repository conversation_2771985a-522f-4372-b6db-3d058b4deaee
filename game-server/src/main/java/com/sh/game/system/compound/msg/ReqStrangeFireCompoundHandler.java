package com.sh.game.system.compound.msg;

import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.CompoundProtos;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.compound.CompoundManager;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.compound.ReqStrangeFireCompoundMessage;

/**
 * <p>请求异火合成</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_PLAYER)
public class ReqStrangeFireCompoundHandler extends AbstractHandler<ReqStrangeFireCompoundMessage> {

    @Override
    public void doAction(ReqStrangeFireCompoundMessage msg) {
        CompoundProtos.ReqStrangeFireCompound strangeFireCompound = msg.getProto();
        CompoundManager.getInstance().reqStrangeFireCompound(SessionUtil.getRole(msg), strangeFireCompound.getConfigId(),
                strangeFireCompound.getACostId(), strangeFireCompound.getBCostId());
    }

}
