package com.sh.game.filter;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @Email <EMAIL>
 * Created by zt on 2022/6/15
 */
@Getter
@Setter
public class UnionNameCheckContext {
    private long roleID;

    private String oldName;

    private String newName;

    private int way;

    private String joinCondition;

    private int autoJoin;
    /**
     * 操作类型: 见UnionConst  中的定义   NAMECHECK_CREATE   NAMECHECK_MODIFY
     */
    private int operatorType;

    private String gongGao;

    private String xuanYan;
}
