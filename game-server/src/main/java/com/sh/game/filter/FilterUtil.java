package com.sh.game.filter;

import com.sh.common.config.ConfigCacheManager;
import com.sh.common.config.ConfigDataManager;
import com.sh.game.GameContext;
import com.sh.game.common.config.cache.ChatMonitorConfigCache;
import com.sh.game.common.config.model.BannedNameConfig;
import com.sh.game.common.config.model.ChatMonitorConfig;
import com.sh.game.system.chat.script.*;
import com.sh.script.ScriptEngine;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * ATO：yumo<br>;
 * 时间：2021/1/28 14:09<br>;
 * 版本：1.0<br>;
 * 描述：敏感词工具
 */
public class FilterUtil {

    /**
     * 敏感词根节点
     */
    private static TrieNode baseNode = new TrieNode();

//    public static Map<Integer, ContentJude> judeMap = new HashMap<>();
    public static Map<Integer, Class<?>> dealChatScript = new TreeMap<>();
    private static ContentJude defaultContentJude = new ContentJudeLocal();

//    static {
//        judeMap.put(0, new ContentJudeLocal());
//        judeMap.put(1, new ContentJudeTanWan());
//        judeMap.put(2, new ContentJudeDiTing());
//    }
    public static void registerDealChatScript(int chatMode, Class<?> clazz){
        dealChatScript.put(chatMode, clazz);
    }

    private static void registerMonitorChatScripts(){
//        //贪玩
//        registerDealChatScript(1, IChatMonitorTanWanScript.class);
//        //谛听平台
//        registerDealChatScript(2, IChatMonitorDiTingScript.class);
//        //1377平台
//        registerDealChatScript(3, IChatMonitor1377Script.class);
//        //八九游平台
//        registerDealChatScript(4, IChatMonitorBaJiuGameScript.class);
//        //玩心聚合聊天
//        registerDealChatScript(5, IChatMonitorWanXingJuHeScript.class);
        //XY平台聊天监控
        registerDealChatScript(1, IChatMonitorXYScript.class);
//        //9377怒火一刀聊天监控
//        registerDealChatScript(7, IChatMonitor9377NHYDScript.class);
//        //掌玩买量聊天监控
//        registerDealChatScript(8, IChatMonitorZWMLScript.class);
//        //玩心聊天监控
//        registerDealChatScript(9, IChatMonitorWanXinScript.class);
//        //上士平台聊天监控
//        registerDealChatScript(10, IChatMonitorShangShiScript.class);
    }
    /**
     * 初始化敏感词树
     */
    public static void init() {
        baseNode = new TrieNode();
        List<BannedNameConfig> nameConfigsList = ConfigDataManager.getInstance().getList(BannedNameConfig.class);
        addKeys(baseNode, nameConfigsList);
        registerMonitorChatScripts();
    }

    public static void addKeys(TrieNode trieNode, List<BannedNameConfig> nameConfigsList ){
        int platID = GameContext.getOption().getPlatformId();
        for (BannedNameConfig config : nameConfigsList) {
            if (config.getQudao_success() != null
                    && !config.getQudao_success().isEmpty()
                    && !config.getQudao_success().contains(platID)){
                continue; // 渠道生效    仅填了的渠道生效 (即,当前有填写渠道号,那么不是指定渠道则跳过
            }
            //渠道不生效     仅填了的渠道不生效
            if (config.getQudao_fail() != null
                    && !config.getQudao_fail().isEmpty()
                    && config.getQudao_fail().contains(platID)){
                continue;  //有填写且是当前渠道则跳过
            }
            trieNode.addKey(config.getText());
        }
    }

    /**
     * 是否包含敏感词
     *
     * @param text
     * @return
     */
    public static boolean hasBadWord(String text) {
        return baseNode.hasBadWord(text);
    }

    /**
     * 替换敏感词
     *
     * @param text
     * @param c
     * @return
     */
    public static String replaceBadWord(String text, char c) {
        return baseNode.replace(text, c);
    }

    public static  ChatMonitorConfig getCurrentFilterConfig(){
        ChatMonitorConfigCache cache = ConfigCacheManager.getInstance().getCache(ChatMonitorConfigCache.class);
        ChatMonitorConfig monitorConfig = cache.getChatMonitorConfig(GameContext.getOption().getPlatformId());
        return monitorConfig;
    }

    public  static  Class<?> getScriptExector(int nType){
        return dealChatScript.get(nType);
    }

    public static void replaceBadWord(ChatContent chatContent) {

//        ContentJude contentJude = judeMap.get(GameContext.getOption().getChatMode());
//        if (contentJude != null) {
//            contentJude.replaceBadWord(chatContent);
//        }
        ChatMonitorConfig monitorConfig = getCurrentFilterConfig();

        if (monitorConfig == null){
            defaultContentJude.replaceBadWord(chatContent);
        }else{
            Class<IChatMonitorScript> classObj = (Class<IChatMonitorScript>)getScriptExector(monitorConfig.getType());
            if (classObj != null) {
                ScriptEngine.invoke1t1(classObj, script ->script.replaceBadWord(chatContent, monitorConfig));
            }else{
                defaultContentJude.replaceBadWord(chatContent);
            }
        }

    }
}
