package com.sh.game.back.msg;

import com.sh.game.data.DataCenter;
import com.sh.game.common.entity.sys.OtherData;
import com.sh.game.common.communication.msg.system.back.ReqSetRoleCreateLimitMessage;
import com.sh.game.common.communication.msg.system.back.ResBackRetMessage;
import com.sh.game.protos.BackProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>请求设定创建开服限制</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-08-31 17:14:25
 */
public class ReqSetRoleCreateLimitHandler extends AbstractHandler<ReqSetRoleCreateLimitMessage> {

    @Override
    public void doAction(ReqSetRoleCreateLimitMessage msg) {
        OtherData otherData = DataCenter.getOtherData();
        BackProtos.ReqSetRoleCreateLimit proto = msg.getProto();
        otherData.setCreateRoleLimitDay(proto.getOpenServerDays());
        otherData.setCreateRoleLimitDescribe(proto.getLimitDescribe());
        otherData.setHasSetCreateRoleLimit(true);
        DataCenter.updateData(otherData);

        ResBackRetMessage res = new ResBackRetMessage();
        res.setSequence(msg.getSequence());
        res.setProto(BackProtos.ResBackRet.newBuilder()
                .setRet("设定创角开服时间限制成功")
                .build());
        msg.getSession().sendMessage(res);
    }

}
