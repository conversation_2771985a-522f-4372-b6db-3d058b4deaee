package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.ReqBackChangeUnionMasterMessage;
import com.sh.game.protos.BackProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>后台改变会长</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 13:01:17
 */
public class ReqBackChangeUnionMasterHandler extends AbstractHandler<ReqBackChangeUnionMasterMessage> {

    @Override
    public void doAction(ReqBackChangeUnionMasterMessage msg) {
//        BackProtos.ReqBackChangeUnionMaster proto = msg.getProto();
//        BackManager.getInstance().backChangeUnionMaster(msg.getSession(), msg.getSequence(), proto.getUnionId(), proto.getUid(), proto.getPlayerName());

    }

}
