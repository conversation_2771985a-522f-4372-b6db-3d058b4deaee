package com.sh.game.back.bugfix.util;

import com.sh.common.jdbc.RowMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PooledDBUtil {
	
	private final static Logger LOGGER = LoggerFactory.getLogger(PooledDBUtil.class);
	
	public static <T> T query(String sql, RowMapper<T> mapper, Object... parameters) {

		PreparedStatement pstmt = null;
		ResultSet rs = null;
		Connection conn = null;
		try {
			conn = PooledConnectionUtil.getConnection("Default");
			pstmt = conn.prepareStatement(sql);
			for (int i = 0; i < parameters.length; i++) {
				pstmt.setObject(i + 1, parameters[i]);
			}
			rs = pstmt.executeQuery();
			if (rs.next()) {
				return mapper.mapping(rs);
			}
		} catch (Exception e) {
			LOGGER.error("查询单条数据失败,sql:" + sql, e);
		} finally {
			release(conn, pstmt, rs, mapper);
		}
		return null;
	}

	public static <T> List<T> queryList(String sql, RowMapper<T> mapper, Object... parameters) {

		PreparedStatement pstmt = null;
		ResultSet rs = null;
		Connection conn = null;
		try {
			conn = PooledConnectionUtil.getConnection("Default");
			pstmt = conn.prepareStatement(sql);
			for (int i = 0; i < parameters.length; i++) {
				pstmt.setObject(i + 1, parameters[i]);
			}
			rs = pstmt.executeQuery();
			List<T> ret = new ArrayList<>();
			while (rs.next()) {
				ret.add(mapper.mapping(rs));
			}
			return ret;
		} catch (Exception e) {
			LOGGER.error("查询多条数据失败,sql:" + sql, e);
		} finally {
			release(conn, pstmt, rs, mapper);
		}
		return Collections.emptyList();
	}

	public static int update(String sql, Object... parameters) {

		PreparedStatement pstmt = null;
		Connection conn = null;
		
		try {
			conn = PooledConnectionUtil.getConnection("Default");
			pstmt = conn.prepareStatement(sql);
			for (int i = 0; i < parameters.length; i++) {
				pstmt.setObject(i + 1, parameters[i]);
			}
			return pstmt.executeUpdate();
		} catch (Exception e) {
			LOGGER.error("数据库更新失败,sql:" + sql, e);
		} finally {
			release(conn, pstmt, null, null);
		}
		return 0;
	}
	
	/**
	 * 执行一条插入 语句，并且返回自增ID
	 * @param sql
	 * @param parameters
	 * @return
	 */
	public static int inertReturnGeneratedKey(String sql, Object... parameters) {

		PreparedStatement pstmt = null;
		Connection conn = null;
		ResultSet rs = null;
		try {
			conn = PooledConnectionUtil.getConnection("Default");
			pstmt = conn.prepareStatement(sql,Statement.RETURN_GENERATED_KEYS);
			for (int i = 0; i < parameters.length; i++) {
				pstmt.setObject(i + 1, parameters[i]);
			}
			pstmt.executeUpdate();
			rs = pstmt.getGeneratedKeys();
			if(rs.next()){
				return rs.getInt(1);
			}
			return 0;
		} catch (Exception e) {
			LOGGER.error("数据库更新失败,sql:" + sql, e);
		} finally {
			release(conn, pstmt, rs, null);
		}
		return 0;
	}

	public static void batchUpdate(String sql, List<Object[]> parameters) {
		PreparedStatement pstmt = null;
		Connection conn = null;
		try {
			conn = PooledConnectionUtil.getConnection("Default");
			pstmt = conn.prepareStatement(sql);
			for (Object[] parameterArray : parameters) {
				for (int i = 0; i < parameterArray.length; i++) {
					pstmt.setObject(i + 1, parameterArray[i]);
				}
				pstmt.addBatch();
			}
			pstmt.executeBatch();
		} catch (Exception e) {
			LOGGER.error("批处理更新失败,sql:" + sql, e);
		} finally {
			release(conn, pstmt, null, null);
		}
	}

	private static void release(Connection conn, PreparedStatement pstmt, ResultSet rs, RowMapper<?> mapper) {

		PooledConnectionUtil.close(conn, pstmt, rs);
		if(mapper != null){
			try {
				mapper.release();
			} catch (Exception e) {
				LOGGER.error("Mapper释放出错。", e);
			}
		}
	}
}
