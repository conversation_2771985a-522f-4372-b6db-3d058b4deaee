package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.ReqBackRoleQueryNameMessage;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.BackProtos;
import com.sh.game.system.user.NameManager;
import com.sh.server.AbstractHandler;

/**
 * <p>请求玩家信息</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_AUTH)
public class ReqBackRoleQueryNameHandler extends AbstractHandler<ReqBackRoleQueryNameMessage> {

    @Override
    public void doAction(ReqBackRoleQueryNameMessage msg) {
//        BackProtos.ReqBackRoleQueryName proto = msg.getProto();
//        long uid = NameManager.getInstance().getRidByName(proto.getName());
//        BackManager.getInstance().queryRole(msg.getSequence(), msg.getSession(), uid);
    }

}
