package com.sh.game.back.msg;

import com.sh.game.common.communication.msg.system.back.ReqRechargeDeliverMessage;
import com.sh.game.protos.BackProtos;
import com.sh.game.system.recharge.RechargeManager;
import com.sh.server.AbstractHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>ping消息</p>
 * <p>Created by MessageUtil</p>
 *
 * @date 2019-01-06 13:01:17
 */
@Slf4j
public class ReqRechargeDeliverHandler extends AbstractHandler<ReqRechargeDeliverMessage> {

    @Override
    public void doAction(ReqRechargeDeliverMessage msg) {
        BackProtos.ReqRechargeDeliver proto = msg.getProto();
        RechargeManager.getInstance().rechargeDeliver(proto.getOrderId(), proto.getThirdOrderId(), proto.getCount(), proto.getProductId(), proto.getSuccessTime(), proto.getSource());
    }

}
