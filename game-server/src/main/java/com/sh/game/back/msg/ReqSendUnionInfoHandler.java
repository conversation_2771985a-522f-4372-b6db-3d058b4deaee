package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.ReqSendUnionInfoMessage;
import com.sh.game.protos.BackProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>通过帮会id查询帮会</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 13:01:17
 */
public class ReqSendUnionInfoHandler extends AbstractHandler<ReqSendUnionInfoMessage> {

    @Override
    public void doAction(ReqSendUnionInfoMessage msg) {
        BackProtos.ReqSendUnionInfo proto = msg.getProto();
        BackManager.getInstance().getUnionInfo(msg.getSession(), msg.getSequence(), proto.getUid());

    }

}
