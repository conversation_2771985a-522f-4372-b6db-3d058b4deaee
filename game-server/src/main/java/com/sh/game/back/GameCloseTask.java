package com.sh.game.back;

import com.sh.game.GameContext;
import com.sh.game.common.close.ICloseTask;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.ExecutorUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.DelayQueryThread;
import com.sh.game.remote.rpc.client.ModuleClient;
import com.sh.game.server.GameNetworkEventListener;
import com.sh.game.server.SessionManager;
import com.sh.game.server.SessionUtil;
import com.sh.game.system.user.command.LogoutCommand;
import com.sh.server.Session;
import io.netty.channel.ChannelFuture;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

@Slf4j
public enum GameCloseTask implements ICloseTask {
    CLOSE_EVENT("发送停服事件") {
        @Override
        public void run() {
            GameContext.setServerCloseLogicExecuted(true);
            // send close event

            // notice remote to close

            GameContext.setClosed(true);
        }
    },

    CLOSE_CONNECTIONS("断开所有连接") {
        @Override
        public void run() {
            Session[] sessions = SessionManager.getInstance().sessions();
            for (Session session : sessions) {
                ChannelFuture future = session.close();
                try {
                    future.get(1000, TimeUnit.MILLISECONDS);
                } catch (Exception e) {
                    log.error(session + ",停服关闭连接失败");
                }
                GameNetworkEventListener.closeSession(session, LogoutCommand.Reason.SERVER_CLOSE);
            }
        }
    },

    CLOSE_CONNECTIONS_CHECK("检查所有连接") {
        @Override
        public void run() {
            Session[] sessions = SessionManager.getInstance().sessions();
            long waitTime = 5000;//5秒
            for (Session session : sessions) {
                if (session == null || SessionUtil.getRole(session) == null) {
                    continue;
                }
                Role role = SessionUtil.getRole(session);
                while(true) {
                    boolean handleCompleted = role.getMemory().isHandleCompletedByServerClose();
                    if(!handleCompleted && waitTime > 0) {
                        try {
                            Thread.sleep(1000);
                            waitTime -= 1000;
                        } catch (InterruptedException e) {
                            log.error("", e);
                        }
                    } else {
                        break;
                    }
                }
            }
        }
    },

    CLOSE_REMOTE_SERVER_CONNECTIONS("断开与远程服务器的连接") {
        @Override
        public void run() {
            ModuleClient client = GameContext.getGameServer().getSceneModule().getClient();
            client.stop();
        }
    },

    CLOSE_LOGIC_THREAD("关闭业务线程") {
        @Override
        public void run() {
            try {
                ExecutorUtil.COMMON_LOGIC_EXECUTOR.shutdown();
                int total = 5;
                // 允许两秒的时间执行当前已经执行的任务
                while (total > 0 && !ExecutorUtil.COMMON_LOGIC_EXECUTOR.awaitTermination(1, TimeUnit.SECONDS)) {
                    log.info("正在关闭业务逻辑线程....");
                    total -= 1;
                }
            } catch (InterruptedException e) {
                log.error("", e);
            }
        }
    },

    CLOSE_EVENT_THREAD("关闭派发线程") {
        @Override
        public void run() {
            try {
                int total = 10;
                ExecutorUtil.EVENT_DISPATCHER_EXECUTOR.shutdown();
                while (total > 0 && !ExecutorUtil.EVENT_DISPATCHER_EXECUTOR.awaitTermination(1, TimeUnit.SECONDS)) {
                    log.info("正在关闭场景事件派发线程...");
                    total--;
                }
            } catch (InterruptedException e) {
                log.error("", e);
            }
        }
    },

    CLOSE_AUTH_THREAD("关闭登录线程") {
        @Override
        public void run() {
            // 等待登录线程中的命令执行完毕
            int totalTime = 60000;
            while (totalTime > 0 && !ExecutorUtil.SERVER_AUTH_EXECUTOR.getQueue().isEmpty()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("", e);
                }
                totalTime -= 100;
            }
            // 关闭登录线程
            try {
                ExecutorUtil.SERVER_AUTH_EXECUTOR.shutdown();
                int total = 10;
                while (total > 0 && !ExecutorUtil.SERVER_AUTH_EXECUTOR.awaitTermination(1, TimeUnit.SECONDS)) {
                    log.info("正在关闭登录线程...");
                    total--;
                }
            } catch (InterruptedException e) {
                log.error("", e);
            }
        }
    },

    CLOSE_COMMON_THREAD("关闭公共线程") {
        @Override
        public void run() {
            int totalTime = 60000;
            while (totalTime > 0 && !ExecutorUtil.SERVER_COMMON_EXECUTOR.getQueue().isEmpty()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("", e);
                }
                totalTime -= 100;
            }
            // 关闭登录线程
            try {
                ExecutorUtil.SERVER_COMMON_EXECUTOR.shutdown();
                int total = 10;
                while (total > 0 && !ExecutorUtil.SERVER_COMMON_EXECUTOR.awaitTermination(1, TimeUnit.SECONDS)) {
                    log.info("正在关闭登录线程...");
                    total--;
                }
            } catch (InterruptedException e) {
                log.error("", e);
            }
        }
    },

    CLOSE_SOCIAL_THREAD("关闭社交线程") {
        @Override
        public void run() {
            int totalTime = 60000;
            while (totalTime > 0 && !ExecutorUtil.SERVER_SOCIAL_EXECUTOR.getQueue().isEmpty()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("", e);
                }
                totalTime -= 100;
            }
            // 关闭登录线程
            try {
                ExecutorUtil.SERVER_SOCIAL_EXECUTOR.shutdown();
                int total = 10;
                while (total > 0 && !ExecutorUtil.SERVER_SOCIAL_EXECUTOR.awaitTermination(1, TimeUnit.SECONDS)) {
                    log.info("正在关闭登录线程...");
                    total--;
                }
            } catch (InterruptedException e) {
                log.error("", e);
            }
        }
    },

    CLOSE_PLAYER_THREAD("关闭工作线程") {
        @Override
        public void run() {
            int totalTime = 60000;
            while (totalTime > 0 && !ExecutorUtil.PLAYER_DRIVER_EXECUTOR.getQueue().isEmpty()) {
                try {
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    log.error("", e);
                }
                totalTime -= 100;
            }
            // 关闭登录线程
            try {
                ExecutorUtil.PLAYER_DRIVER_EXECUTOR.shutdown();
                int total = 10;
                while (total > 0 && !ExecutorUtil.PLAYER_DRIVER_EXECUTOR.awaitTermination(1, TimeUnit.SECONDS)) {
                    log.info("正在关闭登录线程...");
                    total--;
                }
            } catch (InterruptedException e) {
                log.error("", e);
            }
        }
    },

    SAVE_ROLE_DATA("保存所有数据") {
        @Override
        public void run() {
            DataCenter.store();

            try {
                DelayQueryThread.stop();
            } catch (InterruptedException e) {
                log.error("", e);
            }
        }
    },

    CLOSE_NETWORK("关闭网络服务") {
        @Override
        public void run() {
            GameContext.getGameServer().stop();
        }
    },

    GOODBYE_BACK_SERVER("goodbye") {
        @Override
        public void run() {

        }

        @Override
        public int code() {
            return 0;
        }
    },

    CLOSE_BACK_SERVER("关闭后台服务") {
        @Override
        public void run() {
            GameContext.getBackServer().stop();
        }

        @Override
        public int code() {
            return -1;
        }
    },




    ;

    private final String name;

    GameCloseTask(String name) {
        this.name = name;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public int code() {
        return 1;
    }
}
