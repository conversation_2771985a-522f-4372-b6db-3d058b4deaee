package com.sh.game.back.stat;

import com.sh.client.Client;
import com.sh.game.common.communication.msg.system.back.ReqBackLoginMessage;
import com.sh.game.protos.BackProtos;
import com.sh.net.Message;
import lombok.extern.slf4j.Slf4j;

/**
 * 把今天最好的表现当作明天最新的起点．．～
 * いま 最高の表現 として 明日最新の始発．．～
 * Today the best performance  as tomorrow newest starter!
 * Created by IntelliJ IDEA.
 * <p>
 * author: xiaomo
 * github: https://github.com/xiaomoinfo
 * email : <EMAIL>
 * QQ    : 83387856
 * Date  : 2018/2/2 13:01
 * desc  :
 * Copyright(©) 2017 by xiaomo.
 *
 * <AUTHOR>
 */
@Slf4j
public class StatUtil {

    static boolean login(Client client, String sign) {
        ReqBackLoginMessage msg = new ReqBackLoginMessage();
        msg.setProto(BackProtos.ReqBackLogin.newBuilder()
                .setLoginName(sign)
                .build());
        Message ret = client.sendSyncMsg(msg, 5_000);
        if (ret == null) {
            log.info("登录后台服务器失败...");
            return false;
        }
        log.info("登录成功后台服务器....");
        return true;

    }

    static boolean waitInit(Client client) throws InterruptedException {
        int time = 3000;
        while (time > 0) {
            if (client.getChannel(Thread.currentThread().getId()) != null) {
                break;
            }
            System.out.println("连接未初始化，100ms后重新检查...");
            Thread.sleep(100);
            time -= 100;
        }

        if (client.getChannel(Thread.currentThread().getId()) == null) {
            System.out.println("3秒之内找不到可用连接，执行失败...");
            return false;
        }

        return true;


    }


}
