package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.ReqOutsideRankMessage;
import com.sh.game.protos.BackProtos;
import com.sh.server.AbstractHandler;

/**
 * <p>将玩家移出排行榜</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
public class ReqOutsideRankHandler extends AbstractHandler<ReqOutsideRankMessage> {

    @Override
    public void doAction(ReqOutsideRankMessage msg) {
//        BackProtos.ReqOutsideRank proto = msg.getProto();
//        BackManager.getInstance().reqOutsideRank(msg.getSession(), msg.getSequence(), proto.getRid(), proto.getState());
    }

}
