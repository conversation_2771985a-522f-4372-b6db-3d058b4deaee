package com.sh.game.back.bugfix;

import com.google.common.collect.Sets;
import com.sh.common.jdbc.ProtostuffRowMapper;
import com.sh.common.jdbc.RowMapper;
import com.sh.common.jdbc.SerializerUtil;
import com.sh.game.back.bugfix.util.ConnectionUtil;
import com.sh.game.back.bugfix.util.DBUtil;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.entity.RoleCount;

import java.sql.Connection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
 * @Date : 2019/7/30
 * @Desc : to do anything
 */
public class RoleRevertBack {

    public static void main(String[] args) {

        Set<Long> reverRoleIds = Sets.newHashSet(1307042249792006L,
                1307042325720269L,
                1307042987126220L,
                1307043004503289L,
                1307043485845105L,
                1377414044095224L);

        String user = "root";
        String password = "ZmE2NTE0NGzlzz2018.";

        String fromUrl = "***************************************************************************";

        String toUrl = "***************************************************************************";

        Connection fromConnection = ConnectionUtil.getConnection(fromUrl, user, password);

        Connection toConnection = ConnectionUtil.getConnection(toUrl, user, password);


        for (Long reverRoleId : reverRoleIds) {
            revertBag(fromConnection, toConnection, reverRoleId);
            revertRole(fromConnection, toConnection, reverRoleId);
            revertMail(fromConnection, toConnection, reverRoleId);
            revertCount(fromConnection, toConnection, reverRoleId);
            revertSummary(fromConnection, toConnection, reverRoleId);
            System.out.println("玩家 " + reverRoleId + " 回档完成");
        }
    }

    public static void revertSummary(Connection fromConnection, Connection toConnection, long rid) {
        String sqlQuery = "select * from p_summary where id = ?";
        Map<String, Object> summaryList = DBUtil.query(fromConnection, sqlQuery, new RowMapper.MapRowMapper(), rid);

        String sqlUpdate = "update p_summary set " +
                "level = ?, " +
                "reinLevel = ?, " +
                "loginTime = ?, " +
                "offlineTime = ?, " +
                "weaponId = ?," +
                "clothId = ?, " +
                "vipLevel = ?, " +
                "fakeVipLevel = ?, " +
                "wing = ?, " +
                "wingFightPower = ?, " +
                "fashionCloth = ?, " +
                "fashionTitle = ?, " +
                "fashionWeapon = ?, " +
                "fashionWing = ?, " +
                "treasureId = ?, " +
                "szSuitId = ?, " +
                "junxian = ?, " +
                "honor = ?, " +
                "weiwang = ?, " +
                "weimingLevel = ?, " +
                "monthCard = ?, " +
                "showVipLevel = ?, " +
                "contributionHistory = ?, " +
                "vipExp = ?, " +
                "banUser = ?, " +
                "huanShou = ?, " +
                "cut = ?, " +
                "chenDun = ?, " +
                "lingQi = ?, " +
                "jianXin = ?, " +
                "shenFu = ?, " +
                "tianLing = ? " +
                "where id = ? ";
        DBUtil.update(toConnection, sqlUpdate,
                summaryList.get("level"),
                summaryList.get("reinLevel"),
                summaryList.get("loginTime"),
                summaryList.get("offlineTime"),
                summaryList.get("weaponId"),
                summaryList.get("clothId"),
                summaryList.get("vipLevel"),
                summaryList.get("fakeVipLevel"),
                summaryList.get("wing"),
                summaryList.get("wingFightPower"),
                summaryList.get("fashionCloth"),
                summaryList.get("fashionTitle"),
                summaryList.get("fashionWeapon"),
                summaryList.get("fashionWing"),
                summaryList.get("treasureId"),
                summaryList.get("szSuitId"),
                summaryList.get("junxian"),
                summaryList.get("honor"),
                summaryList.get("weiwang"),
                summaryList.get("weimingLevel"),
                summaryList.get("monthCard"),
                summaryList.get("showVipLevel"),
                summaryList.get("contributionHistory"),
                summaryList.get("vipExp"),
                summaryList.get("banUser"),
                summaryList.get("huanShou"),
                summaryList.get("cut"),
                summaryList.get("chenDun"),
                summaryList.get("lingQi"),
                summaryList.get("jianXin"),
                summaryList.get("shenFu"),
                summaryList.get("tianLing"),
                summaryList.get("id")
        );
        System.out.println("玩家" + rid + "还原summary完毕..");
    }

    public static void revertCount(Connection fromConnection, Connection toConnection, long rid) {
        String sql_query = "select data from p_count where id = ?";
        RoleCount count = DBUtil.query(fromConnection, sql_query, new ProtostuffRowMapper<>(RoleCount.class), rid);
        if(count == null) {
            System.out.println("找不到RoleCount：" + rid);
            return;
        }

        byte[] bytes = SerializerUtil.encode(count, RoleCount.class);

        String sql_update = "update p_count set data = ? where id = ?";
        DBUtil.update(toConnection, sql_update, bytes, rid);
        System.out.println("玩家" + rid + "还原count完毕..");
    }

    private static void revertMail(Connection fromConnection, Connection toConnection, long rid) {
        String sqlDelete = "delete from p_mail where uid = ?";
        DBUtil.update(toConnection, sqlDelete, rid);

        String sqlQuery = "select * from p_mail where uid = ?";
        List<Map<String, Object>> mailList = DBUtil.queryList(fromConnection, sqlQuery, new RowMapper.MapRowMapper(), rid);

        String sql_insert = "insert into p_mail (" +
                "id, " +
                "uid, " +
                "sender, " +
                "title, " +
                "content, " +
                "items, " +
                "time, " +
                "state) values (?,?,?,?,?,?,?,?)";
        for (Map<String, Object> mail : mailList) {
            DBUtil.update(toConnection, sql_insert,
                    mail.get("id"),
                    mail.get("uid"),
                    mail.get("sender"),
                    mail.get("title"),
                    mail.get("content"),
                    mail.get("items"),
                    mail.get("time"),
                    mail.get("state"));
        }
        System.out.println("玩家:" + rid + " 还原mail完毕..");
    }

    private static void revertRole(Connection fromConnection, Connection toConnection, long rid) {
        String sqlQuery = "select data from p_role where id = ?";
        Role role = DBUtil.query(fromConnection, sqlQuery, new ProtostuffRowMapper<>(Role.class), rid);
        if(role == null) {
            System.out.println("找不到role：" + rid);
            return;
        }

        byte[] bytes = SerializerUtil.encode(role, Role.class);

        String sql_update = "update p_role set data = ? where id = ?";
        DBUtil.update(toConnection, sql_update, bytes, rid);
        System.out.println("玩家:" + rid + " 还原role完毕..");
    }

    public static void revertBag(Connection fromConnection, Connection toConnection, long rid) {
//        String sqlQuery = "select data from p_bag where id = ?";
//        RoleBag bag = DBUtil.query(fromConnection, sqlQuery, new ProtostuffRowMapper<>(RoleBag.class), rid);
//        if (bag == null) {
//            System.out.println("找不到bag：" + rid);
//            return;
//        }
//
//        byte[] bytes = SerializerUtil.encode(bag, RoleBag.class);
//
//        String sql_update = "update p_bag set data = ? where id = ?";
//        DBUtil.update(toConnection, sql_update, bytes, rid);
//        System.out.println("玩家:" + rid + " 还原bag完毕..");
    }
}
