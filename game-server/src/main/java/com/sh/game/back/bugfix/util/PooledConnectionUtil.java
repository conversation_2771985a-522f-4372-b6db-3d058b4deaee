package com.sh.game.back.bugfix.util;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import javax.sql.DataSource;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 基于连接池的连接管理工具
 * <AUTHOR>
 *
 */
public class PooledConnectionUtil {

	
	public static final Map<String, DataSource> sources = new HashMap<>();

	public static void init(String configFile) {
		
		InputStream in = null;
		try {
			in = new FileInputStream(configFile);
			Properties originalProperties = new Properties();
			originalProperties.load(in);
			HikariConfig config = new HikariConfig(originalProperties);
			DataSource orginalSource = new HikariDataSource(config);
			sources.put("Default", orginalSource);
			
		} catch (Exception e) {
			throw new RuntimeException("数据库连接池初始化错误.", e);
		}finally{
			if(in != null){
				try {
					in.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
			
		}
	}

	public static Connection getConnection(String dbName) {
		try {
			return sources.get(dbName).getConnection();
		} catch (SQLException e) {
			e.printStackTrace();
			return null;
		}
	}

	public static void close(Connection conn, PreparedStatement pstmt,
			ResultSet rs) {
		if (rs != null) {
			try {
					rs.close();
			} catch (Throwable e) {
				e.printStackTrace();
			}
		}
		if (pstmt != null) {
			try {
					pstmt.close();
			} catch (Throwable e) {
				e.printStackTrace();
			}
		}

		if (conn != null) {
			try {
					conn.close();
			} catch (Throwable e) {
				e.printStackTrace();
			}
		}
	}
}
