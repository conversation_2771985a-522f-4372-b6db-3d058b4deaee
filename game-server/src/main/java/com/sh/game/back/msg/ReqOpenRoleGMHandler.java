package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.BackProtos;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.back.ReqOpenRoleGMMessage;

/**
 * <p>请求开启角色GM功能</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(1)
public class ReqOpenRoleGMHandler extends AbstractHandler<ReqOpenRoleGMMessage> {

	@Override
	public void doAction(ReqOpenRoleGMMessage msg) {
//		BackProtos.ReqOpenRoleGM proto = msg.getProto();
//		BackManager.getInstance().openRoleGM(msg.getSequence(), msg.getSession(), proto.getRoleId(), proto.getRoleName(), proto.getState());
	}

}
