package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.constant.ProcessorId;
import com.sh.game.common.msg.MessageHandler;
import com.sh.game.protos.BackProtos;
import com.sh.server.AbstractHandler;
import com.sh.game.common.communication.msg.system.back.ReqRoleFightPowerMessage;

/**
 * <p>请求玩家战力</p>
 * <p>Created by MessageUtil</p>
 * <p>该类是自动生成的，不允许手动修改</p>
 */
@MessageHandler(ProcessorId.SERVER_AUTH)
public class ReqRoleFightPowerHandler extends AbstractHandler<ReqRoleFightPowerMessage> {

    @Override
    public void doAction(ReqRoleFightPowerMessage msg) {
//        BackProtos.ReqRoleFightPower proto = msg.getProto();
//        BackManager.getInstance().roleFightPower(msg.getSequence(), msg.getSession(), proto.getRid());
    }

}
