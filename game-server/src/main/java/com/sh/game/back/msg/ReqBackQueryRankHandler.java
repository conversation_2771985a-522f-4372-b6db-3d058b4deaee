package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.rank.ReqLookRankMessage;
import com.sh.game.protos.RankProtos;
import com.sh.server.AbstractHandler;

/**
 * 查询排行
 */
public class ReqBackQueryRankHandler extends AbstractHandler<ReqLookRankMessage> {

    @Override
    public void doAction(ReqLookRankMessage msg) {
//        RankProtos.ReqLookRank reqLookRank = msg.getProto();
//        BackManager.getInstance().queryRank(msg.getSession(), msg.getSequence(), reqLookRank.getType());

    }

}
