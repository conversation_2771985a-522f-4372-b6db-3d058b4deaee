package com.sh.game.back.msg;

import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.ReqResetCrossBossMessage;
import com.sh.server.AbstractHandler;

/**
 * <p>请求重置跨服boss信息</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 13:01:17
 */
public class ReqResetCrossBossHandler extends AbstractHandler<ReqResetCrossBossMessage> {

    @Override
    public void doAction(ReqResetCrossBossMessage msg) {
        BackManager.getInstance().resetRemoteHost(msg.getSequence(), msg.getSession());

    }

}
