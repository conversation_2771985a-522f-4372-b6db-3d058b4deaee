//package com.sh.game.back.stat;
//
//import com.sh.client.Client;
//import com.sh.common.jdbc.RowMapper;
//import com.sh.common.jdbc.SerializerUtil;
//import com.sh.game.back.bugfix.util.ConnectionUtil;
//import com.sh.game.back.bugfix.util.DBUtil;
//import com.sh.game.common.constant.DataType;
//import com.sh.game.common.entity.usr.Role;
//import com.sh.game.cfg.msg.system.back.ReqFetchMemoryMessage;
//import com.sh.game.cfg.msg.system.back.ResFetchMemoryMessage;
//import javafx.util.Pair;
//
//import java.io.IOException;
//import java.sql.Connection;
//import java.util.List;
//import java.util.Map;
//
///**
// * <AUTHOR> zhanyy  ʕ·͡ˑ·ཻʔෆʕ•̫͡•ོʔ
// * @Date : 2019/11/17
// * @Desc : to do anything
// */
//public class CenterGameClient {
//    public static void main(String[] args) throws Exception {
//        //腾讯
//      /*  String txFromUrl = "********************************************************";
//        String txFromUser = "zztxadmin";
//        String txFromPassword = "MjI3ZjllZDIxM2NjMTli";
//        Connection centerConnection = ConnectionUtil.getConnection(txFromUrl, txFromUser, txFromPassword);*/
//
//        //非腾讯
//        String fromUrl = "jdbc:mysql://*************:3306/zlzz_gm?characterEncoding=UTF8";
//        String fromUser = "ops_user";
//        String fromPassword = "RXLY7wx8rFGsd0dt";
//        Connection centerConnection = ConnectionUtil.getConnection(fromUrl, fromUser, fromPassword);
//        String gameQuerySql = "select oper, serverid, client_serverip, base_port from admin_game_info where merge_toid = 0 and oper <> \"ad_kuafu\"  and oper <> \"ad_pvp\"";
//        List<Map<String, Object>> serverList = DBUtil.queryList(centerConnection, gameQuerySql, new RowMapper.MapRowMapper());
//        for (Map<String, Object> map : serverList) {
//            boolean success = execute(getHostAndPort((String) map.get("client_serverip"), (int) map.get("base_port") + 1));
//            if(success) {
//                System.out.println("oper:" + map.get("oper") + " serverid:" + map.get("serverid"));
//                break;
//            }
//        }
//    }
//
//    static boolean execute(Pair<String, Integer> pair) throws IOException {
//        Client client = null;
//        try {
//            client = ModifyMemoryClient.getClient(pair.getKey(), pair.getValue());
//            return executeModify(client);
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            client.stopQuickly();
////            System.exit(0);
//        }
//        return false;
//    }
//
//    static boolean executeModify(Client client) throws Exception {
////        sendRoleModify(client, 40272284901519L);
////        sendUnionModify(client, 6407420415020388L, 6407421016944099L);
////        executeCmd(client, " test2");
//        return queryRole(client, 36580938090162100L);
////        queryRoleBag(client, 1271857263199232L);
////        sendRoleModify(client, 1271857263199232L);
////        sendRoleBagModify(client, 40282988782104L);
////        sendSysDataModify(client);
//    }
//
//    private static boolean queryRole(Client client, long roleId) {
//        ReqFetchMemoryMessage fetchReq = new ReqFetchMemoryMessage();
//        fetchReq.setMid(roleId);
//        fetchReq.setTableType(DataType.ROLE);
//        ResFetchMemoryMessage res = (ResFetchMemoryMessage) client.sendSyncMsg(fetchReq, 5000);
//
//        byte[] bytes = res.getByteArray();
//        if (bytes == null || bytes.length == 0) {
//            System.out.println("数据为空...");
//            return false;
//        } else {
//            //这里处理具体逻辑，自己请求的什么数据，就怎么解析
//            Role role = SerializerUtil.decode(bytes, Role.class);
//            System.out.println(role.getBasic().getSid() + "#" + role.getId() + "#" + role.getBasic().getName());
//            return true;
//        }
//    }
//
//    static Pair<String, Integer> getHostAndPort(String ip, int port) {
//        return new Pair<>(ip, port);
//    }
//}
