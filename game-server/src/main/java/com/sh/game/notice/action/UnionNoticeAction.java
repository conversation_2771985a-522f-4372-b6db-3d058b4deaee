package com.sh.game.notice.action;

import com.sh.common.config.ConfigDataManager;
import com.sh.game.common.communication.msg.system.union.ResUnionBossRankingChangeMessage;
import com.sh.game.common.communication.notice.*;
import com.sh.game.common.config.model.MapConfig;
import com.sh.game.common.constant.ChatConst;
import com.sh.game.common.entity.Union;
import com.sh.game.common.entity.backpack.item.Item;
import com.sh.game.common.entity.sys.OtherData;
import com.sh.game.common.entity.usr.Role;
import com.sh.game.common.util.MessageUtil;
import com.sh.game.data.DataCenter;
import com.sh.game.data.SysDataProvider;
import com.sh.game.notice.NoticeAction;
import com.sh.game.protos.UnionProtos;
import com.sh.game.system.announce.AnnounceManager;
import com.sh.game.system.auction.AuctionManager;
import com.sh.game.system.union.UnionIntensifyManager;
import com.sh.game.system.union.UnionLevelManager;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * UnionNoticeAction
 *
 * <AUTHOR>
 * @date 2020/8/18 16:17
 */
@NoticeAction
@Slf4j
public class UnionNoticeAction {


    public void UnionDonateNotice(UnionDonateNotice notice) {
        UnionLevelManager.getInstance().addFund(notice.getUnionId(), notice.getFund(), notice.getRid());
    }

    public void UnionIntensifyDeductNotice(UnionIntensifyDeductNotice notice) {
        Role role = DataCenter.getRole(notice.getRid());
        if (role == null) {
            return;
        }

        UnionIntensifyManager.getInstance().intensifyDeduct(role, notice.getCosts(), notice.getUnionId());
    }

    public void UnionIntensifyDeductRetNotice(UnionIntensifyDeductRetNotice notice) {
        UnionIntensifyManager.getInstance().intensifyResult(notice.getUnionId(), notice.isSuccess());
    }

    public void AppendAuctionNotice(AppendAuctionNotice notice) {

    }

    public void UnionBossRankingSyncNotice(UnionBossRankingSyncNotice notice) {
        if (notice.getRankings() == null) {
            return;
        }
        OtherData data = SysDataProvider.get(OtherData.class);
        data.setUnionBossRanking(notice.getRankings());
        DataCenter.updateData(data);

        //拍卖
        Map<Long, List<Item>> unionAuctionMap = notice.getUnionAuctionMap();
        Map<Long, Map<Long, Long>> unionScoreMap = notice.getUnionScoreMap();
        if (unionAuctionMap != null && unionScoreMap != null) {
            for (Map.Entry<Long, List<Item>> longListEntry : unionAuctionMap.entrySet()) {
                long unionId = longListEntry.getKey();
                List<Item> value = longListEntry.getValue();
                Map<Long, Long> scoreMap = unionScoreMap.getOrDefault(unionId, new HashMap<>());
                if (!value.isEmpty() && !scoreMap.isEmpty()) {
                    Union union = DataCenter.get(Union.class,unionId);
                    if (union != null) {
                        union.setAuctionItems(value);
                        union.setAuctionScoreMap(scoreMap);
                        AuctionManager.getInstance().reqUnionAuction(union, scoreMap, 2);
                    }
                }
            }
        }

        ResUnionBossRankingChangeMessage msg;
        for (Union union : DataCenter.getAllUnion()) {
            msg = new ResUnionBossRankingChangeMessage();
            int rank = data.getUnionBossRanking().getOrDefault(union.getId(), 0);
            union.setBossRanking(rank);
            DataCenter.updateData(union);
            if (rank > 0 && rank <= 3) {
                MapConfig mapConfig = ConfigDataManager.getInstance().getById(MapConfig.class, 1704 - rank);
                String mapName = mapConfig != null ? mapConfig.getName() : "";
//                union.getMemberInfos().keySet().forEach(key -> MailManager.getInstance().sendMail(key, 4005, null, rank, mapName));
                if (rank == 1) {
                    AnnounceManager.getInstance().post(ChatConst.AnnounceId.UNION_BOSS_WINNER, 0, union);
                }
            }
            msg.setProto(UnionProtos.ResUnionBossRankingChange.newBuilder()
                    .setBossRanking(union.getBossRanking())
                    .build());
            MessageUtil.sendToUnion(union, msg);
        }
    }

    /**
     * 处理竞拍返还
     *
     * @param dealAuctionTimeOutNotice
     */
    public void dealAuctionTimeOutNotice(DealAuctionTimeOutNotice dealAuctionTimeOutNotice) {
        AuctionManager.getInstance().dealTimeOutAuction();
    }
}
