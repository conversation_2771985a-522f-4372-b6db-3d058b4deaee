package com.sh.game.common.entity.usr;

import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/8/13 13:48
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleBarrierGd extends AbstractRoleEntity {

    /**
     * 角色id
     */
    @Tag(1)
    private long id;

    /**
     * 当前关卡 id （配置表id）
     */
    @Tag(2)
    private int curBarrierId;

    /**
     * 已通过关卡列表
     */
    @Tag(3)
    private Set<Integer> completeBarriers = new HashSet<>();

    /**
     * 未领取奖励得关卡列表
     */
    @Tag(4)
    private Set<Integer> hasRewardBarriers = new HashSet<>();
}
