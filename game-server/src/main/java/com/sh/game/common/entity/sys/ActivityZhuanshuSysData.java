package com.sh.game.common.entity.sys;

import com.sh.game.common.entity.SysDataType;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 专属首爆公共数据
 *
 * <AUTHOR> <PERSON>
 * @Email <EMAIL>
 * @since 2021-09-01
 **/
@Setter
@Getter
public class ActivityZhuanshuSysData extends AbstractSysData {

    @Override
    public void setId(long id) {

    }

    @Override
    public long getId() {
        return SysDataType.ZHUANSHU_ACTIVITY;
    }

    /**
     * 专属首爆数量
     * key: cfgId
     * value: count
     *
     * @see com.sh.game.common.config.model.EquipZhuanshuConfig
     */
    @Tag(1)
    private Map<Integer, Integer> residueCount = new ConcurrentHashMap<>();


    /**
     * Boss首杀数量
     *
     * @see com.sh.game.common.config.model.ActivityFirstKillMonsterConfig
     * key: 区域类型
     * value: [key: cfgId  value: 达成首杀的总人数]
     */
    @Tag(2)
    private Map<Integer, Map<Integer, Integer>> bossFirstKillerCount = new ConcurrentHashMap<>();

    /**
     * 首杀者昵称
     */
    @Tag(3)
    private Map<Integer, String> firstPlayerNickname = new ConcurrentHashMap<>();
}