package com.sh.game.common.util;

import com.sh.game.GameContext;
import org.lionsoul.ip2region.xdb.Searcher;

import java.io.IOException;

/**
 * @version 1.0 2024-12-10 14:08:00
 */
public class SearcherIPUtils {
    static String dbFile;

    static {
        dbFile = GameContext.getOption().getConfigDataPath() + "ip2region.xdb";
        //System.out.println(dbFile);
    }

    public static String getCachePosition(String ip) {
        return SearcherIPUtils.getCachePosition(dbFile, ip, true);
    }

    public static String getPosition(String dbPath, String ip, boolean format) {
        // 1、create searcher object
        Searcher searcher = null;
        try {
            searcher = Searcher.newWithFileOnly(dbPath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 2、query
        try {
            String region = searcher.search(ip);
            if (format) {
                return region;
            }
            String[] split = region.split("\\|");
            String s = split[0] + split[2] + split[3];
            return s;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    /**
     * @Description :
     * <AUTHOR> mabo
     */
    public static String getIndexCachePosition(String dbPath, String ip, boolean format) {
        Searcher searcher = null;
        byte[] vIndex;
        try {
            vIndex = Searcher.loadVectorIndexFromFile(dbPath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            searcher = Searcher.newWithVectorIndex(dbPath, vIndex);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            String region = searcher.search(ip);
            if (format) {
                return region;
            }
            String[] split = region.split("\\|");
            String s = split[0] + split[2] + split[3];
            return s;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @Description :
     * <AUTHOR> mabo
     */
    public static String getCachePosition(String dbPath, String ip, boolean format) {
        byte[] cBuff;
        try {
            cBuff = Searcher.loadContentFromFile(dbPath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        Searcher searcher;
        try {
            searcher = Searcher.newWithBuffer(cBuff);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            String region = searcher.search(ip);
            if (format) {
                return region;
            }
            String[] split = region.split("\\|");
            String s = split[0] + split[2] + split[3];
            return s;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }
}
