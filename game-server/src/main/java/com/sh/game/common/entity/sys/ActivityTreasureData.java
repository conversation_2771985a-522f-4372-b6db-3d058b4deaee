package com.sh.game.common.entity.sys;

import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class ActivityTreasureData extends AbstractSysData {

    @Tag(1)
    private long id;

    @Tag(2)
    private Map<Integer, Integer> treasureMonsterTotalTimeMap = new HashMap<>();

    @Override
    public long getId() {
        return id;
    }

    @Override
    public void setId(long id) {
        this.id = id;
    }
}
