package com.sh.game.common.util.callback;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 异步回调工具类
 * <AUTHOR>
 * @date 2015-7-11 上午10:21:50
 *
 */
public class CallbackUtil {

	private static final ScheduledExecutorService executor = Executors.newScheduledThreadPool(2, new ThreadFactory() {
		
		AtomicInteger count = new AtomicInteger(0);
		@Override
		public Thread newThread(Runnable r) {
			int curCount = count.incrementAndGet();
			return new Thread(r, "异步回调（线程池）-" + curCount);
		}
	});
	
	public static void execute(Callbackable<?> command){
		InnerRunnable runnable = new InnerRunnable();
		runnable.setCommand(command);
		executor.execute(runnable);
	}
	
	private static class InnerRunnable implements Runnable{
		
		private Callbackable<?> command;

		@Override
		public void run() {
			command.doCommand();
		}

		public void setCommand(Callbackable<?> command) {
			this.command = command;
		}
	}
	
}
