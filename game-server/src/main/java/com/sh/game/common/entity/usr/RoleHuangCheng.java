package com.sh.game.common.entity.usr;

import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.huangcheng.entity.HuangChengLog;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-12-25
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleHuangCheng extends AbstractRoleEntity {

    @Tag(1)
    private long id;

    @Tag(2)
    private int myRank = 100;

    @Tag(3)
    private int lastUpdateSecond;

    @Tag(4)
    private List<HuangChengLog> logList = new ArrayList<>();

    @Tag(5)
    private int minRank = 100;
}
