package com.sh.game.common.entity.usr;

import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.activity.entity.role.ActivityZhanLingData;
import com.sh.game.system.task.entity.TaskRecord;
import com.sh.game.system.zhanling.bean.ActivityZhanLingTaskData;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色-战令
 *
 * <AUTHOR> Liao
 * @Email <EMAIL>
 * @since 2022-01-13
 **/
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleZhanLing extends AbstractRoleEntity {
    /**
     * rid
     */
    @Tag(1)
    private long id;

    /**
     * 战令信息
     * key:     活动id
     * value:   战令信息
     */
    @Tag(2)
    private Map<Integer, ActivityZhanLingData> zhanLingMap = new HashMap<>();

    /**
     * 战令任务信息
     * key:     活动id
     * value:   战令任务信息
     */
    @Tag(3)
    private Map<Integer, ActivityZhanLingTaskData> zhanLingTaskMap = new HashMap<>();
}
