package com.sh.game.common.entity.sys;

import com.sh.game.common.entity.SysDataType;
import com.sh.game.system.redpack.entity.RedPack;
import io.protostuff.Tag;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2024/12/9 11:16
 */
@Setter
@Getter
public class CaiShenMiaoData extends AbstractSysData {
    @Tag(1)
    private Map<Integer, List<CaiShenMiaoMemberData>> caiShen = new HashMap<>();
    @Tag(2)
    private Map<Long, RedPack> redPackMap = new HashMap<>();

    @Override
    public void setId(long id) {
    }

    @Override
    public long getId() {
        return SysDataType.CAI_SHEN_MIAO;
    }
}
