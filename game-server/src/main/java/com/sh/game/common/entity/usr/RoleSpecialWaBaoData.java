package com.sh.game.common.entity.usr;

import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.special.entity.RoleSpecialWaBao;
import io.protostuff.Tag;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2022/4/19 17:19
 */
@Getter
@Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleSpecialWaBaoData extends AbstractRoleEntity {

    @Tag(1)
    private long id;

    /**
     * 宝藏之地挖宝
     * key 活动id
     */
    @Tag(2)
    private Map<Integer, RoleSpecialWaBao> waBaoMap = new ConcurrentHashMap<>();
}
