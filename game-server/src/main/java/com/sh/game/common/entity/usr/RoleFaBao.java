package com.sh.game.common.entity.usr;

import com.sh.common.persist.Persistable;
import com.sh.game.common.persist.PersistDaoTemplate;
import com.sh.game.common.persist.dao.PersistSimpleDao;
import com.sh.game.system.fabao.entity.FaBao;
import io.protostuff.Tag;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 角色法宝
 *
 * <AUTHOR>
 */
@lombok.Getter
@lombok.Setter
@PersistDaoTemplate(PersistSimpleDao.class)
public class RoleFaBao extends AbstractRoleEntity implements Persistable {
    /**
     * 玩家rid
     */
    @Tag(1)
    private long id;

    @Tag(2)
    private Map<Long, FaBao> faBaos = new HashMap<>();

    @Tag(3)
    private List<Integer> faBaoHistory = new ArrayList<>();
}