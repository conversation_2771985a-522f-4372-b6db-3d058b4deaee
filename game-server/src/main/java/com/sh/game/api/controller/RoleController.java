package com.sh.game.api.controller;

import com.sh.client.Client;
import com.sh.game.api.GameClientUtil;
import com.sh.game.api.constant.MapKey;
import com.sh.game.api.entity.*;
import com.sh.game.api.util.ParamUtil;
import com.sh.game.api.util.RequestValidator;
import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.*;
import com.sh.game.common.communication.msg.system.rank.ReqLookRankMessage;
import com.sh.game.common.communication.msg.system.rank.ResLookRankMessage;
import com.sh.game.protos.BackProtos;
import com.sh.game.protos.RankProtos;
import com.sh.game.system.user.NameManager;
import com.sh.http.Request;
import com.sh.http.annotation.Controller;
import com.sh.http.annotation.Json;
import com.sh.http.annotation.Path;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@Slf4j
public class RoleController {

	@Path("/role/property/coin/query")
	@Json
	public Result currencyQuery(Request request) {
		Result result = new Result();
		if (!RequestValidator.validate(request, result, "rid")) {
			return result;
		}

		BackRolePropertyCurrencyQueryResult queryResult = BackManager.getInstance().rolePropertyCoinQuery(Long.parseLong(request.getParameter("rid")));
		if (!queryResult.isSuccess()) {
			result.setStatus(Result.FAILED);
			result.setData(queryResult.getMsg());
			log.info("执行失败");
			return result;
		}

		if (queryResult.getRid() == 0) {
			result.setStatus(Result.FAILED);
			result.setData("no such role");
		} else {
			result.setStatus(Result.FAILED);
			List<CoinBean> coinBeanList = new ArrayList<>();
			for (Map.Entry<Integer, Long> entry : queryResult.getCoin().entrySet()) {
				CoinBean bean = new CoinBean();
				bean.setItemId(entry.getKey());
				bean.setCount(entry.getValue());
				coinBeanList.add(bean);
			}
			result.setData(coinBeanList);
		}

		return result;
	}

	@Path("/role/property/coin/alter")
	@Json
	public Result currencyAlter(Request request) {
		Result result = new Result();
		if (!RequestValidator.validate(request, result, "rid", "coin", "count")) {
			return result;
		}
		MessageResult messageResult = BackManager.getInstance().rolePropertyCoinAlter(Long.parseLong(request.getParameter("rid"))
				, Integer.parseInt(request.getParameter("coin"))
				, Long.parseLong(request.getParameter("count")));
		if (!messageResult.isSuccess()) {
			result.setStatus(Result.FAILED);
			result.setData(messageResult.getMsg());
			log.info("执行失败");
			return result;
		}

		return result;
	}

	@Path("/role/property/item/query")
	@Json
	public Result itemQuery(Request request) {
		Result result = new Result();
		if (!RequestValidator.validate(request, result, "rid")) {
			return result;
		}

		BackRolePropertyItemQueryResult queryResult = BackManager.getInstance().rolePropertyItemQuery(Long.parseLong(request.getParameter("rid")));
		if (!queryResult.isSuccess()) {
			result.setStatus(Result.FAILED);
			result.setData(queryResult.getMsg());
			log.info("执行失败");
			return result;
		}

		if (queryResult.getRid() == 0) {
			result.setStatus(Result.FAILED);
			result.setData("no such role");
		} else {
			result.setData(queryResult.getStorageList());
		}


		return result;
	}

	@Path("/role/property/item/delete")
	@Json
	public Result itemDelete(Request request) {
		Result result = new Result();
		if (!RequestValidator.validate(request, result, "rid", "where", "index", "uid")) {
			return result;
		}
		MessageResult messageResult = BackManager.getInstance().rolePropertyItemDelete(Long.parseLong(request.getParameter("rid"))
				, Integer.parseInt(request.getParameter("where"))
				, Integer.parseInt(request.getParameter("index"))
				, Long.parseLong(request.getParameter("uid")));
		if (!messageResult.isSuccess()) {
			result.setStatus(Result.FAILED);
			result.setData(messageResult.getMsg());
			log.info("执行失败");
			return result;
		}
		result.setStatus(Result.SUCCESS);

		return result;
	}

	@Path("/role/property/item/queryByName")
	@Json
	public Result itemQueryByName(Request request) {
		Result result = new Result();
		if (!RequestValidator.validate(request, result, "name")) {
			return result;
		}
		String uName = request.getParameter("name");
		long rid = NameManager.getInstance().getRidByName(uName);
		BackRolePropertyItemQueryResult queryResult = BackManager.getInstance().rolePropertyItemQuery(rid);

		if (!queryResult.isSuccess()) {
			result.setStatus(Result.FAILED);
			result.setData(queryResult.getMsg());
			log.info("执行失败");
			return result;
		}

		if (queryResult.getRid() == 0) {
			result.setStatus(Result.FAILED);
			result.setData("no such role");
		} else {
			result.setData(queryResult.getStorageList());
		}

		return result;
	}

	@Path("/role/queryById")
	@Json
	public Map<String, Object> queryById(Request request) {
		Map<String, Object> map = new HashMap<>();
		if (!ParamUtil.signVerify(request, map, "uid")) {
			return map;
		}

		RoleResult result = BackManager.getInstance().queryRole(Long.parseLong(request.getParameter("uid")));

		if (!result.isSuccess()) {
			map.put(MapKey.STATUS, MapKey.BAD_REQUEST);
			map.put(MapKey.DATA, result.getMsg());
			return map;
		}

		map.put(MapKey.STATUS, MapKey.SUCCESS);
		map.put(MapKey.DATA, result);
		return map;
	}


	@Path("/role/queryByName")
	@Json
	public Map<String, Object> queryByName(Request request) {
		Map<String, Object> map = new HashMap<>();
		if (!ParamUtil.signVerify(request, map, "name")) {
			return map;
		}

		long uid = NameManager.getInstance().getRidByName(request.getParameter("name"));
		RoleResult result = BackManager.getInstance().queryRole(uid);

		if (!result.isSuccess()) {
			map.put(MapKey.STATUS, MapKey.BAD_REQUEST);
			map.put(MapKey.DATA, result.getMsg());
			return map;
		}

		map.put(MapKey.STATUS, MapKey.SUCCESS);
		map.put(MapKey.DATA, result);
		return map;
	}

	@Path("/role/changeState")
	@Json
	public Map<String, Object> stateChange(Request request) {
		Map<String, Object> map = new HashMap<>();
		if (!ParamUtil.signVerify(request, map, "rid")) {
			return map;
		}
		MessageResult result = BackManager.getInstance().reqOutsideRank(Long.parseLong(request.getParameter("rid"))
				, Byte.parseByte(request.getParameter("state")));

		if (!result.isSuccess()) {
			map.put(MapKey.STATUS, MapKey.BAD_REQUEST);
			map.put(MapKey.DATA, result.getMsg());
			return map;
		}

		map.put(MapKey.STATUS, MapKey.SUCCESS);
		map.put(MapKey.DATA, Byte.parseByte(result.getData()));
		return map;
	}


	@Path("/role/queryRank")
	@Json
	public Map<String, Object> queryRank(Request request) {
		Map<String, Object> map = new HashMap<>();
		if (!ParamUtil.signVerify(request, map, "type")) {
			return map;
		}
//		ReqLookRankMessage msg = new ReqLookRankMessage();
//		RankProtos.ReqLookRank proto = RankProtos.ReqLookRank.newBuilder()
//				.setType(Integer.parseInt(request.getParameter("type")))
//				.build();
//		msg.setProto(proto);
//		ResLookRankMessage message = (ResLookRankMessage) GameClientUtil.request0(msg, map);
//		if (message == null) {
//			return map;
//		}
//		map.put(MapKey.STATUS, MapKey.SUCCESS);
//		map.put(MapKey.DATA, message.getProto().getRankListList());
		return map;
	}

	@Path("/count/fautofuli")
	@Json
	public Map<String, Object> fautofuli(Request request) {
		log.info("url:{} params:{}", request.getUri(), request.getParameters());
		Map<String, Object> map = new HashMap<>();
		String rolename = request.getParameter("rolename");
		String oper = request.getParameter("oper");
		String serverid = request.getParameter("serverid");
		String rtime = request.getParameter("rtime");
		String rechargeId = request.getParameter("rechargeId");
		long rid = Long.parseLong(request.getParameter("rid"));
		String sign = request.getParameter("sign");
		String md5 = ParamUtil.md5(rolename, oper, serverid, rtime, rechargeId, rid);
		if (!sign.equals(md5)) {
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, "签名无效");
			log.error("fautofuli方法：请求发送玩家福利签名错误");
			log.error("签名失败,sign:{} md5:{}", sign, md5);
			return map;
		}
		MessageResult result = BackManager.getInstance().addItem(rid, Integer.parseInt(rechargeId), 1);

		if (!result.isSuccess()) {
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, result.getMsg());
			log.error("fautofuli方法：请求发送玩家福利,发放福利失败");
			return map;
		}
		map.put(MapKey.STATUS, MapKey.SUCCESS);
		map.put(MapKey.DATA, "成功");
		log.info("fautofuli方法：请求发送玩家福利发送成功");
		return map;
	}

	@Path("/role/kickAllRole")
	@Json
	public Map<String, Object> kickAllRole(Request request) {
		log.info("url:{} params:{}", request.getUri(), request.getParameters());
		String sign = request.getParameter("sign");
		Map<String, Object> map = new HashMap<>();
		if (!sign.equals(ParamUtil.md5(""))) {
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, "非法请求");
			return map;
		}
		MessageResult result = BackManager.getInstance().kickAllRole();
		if (!result.isSuccess()) {
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, result.getMsg());
			log.error("tAllRole方法：请求踢除玩家失败");
			return map;
		}
		map.put(MapKey.STATUS, MapKey.SUCCESS);
		map.put(MapKey.DATA, "成功");
		log.info("tAllRole方法：请求踢除玩家发送成功");
		return map;
	}

	@Path("/role/openGM")
	@Json
	public Map<String, Object> openRoleGM(Request request) {
		log.info("url:{} params:{}", request.getUri(), request.getParameters());
		String roleName = request.getParameter("roleName");
		long roleId = Long.parseLong(request.getParameter("roleId"));
		int state = Integer.parseInt(request.getParameter("state"));
		String sign = request.getParameter("sign");
		Map<String, Object> map = new HashMap<>();
		if (!sign.equals(ParamUtil.md5(roleName, roleId, state))) {
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, "非法请求");
			log.error("openRoleGM方法：签名错误");
			return map;
		}

		MessageResult result = BackManager.getInstance().openRoleGM(roleId, roleName, state);

		if (!result.isSuccess()) {
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, result.getMsg());
			log.error("openRoleGM方法：请求开放角色GM失败");
			return map;
		}
		map.put(MapKey.STATUS, MapKey.SUCCESS);
		map.put(MapKey.DATA, "成功");
		log.info("openRoleGM方法：发送成功");
		return map;
	}

	@Path("/role/roleLogicalOperation")
	@Json
	public Map<String, Object> roleLogicalOperation(Request req) {
		log.info("url:{} params:{}", req.getUri(), req.getParameters());
		Map<String, Object> map = new HashMap<>(8);
		long rid = Long.parseLong(req.getParameter("rid"));
		int type = Integer.parseInt(req.getParameter("type"));
		String sign = req.getParameter("sign");
		if (!sign.equals(ParamUtil.md5(rid ,type))) {
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, "非法请求");
			return map;
		}

		MessageResult result = BackManager.getInstance().roleLogicalOperation(rid, type);

		if (!result.isSuccess()) {
			log.info("执行失败");
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, result.getMsg());
			return map;
		}
		map.put(MapKey.STATUS, MapKey.SUCCESS);
		map.put(MapKey.DATA, "操作成功");
		log.info("玩家逻辑操作成功 , rid = {} , type = {}", rid, type);
		return map;
	}

	@Path("/role/fightPower")
	@Json
	public Result roleFightPower(Request request) {
		log.info("url:{} params:{}", request.getUri(), request.getParameters());
		Result result = new Result();
		if (!RequestValidator.validate(request, result, "rid")) {
			return result;
		}
		long rid = Long.parseLong(request.getParameter("rid"));

		MessageResult messageResult = BackManager.getInstance().roleFightPower(rid);

		if (!messageResult.isSuccess()) {
			result.setStatus(Result.FAILED);
			result.setData(messageResult.getMsg());
			return result;
		}
		result.setData(Long.parseLong(messageResult.getData()));
		return result;
	}

    @Path("/role/changeGameState")
    @Json
    public Result changeState(Request request) {
        log.info("url:{} params:{}", request.getUri(), request.getParameters());
        Result result = new Result();
		if (!RequestValidator.validate(request, result, "rid")) {
			return result;
		}
        long rid = Long.parseLong(request.getParameter("rid"));
        int state = Integer.parseInt(request.getParameter("state"));
        boolean open = Boolean.parseBoolean(request.getParameter("open"));

		MessageResult messageResult = BackManager.getInstance().changeState(rid, state, open);
		if (!messageResult.isSuccess()) {
			result.setStatus(Result.FAILED);
			result.setData(messageResult.getMsg());
			return result;
		}

		result.setStatus(Result.SUCCESS);
		result.setData("operation success");
        return result;
    }

	@Path("/role/watcherMode")
	@Json
	public Map<String, Object> watcherMode(Request req) {
		log.info("url:{} params:{}", req.getUri(), req.getParameters());
		Map<String, Object> map = new HashMap<>(8);
		long rid = Long.parseLong(req.getParameter("rid"));
		int open = Integer.parseInt(req.getParameter("open"));
		String sign = req.getParameter("sign");
		if (!sign.equals(ParamUtil.md5(rid, open))) {
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, "非法请求");
			return map;
		}

		MessageResult result = BackManager.getInstance().watcherMode(rid, open);
		if (!result.isSuccess()) {
			log.info("执行失败");
			map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
			map.put(MapKey.DATA, result.getMsg());
			return map;
		}
		map.put(MapKey.STATUS, MapKey.SUCCESS);
		map.put(MapKey.DATA, "操作成功");
		log.info("修改玩家观战模式成功，rid {} watcherMode {}", rid, open);
		return map;
	}
}
