package com.sh.game.api.controller;

import com.sh.commons.util.Cast;
import com.sh.game.api.constant.MapKey;
import com.sh.game.api.entity.MessageResult;
import com.sh.game.api.util.ParamUtil;
import com.sh.game.back.BackManager;
import com.sh.http.Request;
import com.sh.http.annotation.Controller;
import com.sh.http.annotation.Json;
import com.sh.http.annotation.Path;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/6 16:13
 */
@Controller
@Slf4j
public class ChatConditionController {

    @Path("/chat/settings")
    @Json
    public Map<String, Object> settings(Request req) {
        log.info("url:{}, params:{}", req.getUri(), req.getParameters());
        // 聊天频道
        int channel = Cast.toInteger(req.getParameter("channel"));
        // 1表示金额，2表示等级，3表示聊天间隔，4表示是与的关系，5 表示开服第几天启用 6 聊天总开关
        int type = Cast.toInteger(req.getParameter("type"));
        // 类型1时发开服天数#充值金额|开服天数#充值金额，类型2时发开服天数#玩家等级#转生等级|开服天数#玩家等级#转生等级，类型3时发秒
        String value = req.getParameter("value");

        Map<String, Object> map = new HashMap<>(10);
        String sign = req.getParameter("sign");
         if (!sign.equals(ParamUtil.md5(channel, type, value))) {
             map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
             map.put(MapKey.DATA, "非法清求");
             return map;
         }

        MessageResult result = BackManager.getInstance().chatSetting(channel, type, value);

        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            log.error("执行失败");
            return map;
        }
        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, "设置成功");
        log.info("发送成功");
        return map;
    }

    @Path("/chat/openChangeRoleName")
    @Json
    public Map<String, Object> settingChangeRoleName(Request req) {
        log.info("url:{}, params:{}", req.getUri(), req.getParameters());
        // 控制是否禁止玩家改名（自定义昵称
        boolean open = Boolean.parseBoolean(req.getParameter("open"));

        Map<String, Object> map = new HashMap<>(10);
        String sign = req.getParameter("sign");
         if (!sign.equals(ParamUtil.md5(open))) {
             map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
             map.put(MapKey.DATA, "非法清求");
             return map;
         }
        MessageResult result = BackManager.getInstance().openRoleChangeName(open);

        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            log.error("执行失败");
            return map;
        }
        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, "设置成功");
        log.info("发送成功");
        return map;
    }
}
