package com.sh.game.api.controller;

import com.sh.client.Client;
import com.sh.game.api.GameClientUtil;
import com.sh.game.api.constant.MapKey;
import com.sh.game.api.entity.MessageResult;
import com.sh.game.api.util.ParamUtil;
import com.sh.game.back.BackManager;
import com.sh.game.common.communication.msg.system.back.*;
import com.sh.game.common.communication.msg.system.union.ResSendAllUnionInfoMessage;
import com.sh.game.common.communication.msg.system.union.ResSendPlayerUnionInfoMessage;
import com.sh.game.protos.BackProtos;
import com.sh.game.protos.UnionProtos;
import com.sh.http.Request;
import com.sh.http.annotation.Controller;
import com.sh.http.annotation.Json;
import com.sh.http.annotation.Path;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Controller
@Slf4j
public class UnionController {
    @Path("/union/all")
    @Json
    public Map<String, Object> getAllUnion(Request req) {
        Map<String, Object> map = new HashMap<>(3);

        if (!ParamUtil.signValidate(req, Long.parseLong(req.getParameter("time")))) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            return map;
        }

//        ResSendAllUnionInfoMessage res = (ResSendAllUnionInfoMessage) GameClientUtil.request0(new ReqSendAllUnionMessage(), map);
//        if ((int) map.get(MapKey.STATUS) != MapKey.SUCCESS) {
//            return map;
//        }
//        List<UnionProtos.UnionInfoBean> unionInfoBeanList = new ArrayList<>();
//        if (res != null) {
//            UnionProtos.ResSendAllUnionInfo proto = res.getProto();
//            unionInfoBeanList.addAll(proto.getUnionInfoList());
//        }
//        map.put(MapKey.DATA, unionInfoBeanList);
        return map;
    }


    @Path("/union/query")
    @Json
    public Map<String, Object> query(Request req) {
        Map<String, Object> map = new HashMap<>(3);
        log.info("url:{} params:{}", req.getUri(), req.getParameters());
        long uid = Long.parseLong(req.getParameter("uid"));
        if (!ParamUtil.signValidate(req, uid)) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            return map;
        }

//        ReqSendUnionInfoMessage msg = new ReqSendUnionInfoMessage();
//        BackProtos.ReqSendUnionInfo reqProto = BackProtos.ReqSendUnionInfo.newBuilder()
//                .setUid(uid)
//                .build();
//        msg.setProto(reqProto);
//        ResSendPlayerUnionInfoMessage res = (ResSendPlayerUnionInfoMessage) GameClientUtil.request0(msg, map);
//        if ((int) map.get(MapKey.STATUS) != MapKey.SUCCESS) {
//            return map;
//        }
//        if (res == null || res.getProto() == null || res.getProto().getUnionInfo() == null) {
//            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
//            map.put(MapKey.DATA, "该帮会不存在");
//            return map;
//        }
//
//        UnionProtos.ResSendPlayerUnionInfo resProto = res.getProto();
//        map.put(MapKey.DATA, resProto.getUnionInfo().getUnionName());
//        map.put("members", resProto.getMemberInfoList());
        return map;
    }

    @Path("/union/changeLeader")
    @Json
    public Map<String, Object> changeLeader(Request req) {
        Map<String, Object> map = new HashMap<>(10);

        log.info("url:{} params:{}", req.getUri(), req.getParameters());
        long uid = Long.parseLong(req.getParameter("uid"));
        long rid = Long.parseLong(req.getParameter("rid"));
        if (!ParamUtil.signValidate(req, uid, rid)) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            return map;
        }

        MessageResult result = BackManager.getInstance().backChangeUnionMaster(uid, rid, "");
        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            return map;
        }
        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, result.getData());
        return map;
    }


    @Path("/union/changeAnnouncement")
    @Json
    public Map<String, Object> changeAnnouncement(Request req) {
        Map<String, Object> map = new HashMap<>(10);
        log.info("url:{} params:{}", req.getUri(), req.getParameters());
        long uid = Long.parseLong(req.getParameter("uid"));
        String announcement = req.getParameter("announcement");
        if (!ParamUtil.signValidate(req, uid, announcement)) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            return map;
        }

        MessageResult result = BackManager.getInstance().backChangeUnionAnnounce(uid, announcement);

        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            return map;
        }
        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, result.getData());
        return map;
    }


    @Path("/union/delete")
    @Json
    public Map<String, Object> delete(Request req) {
        Map<String, Object> map = new HashMap<>(10);
        log.info("url:{} params:{}", req.getUri(), req.getParameters());
        long uid = Long.parseLong(req.getParameter("uid"));
        if (!ParamUtil.signValidate(req, uid)) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            return map;
        }

        MessageResult result = BackManager.getInstance().deleteUnion(uid);
        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            return map;
        }

        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, "帮会" + uid + "删除成功！");

        return map;
    }

    @Path("/union/changePosition")
    @Json
    public Map<String, Object> changePosition(Request request) {
        Map<String, Object> map = new HashMap<>();
        log.info("url:{} params:{}", request.getUri(), request.getParameters());
        long roleId = Long.parseLong(request.getParameter("roleId"));
        int position = Integer.parseInt(request.getParameter("position"));
        if (!ParamUtil.signValidate(request, roleId, position)) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, "签名错误");
            log.error("changePosition方法:签名错误");
            return map;
        }

        MessageResult result = BackManager.getInstance().changePosition(roleId, position);
        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            return map;
        }

        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, "修改职位成功");
        log.info("changePosition方法：{}", "修改职位成功");
        return map;
    }

    @Path("/union/changeUnionName")
    @Json
    public Map<String, Object> changeUnionName(Request request) {
        Map<String, Object> map = new HashMap<>();
        log.info("url:{} params:{}", request.getUri(), request.getParameters());
        long unionId = Long.parseLong(request.getParameter("unionId"));
        String unionName = request.getParameter("unionName");
        if (!ParamUtil.signValidate(request, unionId, unionName)) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, "签名错误");
            log.error("changeUnionName方法:签名错误");
            return map;
        }

        MessageResult result = BackManager.getInstance().changeUnionName(unionId, unionName);
        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            return map;
        }

        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, result.getData());
        log.info("changeUnionName方法：{}", result.getData());
        return map;
    }

    @Path("/union/removeMember")
    @Json
    public Map<String, Object> removeMember(Request request) {
        Map<String, Object> map = new HashMap<>();
        log.info("url:{} params:{}", request.getUri(), request.getParameters());
        long unionId = Long.parseLong(request.getParameter("unionId"));
        long roleId = Long.parseLong(request.getParameter("roleId"));
        if (!ParamUtil.signValidate(request, unionId, roleId)) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, "签名错误");
            log.error("removeMember方法:签名错误");
            return map;
        }

        MessageResult result = BackManager.getInstance().removeMember(unionId, roleId);

        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            return map;
        }

        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, result.getData());
        log.info("removeMember方法：{}", result.getData());
        return map;
    }

    @Path("/union/enable")
    @Json
    public Map<String, Object> unionEnableSetting(Request request) {
        log.info("url:{}, params:{}", request.getUri(), request.getParameters());

        boolean enable = Boolean.parseBoolean(request.getParameter("enable"));
        String sign = request.getParameter("sign");

        Map<String, Object> map = new HashMap<>();
        if (!sign.equals(ParamUtil.md5(enable))) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, "非法清求");
            return map;
        }

        MessageResult result = BackManager.getInstance().customNameEnableSetting(enable);
        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            return map;
        }

        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, "设置成功");
        return map;
    }

    @Path("/union/settings")
    @Json
    public Map<String, Object> unionCreateConditionSetting(Request request) {
        log.info("url:{}, params:{}", request.getUri(), request.getParameters());

        String value = request.getParameter("value");
        String sign = request.getParameter("sign");

        Map<String, Object> map = new HashMap<>();
        // if (!sign.equals(ParamUtil.md5(value))) {
        //     map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
        //     map.put(MapKey.DATA, "非法清求");
        //     return map;
        // }

        MessageResult result = BackManager.getInstance().unionCreateConditionSetting(value);
        if (!result.isSuccess()) {
            map.put(MapKey.STATUS, MapKey.AVAILABLE_REQUEST);
            map.put(MapKey.DATA, result.getMsg());
            return map;
        }

        map.put(MapKey.STATUS, MapKey.SUCCESS);
        map.put(MapKey.DATA, "设置成功");
        return map;
    }
}
