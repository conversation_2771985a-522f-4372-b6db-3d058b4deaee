package com.sh.game.event;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.system.role.entity.Hero;
import com.sh.script.IScript;

public interface IEventOnRoleOrHeroAtkChanged extends IScript {

    default void onRoleAtkChanged(Role role) {

    }

    default void onRoleAtkMaxChanged(Role role) {

    }

    default void onHeroAtkChanged(Hero hero) {

    }

    default void onHeroAtkMaxChanged(Hero hero) {

    }
}
