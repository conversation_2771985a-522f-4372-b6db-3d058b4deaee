package com.sh.game.event;

import com.sh.game.common.entity.usr.Role;
import com.sh.game.scene.MapProxy;
import com.sh.script.IScript;

public interface IEventOnRoleDieScript extends IScript {

    /**
     * 玩家死亡事件
     *
     * @param role
     * @param killerId 击杀者编号
     * @param killerName 名称
     * @param mapProxy 所在地图
     * @param x 坐标
     * @param y 坐标
     * @param dropAdd 掉落属性增益
     * @param dropResist 掉落属性减免
     */
    void onRoleDie(Role role, long killerId, String killerName, MapProxy mapProxy, int x, int y, int dropAdd, int dropResist);
}
