package com.sh.game.scene.alloc;

import com.sh.game.common.entity.map.MapAllocContext;
import com.sh.game.common.util.IDUtil;
import com.sh.game.scene.MapProxy;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/9/22 20:24
 */
@Slf4j
public class MapAllocDuplicateZhuCheng extends MapAlloc {

    @Override
    public MapProxy alloc(MapAllocContext context) {
        if (context.getMapId() == 0) {
            // TODO map key gen
            context.setMapId((long) 1 << 32 | IDUtil.getDuplicateId());
        }

        MapProxy mapProxy = maps.get(context.getMapId());
        if (mapProxy == null) {
            mapProxy = create(context.getMapId());
        }

        return mapProxy;
    }

    @Override
    public void destroy() {
        for (MapProxy mapProxy : maps.values()) {
            mapProxy.destroy();
        }
    }

    @Override
    public MapProxy getOne() {
        long id = (long) 1 << 32 | IDUtil.getDuplicateId();
        return create(id);
    }
}
