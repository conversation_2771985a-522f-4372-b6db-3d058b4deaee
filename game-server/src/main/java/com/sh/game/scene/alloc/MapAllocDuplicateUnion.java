package com.sh.game.scene.alloc;


import com.sh.game.common.entity.map.MapAllocContext;
import com.sh.game.scene.MapProxy;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class MapAllocDuplicateUnion extends MapAlloc {

    @Override
    public MapProxy alloc(MapAllocContext context) {
        if (context.getUnionId() == 0) {
            log.error("union id is 0, can not alloc union duplicate");
            return null;
        }
        context.setMapId(context.getUnionId());

        MapProxy mapProxy = maps.get(context.getMapId());
        if (mapProxy == null) {
            mapProxy = create(context.getMapId());
        }

        return mapProxy;
    }
}
