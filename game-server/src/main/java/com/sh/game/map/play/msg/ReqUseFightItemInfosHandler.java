package com.sh.game.map.play.msg;

import com.sh.game.map.play.MapPlayManager;
import com.sh.game.common.communication.msg.map.play.ReqUseFightItemInfosMessage;
import com.sh.game.server.SessionUtil;
import com.sh.server.AbstractHandler;

/**
 * <p>请求使用道具次数信息</p>
 * <p>Created by MessageUtil</p>
 * @date 2019-01-06 15:51:40
 */
public class ReqUseFightItemInfosHandler extends AbstractHandler<ReqUseFightItemInfosMessage> {

    @Override
    public void doAction(ReqUseFightItemInfosMessage msg) {
        MapPlayManager.getInstance().getUseFightItemInfos(SessionUtil.getRole(msg.getSession()));

    }

}
