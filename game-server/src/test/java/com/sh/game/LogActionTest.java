package com.sh.game;

import com.sh.game.common.constant.LogAction;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashSet;
import java.util.Set;

public class LogActionTest {
    @Test
    public void checkLogAction() {
        Set<Integer> codes = new HashSet<>();
        for (LogAction value : LogAction.values()) {
            if (!codes.add(value.getCode())) {
                Assert.fail("LogAction#Log码：{" + value.getCode() + "}重复，行为：{" + value.getComment() + "}");
            }
        }
    }
}
